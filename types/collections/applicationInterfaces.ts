/* tslint:disable */
/* eslint-disable */
/**
/* This file was automatically generated from pydantic models by running pydantic2ts.
/* Do not modify it by hand - just update the pydantic models and then re-run the script
*/

export interface Application {
  senderId: string;
  recipientId: string;
  status: string;
  appliedAt: Date;
  commission: number;
}
export interface ApplicationMatcher {
  senderId: string;
  recipientId: string;
  status: string;
  appliedAt: Date;
  commission: number;
  producerDisplayName: string;
}
/**
 * Base model config for all models
 */
export interface BaseModelConfig {}
export interface CommissionValidatorMixin {}
