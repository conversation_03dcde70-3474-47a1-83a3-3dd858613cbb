/* tslint:disable */
/* eslint-disable */
/**
/* This file was automatically generated from pydantic models by running pydantic2ts.
/* Do not modify it by hand - just update the pydantic models and then re-run the script
*/

export interface Address {
  streetNumber?: string | null;
  streetName: string;
  houseNumber?: string | null;
  additionalInfo?: string | null;
  zipCode: string;
  city: string;
  state?: string | null;
  country: string;
}
/**
 * Base model config for all models
 */
export interface BaseModelConfig {}
export interface CommissionValidatorMixin {}
/**
 * new model, going to be used for the new store
 */
export interface StoreV2 {
  createdAt: Date;
  displayName: string;
  email: string;
  parentId: string;
  taxA2: string;
  defaultCommission?: number | null;
  shopifyApiKeyReference?: string | null;
  description?: string | null;
  address?: Address | null;
}
