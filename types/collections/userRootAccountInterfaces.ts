/* tslint:disable */
/* eslint-disable */
/**
/* This file was automatically generated from pydantic models by running pydantic2ts.
/* Do not modify it by hand - just update the pydantic models and then re-run the script
*/

/**
 * Base model config for all models
 */
export interface BaseModelConfig {}
export interface UserRootAccount {
  createdAt: Date;
  email: string;
  stores: {
    [k: string]: string;
  };
  producers: {
    [k: string]: string;
  };
  updatedAt?: Date | null;
}
