/* tslint:disable */
/* eslint-disable */
/**
/* This file was automatically generated from pydantic models by running pydantic2ts.
/* Do not modify it by hand - just update the pydantic models and then re-run the script
*/

/**
 * Base model config for all models
 */
export interface BaseModelConfig {}
export interface Sale {
  storeId: string;
  orderId: string;
  lineItemId: string;
  productId: string | null;
  title: string;
  variantTitle: string | null;
  variantDisplayName: string | null;
  quantity: number;
  unitPrice: number;
  subtotal: number;
  totalPrice: number;
  currency: string;
  discount: number;
  vendor: string | null;
  updatedAt: Date;
  storeDisplayName: string;
}
/**
 * Adding fields for the invoice generation
 */
export interface SalesGold {
  storeId: string;
  orderId: string;
  lineItemId: string;
  productId: string | null;
  title: string;
  variantTitle: string | null;
  variantDisplayName: string | null;
  quantity: number;
  unitPrice: number;
  subtotal: number;
  totalPrice: number;
  currency: string;
  discount: number;
  vendor: string | null;
  updatedAt: Date;
  storeDisplayName: string;
  documentId: string;
  producerDisplayName: string;
  producerId: string;
  commission: number;
  producerTaxA2: string;
  storeTaxA2: string;
  agreementId?: string;
  saleId: string;
  assignedVatRate?: number | null;
  vatExcludedSale?: number | null;
  netSales?: number | null;
  storeNetPayout?: number | null;
  vatOnSalesService?: number | null;
  storeGrossPayout: number;
  producerGrossPayout: number;
  invoiceId?: string | null;
  salesReportId?: string | null;
}
export interface SalesSilver {
  storeId: string;
  orderId: string;
  lineItemId: string;
  productId: string | null;
  title: string;
  variantTitle: string | null;
  variantDisplayName: string | null;
  quantity: number;
  unitPrice: number;
  subtotal: number;
  totalPrice: number;
  currency: string;
  discount: number;
  vendor: string | null;
  updatedAt: Date;
  storeDisplayName: string;
  documentId: string;
  producerDisplayName: string;
  producerId: string;
  commission: number;
  producerTaxA2: string;
  storeTaxA2: string;
  agreementId?: string;
}
export interface SalesStaging {
  storeId: string;
  orderId: string;
  lineItemId: string;
  productId: string | null;
  title: string;
  variantTitle: string | null;
  variantDisplayName: string | null;
  quantity: number;
  unitPrice: number;
  subtotal: number;
  totalPrice: number;
  currency: string;
  discount: number;
  vendor: string | null;
  updatedAt: Date;
  storeDisplayName: string;
  documentId: string;
  agreementIds?: string[] | null;
}
