/* tslint:disable */
/* eslint-disable */
/**
/* This file was automatically generated from pydantic models by running pydantic2ts.
/* Do not modify it by hand - just update the pydantic models and then re-run the script
*/

export type Role = "store" | "producer" | "system";

export interface Agreement {
  storeId: string;
  producerId: string;
  partnershipId?: string | null;
  status?: string;
  title?: string | null;
  effectiveDate: Date;
  expirationDate?: Date | null;
  version?: string;
  documentUrl?: string | null;
  commission: number;
  approvalWorkflow: ApprovalStep[];
  negotiationHistory?: NegotiationChange[] | null;
  createdBy: string;
  createdByRole: Role;
  createdAt: Date;
  updatedBy?: string | null;
  updatedAt?: Date | null;
  renewedBy?: string | null;
}
export interface ApprovalStep {
  role: Role;
  approverId?: string | null;
  status?: string;
  comments?: string | null;
  timestamp?: Date | null;
}
export interface NegotiationChange {
  userId: string;
  role: Role;
  timestamp: Date;
  changes: {
    [k: string]: unknown;
  };
  comments?: string | null;
}
/**
 * Base model config for all models
 */
export interface BaseModelConfig {}
export interface CommissionValidatorMixin {}
