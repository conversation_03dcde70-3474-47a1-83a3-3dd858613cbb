/* tslint:disable */
/* eslint-disable */
/**
/* This file was automatically generated from pydantic models by running pydantic2ts.
/* Do not modify it by hand - just update the pydantic models and then re-run the script
*/

/**
 * Base model config for all models
 */
export interface BaseModelConfig {}
/**
 * Represents a producer in the database.
 * these key are necessary for the invoice data
 * computation
 */
export interface Producer {
  createdAt: Date;
  displayName: string;
  email: string;
  taxA2: string;
  parentId: string;
  createdBy: string;
  createdByStoreId?: string | null;
}
