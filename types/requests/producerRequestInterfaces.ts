/* tslint:disable */
/* eslint-disable */
/**
/* This file was automatically generated from pydantic models by running pydantic2ts.
/* Do not modify it by hand - just update the pydantic models and then re-run the script
*/

/**
 * Base model config for all models
 */
export interface BaseModelConfig {}
/**
 * Contract between the FE and BE
 */
export interface CreateProducerRequest {
  displayName: string;
  email: string;
  taxA2: string;
  accessRight?: string;
}
export interface DeleteProducerRequest {
  producerId: string;
  hardDelete?: boolean;
}
