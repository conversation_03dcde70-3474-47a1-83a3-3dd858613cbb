/* tslint:disable */
/* eslint-disable */
/**
/* This file was automatically generated from pydantic models by running pydantic2ts.
/* Do not modify it by hand - just update the pydantic models and then re-run the script
*/

export interface Address {
  streetNumber?: string | null;
  streetName: string;
  houseNumber?: string | null;
  additionalInfo?: string | null;
  zipCode: string;
  city: string;
  state?: string | null;
  country: string;
}
/**
 * Base model config for all models
 */
export interface BaseModelConfig {}
export interface CommissionValidatorMixin {}
/**
 * This is the request object for the create store function,
 * contract between the FE and BE,
 * with request, we should keep it as flat as possible.
 */
export interface CreateStoreRequest {
  displayName: string;
  email: string;
  taxA2: string;
  defaultCommission?: number | null;
  description?: string | null;
  address?: Address | null;
  shopifyApiKeyObject: ShopifyStoreCredentials;
  accessRight?: string;
}
export interface ShopifyStoreCredentials {
  shopName: string;
  shopifyApiKey: string;
}
export interface DeleteStoreRequest {
  storeId: string;
  hardDelete?: boolean;
}
