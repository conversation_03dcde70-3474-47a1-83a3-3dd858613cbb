/* tslint:disable */
/* eslint-disable */
/**
/* This file was automatically generated from pydantic models by running pydantic2ts.
/* Do not modify it by hand - just update the pydantic models and then re-run the script
*/

export interface CommissionValidatorMixin {}
export interface CreatePartnerRequest {
  senderId: string;
  recipientId: string;
  startDate: string;
  endDate: string;
  commission: number;
  contractLink?: string | null;
}
export interface PartnerBase {
  senderId: string;
  recipientId: string;
  startDate: string;
  endDate: string;
}
export interface UpdatePartnerRequest {
  senderId: string;
  recipientId: string;
  startDate: string;
  endDate: string;
}
