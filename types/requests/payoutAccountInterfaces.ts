/* tslint:disable */
/* eslint-disable */
/**
/* This file was automatically generated from pydantic models by running pydantic2ts.
/* Do not modify it by hand - just update the pydantic models and then re-run the script
*/

export interface Address {
  streetNumber?: string | null;
  streetName: string;
  houseNumber?: string | null;
  additionalInfo?: string | null;
  zipCode: string;
  city: string;
  state?: string | null;
  country: string;
}
/**
 * Base model config for all models
 */
export interface BaseModelConfig {}
export interface PayoutAccount {
  paymentReferenceId: string;
  accountHolderName: string;
  email: string;
  phoneNumber?: string | null;
  address: Address;
  companyName?: string | null;
  companyNumber?: string | null;
  vatNumber?: string | null;
  accountType: string;
  bankName: string;
  bankCountry: string;
  currency: string;
  bankAccountNumber: string;
  iban?: string | null;
  routingNumber?: string | null;
  sortCode?: string | null;
  bic?: string | null;
  taxResidenceCountry?: string | null;
  payoutMethod?: string;
}
