/* tslint:disable */
/* eslint-disable */
/**
/* This file was automatically generated from pydantic models by running pydantic2ts.
/* Do not modify it by hand - just update the pydantic models and then re-run the script
*/

/**
 * Base model config for all models
 */
export interface BaseModelConfig {}
export interface CommissionValidatorMixin {}
export interface ForceAgreementRequest {
  storeId: string;
  title?: string | null;
  effectiveDate: Date;
  expirationDate?: Date | null;
  commission: number;
  documentUrl?: string | null;
  displayName: string;
  email: string;
  taxA2: string;
}
export interface SanitizeSalesStagingWithAgreementRequest {
  storeId: string;
  title?: string | null;
  effectiveDate: Date;
  expirationDate?: Date | null;
  commission: number;
  documentUrl?: string | null;
  displayName: string;
  email: string;
  taxA2: string;
  saleId: string;
}
