/* tslint:disable */
/* eslint-disable */
/**
/* This file was automatically generated from pydantic models by running pydantic2ts.
/* Do not modify it by hand - just update the pydantic models and then re-run the script
*/

export type Role = "store" | "producer" | "system";
export type Role1 = "store" | "producer" | "system";

export interface ApproveAgreementRequest {
  agreementId: string;
  storeOrProducerId: string;
  role: string;
  comments?: string | null;
}
/**
 * Base model with Gaco-specific configuration
 */
export interface BaseGacoModel {}
/**
 * Base model config for all models
 */
export interface BaseModelConfig {}
export interface CommissionValidatorMixin {}
export interface CreateAgreementRequest {
  storeId: string;
  producerId: string;
  title?: string | null;
  effectiveDate: Date;
  expirationDate?: Date | null;
  commission: number;
  documentUrl?: string | null;
  createdByRole: Role;
}
export interface DeleteAgreementRequest {
  agreementId: string;
}
/**
 * Base class for all request models
 */
export interface GacoRequest {}
export interface RejectAgreementRequest {
  agreementId: string;
  role: string;
  comments?: string | null;
}
export interface SanitizeSaleStagingWithExistingAgreementRequest {
  saleId: string;
  agreementId: string;
}
/**
 * Actual task payload that is conencted to a document with data.
 * This is used to enqueue and execute the task.
 */
export interface SanitizeSaleWithAgreementTask {
  sanitizationRequestId: string;
}
/**
 * Content of the task to be executed.
 */
export interface SanitizeSaleWithAgreementTaskRequest {
  sanitizationRequestId: string;
  data: SanitizeSaleStagingWithExistingAgreementRequest;
}
export interface SanitizeSalesStagingWithActiveAgreementRequest {
  storeId: string;
  producerId: string;
  title?: string | null;
  effectiveDate: Date;
  expirationDate?: Date | null;
  commission: number;
  documentUrl?: string | null;
  createdByRole?: Role1;
  saleId: string;
}
export interface SanitizeSalesStagingWithExistingAgreementRequest {
  saleIds: string[];
  agreementId: string;
}
export interface SubmitAgreementForApprovalRequest {
  agreementId: string;
  role: string;
}
export interface TerminateAgreementRequest {
  agreementId: string;
}
export interface UpdateDraftAgreementRequest {
  agreementId: string;
  title?: string | null;
  effectiveDate?: Date | null;
  expirationDate?: Date | null;
  commission?: number | null;
  documentUrl?: string | null;
}
