/* tslint:disable */
/* eslint-disable */
/**
/* This file was automatically generated from pydantic models by running pydantic2ts.
/* Do not modify it by hand - just update the pydantic models and then re-run the script
*/

export type AllocationStatus =
  | "PENDING_CONFIRMATION"
  | "PENDING_SHIPMENT"
  | "IN_TRANSIT"
  | "DELIVERED"
  | "RECEIVED_WITH_ISSUES"
  | "CANCELLED";

/**
 * Base model config for all models
 */
export interface BaseModelConfig {}
export interface GetAllocationRequest {
  /**
   * The ID of the product allocation to retrieve.
   */
  allocationId: string;
}
export interface ListAllocationsByProducerRequest {
  /**
   * The logical ID of the producer whose allocations are to be listed.
   */
  producerId: string;
  /**
   * Optional status to filter allocations by.
   */
  status?: AllocationStatus | null;
}
export interface ListAllocationsByStoreRequest {
  /**
   * The logical ID of the store whose allocations are to be listed.
   */
  storeId: string;
  /**
   * Optional status to filter allocations by.
   */
  status?: AllocationStatus | null;
}
export interface UpdateAllocationStatusRequest {
  /**
   * The ID of the product allocation to update.
   */
  allocationId: string;
  /**
   * The new status for the allocation.
   */
  newStatus: string;
  /**
   * Optional dictionary for additional details like tracking_number, notes, etc.
   */
  details?: {
    [k: string]: unknown;
  } | null;
}
