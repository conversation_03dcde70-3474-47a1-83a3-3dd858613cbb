/* tslint:disable */
/* eslint-disable */
/**
/* This file was automatically generated from pydantic models by running pydantic2ts.
/* Do not modify it by hand - just update the pydantic models and then re-run the script
*/

export type Platform = "shopify" | "other";

/**
 * Base model config for all models
 */
export interface BaseModelConfig {}
/**
 * Request model for getting Shopify orders.
 */
export interface GetShopifyOrdersRequest {
  storeId: string;
  startDate: string;
  endDate: string;
}
/**
 * Request model for getting store orders that will be coming
 * from the client.
 */
export interface GetShopifyOrdersWithDynamicQueryRequest {
  storeId: string;
  type?: Platform;
  forceRefresh?: boolean | null;
  daysBack?: number | null;
}
