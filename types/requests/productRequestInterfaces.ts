/* tslint:disable */
/* eslint-disable */
/**
/* This file was automatically generated from pydantic models by running pydantic2ts.
/* Do not modify it by hand - just update the pydantic models and then re-run the script
*/

/**
 * Base model config for all models
 */
export interface BaseModelConfig {}
export interface DeleteProductRequest {
  /**
   * The logical ID of the product to delete.
   */
  logicalProductId: string;
  /**
   * Whether to delete associated variants. Defaults to True.
   */
  deleteVariants?: boolean | null;
}
export interface DeleteVariantRequest {
  /**
   * The logical ID of the variant to delete.
   */
  logicalVariantId: string;
}
export interface GetProductRequest {
  /**
   * The logical ID of the product to retrieve.
   */
  logicalProductId: string;
}
export interface GetVariantRequest {
  /**
   * The logical ID of the variant to retrieve.
   */
  logicalVariantId: string;
}
export interface GetVariantsForProductRequest {
  /**
   * The logical ID of the parent product whose variants are to be retrieved.
   */
  logicalProductId: string;
}
export interface UpdateProductRequest {
  /**
   * The logical ID of the product to update.
   */
  logicalProductId: string;
  /**
   * A dictionary containing the fields to update and their new values.
   */
  updateData: {
    [k: string]: unknown;
  };
}
export interface UpdateVariantRequest {
  /**
   * The logical ID of the variant to update.
   */
  logicalVariantId: string;
  /**
   * A dictionary containing the fields to update and their new values.
   */
  updateData: {
    [k: string]: unknown;
  };
}
