/* tslint:disable */
/* eslint-disable */
/**
/* This file was automatically generated from pydantic models by running pydantic2ts.
/* Do not modify it by hand - just update the pydantic models and then re-run the script
*/

export interface ApproveStoreProductRequest {
  /**
   * The ID of the store product request to approve.
   */
  requestId: string;
  /**
   * The ID of the producer approving the request.
   */
  actingProducerId: string;
  /**
   * Optional notes from the producer.
   */
  producerResponseNotes?: string | null;
  /**
   * Delivery method for the allocation that will be created.
   */
  deliveryMethodForAllocation?: string;
  /**
   * Specific notes for the auto-created product allocation.
   */
  allocationProducerNotes?: string | null;
}
/**
 * Base model config for all models
 */
export interface BaseModelConfig {}
export interface CancelStoreProductRequest {
  /**
   * The ID of the store product request to cancel.
   */
  requestId: string;
  /**
   * The ID of the store cancelling the request.
   */
  actingStoreId: string;
}
export interface RejectStoreProductRequest {
  /**
   * The ID of the store product request to reject.
   */
  requestId: string;
  /**
   * The ID of the producer rejecting the request.
   */
  actingProducerId: string;
  /**
   * Reason for rejection or other notes from the producer.
   */
  producerResponseNotes?: string | null;
}
