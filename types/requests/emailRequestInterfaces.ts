/* tslint:disable */
/* eslint-disable */
/**
/* This file was automatically generated from pydantic models by running pydantic2ts.
/* Do not modify it by hand - just update the pydantic models and then re-run the script
*/

/**
 * Base model config for all models
 */
export interface BaseModelConfig {}
export interface Email {
  fromAlias: string;
  fromEmail: string;
  toAlias: string;
  toEmail: string;
  subject: string;
  text: string;
}
export interface EmailTemplate {
  templateDisplayName: string;
  storeId: string;
  subject: string;
  text: string;
}
export interface SendEmailRequest {
  email: Email;
  senderId: string;
  recipientId: string;
}
export interface SendEmailTask {
  email_request_id: string;
}
export interface SendEmailTaskRequest {
  emailRequestId: string;
  emailRequest: SendEmailRequest;
}
export interface SendInviteEmailRequest {
  templateId?: string;
  storeId: string;
  producerId: string;
}
export interface SendSalesReportEmailRequest {
  templateId?: string;
  storeId: string;
  salesReportId: string;
}
