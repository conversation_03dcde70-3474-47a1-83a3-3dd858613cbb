/** @type {import('next').NextConfig} */

import path from "path";

const nextConfig = {
  compiler: {
    styledComponents: true,
  },
  webpack(config) {
    config.resolve.alias = {
      ...config.resolve.alias,
      "styled-components": path.resolve("./node_modules/styled-components"),
    };

    return config;
  },
  images: {
    domains: ["firebasestorage.googleapis.com"],
  },
  env: {
    NEXT_PUBLIC_API_KEY: process.env.NEXT_PUBLIC_API_KEY,
    NEXT_PUBLIC_AUTH_DOMAIN: process.env.NEXT_PUBLIC_AUTH_DOMAIN,
    NEXT_PUBLIC_DATABASE_URL: process.env.NEXT_PUBLIC_DATABASE_URL,
    NEXT_PUBLIC_PROJECT_ID: process.env.NEXT_PUBLIC_PROJECT_ID,
    NEXT_PUBLIC_STORAGE_BUCKET: process.env.NEXT_PUBLIC_STORAGE_BUCKET,
    NEXT_PUBLIC_MESSAGING_SENDER_ID:
      process.env.NEXT_PUBLIC_MESSAGING_SENDER_ID,
    NEXT_PUBLIC_APP_ID: process.env.NEXT_PUBLIC_APP_ID,
    NEXT_PUBLIC_MEASUREMENT_ID: process.env.NEXT_PUBLIC_MEASUREMENT_ID,
    NEXT_PUBLIC_MAPTILER_API_KEY: process.env.NEXT_PUBLIC_MAPTILER_API_KEY,
    NEXT_PUBLIC_FIRESTORE_EMULATOR: process.env.NEXT_PUBLIC_FIRESTORE_EMULATOR,
  },
};

export default nextConfig;
