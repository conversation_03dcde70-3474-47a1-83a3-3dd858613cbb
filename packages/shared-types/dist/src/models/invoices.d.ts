import { Timestamp } from "firebase/firestore";
export interface InvoiceResponse {
  id: string;
  createdAt: Timestamp;
  created_at: Timestamp;
  download_url?: string;
  factuur_date: string;
  invoice_id: string;
  payout_due_date: string;
  producer_level: {
    net_sales: number;
    producer_gross_payout: number;
    producer_id: string;
    store_net_payout: number;
    store_total_gross_payout: number;
    subtotal: number;
    vat_excluded_sales: number;
    vat_excluded_service: number;
  };
  status: string;
  store_id: string;
  updated_at: Timestamp;
  uri: string;
}
