import { Timestamp } from "firebase/firestore";
export declare enum AccountType {
    PRODUCER = "producer",
    STORE = "store"
}
export interface AccountCreatePayload {
    email: string;
    displayName: string;
    userType: string;
    uuid: string;
}
export interface SanitizeSalesWithNewProducerPayload {
    producerName: string;
    email: string;
    country: string;
    commission: number;
    saleId: string;
}
export interface UploadJsonPayload<T = Record<string, unknown>> {
    collection_name: string;
    document_id?: string;
    data: T;
}
export interface ApplicationPayload {
    senderId: string;
    recipientId: string;
}
export declare enum ApplicationResponseStatus {
    PENDING = "pending",
    ACCEPTED = "accepted",
    REJECTED = "rejected"
}
export interface ApplicationResponse {
    commission: number;
    appliedAt: Timestamp;
    senderId: string;
    recipientId: string;
    status: ApplicationResponseStatus;
    createdAt: Timestamp;
}
export interface PendingApplicationPayload extends ApplicationPayload {
    commission: number;
}
