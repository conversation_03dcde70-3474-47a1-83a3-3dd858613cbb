export interface ShopifyOrders {
  orders: OrderConnection;
}

export interface OrderConnection {
  __typename: string;
  edges: OrderEdge[];
}

export interface OrderEdge {
  __typename: string;
  node: Order;
}

export interface Order {
  __typename: string;
  id: string;
  updatedAt: string;
  closed: boolean;
  fullyPaid: boolean;
  currentSubtotalPriceSet: MoneyBag;
  cartDiscountAmountSet: MoneyBag | null;
  lineItems: LineItemConnection;
}

export interface MoneyBag {
  __typename: string;
  shopMoney: MoneyV2;
}

export interface MoneyV2 {
  __typename: string;
  amount: string;
  currencyCode: string;
}

export interface LineItemConnection {
  __typename: string;
  edges: LineItemEdge[];
}

export interface LineItemEdge {
  __typename: string;
  node: LineItem;
}

export interface LineItem {
  __typename: string;
  id: string;
  title: string;
  quantity: number;
  vendor: string;
  product: Product | null;
  variantTitle: string | null;
  variant: ProductVariant | null;
}

export interface Product {
  __typename: string;
  id: string;
}

export interface ProductVariant {
  __typename: string;
  displayName: string;
  price: string;
}