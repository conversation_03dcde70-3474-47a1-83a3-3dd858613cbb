export interface OverviewStore {
  total_revenue: number;
  total_artists: number;
  sales_count: number;
  sales_data: SalesDataEntryArtist[];
  line_data_keys: string[];
}

export interface SalesDataEntryArtist {
  name: string;
  total_sales: number;
  [artistName: string]: number | string;
}

export interface OverviewArtist {
  total_revenue: number;
  total_artists: number;
  sales_count: number;
  sales_data: SalesDataEntryStore[];
  line_data_keys: string[];
}

export interface SalesDataEntryStore {
  name: string;
  total_sales: number;
  [storeName: string]: number | string;
}
