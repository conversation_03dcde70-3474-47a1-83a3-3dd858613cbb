# GACO
project gaco, a platform that connects producers and stores.


## Getting Started

First, run the development server:

```bash
yarn dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `app/page.tsx`. The page auto-updates as you edit the file.

This project uses [`next/font`](https://nextjs.org/docs/app/building-your-application/optimizing/fonts) to automatically optimize and load [<PERSON><PERSON><PERSON>](https://vercel.com/font), a new font family for Vercel.


# Gaco Platform Endpoints Documentation

This documentation provides an overview of all available cloud functions that can be used by the frontend application.

## Table of Contents
- [Authentication](#authentication)
- [Store Management](#store-management)
- [Producer Management](#producer-management)
- [Agreement Management](#agreement-management)
  - [Contract Lifecycle Management Flow](#contract-lifecycle-management)
  - [Agreement Management Endpoints](#agreement-management-endpoints)
- [Partnership Management](#partnership-management)  
- [Product Management](#product-management)
- [Product Allocation Management](#product-allocation-management)
- [Product Request Management](#product-request-management)
- [Sales Report Management](#sales-report-management)
- [Shopify Integration](#shopify-integration)
- [Sales Data Processing](#sales-data-processing)
- [Email Management](#email-management)
- [Secret Management](#secret-management)
- [Event-Driven Functions](#event-driven-functions)
- [Common Response Format](#common-response-format)
- [Error Handling](#error-handling)

## Authentication

All cloud functions require authentication. The user must be logged in via Firebase Authentication to call these functions.

```typescript
// Example of calling a cloud function with authentication
const functions = getFunctions();
const callableFunction = httpsCallable(functions, 'create_store');

// Call the function with data
const result = await callableFunction(requestData);
```

## Store Management

### Create Store

Create a new store in the system.

**Function:** `create_store`
**Endpoint:** `https://europe-west3-your-project.cloudfunctions.net/create_store`
**Region:** europe-west3
**Memory:** 1GB
**Timeout:** 60 seconds

**Request Format:** [Source: `/types/requests/storeRequestInterfaces.ts`](../types/requests/storeRequestInterfaces.ts)
```typescript
interface CreateStoreRequest {
  data: {
    displayName: string;
    email: string;
    taxA2: string;
    defaultCommission?: number | null;
    description?: string | null;
    address?: Address | null;
    shopifyApiKeyObject: ShopifyStoreCredentials;
  }
}
```

**Response:**
```typescript
interface CreateStoreResponse {
  success: boolean;
  message: string;
  code: number;
}
```

**Success Response:**
```json
{
  "success": true,
  "message": "Store created successfully with id: {store_id}",
  "code": 200
}
```

**Error Response (400):**
```json
{
  "success": false,
  "message": "Validation error: {error_details}",
  "code": 400
}
```

### Delete Store

Delete a store and all associated data.

**Function:** `delete_store`
**Endpoint:** `https://europe-west3-your-project.cloudfunctions.net/delete_store`
**Region:** europe-west3
**Memory:** 4GB
**Timeout:** 300 seconds (5 minutes)

**Request Format:** [Source: `/types/requests/storeRequestInterfaces.ts`](../types/requests/storeRequestInterfaces.ts)
```typescript
interface DeleteStoreRequest {
  data: {
    storeId: string;
    hardDelete?: boolean; // Optional flag for permanent deletion, defaults to false
  }
}
```

**Response:**
```typescript
interface DeleteStoreResponse {
  success: boolean;
  message: string;
  code: number;
  data?: any; // Deletion results data
}
```

**Success Response:**
```json
{
  "success": true,
  "message": "Store deleted successfully",
  "code": 200,
  "data": {...}
}
```

## Producer Management

```mermaid
flowchart TD
    subgraph User Initiated Producer Creation
        direction LR
        UC_Start[User Call: create_producer Cloud Function] --> UC_Auth{Authenticated?}
        UC_Auth -- No --> UC_AuthFail[Return 401 Error]
        UC_Auth -- Yes --> UC_Parse[Parse Request]
        UC_Parse --> UC_PMInst[Instantiate ProducerManager]
        UC_PMInst --> UC_PM_Create[Call ProducerManager::create_producer with user_uuid]
        UC_PM_Create --> UC_CheckEmail{Email Exists?}
        UC_CheckEmail -- Yes --> UC_ValError[Return 400 Error: Producer Exists]
        UC_CheckEmail -- No --> UC_CreateProdDB[Create Producer in DB parent_id = user_uuid]
        UC_CreateProdDB --> UC_LinkUserRoot[Link to UserRootAccount]
        UC_LinkUserRoot -- Failure --> UC_DeleteProd[Delete Producer Rollback] --> UC_ServerError[Return 500 Error]
        UC_LinkUserRoot -- Success --> UC_Success[Return 200: Producer Created]
    end

    subgraph Force Active Partnership Flow
        direction TB
        FAP_Start[Call: ForceActivePartnershipFlow::force_create_active_partnership] --> FAP_PrepProdReq[Prepare CreateProducerRequest]
        FAP_PrepProdReq --> FAP_CallForceProd[Call self::force_producer]
        
        subgraph Internal: force_producer
            direction TB
            FP_Start --> FP_AttemptCreate[Attempt ProducerManager::create_producer_no_parentId]
            FP_AttemptCreate --> FP_CheckEmail{Email Exists in DB via _check_producer_exists?}
            FP_CheckEmail -- No --> FP_CreateProdDB[Create Producer in DB parent_id="", created_by_store_id=store_id]
            FP_CreateProdDB --> FP_LinkUserRoot[Link to UserRootAccount of request_user_uuid]
            FP_LinkUserRoot -- Failure --> FP_DeleteProd[Delete Producer Rollback] --> FP_RaiseEx[Raise Exception - Handled by caller]
            FP_LinkUserRoot -- Success --> FP_ReturnNewID[Return new_producer_id]
            FP_CheckEmail -- Yes Exception from create_producer_no_parentId --> FP_CatchEx[Catch Exception]
            FP_CatchEx --> FP_QueryExisting[Query DB for existing producer by email]
            FP_QueryExisting --> FP_ReturnExistingID[Return existing_producer_id]
        end

        FAP_CallForceProd --> FP_ReturnNewID --> FAP_ProdID_New[producer_id New]
        FAP_CallForceProd --> FP_ReturnExistingID --> FAP_ProdID_Existing[producer_id Existing]

        FAP_ProdID_New --> FAP_PrepAgreeReq[Prepare CreateAgreementRequest]
        FAP_ProdID_Existing --> FAP_PrepAgreeReq

        FAP_PrepAgreeReq --> FAP_CallForceCreateAgree[Call self.force_create_active_agreement]

        subgraph Internal: force_create_active_agreement
            direction TB
            FCA_Start --> FCA_CreateDraft[AgreementManager::create_draft_agreement]
            FCA_CreateDraft --> FCA_Submit[AgreementManager::submit_for_approval]
            FCA_Submit --> FCA_ApproveStore[AgreementManager::approve_agreement Role.STORE]
            FCA_ApproveStore --> FCA_ApproveProducer[AgreementManager::approve_agreement Role.PRODUCER]
            FCA_ApproveProducer --> FCA_ReturnAgreeID[Return agreement_id]
        end
        
        FAP_CallForceCreateAgree --> FCA_ReturnAgreeID --> FAP_ReturnIDs[Return producer_id & agreement_id]
    end

    %% Styling (Optional, for better readability)
    classDef cloudFunction fill:#404080,stroke:#b0b0e0,stroke-width:2px;
    classDef managerMethod fill:#207020,stroke:#a0d0a0,stroke-width:2px;
    classDef flowMethod fill:#b8860b,stroke:#ffe066,stroke-width:2px;
    classDef decision fill:#cc8400,stroke:#ffd488,stroke-width:1px;
    classDef dbOp fill:#2c5d76,stroke:#8db8c6,stroke-width:1px;
    classDef errorState fill:#8b0000,stroke:#ff8c8c,stroke-width:2px;
    classDef successState fill:#006400,stroke:#70c070,stroke-width:2px;

    class UC_Start,UC_AuthFail,UC_ValError,UC_ServerError,UC_Success cloudFunction;
    class UC_PM_Create,FP_AttemptCreate,FP_CreateProdDB,FCA_CreateDraft,FCA_Submit,FCA_ApproveStore,FCA_ApproveProducer managerMethod;
    class FAP_Start,FAP_CallForceProd,FAP_CallForceCreateAgree,FP_Start,FCA_Start flowMethod;
    class UC_Auth,UC_CheckEmail,FP_CheckEmail decision;
    class UC_CreateProdDB,UC_LinkUserRoot,UC_DeleteProd,FP_LinkUserRoot,FP_DeleteProd,FP_QueryExisting dbOp;
```

### Create Producer

Create a new producer in the system.

**Function:** `create_producer`
**Endpoint:** `https://europe-west3-your-project.cloudfunctions.net/create_producer`
**Region:** europe-west3
**Memory:** 1GB
**Timeout:** 60 seconds

**Request Format:** [Source: `/types/requests/producerRequestInterfaces.ts`](../types/requests/producerRequestInterfaces.ts)
```typescript
interface CreateProducerRequest {
  data: {
    displayName: string;
    email: string;
    taxA2: string;
  }
}
```

**Response:**
```typescript
interface CreateProducerResponse {
  success: boolean;
  message: string;
  code: number;
}
```

**Success Response:**
```json
{
  "success": true,
  "message": "Producer created successfully with id: {producer_id}",
  "code": 200
}
```

### Delete Producer

Delete a producer and associated data.

**Function:** `delete_producer`
**Endpoint:** `https://europe-west3-your-project.cloudfunctions.net/delete_producer`
**Region:** europe-west3
**Memory:** 1GB
**Timeout:** 60 seconds

**Request Format:** [Source: `/types/requests/producerRequestInterfaces.ts`](../types/requests/producerRequestInterfaces.ts)
```typescript
interface DeleteProducerRequest {
  data: {
    producerId: string;
  }
}
```

**Response:**
```typescript
interface DeleteProducerResponse {
  success: boolean;
  message: string;
  code: number;
}
```

**Success Response:**
```json
{
  "success": true,
  "message": "Producer deleted successfully",
  "code": 200
}
```

## Agreement Management

Based on `agreement_manager.py` and `test_agreement_manager.py`:

### Contract Lifecycle Management

1.  **Initialization:**
    *   An agreement starts as a `DRAFT` upon calling `create_draft_agreement`.
    *   It includes a basic approval workflow structure (Store, Producer both with status `PENDING`).

2.  **Draft Stage:**
    *   Status: `DRAFT`
    *   Allowed Actions: `update_agreement` can modify core fields (title, dates, commission, doc URL).

3.  **Path Choice (from Draft):**
    *   To **Direct Approval:** Call `submit_for_approval` -> Status becomes `PENDING_APPROVAL`.
    *   To **Negotiation:** Call `submit_for_negotiation` -> Status becomes `NEGOTIATION`.

4.  **Negotiation Stage (Optional):**
    *   Status: `NEGOTIATION`
    *   Allowed Actions:
        *   `propose_changes`: Updates agreement fields, adds entry to `negotiation_history`. Status remains `NEGOTIATION`.
        *   `accept_changes`: Adds an "Accepted" entry to `negotiation_history`. Status becomes `PENDING_APPROVAL`.

5.  **Pending Approval Stage:**
    *   Status: `PENDING_APPROVAL`
    *   Allowed Actions: `approve_agreement` (called by Store or Producer role).
        *   **Partial Approval:** If it's the first approval, the specific step in `approval_workflow` is marked `APPROVED`. Overall status remains `PENDING_APPROVAL`.
        *   **Final Approval:** If it's the last required approval, the final step is marked `APPROVED`. Overall status becomes `APPROVED`.

6.  **Approved Stage:**
    *   Status: `APPROVED`
    *   **Internal Trigger:** Immediately after the status becomes `APPROVED` (triggered within the `approve_agreement` method on final approval), the `activate_agreement` method is called internally.

7.  **Activation Process (Internal):**
    *   Called by: `approve_agreement` (on final approval).
    *   Actions:
        *   Sets agreement status to `ACTIVE`.
        *   Calls `partner_manager.create_partnership` using the agreement details.
        *   Stores the returned `partnership_id` on the agreement document.

8.  **Active Stage:**
    *   Status: `ACTIVE`
    *   Implicit Next Step: Agreement remains active until `expiration_date` is reached or it's terminated.

9.  **Termination (from Active):**
    *   Triggered by: `terminate_agreement` call.
    *   Actions:
        *   Sets agreement status to `TERMINATED`.
        *   Calls `partner_manager.terminate_partnership_on_agreement` (which should update the linked Partnership status).

10. **Expiration (from Active):**
    *   Trigger: `expiration_date` passes (Logic likely external to `AgreementManager`, e.g., scheduled function).
    *   Action: Status becomes `EXPIRED`.

11. **Renewal (from Expired/Terminated):**
    *   Triggered by: `create_renewal_draft` call, providing the original agreement ID.
    *   Allowed Original Statuses: `EXPIRED` or `TERMINATED`.
    *   Actions:
        *   Creates a **new** agreement document.
        *   New agreement status: `DRAFT`.
        *   Copies relevant details, increments version, sets new dates.
        *   Sets `renewed_by` field on the new agreement pointing to the original ID.
        *   Updates the original agreement document, setting its `renewed_by` field to the new agreement's ID.
    *   Result: A new draft agreement, linked to the old one, enters the standard lifecycle starting from the `DRAFT` state.

12. **Deletion:**
    *   Triggered by: `delete_agreement` call.
    *   Purpose: Primarily for development and testing.
    *   Action: Removes the agreement document from Firestore. Can be performed on agreements in various states.

```mermaid
flowchart TD
    Start --> CreateDraft{create_draft_agreement}
    CreateDraft --> DraftState[Draft]

    DraftState -->|update_agreement| DraftState
    DraftState -->|submit_for_approval| PendingApprovalState[Pending Approval]
    DraftState -->|submit_for_negotiation| NegotiationState[Negotiation]

    NegotiationState -->|propose_changes| NegotiationState
    NegotiationState -->|accept_changes| PendingApprovalState

    PendingApprovalState -->|approve_agreement Partial| PendingApprovalState
    PendingApprovalState -->|approve_agreement Final| ApprovedState{Approved}

    ApprovedState -->|activate_agreement Internal Call| ActiveState[Active]
    subgraph Activation Side Effects
        direction LR
        ApprovedState -- Creates --> PartnershipManagerService[PartnershipManager]
        PartnershipManagerService -- Creates --> PartnershipDoc[Partnership Active]
        PartnershipDoc -- Links --> ActiveState
    end


    ActiveState -->|terminate_agreement| TerminatedState[Terminated]
    subgraph Termination Side Effects
        direction LR
         TerminatedState -- Updates --> PartnershipManagerService2[PartnershipManager]
         PartnershipManagerService2 -- Updates --> PartnershipDoc2[Partnership Terminated]
    end


    ActiveState -->|Expiration Date Reached| ExpiredState[Expired]

    TerminatedState -->|create_renewal_draft| NewDraft[Draft New Renewal Agreement]
    ExpiredState -->|create_renewal_draft| NewDraft

    NewDraft --> DraftState

    %% Deletion is possible from most states but is an administrative action
    DraftState -->|delete_agreement| EndDelete[(Deleted)]
    PendingApprovalState -->|delete_agreement| EndDelete
    ApprovedState -->|delete_agreement| EndDelete
    ActiveState -->|delete_agreement| EndDelete
    TerminatedState -->|delete_agreement| EndDelete
    ExpiredState -->|delete_agreement| EndDelete

    style PartnershipDoc fill:#dff,stroke:#333,stroke-width:2px
    style PartnershipDoc2 fill:#fcc,stroke:#333,stroke-width:2px
    style NewDraft fill:#def,stroke:#333,stroke-width:2px

```

## Agreement Management Endpoints

All functions require authentication and have the following specs:
- **Region:** europe-west3
- **Memory:** 512MB
- **Timeout:** Standard

### Create Agreement

Create a draft agreement between a store and a producer.

**Function:** `create_agreement`

**Request Format:** [Source: `/types/requests/agreementsInterfaces.ts`](../types/requests/agreementsInterfaces.ts)
```typescript
interface CreateAgreementRequest {
  data: {
    storeId: string;
    producerId: string;
    title: string;
    effectiveDate: string;
    expirationDate: string;
    commission: number;
    documentUrl?: string | null;
  }
}
```

**Response:**
```typescript
interface CreateAgreementResponse {
  success: boolean;
  message: string;
  code: number;
  data?: {
    agreement_id: string;
  };
}
```

### Update Draft Agreement

Update an existing draft agreement.

**Function:** `update_draft_agreement`

**Request Format:** [Source: `/types/requests/agreementsInterfaces.ts`](../types/requests/agreementsInterfaces.ts)
```typescript
interface UpdateDraftAgreementRequest {
  data: {
    agreementId: string;
    title?: string | null;
    effectiveDate?: string | null;
    expirationDate?: string | null;
    commission?: number | null;
    documentUrl?: string | null;
  }
}
```

### Submit Agreement for Approval

Submit a draft agreement for approval.

**Function:** `submit_agreement_for_approval`

**Request Format:** [Source: `/types/requests/agreementsInterfaces.ts`](../types/requests/agreementsInterfaces.ts)
```typescript
interface SubmitAgreementForApprovalRequest {
  data: {
    agreementId: string;
    role: string;
  }
}
```

### Approve Agreement

Approve an agreement step (either store or producer approval).

**Function:** `approve_agreement`

**Request Format:** [Source: `/types/requests/agreementsInterfaces.ts`](../types/requests/agreementsInterfaces.ts)
```typescript
interface ApproveAgreementRequest {
  data: {
    agreementId: string;
    storeOrProducerId: string;
    comments?: string | null;
  }
}
```

### Reject Agreement

Reject an agreement.

**Function:** `reject_agreement`

**Request Format:** [Source: `functions/python/models/requests/agreements_requests.py`](../functions/python/models/requests/agreements_requests.py)
```typescript
interface RejectAgreementRequest {
  data: {
    agreementId: string;
    role: string; // "store" or "producer"
    comments?: string | null;
  }
}
```

### Terminate Agreement

Terminate an active agreement.

**Function:** `terminate_agreement`

**Request Format:** [Source: `/types/requests/agreementsInterfaces.ts`](../types/requests/agreementsInterfaces.ts)
```typescript
interface TerminateAgreementRequest {
  data: {
    agreementId: string;
  }
}
```

### Delete Agreement

Delete an agreement from the system (in any state).

**Function:** `delete_agreement`
**Endpoint:** `https://europe-west3-your-project.cloudfunctions.net/delete_agreement`
**Region:** europe-west3
**Memory:** 512MB
**Timeout:** Standard

**Request Format:** [Source: `/types/requests/agreementsInterfaces.ts`](../types/requests/agreementsInterfaces.ts)
```typescript
interface DeleteAgreementRequest {
  data: {
    agreementId: string;
  }
}
```

**Response:**
```typescript
interface DeleteAgreementResponse {
  success: boolean;
  message: string;
  code: number;
}
```

**Success Response:**
```json
{
  "success": true,
  "message": "Agreement deleted successfully",
  "code": 200
}
```

## Partnership Management

### Delete Partnership

Delete a partnership and related agreement.

**Function:** `delete_partnership`
**Endpoint:** `https://europe-west3-your-project.cloudfunctions.net/delete_partnership`
**Region:** europe-west3
**Memory:** 512MB
**Timeout:** Standard

**Request Format:** [Source: `functions/python/models/requests/partnership_requests.py`](../functions/python/models/requests/partnership_requests.py)
```typescript
interface DeletePartnershipRequest {
  data: {
    partnership_id: string;
  }
}
```

**Response:**
```typescript
interface DeletePartnershipResponse {
  success: boolean;
  message: string;
  code: number;
  data?: any; // Deletion results data
}
```

**Success Response:**
```json
{
  "success": true,
  "message": "Partnership deleted successfully",
  "code": 200,
  "data": {...}
}
```

## Product Management

The product management endpoints allow for creating, updating, and deleting products and their variants.

### Create Product

Create a new product in the system.

**Function:** `create_product`
**Endpoint:** `https://europe-west3-your-project.cloudfunctions.net/create_product`
**Region:** europe-west3
**Memory:** 1GB
**Timeout:** 60 seconds

**Request Format:** [Source: `functions/python/models/products.py`](../functions/python/models/products.py)
```typescript
interface Product {
  data: {
    product_id?: string | null; // Optional, will be assigned if not provided
    title: string;
    vendor?: string | null;
    handle?: string | null;
    productType?: string | null;
    status: string; // e.g., "active", "draft"
    createdAt: string; // ISO date string
    updatedAt: string; // ISO date string
    variants: ProductVariant[];
    
    // Optional contextual fields
    store_id?: string | null;
    store_display_name?: string | null;
    producer_id?: string | null;
    producer_display_name?: string | null;
  }
}
```

**Response:**
```typescript
interface CreateProductResponse {
  success: boolean;
  message: string;
  code: number;
  data?: Product; // The created product
}
```

**Success Response:**
```json
{
  "success": true,
  "message": "Product created successfully.",
  "data": {
    "product_id": "generated-id",
    "title": "Example Product",
    // other product fields
  },
  "code": 201
}
```

### Update Product

Update an existing product's information.

**Function:** `update_product`
**Endpoint:** `https://europe-west3-your-project.cloudfunctions.net/update_product`
**Region:** europe-west3
**Memory:** 1GB
**Timeout:** 60 seconds

**Request Format:** [Source: `functions/python/models/requests/product_requests.py`](../functions/python/models/requests/product_requests.py)
```typescript
interface UpdateProductRequest {
  data: {
    logical_product_id: string; // The ID of the product to update
    update_data: {
      // Any product fields that need to be updated
      title?: string;
      vendor?: string;
      status?: string;
      // etc.
    }
  }
}
```

**Response:**
```typescript
interface UpdateProductResponse {
  success: boolean;
  message: string;
  code: number;
  data?: Product; // The updated product
}
```

**Success Response:**
```json
{
  "success": true,
  "message": "Product updated successfully.",
  "data": {
    "product_id": "product-123",
    "title": "Updated Product Title",
    // other product fields
  },
  "code": 200
}
```

### Delete Product

Delete a product and optionally its variants.

**Function:** `delete_product`
**Endpoint:** `https://europe-west3-your-project.cloudfunctions.net/delete_product`
**Region:** europe-west3
**Memory:** 1GB
**Timeout:** 60 seconds

**Request Format:** [Source: `functions/python/models/requests/product_requests.py`](../functions/python/models/requests/product_requests.py)
```typescript
interface DeleteProductRequest {
  data: {
    logical_product_id: string; // The ID of the product to delete
    delete_variants?: boolean; // Whether to delete associated variants, defaults to true
  }
}
```

**Response:**
```typescript
interface DeleteProductResponse {
  success: boolean;
  message: string;
  code: number;
}
```

**Success Response:**
```json
{
  "success": true,
  "message": "Product (and variants, if requested) deleted successfully.",
  "code": 200
}
```

### Create Product Variant

Create a new variant for an existing product.

**Function:** `create_variant`
**Endpoint:** `https://europe-west3-your-project.cloudfunctions.net/create_variant`
**Region:** europe-west3
**Memory:** 1GB
**Timeout:** 60 seconds

**Request Format:** [Source: `functions/python/models/products.py`](../functions/python/models/products.py)
```typescript
interface ProductVariant {
  data: {
    product_variant_id: string;
    product_id?: string | null; // Will be populated with parent product's ID
    title: string;
    sku?: string | null;
    price: number;
    compare_at_price?: number | null;
    inventory_quantity?: number | null;
    barcode?: string | null;
    createdAt: string; // ISO date string
    updatedAt: string; // ISO date string
  }
}
```

**Response:**
```typescript
interface CreateVariantResponse {
  success: boolean;
  message: string;
  code: number;
  data?: ProductVariant; // The created variant
}
```

**Success Response:**
```json
{
  "success": true,
  "message": "Variant created successfully.",
  "data": {
    "product_variant_id": "variant-123",
    "product_id": "product-123",
    "title": "Small",
    "price": 19.99,
    // other variant fields
  },
  "code": 201
}
```

### Update Product Variant

Update an existing product variant.

**Function:** `update_variant`
**Endpoint:** `https://europe-west3-your-project.cloudfunctions.net/update_variant`
**Region:** europe-west3
**Memory:** 1GB
**Timeout:** 60 seconds

**Request Format:** [Source: `functions/python/models/requests/product_requests.py`](../functions/python/models/requests/product_requests.py)
```typescript
interface UpdateVariantRequest {
  data: {
    logical_variant_id: string; // The ID of the variant to update
    update_data: {
      // Any variant fields that need to be updated
      title?: string;
      sku?: string;
      price?: number;
      // etc.
    }
  }
}
```

**Response:**
```typescript
interface UpdateVariantResponse {
  success: boolean;
  message: string;
  code: number;
  data?: ProductVariant; // The updated variant
}
```

**Success Response:**
```json
{
  "success": true,
  "message": "Variant updated successfully.",
  "data": {
    "product_variant_id": "variant-123",
    "title": "Updated Variant Title",
    "price": 24.99,
    // other variant fields
  },
  "code": 200
}
```

### Delete Product Variant

Delete a product variant.

**Function:** `delete_variant`
**Endpoint:** `https://europe-west3-your-project.cloudfunctions.net/delete_variant`
**Region:** europe-west3
**Memory:** 1GB
**Timeout:** 60 seconds

**Request Format:** [Source: `functions/python/models/requests/product_requests.py`](../functions/python/models/requests/product_requests.py)
```typescript
interface DeleteVariantRequest {
  data: {
    logical_variant_id: string; // The ID of the variant to delete
  }
}
```

**Response:**
```typescript
interface DeleteVariantResponse {
  success: boolean;
  message: string;
  code: number;
}
```

**Success Response:**
```json
{
  "success": true,
  "message": "Variant deleted successfully.",
  "code": 200
}
```

## Product Allocation Management

The product allocation management endpoints facilitate the distribution of products from producers to stores.

### Create Allocation

Create a new product allocation from a producer to a store.

**Function:** `create_allocation`
**Endpoint:** `https://europe-west3-your-project.cloudfunctions.net/create_allocation`
**Region:** europe-west3
**Memory:** 1GB
**Timeout:** 60 seconds

**Request Format:** [Source: `functions/python/models/allocations.py`](../functions/python/models/allocations.py)
```typescript
interface ProductAllocation {
  data: {
    producer_id: string; // ID of the producer initiating the allocation
    store_id: string; // ID of the store receiving the products
    items: [
      {
        product_id: string; // ID of the product
        product_variant_id?: string | null; // Optional variant ID
        quantity: number; // Number of units allocated (must be > 0)
      }
    ];
    status?: string; // Default: "PENDING_CONFIRMATION"
    delivery_method: string; // "PRODUCER_SHIPMENT", "PRODUCER_DELIVERY", or "STORE_PICKUP"
    allocation_date?: string; // ISO date, defaults to current timestamp
    shipped_on?: string | null; // ISO date when shipped
    delivered_on?: string | null; // ISO date when delivered
    producer_notes?: string | null; // Optional notes from producer
    store_notes?: string | null; // Optional notes from store
    tracking_number?: string | null; // Optional tracking number for shipments
  }
}
```

**Response:**
```typescript
interface CreateAllocationResponse {
  success: boolean;
  message: string;
  code: number;
  data?: ProductAllocation; // The created allocation
}
```

**Success Response:**
```json
{
  "success": true,
  "message": "Allocation created successfully.",
  "data": {
    "allocation_id": "generated-id",
    "producer_id": "producer-123",
    "store_id": "store-456",
    "status": "PENDING_CONFIRMATION",
    // other allocation fields
  },
  "code": 201
}
```

### Update Allocation Status

Update the status of an existing product allocation.

**Function:** `update_allocation_status`
**Endpoint:** `https://europe-west3-your-project.cloudfunctions.net/update_allocation_status`
**Region:** europe-west3
**Memory:** 1GB
**Timeout:** 60 seconds

**Request Format:** [Source: `functions/python/models/requests/allocation_requests.py`](../functions/python/models/requests/allocation_requests.py)
```typescript
interface UpdateAllocationStatusRequest {
  data: {
    allocation_id: string; // ID of the allocation to update
    new_status: string; // New status value (e.g., "PENDING_SHIPMENT", "IN_TRANSIT", "DELIVERED")
    details?: { // Optional additional details
      tracking_number?: string;
      producer_notes?: string;
      store_notes?: string;
      shipped_on?: string; // ISO date
      delivered_on?: string; // ISO date
      // Other fields as needed
    }
  }
}
```

**Response:**
```typescript
interface UpdateAllocationStatusResponse {
  success: boolean;
  message: string;
  code: number;
  data?: ProductAllocation; // The updated allocation
}
```

**Success Response:**
```json
{
  "success": true,
  "message": "Allocation status updated.",
  "data": {
    "allocation_id": "allocation-123",
    "status": "IN_TRANSIT",
    "tracking_number": "1Z999AA10123456784",
    // other allocation fields
  },
  "code": 200
}
```

**Available Allocation Statuses:**

The following statuses can be used when updating an allocation:

- `PENDING_CONFIRMATION`: Initial status, waiting for store confirmation
- `PENDING_SHIPMENT`: Confirmed but not yet shipped
- `IN_TRANSIT`: Products have been shipped and are in transit
- `DELIVERED`: Products have been delivered to the store
- `RECEIVED_WITH_ISSUES`: Delivery had issues that need resolution
- `CANCELLED`: Allocation has been cancelled

## Product Request Management

The product request management endpoints enable stores to request products from producers, and for producers to approve or reject these requests.

### Create Store Product Request

Create a new product request from a store to a producer.

**Function:** `create_store_product_request`
**Endpoint:** `https://europe-west3-your-project.cloudfunctions.net/create_store_product_request`
**Region:** europe-west3
**Memory:** 1GB
**Timeout:** 60 seconds

**Request Format:** [Source: `functions/python/models/allocations.py`](../functions/python/models/allocations.py)
```typescript
interface StoreProductRequest {
  data: {
    store_id: string; // ID of the store making the request
    producer_id: string; // ID of the producer the request is for
    requested_items: [
      {
        product_id: string; // ID of the product
        product_variant_id?: string | null; // Optional variant ID
        quantity: number; // Number of units requested (must be > 0)
      }
    ];
    desired_delivery_date?: string | null; // Optional ISO date for desired delivery
    store_request_notes?: string | null; // Optional notes from the store
  }
}
```

**Response:**
```typescript
interface CreateStoreProductRequestResponse {
  success: boolean;
  message: string;
  code: number;
  data?: StoreProductRequest; // The created request
}
```

**Success Response:**
```json
{
  "success": true,
  "message": "Product request created successfully.",
  "data": {
    "request_id": "generated-id",
    "store_id": "store-456",
    "producer_id": "producer-123",
    "status": "PENDING_PRODUCER_APPROVAL",
    "request_date": "2025-05-17T14:30:00Z",
    // other request fields
  },
  "code": 201
}
```

### Approve Store Product Request

Producer approves a store's product request, which creates a product allocation.

**Function:** `approve_store_product_request`
**Endpoint:** `https://europe-west3-your-project.cloudfunctions.net/approve_store_product_request`
**Region:** europe-west3
**Memory:** 1GB
**Timeout:** 60 seconds

**Request Format:** [Source: `functions/python/models/requests/product_request_requests.py`](../functions/python/models/requests/product_request_requests.py)
```typescript
interface ApproveStoreProductRequest {
  data: {
    request_id: string; // ID of the request to approve
    acting_producer_id: string; // ID of the producer approving the request
    producer_response_notes?: string | null; // Optional notes from producer
    delivery_method_for_allocation: string; // Method for delivery (default: "PRODUCER_SHIPMENT")
    allocation_producer_notes?: string | null; // Optional notes for the allocation
  }
}
```

**Response:**
```typescript
interface ApproveStoreProductRequestResponse {
  success: boolean;
  message: string;
  code: number;
  data?: StoreProductRequest; // The updated request with approved status
}
```

**Success Response:**
```json
{
  "success": true,
  "message": "Request approved and allocation created.",
  "data": {
    "request_id": "request-123",
    "status": "APPROVED_BY_PRODUCER",
    "related_allocation_id": "allocation-456",
    // other request fields
  },
  "code": 200
}
```

### Reject Store Product Request

Producer rejects a store's product request.

**Function:** `reject_store_product_request`
**Endpoint:** `https://europe-west3-your-project.cloudfunctions.net/reject_store_product_request`
**Region:** europe-west3
**Memory:** 1GB
**Timeout:** 60 seconds

**Request Format:** [Source: `functions/python/models/requests/product_request_requests.py`](../functions/python/models/requests/product_request_requests.py)
```typescript
interface RejectStoreProductRequest {
  data: {
    request_id: string; // ID of the request to reject
    acting_producer_id: string; // ID of the producer rejecting the request
    producer_response_notes?: string | null; // Optional notes explaining the rejection
  }
}
```

**Response:**
```typescript
interface RejectStoreProductRequestResponse {
  success: boolean;
  message: string;
  code: number;
  data?: StoreProductRequest; // The updated request with rejected status
}
```

**Success Response:**
```json
{
  "success": true,
  "message": "Request rejected.",
  "data": {
    "request_id": "request-123",
    "status": "REJECTED_BY_PRODUCER",
    "producer_response_notes": "Products currently out of stock",
    // other request fields
  },
  "code": 200
}
```

### Cancel Store Product Request

Store cancels their own pending product request.

**Function:** `cancel_store_product_request`
**Endpoint:** `https://europe-west3-your-project.cloudfunctions.net/cancel_store_product_request`
**Region:** europe-west3
**Memory:** 1GB
**Timeout:** 60 seconds

**Request Format:** [Source: `functions/python/models/requests/product_request_requests.py`](../functions/python/models/requests/product_request_requests.py)
```typescript
interface CancelStoreProductRequest {
  data: {
    request_id: string; // ID of the request to cancel
    acting_store_id: string; // ID of the store cancelling the request
  }
}
```

**Response:**
```typescript
interface CancelStoreProductRequestResponse {
  success: boolean;
  message: string;
  code: number;
  data?: StoreProductRequest; // The updated request with cancelled status
}
```

**Success Response:**
```json
{
  "success": true,
  "message": "Request cancelled.",
  "data": {
    "request_id": "request-123",
    "status": "CANCELLED_BY_STORE",
    // other request fields
  },
  "code": 200
}
```

**Available Request Statuses:**

The following statuses represent the lifecycle of a store product request:

- `PENDING_PRODUCER_APPROVAL`: Initial status when created by a store
- `APPROVED_BY_PRODUCER`: Producer has approved the request and created an allocation
- `REJECTED_BY_PRODUCER`: Producer has rejected the request
- `CANCELLED_BY_STORE`: Store has cancelled the request before producer action

## Sales Report Management

### Create Sales Reports

Create sales reports for a store or producer within a specific date range.

**Function:** `create_sales_reports`
**Endpoint:** `https://europe-west3-your-project.cloudfunctions.net/create_sales_reports`
**Region:** europe-west3
**Memory:** 4GB
**Timeout:** 600 seconds (10 minutes)

**Request Format:** [Source: `/types/requests/salesReportRequestInterfaces.ts`](../types/requests/salesReportRequestInterfaces.ts)
```typescript
interface CreateSalesReportRequest {
  data: {
    storeId: string;
    producerId?: string | null;
    startDate: Date;
    endDate: Date;
  }
}
```

**Response:**
```typescript
interface CreateSalesReportResponse {
  success: boolean;
  message: string;
  code: number;
  data?: {
    sales_reports: any[]; // Array of created sales reports
  };
}
```

**Success Response:**
```json
{
  "success": true,
  "message": "Successfully created {count} sales reports",
  "data": {
    "sales_reports": [...]
  },
  "code": 200
}
```

### Delete Sales Report

Delete a sales report and revert all related changes.

**Function:** `delete_sales_report`
**Endpoint:** `https://europe-west3-your-project.cloudfunctions.net/delete_sales_report`
**Region:** europe-west3
**Memory:** 4GB
**Timeout:** 600 seconds (10 minutes)

**Request Format:** [Source: `/types/requests/salesReportRequestInterfaces.ts`](../types/requests/salesReportRequestInterfaces.ts)
```typescript
interface DeleteSalesReportRequest {
  data: {
    salesReportId: string;
  }
}
```

**Response:**
```typescript
interface DeleteSalesReportResponse {
  success: boolean;
  message: string;
  code: number;
  data?: {
    result_data: any; // Result data from deletion process
  };
}
```

**Success Response:**
```json
{
  "success": true,
  "message": "Successfully deleted sales report",
  "data": {
    "result_data": {...}
  },
  "code": 200
}
```

## Shopify Integration

### Get Shopify Orders

Fetch orders from Shopify. This function retrieves the first page of orders and triggers background processing for additional pages.

**Function:** `get_shopify_orders`
**Endpoint:** `https://europe-west3-your-project.cloudfunctions.net/get_shopify_orders`
**Region:** europe-west3
**Memory:** 2GB
**Timeout:** 300 seconds (5 minutes)

**Request Format:** [Source: `/types/requests/shopifyRequestInterfaces.ts`](../types/requests/shopifyRequestInterfaces.ts)
```typescript
interface GetShopifyOrdersRequest {
  data: {
    store_id: string;
    start_date: string; // ISO date string
    end_date: string;   // ISO date string
  }
}
```

**Response:**
```typescript
interface GetShopifyOrdersResponse {
  success: boolean;
  message: string;
  code: number;
  data?: {
    // Order data plus additional info
    hasMorePages: boolean;
    note: string;
  };
}
```

**Success Response:**
```json
{
  "success": true,
  "message": "Started getting orders from Shopify",
  "code": 200,
  "data": {
    "hasMorePages": true,
    "note": "Additional pages are being processed in the background"
  }
}
```

### Get Shopify Orders with Dynamic Query

Fetch Shopify orders with dynamically determined date ranges.

**Function:** `get_shopify_orders_with_dynamic_query`
**Endpoint:** `https://europe-west3-your-project.cloudfunctions.net/get_shopify_orders_with_dynamic_query`
**Region:** europe-west3
**Memory:** 2GB
**Timeout:** 300 seconds (5 minutes)

**Request Format:** [Source: `/types/requests/shopifyRequestInterfaces.ts`](../types/requests/shopifyRequestInterfaces.ts)
```typescript
interface GetShopifyOrdersWithDynamicQueryRequest {
  data: {
    store_id: string;
    // Additional query parameters
  }
}
```

**Response:** Same format as `get_shopify_orders`

### Create Product in Shopify

Create a new product directly in a Shopify store.

**Function:** `create_product_in_shopify`
**Endpoint:** `https://europe-west3-your-project.cloudfunctions.net/create_product_in_shopify`
**Region:** europe-west3
**Memory:** 4GB
**Timeout:** 120 seconds (2 minutes)

**Request Format:** [Source: `functions/python/models/requests/create_product_in_shopify.py`](../functions/python/models/requests/create_product_in_shopify.py)
```typescript
interface CreateShopifyProductRequest {
  data: {
    store_id: string; // Internal ID of the store to identify Shopify credentials
    product_data: Product; // Product data conforming to the internal Product model
    shopify_api_version?: string | null; // Optional Shopify API version (defaults to service default)
    shopify_location_gid?: string | null; // Optional Shopify Location GID for inventory
  }
}
```

**Response:**
```typescript
interface CreateShopifyProductResponse {
  success: boolean;
  message: string;
  code: number;
  data?: {
    shopify_product_gid: string; // The Shopify GID of the created product
  };
}
```

**Success Response:**
```json
{
  "success": true,
  "message": "Product successfully created in Shopify.",
  "data": {
    "shopify_product_gid": "gid://shopify/Product/1234567890"
  },
  "code": 201
}
```

### Initialize Shopify Product Import

Initialize the import of products from a Shopify store into the platform.

**Function:** `initialize_shopify_product_import`
**Endpoint:** `https://europe-west3-your-project.cloudfunctions.net/initialize_shopify_product_import`
**Region:** europe-west3
**Memory:** 4GB
**Timeout:** 240 seconds (4 minutes)

**Request Format:** [Source: `functions/python/models/requests/shopify_product_import.py`](../functions/python/models/requests/shopify_product_import.py)
```typescript
interface InitializeShopifyProductImportRequest {
  data: {
    store_producer_id: string; // ID of the store or producer to import products for
  }
}
```

**Response:**
```typescript
interface InitializeShopifyProductImportResponse {
  success: boolean;
  message: string;
  code: number;
  data?: {
    data: any; // Import operation details
  };
}
```

**Success Response:**
```json
{
  "success": true,
  "message": "Shopify product import initialized",
  "data": {
    "data": {
      "operation_id": "import-operation-123",
      "status": "initialized"
    }
  },
  "code": 200
}
```

### Background Order Processing

The background task continues to fetch additional pages of orders after the initial request.

**Function:** `continueGettingOrders`
**Type:** Task-based function
**Region:** europe-west3
**Memory:** 4GB
**Retry Config:** 5 attempts with exponential backoff between 10-60 seconds
**Rate Limits:** Maximum 5 concurrent dispatches

This function is automatically triggered when needed and doesn't require direct frontend interaction.

**Background Task Data:**
```json
{
  "order_getter_id": "unique_id_for_tracking_fetch_operation"
}
```

### Background Product Processing

The background task continues to fetch additional pages of products after the initial import request.

**Function:** `continueGettingProducts`
**Type:** Task-based function
**Region:** europe-west3
**Memory:** 4GB
**Retry Config:** 2 attempts with 60 seconds backoff
**Rate Limits:** Maximum 5 concurrent dispatches

This function is automatically triggered when needed and doesn't require direct frontend interaction.

**Background Task Data:**
```json
{
  "product_getter_id": "unique_id_for_tracking_product_fetch_operation"
}
```

### Create Product in Shopify

Create a new product directly in a Shopify store.

**Function:** `create_product_in_shopify`
**Endpoint:** `https://europe-west3-your-project.cloudfunctions.net/create_product_in_shopify`
**Region:** europe-west3
**Memory:** 4GB
**Timeout:** 120 seconds (2 minutes)

**Request Format:** [Source: `functions/python/models/requests/create_product_in_shopify.py`](../functions/python/models/requests/create_product_in_shopify.py)
```typescript
interface CreateShopifyProductRequest {
  data: {
    store_id: string; // Internal ID of the store for Shopify credentials
    product_data: Product; // Product data conforming to internal Product model
    shopify_api_version?: string | null; // Optional Shopify API version
    shopify_location_gid?: string | null; // Optional Shopify Location GID for inventory
  }
}
```

**Response:**
```typescript
interface CreateShopifyProductResponse {
  success: boolean;
  message: string;
  code: number;
  data?: {
    shopify_product_gid: string; // The Shopify Global ID of the created product
  };
}
```

**Success Response:**
```json
{
  "success": true,
  "message": "Product successfully created in Shopify.",
  "data": {
    "shopify_product_gid": "gid://shopify/Product/1234567890"
  },
  "code": 201
}
```

### Initialize Shopify Product Import

Initialize the process to import products from a Shopify store.

**Function:** `initialize_shopify_product_import`
**Endpoint:** `https://europe-west3-your-project.cloudfunctions.net/initialize_shopify_product_import`
**Region:** europe-west3
**Memory:** 4GB
**Timeout:** 240 seconds (4 minutes)

**Request Format:** [Source: `functions/python/models/requests/shopify_product_import.py`](../functions/python/models/requests/shopify_product_import.py)
```typescript
interface InitializeShopifyProductImportRequest {
  data: {
    store_producer_id: string; // Either store_id or producer_id
  }
}
```

**Response:**
```typescript
interface InitializeShopifyProductImportResponse {
  success: boolean;
  message: string;
  code: number;
  data?: {
    data: any; // Import initialization result data
  };
}
```

**Success Response:**
```json
{
  "success": true,
  "message": "Shopify product import initialized",
  "data": {
    "data": {
      // Import initialization details
    }
  },
  "code": 200
}
```

### Background Product Import Processing

The background task continues to fetch additional pages of products after the initial import request.

**Function:** `continueGettingProducts`
**Type:** Task-based function
**Region:** europe-west3
**Memory:** 4GB
**Retry Config:** 2 attempts with 60 seconds backoff
**Rate Limits:** Maximum 5 concurrent dispatches

This function is automatically triggered when needed and doesn't require direct frontend interaction.

**Background Task Data:**
```json
{
  "product_getter_id": "unique_id_for_tracking_product_fetch_operation"
}
```

## Secret Management

### Store API Key

Store Shopify API keys securely.

**Function:** `store_api_key`
**Endpoint:** `https://europe-west3-your-project.cloudfunctions.net/store_api_key`
**Region:** europe-west3
**Memory:** 1GB
**Timeout:** 300 seconds (5 minutes)

**Request Format:**
```typescript
interface StoreApiKeyRequest {
  data: {
    store_id: string;
    api_key: string;
    shop_name?: string;
  }
}
```

**Response:**
```typescript
interface StoreApiKeyResponse {
  success: boolean;
  message: string;
  code: number;
}
```

### Delete API Key

Delete stored API keys.

**Function:** `delete_api_key`
**Endpoint:** `https://europe-west3-your-project.cloudfunctions.net/delete_api_key`
**HTTP Method:** POST/DELETE
**Region:** europe-west3
**Timeout:** 300 seconds (5 minutes)
**CORS:** Enabled for all origins

**Request Format:**
```typescript
interface DeleteApiKeyRequest {
  data: {
    store_id: string;
  }
}
```

**Response:**
```typescript
interface DeleteApiKeyResponse {
  success: boolean;
  message: string;
  code: number;
}
```

**Note:** This function uses HTTP request authentication instead of Firebase callable functions.

## Email Management

The platform provides several endpoints for sending different types of emails. These functions use a queue-based approach for reliable email delivery.

### Send Sales Report Email

Send an email notification about a sales report to relevant stakeholders.

**Function:** `enqueue_send_sales_report_email`
**Endpoint:** `https://europe-west3-your-project.cloudfunctions.net/enqueue_send_sales_report_email`
**Region:** europe-west3
**Memory:** 1GB
**Timeout:** 60 seconds

**Request Format:**
```typescript
interface SendSalesReportEmailRequest {
  data: {
    template_id?: string; // Optional, defaults to 'default-sales-report'
    store_id: string;
    sales_report_id: string;
  }
}
```

**Response:**
```typescript
interface SendEmailResponse {
  success: boolean;
  message: string;
  code: number;
  data?: {
    // Task payload details
  };
}
```

**Success Response:**
```json
{
  "success": true,
  "message": "Email queued successfully",
  "code": 200,
  "data": {
    "email_request_id": "generated-id"
  }
}
```

### Send Invite Email

Send an invitation email to a producer to join the platform.

**Function:** `enqueue_send_invite_email`
**Endpoint:** `https://europe-west3-your-project.cloudfunctions.net/enqueue_send_invite_email`
**Region:** europe-west3
**Memory:** 1GB
**Timeout:** 60 seconds

**Request Format:**
```typescript
interface SendInviteEmailRequest {
  data: {
    template_id?: string; // Optional, defaults to 'default-signup-invite'
    store_id: string;
    producer_id: string;
  }
}
```

**Response:**
```typescript
interface SendEmailResponse {
  success: boolean;
  message: string;
  code: number;
  data?: {
    // Task payload details
  };
}
```

**Success Response:**
```json
{
  "success": true,
  "message": "Email queued successfully",
  "code": 200,
  "data": {
    "email_request_id": "generated-id"
  }
}
```

### Background Email Processing

Emails are processed in the background using a task queue system for reliability.

**Function:** `sendEmail`
**Type:** Task-based function
**Region:** europe-west3
**Memory:** 1GB
**Retry Config:** 2 attempts with exponential backoff between 10-60 seconds
**Rate Limits:** Maximum 5 concurrent dispatches

This function is automatically triggered when an email is queued and doesn't require direct frontend interaction.

## Sales Data Processing

### Core Scenario
```mermaid
flowchart TD
    A0["Start: sanitize_sales_from_sale_object"] --> A1["1: Prepare Sale Input _prepare_sale_input"]
    A1 --> A2["2: Fetch Active Partnerships for Store if not provided fetch_active_partnerships"]
    
    A2 --> B1{"Store Exists?\n(_get_store_document)"}
    B1 -- "No" --> Z1["Log & Output to Staging: Store Not Found"]
    B1 -- "Yes" --> B2{"Active Partnerships Found?"}
    B2 -- "No" --> Z2["Log & Output to Staging: No Active Partnerships"]
    B2 -- "Yes" --> B3["3: Match Sale to Partnership\n(_get_matched_partnership)\n(Handles forced producer ID if provided)"]
    
    B3 --> C1{"Partnership Matched?"}
    C1 -- "No" --> Z3["Log & Output to Staging: No Matching Partnership\n(If forced, logs error internally if not found)"]
    C1 -- "Yes" --> C2["4: Fetch Producer Details\n(_get_producer_document)"]
    
    C2 --> D1["5: Fetch Agreement for Sale\n(agreement_manager.get_agreement_for_sale_doc, if not provided via args)"]
    D1 --> E1{"Agreement(s) Found?"}
    E1 -- "No" --> Z4["Log & Output to Staging: No Agreement Found"]
    E1 -- "Yes" --> E2{"Exactly One Agreement Found?"}
    
    E2 -- "No (Multiple Agreements)" --> F1["Log & Add Agreement IDs to Sale Dict"]
    F1 --> Z5["Output to Staging: Multiple Agreements"]
    
    E2 -- "Yes (One Agreement)" --> G1["6: Enrich Sale with Producer & Agreement Data\n(_enrich_sale_with_producer_data)"]
    G1 --> H1{"Enrichment & SalesSilver Validation Successful?\n(SalesSilver.model_validate)"}
    H1 -- "No" --> Z6["Log & Output to Staging: Enrichment/Validation Error"]
    H1 -- "Yes" --> S1["Output: Validated SalesSilver Data"]

    Z1 -- "calls" --> CreateStaging["_create_staging_output"]
    Z2 -- "calls" --> CreateStaging
    Z3 -- "calls" --> CreateStaging
    Z4 -- "calls" --> CreateStaging
    Z5 -- "calls" --> CreateStaging
    Z6 -- "calls" --> CreateStaging
    
    CreateStaging --> EndStaging["Sale data prepared for 'sales-staging' collection"]
    S1 --> EndSilver["Sale data prepared for 'sales-silver' collection"]

    A0 -- "Overall Exception Handling" --> AttemptStaging{"Attempt Staging Output on Error"}
    AttemptStaging -- "If sale_dict available" --> Z_Error["Log & Output to Staging: Unhandled Error"]
    Z_Error -- "calls" --> CreateStaging
    AttemptStaging -- "Else (e.g., error in _prepare_sale_input)" --> RaiseGenericError["Raise Exception"]

    style Z1 fill:#191970,stroke:#cccccc,stroke-width:2px
    style Z2 fill:#8B4513,stroke:#cccccc,stroke-width:2px
    style Z3 fill:#4B0082,stroke:#cccccc,stroke-width:2px
    style Z4 fill:#005F5F,stroke:#cccccc,stroke-width:2px
    style Z5 fill:#AF4E2C,stroke:#cccccc,stroke-width:2px
    style Z6 fill:#505050,stroke:#cccccc,stroke-width:2px
    style Z_Error fill:#8B0000,stroke:#ffcc00,stroke-width:2px
    style S1 fill:#006400,stroke:#cccccc,stroke-width:2px
    style EndStaging fill:#CC5500,stroke:#cccccc,stroke-width:2px
    style EndSilver fill:#2E8B57,stroke:#cccccc,stroke-width:2px
    style CreateStaging fill:#686868,stroke:#cccccc,stroke-width:1px
```

### Sanitize Sales Staging with New Producer
```mermaid
flowchart TD
    SN0["Start: sanitize_sale_with_no_parentId_producer"] --> SN1["1: Create New Producer, Agreement & Active Partnership\n(force_active_partnership_flow.force_create_active_partnership)"]
    SN1 --> SN2{"2: Fetch Target Sale's Staging Document\n(sales_staging_query_builder.get_document by sale_id)"}
    SN2 -- "Not Found" --> SN_Error1["Raise Critical Error: Target Sale Document Not Found"]
    SN2 -- "Found" --> SN3["3: Fetch All Active Partnerships for Store (includes the new one)"]
    SN3 --> SN4["4: Get Newly Created Agreement Document"]
    
    SN4 --> SN5["5: Sanitize Target Sale (Forced Match)\nCall sanitize_sales_from_sale_object\n(using new producer_id & specific agreement_data)"]
    SN5 -- "Sanitized Result" --> SN6["6: Process Sanitized Target Sale(process_sanitized_sale: Save to Silver/Staging & cleanup)"]
    
    SN6 --> SN7["7: Fetch ALL Other Sales Staging Docs for the Same Store"]
    SN7 --> SN_LoopStart["8: For each other sale document from store:"]
    SN_LoopStart -- "Next Doc" --> SN_LoopSanitize["Call sanitize_sales_from_sale_object(normal matching logic, using all active_partnership including new one)"]
    SN_LoopSanitize -- "Sanitized Result" --> SN_LoopProcess["Process This Sanitized Sale\n(process_sanitized_sale)"]
    SN_LoopProcess --> SN_LoopEnd{"More sales for this store?"}
    SN_LoopEnd -- "Yes" --> SN_LoopStart
    SN_LoopEnd -- "No" --> SN_Return["9: Return Result of Target Sale's Sanitization (from step 5)"]

    SN5 -.-> MainSanitizationFlowRef["Ref: Core Sanitization Logic Diagram"]
    SN_LoopSanitize -.-> MainSanitizationFlowRef
    SN6 -.-> ProcessSaleRef["Ref: Process Sanitized Sale Logic Diagram"]
    SN_LoopProcess -.-> ProcessSaleRef

    style SN_Error1 fill:#f00,stroke:#333,stroke-width:2px,color:#fff
    style SN_Return fill:#ccffcc,stroke:#333,stroke-width:2px
    style MainSanitizationFlowRef fill:#e6f3ff,stroke:#333,stroke-width:1px
    style ProcessSaleRef fill:#e6f3ff,stroke:#333,stroke-width:1px
```

### Parse logic
```mermaid
flowchart TD
    PS0["Start: process_sanitized_sale"] --> PS1["1: Save Sale Data to Specified Collection (Silver or Staging)\n(db.collection().document().set())"]
    PS1 --> PS2{"Target Collection is 'sales_silver_collection'?"}
    PS2 -- "Yes" --> PS3["2: Delete Original Sale from 'sales_staging_collection'\n(db.collection().document().delete())"]
    PS3 --> PS_End["End: Sale Processed"]
    PS2 -- "No (Target is 'sales_staging_collection')" --> PS_End
```

### Sanitize Sales Staging with New Producer And Agreement

Process sales data for producers without parent IDs.

**Function:** `sanitize_sales_staging_with_new_agreement_and_producer`
**Endpoint:** `https://europe-west3-your-project.cloudfunctions.net/sanitize_sales_staging_with_new_agreement_and_producer`
**Region:** europe-west3
**Memory:** 4GB
**Timeout:** 540 seconds (9 minutes)

**Request Format:** [Source: `/types/requests/sanitizationRequestInterfaces.ts`](../types/requests/sanitizationRequestInterfaces.ts)
```typescript
interface SanitizeSalesStagingWithAgreementRequest {
  data: {
    // Sanitization request properties
  }
}
```

**Response:**
```typescript
interface SanitizeSalesStagingResponse {
  success: boolean;
  message: string;
  code: number;
}
```

**Success Response:**
```json
{
  "success": true,
  "message": "Sales staging with no parentId producer sanitized successfully",
  "code": 200
}
```

### Sanitize Sales Staging with New Active Agreement

Create a new active agreement and sanitize sales staging data without creating a new producer.

**Function:** `sanitize_sales_staging_with_new_active_agreement`
**Endpoint:** `https://europe-west3-your-project.cloudfunctions.net/sanitize_sales_staging_with_new_active_agreement`
**Region:** europe-west3
**Memory:** 1GB
**Timeout:** 60 seconds

**Request Format:** [Source: `/types/requests/agreementsInterfaces.ts`](../types/requests/agreementsInterfaces.ts)
```typescript
interface SanitizeSalesStagingWithActiveAgreementRequest {
  data: {
    storeId: string;
    producerId: string;
    title?: string | null;
    effectiveDate: Date;
    expirationDate?: Date | null;
    commission: number;
    documentUrl?: string | null;
    createdByRole?: Role;
    saleId: string;
  }
}
```

**Response:**
```typescript
interface SanitizeSalesStagingResponse {
  success: boolean;
  message: string;
  code: number;
  data?: any; // Sanitized sales staging data
}
```

**Success Response:**
```json
{
  "success": true,
  "message": "Sales staging sanitized successfully",
  "code": 200,
  "data": {
    // Sanitized sales staging data
  }
}
```

### Sanitize Sales Staging with Existing Agreement

Sanitize sales staging data using an existing agreement.

**Function:** `sanitize_sales_staging_with_existing_agreement`
**Endpoint:** `https://europe-west3-your-project.cloudfunctions.net/sanitize_sales_staging_with_existing_agreement`
**Region:** europe-west3
**Memory:** 1GB
**Timeout:** 60 seconds

**Request Format:** [Source: `/types/requests/agreementsInterfaces.ts`](../types/requests/agreementsInterfaces.ts)
```typescript
interface SanitizeSalesStagingWithExistingAgreementRequest {
  data: {
    saleId: string;
    agreementId: string;
  }
}
```

**Response:**
```typescript
interface SanitizeSalesStagingResponse {
  success: boolean;
  message: string;
  code: number;
  data?: any; // Sanitized sales staging data
}
```

**Success Response:**
```json
{
  "success": true,
  "message": "Sales staging sanitized successfully",
  "code": 200,
  "data": {
    // Sanitized sales staging data
  }
}
```

## Event-Driven Functions

The following functions are triggered automatically by system events and don't require direct frontend calls:

### Sales Sanitization (Firestore Triggers)

- **Functions:** `on_sales_staging_created`, `on_sales_staging_updated`
- **Trigger:** When sales-staging documents are created or updated
- **Purpose:** Automatically sanitize sales data based on active partnerships

### Sales Silver to Gold (Firestore Triggers)

- **Functions:** `on_sales_silver_created`, `on_sales_silver_updated`
- **Trigger:** When sales-silver documents are created or updated
- **Purpose:** Generate sales report data from sanitized sales records

### Shopify Order Processing (Cloud Storage Trigger)

- **Function:** `on_storage_shopify_orders_file_added`
- **Trigger:** When Shopify order files are uploaded to Cloud Storage
- **Purpose:** Process Shopify order files and store them in the database

**Note:** These functions run automatically in response to system events and don't expose HTTP endpoints for direct frontend interaction.

## Common Response Format

All functions return responses in a consistent format:

```typescript
interface ResponseData {
  success: boolean;
  message: string;
  code: number;
  data?: any; // Optional data payload
}
```

## Error Handling

### Common Error Codes

- **401:** Unauthorized - User must be authenticated
- **400:** Bad Request - Validation error or missing parameters
- **422:** Unprocessable Entity - Request data validation failed
- **500:** Internal Server Error - Unexpected server-side error

### Example Error Responses

**401 - Unauthorized:**
```json
{
  "success": false,
  "message": "User must be authenticated",
  "code": 401
}
```

**400 - Bad Request:**
```json
{
  "success": false,
  "message": "Missing required fields: store_id and api_key",
  "code": 400
}
```

**500 - Internal Server Error:**
```json
{
  "success": false,
  "message": "An unexpected error occurred: {error_details}",
  "code": 500
}
```

## Best Practices

1. **Error Handling:** Always handle both success and error cases in your frontend code
2. **Loading States:** Show loading indicators during cloud function calls
3. **Timeout Handling:** Be aware of function timeouts and implement appropriate UI feedback
4. **Data Validation:** Validate request data before sending to cloud functions
5. **Authentication:** Ensure user is authenticated before calling functions
6. **Retry Logic:** Implement retry logic for failed requests when appropriate


## Additional Information

- All functions are deployed in the `europe-west3` region
- Functions use varying amounts of memory based on their complexity
- Most functions timeout between 60-600 seconds
- Background processes use Firebase Tasks for reliable execution
- Secrets are managed securely using Google Cloud Secret Manager

For more information or support, please contact the backend team.