import { FirebaseCollections } from "@/constants/firebaseCollections";
import { Store } from "@/types/collections/storeInterfaces";
import { useMemo } from "react";
import { useFirestoreDocData } from "reactfire";
import { useFirestoreTypedRefs } from "./useFirestoreTypedRefs";

interface useStoreProps {
  storeId: string;
}

export const useStore = ({ storeId }: useStoreProps) => {
  const { getTypedDocumentRef } = useFirestoreTypedRefs();

  const storeDocumentRef = useMemo(
    () => getTypedDocumentRef<Store>(FirebaseCollections.STORES, storeId),
    [getTypedDocumentRef, storeId]
  );

  const { status, data: store } = useFirestoreDocData(storeDocumentRef);

  return {
    status,
    store,
  };
};
