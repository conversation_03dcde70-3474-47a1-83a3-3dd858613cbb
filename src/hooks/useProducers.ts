import { FirebaseCollections } from "@/constants/firebaseCollections";
import { Producer } from "@/types/collections/producerInterfaces";
import { useMemo } from "react";
import { useFirestoreCollectionData } from "reactfire";
import { useFirestoreTypedRefs } from "./useFirestoreTypedRefs";

export const useProducers = () => {
  const { getTypedCollectionRef } = useFirestoreTypedRefs();

  const producersCollectionRef = useMemo(
    () => getTypedCollectionRef<Producer>(FirebaseCollections.PRODUCERS),
    [getTypedCollectionRef]
  );

  const { status, data: producers } = useFirestoreCollectionData(
    producersCollectionRef,
    {
      idField: "id",
    }
  );

  const getProducerById = useMemo(
    () => (producerId: string) =>
      producers?.find((producer) => producer.id === producerId),
    [producers]
  );

  return {
    status,
    producers,
    getProducerById,
  };
};
