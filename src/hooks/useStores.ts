import { FirebaseCollections } from "@/constants/firebaseCollections";
import { useFirestoreTypedRefs } from "@/hooks/useFirestoreTypedRefs";
import { Store } from "@/types/collections/storeInterfaces";
import { useMemo } from "react";
import { useFirestoreCollectionData } from "reactfire";

export const useStores = () => {
  const { getTypedCollectionRef } = useFirestoreTypedRefs();

  const storesCollectionRef = useMemo(
    () => getTypedCollectionRef<Store>(FirebaseCollections.STORES),
    [getTypedCollectionRef]
  );

  const { status, data: stores } = useFirestoreCollectionData(
    storesCollectionRef,
    {
      idField: "id",
    }
  );

  const getStoreById = useMemo(
    () => (storeId: string) => stores?.find((store) => store.id === storeId),
    [stores]
  );

  return { stores: stores || [], status, getStoreById };
};
