import { FirebaseCollections } from "@/constants/firebaseCollections";
import { SalesGold, SalesStaging } from "@/types/collections/salesInterfaces";
import { useMemo } from "react";
import { useFirestoreDocData } from "reactfire";
import { useFirestoreTypedRefs } from "./useFirestoreTypedRefs";

interface useSalesProps {
  saleId: string;
}

export const useSale = ({ saleId }: useSalesProps) => {
  const { getTypedDocumentRef } = useFirestoreTypedRefs();

  const saleGoldDocumentRef = useMemo(
    () =>
      getTypedDocumentRef<SalesGold>(FirebaseCollections.SALES_GOLD, saleId),
    [getTypedDocumentRef, saleId]
  );

  const saleStagingDocumentRef = useMemo(
    () =>
      getTypedDocumentRef<SalesStaging>(
        FirebaseCollections.SALES_STAGING,
        saleId
      ),
    [getTypedDocumentRef, saleId]
  );

  const { status: goldStatus, data: goldSale } =
    useFirestoreDocData(saleGoldDocumentRef);
  const { status: stagingStatus, data: stagingSale } = useFirestoreDocData(
    saleStagingDocumentRef
  );

  const status = useMemo(() => {
    if (goldStatus === "loading" || stagingStatus === "loading") {
      return "loading";
    }
    if (goldStatus === "error" || stagingStatus === "error") {
      return "error";
    }
    return "success";
  }, [goldStatus, stagingStatus]);

  return {
    status,
    sale: goldSale || stagingSale,
  };
};
