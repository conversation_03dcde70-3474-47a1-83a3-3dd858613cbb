import { FirebaseCollections } from "@/constants/firebaseCollections";
import { SalesGold, SalesStaging } from "@/types/collections/salesInterfaces";
import { or, orderBy, query, where } from "firebase/firestore";
import { useMemo } from "react";
import { useFirestoreCollectionData } from "reactfire";
import { useFirestoreTypedRefs } from "./useFirestoreTypedRefs";

interface useSalesProps {
  producerId?: string;
  storeId?: string;
}

export const useSales = ({ producerId, storeId }: useSalesProps) => {
  const { getTypedCollectionRef } = useFirestoreTypedRefs();

  const salesCollectionRef = useMemo(
    () => getTypedCollectionRef<SalesGold>(FirebaseCollections.SALES_GOLD),
    [getTypedCollectionRef]
  );

  const salesStagingCollectionRef = useMemo(
    () =>
      getTypedCollectionRef<SalesStaging>(FirebaseCollections.SALES_STAGING),
    [getTypedCollectionRef]
  );

  const whereFunctions = useMemo(() => {
    const whereFunctions = [];
    if (producerId) {
      whereFunctions.push(where("producerId", "==", producerId));
    }

    if (storeId) {
      whereFunctions.push(where("storeId", "==", storeId));
    }

    return whereFunctions;
  }, [producerId, storeId]);

  const salesQuery = useMemo(() => {
    const finalQuery =
      whereFunctions.length > 1
        ? query(
            salesCollectionRef,
            or(...whereFunctions),
            orderBy("updatedAt", "desc")
          )
        : query(
            salesCollectionRef,
            whereFunctions[0],
            orderBy("updatedAt", "desc")
          );

    return finalQuery;
  }, [salesCollectionRef, whereFunctions]);

  const salesStagingQuery = useMemo(() => {
    const finalQuery =
      whereFunctions.length > 1
        ? query(
            salesStagingCollectionRef,
            or(...whereFunctions),
            orderBy("updatedAt", "desc")
          )
        : query(
            salesStagingCollectionRef,
            whereFunctions[0],
            orderBy("updatedAt", "desc")
          );

    return finalQuery;
  }, [salesStagingCollectionRef, whereFunctions]);

  const { status: salesGoldStatus, data: salesGold = [] } =
    useFirestoreCollectionData(salesQuery, {
      idField: "id",
    });

  const { status: salesStagingStatus, data: salesStaging = [] } =
    useFirestoreCollectionData(salesStagingQuery, {
      idField: "id",
    });

  const sales: (SalesStaging | SalesGold)[] = useMemo(
    () => [...salesGold, ...salesStaging],
    [salesGold, salesStaging]
  );

  const totalRevenue = useMemo(
    () => sales?.reduce((acc, sale) => acc + sale.totalPrice, 0) || 0,
    [sales]
  );

  const status = useMemo(() => {
    if (salesGoldStatus === "loading" || salesStagingStatus === "loading") {
      return "loading";
    }
    if (salesGoldStatus === "error" || salesStagingStatus === "error") {
      return "error";
    }
    return "success";
  }, [salesGoldStatus, salesStagingStatus]);

  return {
    status,
    sales,
    salesStagingStatus,
    salesStaging,
    salesGoldStatus,
    salesGold,
    totalSales: sales?.length || 0,
    totalRevenue,
  };
};
