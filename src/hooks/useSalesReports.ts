import { Producer } from "@/types/collections/producerInterfaces";
import { SalesReportModel } from "@/types/collections/salesReportInterfaces";
import { Store } from "@/types/collections/storeInterfaces";
import { or, orderBy, query, where } from "firebase/firestore";
import { useMemo } from "react";
import { useFirestoreCollectionData } from "reactfire";
import { useFirestoreTypedRefs } from "./useFirestoreTypedRefs";
import { useProducers } from "./useProducers";
import { useStores } from "./useStores";

interface useSalesReportsProps {
  producerId?: string;
  storeId?: string;
}

export const useSalesReports = ({
  storeId,
  producerId,
}: useSalesReportsProps) => {
  const { getTypedCollectionRef } = useFirestoreTypedRefs();
  const { getProducerById } = useProducers();
  const { getStoreById } = useStores();

  const salesReportsCollectionRef = useMemo(
    () => getTypedCollectionRef<SalesReportModel>("sales-reports"),
    [getTypedCollectionRef]
  );

  const salesReportsQuery = useMemo(() => {
    const whereFunctions = [];
    if (producerId) {
      whereFunctions.push(where("producerId", "==", producerId));
    }

    if (storeId) {
      whereFunctions.push(where("storeId", "==", storeId));
    }

    const finalQuery =
      whereFunctions.length > 1
        ? query(
            salesReportsCollectionRef,
            or(...whereFunctions),
            orderBy("updatedAt", "desc")
          )
        : query(
            salesReportsCollectionRef,
            whereFunctions[0],
            orderBy("updatedAt", "desc")
          );

    return finalQuery;
  }, [producerId, storeId, salesReportsCollectionRef]);

  const { status, data: salesReports } = useFirestoreCollectionData(
    salesReportsQuery,
    {
      idField: "id",
    }
  );

  const enrichSalesReportWithProducer = (
    salesReport: SalesReportModel
  ): SalesReportModel & { producer: Producer } => {
    const producer = getProducerById(salesReport.producerId);

    if (!producer) {
      throw new Error(`Producer with ID ${salesReport.producerId} not found`);
    }

    return {
      ...salesReport,
      producer,
    };
  };

  const enrichSalesReportWithStore = (
    salesReport: SalesReportModel
  ): SalesReportModel & { store: Store } => {
    const store = getStoreById(salesReport.storeId);

    if (!store) {
      throw new Error(`Producer with ID ${salesReport.storeId} not found`);
    }

    return {
      ...salesReport,
      store,
    };
  };

  return {
    status,
    salesReports,
    enrichSalesReportWithProducer,
    enrichSalesReportWithStore,
  };
};
