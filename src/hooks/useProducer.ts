import { FirebaseCollections } from "@/constants/firebaseCollections";
import { Producer } from "@/types/collections/producerInterfaces";
import { useMemo } from "react";
import { useFirestoreDocData } from "reactfire";
import { useFirestoreTypedRefs } from "./useFirestoreTypedRefs";

interface useProducerProps {
  producerId: string;
}

export const useProducer = ({ producerId }: useProducerProps) => {
  const { getTypedDocumentRef } = useFirestoreTypedRefs();

  const producerDocumentRef = useMemo(
    () =>
      getTypedDocumentRef<Producer>(FirebaseCollections.PRODUCERS, producerId),
    [getTypedDocumentRef, producerId]
  );

  const { status, data: producer } = useFirestoreDocData(producerDocumentRef);

  return {
    status,
    producer,
  };
};
