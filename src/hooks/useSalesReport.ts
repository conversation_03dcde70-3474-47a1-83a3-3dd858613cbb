import { FirebaseCollections } from "@/constants/firebaseCollections";
import { SalesReportModel } from "@/types/collections/salesReportInterfaces";
import { useMemo } from "react";
import { useFirestoreDocData } from "reactfire";
import { useFirestoreTypedRefs } from "./useFirestoreTypedRefs";

interface useSalesReportProps {
  salesReportId: string;
}

export const useSalesReport = ({ salesReportId }: useSalesReportProps) => {
  const { getTypedDocumentRef } = useFirestoreTypedRefs();

  const salesReportDocumentRef = useMemo(
    () =>
      getTypedDocumentRef<SalesReportModel>(
        FirebaseCollections.SALES_REPORTS,
        salesReportId
      ),
    [getTypedDocumentRef, salesReportId]
  );

  const { status, data } = useFirestoreDocData(salesReportDocumentRef);

  return {
    status,
    data,
  };
};
