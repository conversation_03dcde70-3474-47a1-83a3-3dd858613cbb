"use client";

import { FirebaseCollections } from "@/constants/firebaseCollections";
import { useFirestoreTypedRefs } from "@/hooks/useFirestoreTypedRefs";
import { useProducers } from "@/hooks/useProducers";
import {
  callApproveAgreementApi,
  callCreateDraftAgreementApi,
  callDeleteAgreementApi,
  callRejectAgreementApi,
  callSubmitDraftAgreementApi,
  callTerminateAgreementApi,
  callUpdateDraftAgreementApi,
} from "@/lib/api";
import {
  Agreement,
  AgreementStatus,
} from "@/types/collections/agreementInterfaces";
import { Producer } from "@/types/collections/producerInterfaces";
import { Store } from "@/types/collections/storeInterfaces";
import { and, query, where } from "firebase/firestore";
import { useMemo } from "react";
import { useFirestoreCollectionData } from "reactfire";
import { Role } from "types/collections/agreementInterfaces";
import {
  CreateAgreementRequest,
  SubmitAgreementForApprovalRequest,
  UpdateDraftAgreementRequest,
} from "types/requests/agreementsInterfaces";
import { useFirebaseFunctions } from "./useFirebaseFunctions";
import { useStores } from "./useStores";

interface useAgreementsProps {
  accountRole: Role;
  storeId?: string;
  producerId?: string;
}

export const useAgreements = ({
  accountRole,
  storeId = "",
  producerId = "",
}: useAgreementsProps) => {
  const functions = useFirebaseFunctions();
  const { getTypedCollectionRef } = useFirestoreTypedRefs();
  const { getStoreById } = useStores();
  const { getProducerById } = useProducers();

  const agreementsCollectionRef = useMemo(
    () => getTypedCollectionRef<Agreement>(FirebaseCollections.AGREEMENTS),
    [getTypedCollectionRef]
  );

  const outboundAgreementQuery = useMemo(
    () =>
      query(
        agreementsCollectionRef,
        and(
          accountRole === "store"
            ? where("storeId", "==", storeId)
            : where("producerId", "==", producerId),
          where("createdByRole", "==", accountRole)
        )
      ),
    [agreementsCollectionRef, storeId, producerId, accountRole]
  );

  const recievedAgreementQuery = useMemo(
    () =>
      query(
        agreementsCollectionRef,
        and(
          accountRole === "store"
            ? where("storeId", "==", storeId)
            : where("producerId", "==", producerId),
          where(
            "createdByRole",
            "==",
            accountRole === "store" ? "producer" : "store"
          ),
          where("status", "!=", "draft")
        )
      ),
    [agreementsCollectionRef, accountRole, storeId, producerId]
  );

  const { status: inboundStatus, data: inboundAgreements = [] } =
    useFirestoreCollectionData(recievedAgreementQuery, {
      idField: "id",
    });
  const { status: outboundStatus, data: outboundAgreements = [] } =
    useFirestoreCollectionData(outboundAgreementQuery, {
      idField: "id",
    });

  const createDraftAgreement = async ({
    storeId,
    producerId,
    title,
    commission,
    expirationDate,
    effectiveDate = new Date("2100-01-01"),
    createdByRole,
  }: CreateAgreementRequest) =>
    await callCreateDraftAgreementApi(
      {
        storeId,
        producerId,
        title,
        effectiveDate,
        expirationDate,
        commission,
        createdByRole,
      },
      functions
    );

  const updateDraftAgreement = async (
    agreementId: string,
    {
      title,
      commission,
      expirationDate,
      effectiveDate = new Date("2100-01-01"),
    }: Omit<UpdateDraftAgreementRequest, "agreementId">
  ) => {
    await callUpdateDraftAgreementApi(
      {
        agreementId,
        title,
        effectiveDate,
        expirationDate,
        commission,
      },
      functions
    );
  };

  const submitDraftAgreement = async (
    payload: SubmitAgreementForApprovalRequest
  ) => await callSubmitDraftAgreementApi(payload, functions);

  const deleteAgreement = async (agreementId: string) =>
    await callDeleteAgreementApi(
      {
        agreementId,
      },
      functions
    );

  const approveAgreement = async (agreementId: string) => {
    try {
      await callApproveAgreementApi(
        {
          agreementId,
          storeOrProducerId: storeId || producerId,
          role: storeId ? "store" : "producer",
        },
        functions
      );
    } catch (error) {
      throw new Error(error instanceof Error ? error.message : String(error));
    }
  };

  const terminateAgreement = async (agreementId: string) => {
    try {
      await callTerminateAgreementApi(
        {
          agreementId,
        },
        functions
      );
    } catch (error) {
      throw new Error(error instanceof Error ? error.message : String(error));
    }
  };

  const rejectAgreement = async (senderId: string) => {
    try {
      await callRejectAgreementApi(
        {
          agreementId: senderId,
          role: storeId ? "store" : "producer",
        },
        functions
      );
    } catch (error) {
      throw new Error(error instanceof Error ? error.message : String(error));
    }
  };

  const acceptedAgreements = useMemo(
    () =>
      [...outboundAgreements, ...inboundAgreements]?.filter(
        (application) => application.status === AgreementStatus.ACTIVE
      ) || [],
    [inboundAgreements, outboundAgreements]
  );

  const expiredAgreements = useMemo(
    () =>
      [...outboundAgreements, ...inboundAgreements]?.filter(
        (application) => application.status === AgreementStatus.EXPIRED
      ) || [],
    [inboundAgreements, outboundAgreements]
  );

  const enrichAgreementWithProducer = (
    agreement: Agreement
  ): Agreement & { producer: Producer } => {
    const producer = getProducerById(agreement.producerId);

    if (!producer) {
      throw new Error(`Producer with ID ${agreement.producerId} not found`);
    }

    return {
      ...agreement,
      producer,
    };
  };

  const enrichAgreementWithStore = (
    agreement: Agreement
  ): Agreement & { store: Store } => {
    const store = getStoreById(agreement.storeId);

    if (!store) {
      throw new Error(`Store with ID ${agreement.storeId} not found`);
    }

    return {
      ...agreement,
      store,
    };
  };

  return {
    outboundAgreements,
    inboundAgreements,
    acceptedAgreements,
    expiredAgreements,
    status: outboundStatus || inboundStatus, //TODO fix this
    createDraftAgreement,
    updateDraftAgreement,
    submitDraftAgreement,
    deleteAgreement,
    enrichAgreementWithProducer,
    enrichAgreementWithStore,
    approveAgreement,
    rejectAgreement,
    terminateAgreement,
  };
};
