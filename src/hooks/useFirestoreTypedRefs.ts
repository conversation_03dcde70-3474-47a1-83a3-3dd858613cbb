import { Timestamp } from "firebase-admin/firestore";
import {
  collection,
  doc,
  QueryDocumentSnapshot,
  serverTimestamp,
  SnapshotOptions,
} from "firebase/firestore";
import { useCallback } from "react";
import { useFirestore } from "reactfire";

const createFirestoreConverter = <T extends { createdAt: Timestamp }>() => {
  return {
    toFirestore: (item: T) => {
      return {
        ...item,
        createdAt: serverTimestamp(),
      };
    },
    fromFirestore: (
      snapshot: QueryDocumentSnapshot<T>,
      options?: SnapshotOptions
    ) => {
      const data = snapshot.data(options);
      return {
        ...data,
        id: snapshot.id,
        createdAt: data.createdAt,
      };
    },
  };
};

export const useFirestoreTypedRefs = () => {
  const firestore = useFirestore();

  const getTypedCollectionRef = useCallback(
    <T extends { createdAt: Timestamp }>(path: string) => {
      return collection(firestore, path).withConverter(
        createFirestoreConverter<T>()
      );
    },
    [firestore]
  );

  const getTypedDocumentRef = useCallback(
    <T extends { createdAt: Timestamp }>(
      collectionPath: string,
      documentId: string
    ) => {
      return doc(firestore, collectionPath, documentId).withConverter(
        createFirestoreConverter<T>()
      );
    },
    [firestore]
  );

  return { getTypedCollectionRef, getTypedDocumentRef };
};
