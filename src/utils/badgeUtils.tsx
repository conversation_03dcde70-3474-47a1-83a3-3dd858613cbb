import { AgreementStatus } from "@/types/collections/agreementInterfaces";
import { Badge } from "@gaco/gaco-library";

export const getStatusBadge = (status: string | null | undefined) => {
  switch (status?.toLowerCase()) {
    case AgreementStatus.ACTIVE:
      return <Badge variant="success">Active</Badge>;
    case AgreementStatus.EXPIRED:
      return <Badge variant="error">Expired</Badge>;
    case AgreementStatus.DRAFT:
      return <Badge variant="default">Draft</Badge>;
    case AgreementStatus.REJECTED:
      return <Badge variant="error">Rejected</Badge>;
    case AgreementStatus.PENDING:
      return <Badge variant="warning">Pending</Badge>;
    case AgreementStatus.TERMINATED:
      return <Badge variant="error">Terminated</Badge>;
    default:
      return <Badge variant="default">No Status</Badge>;
  }
};
