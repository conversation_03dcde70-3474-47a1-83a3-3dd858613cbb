export const overviewData = {
  total_revenue: 5600,
  total_artists: 3,
  sales_count: 1000,
  sales_data: [
    {
      name: "2024-01",
      total_sales: 7000,
      store_A: 4000,
      store_B: 2000,
      store_C: 1000,
    },
    {
      name: "2024-02",
      total_sales: 5000,
      store_A: 3000,
      store_B: 1500,
      store_C: 500,
    },
    {
      name: "2024-03",
      total_sales: 3000,
      store_A: 2000,
      store_B: 1000,
      store_C: 0,
    },
    {
      name: "2024-04",
      total_sales: 3780,
      store_A: 2780,
      store_B: 1200,
      store_C: 100,
    },
    {
      name: "2024-05",
      total_sales: 2890,
      store_A: 1890,
      store_B: 800,
      store_C: 200,
    },
    {
      name: "2024-06",
      total_sales: 3390,
      store_A: 2390,
      store_B: 1100,
      store_C: 300,
    },
    {
      name: "2024-07",
      total_sales: 4490,
      store_A: 3490,
      store_B: 1300,
      store_C: 400,
    },
    {
      name: "2024-08",
      total_sales: 3500,
      store_A: 2000,
      store_B: 1000,
      store_C: 500,
    },
    {
      name: "2024-09",
      total_sales: 1600,
      store_A: 1000,
      store_B: 500,
      store_C: 100,
    },
    {
      name: "2024-10",
      total_sales: 2600,
      store_A: 1890,
      store_B: 900,
      store_C: 100,
    },
    {
      name: "2024-11",
      total_sales: 3600,
      store_A: 2390,
      store_B: 1100,
      store_C: 200,
    },
    {
      name: "2024-12",
      total_sales: 4600,
      store_A: 3490,
      store_B: 1300,
      store_C: 300,
    },
  ],
  line_data_keys: ["total_sales", "store_A", "store_B", "store_C"],
};

export const products = [
  {
    id: 1,
    name: "Abstract Painting",
    image: "/placeholder.svg?height=100&width=100",
    price: 299.99,
    variants: ["Small", "Medium", "Large"],
    store: "Artistry Haven",
  },
  {
    id: 2,
    name: "Ceramic Vase",
    image: "/placeholder.svg?height=100&width=100",
    price: 79.99,
    variants: ["Blue", "Green", "White"],
    store: "Creative Corner",
  },
  {
    id: 3,
    name: "Digital Print",
    image: "/placeholder.svg?height=100&width=100",
    price: 49.99,
    variants: ["8x10", "11x14", "16x20"],
    store: "Digital Dreams Gallery",
  },
  {
    id: 4,
    name: "Handmade Jewelry",
    image: "/placeholder.svg?height=100&width=100",
    price: 129.99,
    variants: ["Necklace", "Bracelet", "Earrings"],
    store: "Palette Paradise",
  },
  {
    id: 5,
    name: "Sculpture",
    image: "/placeholder.svg?height=100&width=100",
    price: 599.99,
    variants: ["Bronze", "Marble", "Wood"],
    store: "Sculpture Studio",
  },
];

export const salesData = [
  {
    id: 1,
    name: "Product A",
    price: 19.99,
    date: "2024-03-15",
  },
  {
    id: 2,
    name: "Product B",
    price: 29.99,
    date: "2024-03-14",
  },
  {
    id: 3,
    name: "Product C",
    price: 39.99,
    date: "2024-03-13",
  },
  {
    id: 4,
    name: "Product D",
    price: 49.99,
    date: "2024-03-12",
  },
  {
    id: 5,
    name: "Product E",
    price: 59.99,
    date: "2024-03-11",
  },
  {
    id: 6,
    name: "Product F",
    price: 69.99,
    date: "2024-03-10",
  },
  {
    id: 7,
    name: "Product G",
    price: 79.99,
    date: "2024-03-09",
  },
  {
    id: 8,
    name: "Product H",
    price: 89.99,
    date: "2024-03-08",
  },
  {
    id: 9,
    name: "Product I",
    price: 99.99,
    date: "2024-03-07",
  },
  {
    id: 10,
    name: "Product J",
    price: 109.99,
    date: "2024-03-06",
  },
  {
    id: 11,
    name: "Product K",
    price: 119.99,
    date: "2024-03-05",
  },
  {
    id: 12,
    name: "Product L",
    price: 129.99,
    date: "2024-03-04",
  },
  {
    id: 13,
    name: "Product M",
    price: 139.99,
    date: "2024-03-03",
  },
  {
    id: 14,
    name: "Product N",
    price: 149.99,
    date: "2024-03-02",
  },
  {
    id: 15,
    name: "Product O",
    price: 159.99,
    date: "2024-03-01",
  },
];
