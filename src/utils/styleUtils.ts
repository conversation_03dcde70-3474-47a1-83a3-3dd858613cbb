import { css } from "styled-components";

export const device = {
  xs: "400px",
  sm: "600px",
  md: "900px",
  lg: "1280px",
  xl: "1440px",
  xxl: "1920px",
};

export const media = {
  xs: (...args: Parameters<typeof css>) => css`
    @media (min-width: ${device.xs}) {
      ${css(...args)};
    }
  `,
  sm: (...args: Parameters<typeof css>) => css`
    @media (min-width: ${device.sm}) {
      ${css(...args)};
    }
  `,
  md: (...args: Parameters<typeof css>) => css`
    @media (min-width: ${device.md}) {
      ${css(...args)};
    }
  `,
  lg: (...args: Parameters<typeof css>) => css`
    @media (min-width: ${device.lg}) {
      ${css(...args)};
    }
  `,
  xl: (...args: Parameters<typeof css>) => css`
    @media (min-width: ${device.xl}) {
      ${css(...args)};
    }
  `,
  xxl: (...args: Parameters<typeof css>) => css`
    @media (min-width: ${device.xxl}) {
      ${css(...args)};
    }
  `,
};
