import { Address } from "types/collections/storeInterfaces";

export const formatAddressToOneLine = (address: Address): string => {
  const {
    streetNumber,
    streetName,
    houseNumber,
    additionalInfo,
    zipCode,
    city,
    state,
    country,
  } = address;

  const formattedAddress = [
    `${streetNumber} ${streetName}${houseNumber ? ` ${houseNumber}` : ""}`,
    additionalInfo,
    `${zipCode} ${city}`,
    state,
    country,
  ]
    .filter(Boolean)
    .join(", ");

  return formattedAddress;
};
