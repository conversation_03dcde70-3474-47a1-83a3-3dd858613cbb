import { SalesGold, SalesStaging } from "@/types/collections/salesInterfaces";
import { ChartLabel } from "@gaco/gaco-library";
import { isWithinInterval } from "date-fns";
import { safeColor } from "./colorUtils";
import hasKey from "./hasKey";

export const filterSalesIntoMonthlySeperatedData = (
  sales: (SalesGold | SalesStaging)[],
  nmOfMonths: number,
  startingMonthOffset: number
) => {
  return Array.from({ length: nmOfMonths })
    .map((_, index) => {
      const date = new Date();
      const offset = -startingMonthOffset - index;

      date.setMonth(date.getMonth() - 1 + offset);
      return {
        name: date.toLocaleString("default", {
          month: "long",
          year: nmOfMonths > 12 ? "numeric" : undefined,
        }),
        data: sales.filter((sale) =>
          isWithinInterval(sale.updatedAt.toDate(), {
            start: new Date(
              new Date().getFullYear(),
              new Date().getMonth() - 1 + offset,
              1
            ),
            end: new Date(
              new Date().getFullYear(),
              new Date().getMonth() + offset,
              0
            ),
          })
        ),
      };
    })
    .reverse();
};

export const filterSalesBasedOnSelectedLabelOptions = (
  sales: (SalesGold | SalesStaging)[],
  optionsAndKey: {
    labelOptions: ChartLabel[];
    labelKey: string;
  }[]
) =>
  sales.filter((sale) =>
    optionsAndKey
      .filter(
        ({ labelOptions, labelKey }) =>
          !!labelOptions.length && hasKey(sale, labelKey)
      )
      .some(({ labelOptions, labelKey }) => {
        const dataKeys = labelOptions.map(({ dataKey }) => dataKey);
        return dataKeys.includes(
          sale[labelKey as keyof (SalesGold | SalesStaging)] as string
        );
      })
  );

export const filterSalesIntoIntervalSeperatedData = (
  sales: (SalesGold | SalesStaging)[],
  startDate: Date,
  endDate: Date,
  intervals: number
) => {
  const reversedSales = sales.slice().reverse();

  return Array.from({ length: intervals }).map((_, index) => {
    const intervalStart = new Date(
      startDate.getTime() +
        ((endDate.getTime() - startDate.getTime()) * index) / intervals
    );
    const intervalEnd = new Date(
      startDate.getTime() +
        ((endDate.getTime() - startDate.getTime()) * (index + 1)) / intervals
    );

    const isIntervalEndOneDayAfterInteralStart =
      intervalEnd.getTime() - intervalStart.getTime() <= 86400000;

    return {
      name: isIntervalEndOneDayAfterInteralStart
        ? intervalEnd.toLocaleDateString()
        : `${intervalStart.toLocaleDateString()} - ${intervalEnd.toLocaleDateString()}`,
      data: reversedSales.filter(
        (sale) =>
          new Date(sale.updatedAt.toDate()) >= intervalStart &&
          new Date(sale.updatedAt.toDate()) < intervalEnd
      ),
    };
  });
};

export const createPieChartDataByInterval = (
  salesPeriod: { name: string; data: (SalesGold | SalesStaging)[] }[]
) => {
  const slices = salesPeriod.map(({ name, data }) => {
    const value = data.reduce((acc, sale) => acc + Number(sale.totalPrice), 0);

    return {
      value,
      color: safeColor.random(),
      name: name,
    };
  });

  return slices;
};

export const createPieChartDataByLabelOption = (
  sales: (SalesGold | SalesStaging)[],
  optionsAndKey: {
    labelOptions: ChartLabel[];
    labelKey: string;
  }[]
) => {
  const slices = optionsAndKey.flatMap(({ labelOptions, labelKey }) => {
    const slice = labelOptions.map(({ dataKey, stroke, label }) => {
      const value = sales
        .filter((sale) => {
          if (!hasKey(sale, labelKey)) return false;
          return dataKey === "Unknown"
            ? !sale[labelKey]
            : sale[labelKey] === dataKey;
        })
        .reduce((acc, sale) => acc + Number(sale.totalPrice), 0);

      return {
        value,
        color: stroke,
        name: label,
      };
    });

    return slice;
  });

  return slices;
};

export const createChartDataBasedOnLabelsAndSalesArray = (
  salesPeriod: { name: string; data: (SalesGold | SalesStaging)[] }[],
  optionsAndKey: {
    labelOptions: ChartLabel[];
    labelKey: string;
  }[]
) => {
  return salesPeriod.map(({ name, data }) => {
    const obj: Record<string, number | string> = {
      name,
    };

    optionsAndKey.forEach(({ labelOptions, labelKey }) => {
      labelOptions.forEach(({ dataKey }) => {
        const sales = data.filter((sale) => {
          if (!hasKey(sale, labelKey)) return false;
          return dataKey === "Unknown"
            ? !sale[labelKey]
            : sale[labelKey] === dataKey;
        });

        obj[dataKey] = sales.reduce(
          (acc, sale) => acc + Number(sale.totalPrice),
          0
        );
      });
    });
    return obj;
  });
};
