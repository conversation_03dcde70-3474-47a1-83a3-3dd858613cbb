"use client";

import { Agreement } from "@/types/collections/agreementInterfaces";
import { SalesGold, SalesStaging } from "@/types/collections/salesInterfaces";
import { DateRangeKeys } from "@/types/date";
import {
  createChartDataBasedOnLabelsAndSalesArray,
  createPieChartDataByInterval,
  createPieChartDataByLabelOption,
  filterSalesBasedOnSelectedLabelOptions,
  filterSalesIntoIntervalSeperatedData,
  filterSalesIntoMonthlySeperatedData,
} from "@/utils/chartUtils";
import { safeColor } from "@/utils/colorUtils";
import hasKey from "@/utils/hasKey";
import {
  ChartLabel,
  Dialog,
  Form,
  FormFieldComponents,
  SelectOption,
} from "@gaco/gaco-library";
import { createContext, ReactNode, useContext, useMemo, useState } from "react";
import { useLocalStorage } from "react-use";
import * as yup from "yup";

const timeRangeOptions = [
  {
    label: "Preset",
    options: [
      { value: DateRangeKeys.LAST_MONTH, label: "Last month" },
      { value: DateRangeKeys.LAST_3_MONTHS, label: "Last 3 months" },
      { value: DateRangeKeys.LAST_6_MONTHS, label: "Last 6 months" },
      { value: DateRangeKeys.LAST_YEAR, label: "Last year" },
    ],
  },
  {
    label: "Custom",
    options: [{ value: DateRangeKeys.CUSTOM, label: "Choose date range" }],
  },
];

const filterSalesData = (
  sales: (SalesStaging | SalesGold)[],
  applicationLabels: ChartLabel[],
  applicationLabelKey: "producerId" | "storeId",
  timeRange: string,
  salesLabels: ChartLabel[],
  salesLabelKey?: string,
  customDateRange?: {
    startDate: Date;
    endDate: Date;
  }
) => {
  if (!sales.length)
    return {
      chartData: [],
      pieChartDataByLabelOption: [],
      pieChartDataByInterval: [],
      filteredSales: [],
    };

  const lastDayOfLastMonth = new Date(
    new Date().getFullYear(),
    new Date().getMonth(),
    0
  );

  const labelOptionsArray = [
    {
      labelOptions: applicationLabels,
      labelKey: applicationLabelKey,
    },
    ...(salesLabelKey
      ? [
          {
            labelOptions: salesLabels,
            labelKey: salesLabelKey,
          },
        ]
      : []),
  ];

  const filteredSalesBasedOnSelectedLabelOptions =
    filterSalesBasedOnSelectedLabelOptions(sales, labelOptionsArray);

  switch (timeRange) {
    case DateRangeKeys.CUSTOM:
      if (!customDateRange) {
        return {
          chartData: [],
          pieChartDataByLabelOption: [],
          pieChartDataByInterval: [],
          filteredSales: [],
        };
      }

      const adjustedEndDate = new Date(customDateRange.endDate);
      adjustedEndDate.setHours(23, 59, 59, 999);

      const daysInRange = Math.ceil(
        (adjustedEndDate.getTime() - customDateRange.startDate.getTime()) /
          (1000 * 60 * 60 * 24)
      );

      const sales =
        daysInRange > 90
          ? filterSalesIntoMonthlySeperatedData(
              filteredSalesBasedOnSelectedLabelOptions,
              Math.ceil(daysInRange / 30),
              0
            )
          : filterSalesIntoIntervalSeperatedData(
              filteredSalesBasedOnSelectedLabelOptions,
              customDateRange.startDate,
              adjustedEndDate,
              daysInRange
            );

      return {
        chartData: createChartDataBasedOnLabelsAndSalesArray(
          sales,
          labelOptionsArray
        ),
        pieChartDataByLabelOption: createPieChartDataByLabelOption(
          sales.flatMap(({ data }) => data),
          labelOptionsArray
        ),
        pieChartDataByInterval: createPieChartDataByInterval(sales),
        filteredSales: sales.map((interval) => interval.data).flat(),
      };

    case DateRangeKeys.LAST_MONTH:
      const numOfDaysInLastMonth = lastDayOfLastMonth.getDate();
      const salesIntoLastMonthDayIntervals =
        filterSalesIntoIntervalSeperatedData(
          filteredSalesBasedOnSelectedLabelOptions,
          new Date(new Date().getFullYear(), new Date().getMonth() - 1, 1),
          lastDayOfLastMonth,
          numOfDaysInLastMonth
        );

      return {
        chartData: createChartDataBasedOnLabelsAndSalesArray(
          salesIntoLastMonthDayIntervals,
          labelOptionsArray
        ),
        pieChartDataByLabelOption: createPieChartDataByLabelOption(
          salesIntoLastMonthDayIntervals.flatMap(({ data }) => data),
          labelOptionsArray
        ),
        pieChartDataByInterval: createPieChartDataByInterval(
          salesIntoLastMonthDayIntervals
        ),
        filteredSales: salesIntoLastMonthDayIntervals
          .map((interval) => interval.data)
          .flat(),
      };

    case DateRangeKeys.LAST_3_MONTHS:
      const salesInto3Months = filterSalesIntoIntervalSeperatedData(
        filteredSalesBasedOnSelectedLabelOptions,
        new Date(new Date().getFullYear(), new Date().getMonth() - 3, 1),
        lastDayOfLastMonth,
        12
      );

      return {
        chartData: createChartDataBasedOnLabelsAndSalesArray(
          salesInto3Months,
          labelOptionsArray
        ),
        pieChartDataByLabelOption: createPieChartDataByLabelOption(
          salesInto3Months.flatMap(({ data }) => data),
          labelOptionsArray
        ),
        pieChartDataByInterval: createPieChartDataByInterval(salesInto3Months),
        filteredSales: salesInto3Months.map((interval) => interval.data).flat(),
      };

    case DateRangeKeys.LAST_6_MONTHS:
      const salesIntoMonths = filterSalesIntoMonthlySeperatedData(
        filteredSalesBasedOnSelectedLabelOptions,
        6,
        0
      );

      return {
        chartData: createChartDataBasedOnLabelsAndSalesArray(
          salesIntoMonths,
          labelOptionsArray
        ),
        pieChartDataByLabelOption: createPieChartDataByLabelOption(
          salesIntoMonths.flatMap(({ data }) => data),
          labelOptionsArray
        ),
        pieChartDataByInterval: createPieChartDataByInterval(salesIntoMonths),
        filteredSales: salesIntoMonths.map((month) => month.data).flat(),
      };

    case DateRangeKeys.LAST_YEAR:
      const salesIntoYears = filterSalesIntoMonthlySeperatedData(
        filteredSalesBasedOnSelectedLabelOptions,
        12,
        new Date().getMonth()
      );

      return {
        chartData: createChartDataBasedOnLabelsAndSalesArray(
          salesIntoYears,
          labelOptionsArray
        ),
        pieChartDataByLabelOption: createPieChartDataByLabelOption(
          salesIntoYears.flatMap(({ data }) => data),
          labelOptionsArray
        ),
        pieChartDataByInterval: createPieChartDataByInterval(salesIntoYears),
        filteredSales: salesIntoYears.map((month) => month.data).flat(),
      };
  }

  return {
    chartData: createChartDataBasedOnLabelsAndSalesArray(
      [{ name: "All", data: sales.reverse() }],
      labelOptionsArray
    ),
    pieChartDataByLabelOption: createPieChartDataByLabelOption(
      sales,
      labelOptionsArray
    ),
    pieChartDataByInterval: createPieChartDataByInterval([
      { name: "All", data: sales },
    ]),
    filteredSales: sales.reverse(),
  };
};

const getSalesLabels = (
  sales: (SalesGold | SalesStaging)[],
  salesLabelKey: string
) =>
  Array.from(
    new Set(
      sales.map((sale) =>
        hasKey(sale, salesLabelKey) && sale[salesLabelKey]
          ? String(sale[salesLabelKey])
          : "Unknown"
      )
    )
  )
    .sort((a, b) => a.localeCompare(b))
    .map((label) => ({
      label,
      dataKey: label,
      stroke: safeColor.random(),
    }));

const getApplicationLabels = (agreements: EnrichedApplication[]) =>
  agreements
    .map((agreement) => ({
      label:
        agreement.producer?.displayName || agreement.store?.displayName || "",
      dataKey:
        agreement.producer?.displayName || agreement.store?.displayName || "",
      stroke: safeColor.random(),
    }))
    .filter(({ label, dataKey }) => label !== "" && dataKey !== "")
    .sort((a, b) => a.label.localeCompare(b.label));

const getlabelOptions = (
  salesLabels: ChartLabel[],
  acceptedApplicationsLabels: ChartLabel[]
) => [
  {
    label: "Connected",
    options: acceptedApplicationsLabels.map(({ dataKey, label }) => ({
      value: dataKey,
      label,
    })),
  },
  {
    label: "Unconnected",
    options: salesLabels.map(({ dataKey }) => ({
      value: dataKey,
      label: dataKey,
    })),
  },
];

const filterSelectedLabels = (
  labels: ChartLabel[],
  selectedOptions: SelectOption[]
) =>
  labels.filter(({ dataKey }) =>
    selectedOptions.some((selected) => selected.value === dataKey)
  );

interface SalesFiltersContextType {
  filteredSales: (SalesGold | SalesStaging)[];
  chartData: {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    [key: string]: any;
  }[];
  pieChartDataByLabelOption: {
    name: string;
    value: number;
    color: string;
  }[];
  pieChartDataByInterval: {
    name: string;
    value: number;
    color: string;
  }[];
  timeRangeOptions: { label: string; options: SelectOption[] }[];
  selectedTimeRangeOption?: SelectOption;
  setSelectedTimeRangeOption: (option: SelectOption) => void;
  labelOptions: {
    label: string;
    options: SelectOption[];
  }[];
  selectedLabelOptions: SelectOption[];
  setSelectedLabelOptions: (options: SelectOption[]) => void;
  labels: ChartLabel[];
}

export const SalesFiltersContext = createContext<SalesFiltersContextType>({
  filteredSales: [],
  chartData: [],
  pieChartDataByLabelOption: [],
  pieChartDataByInterval: [],
  timeRangeOptions: [],
  selectedTimeRangeOption: undefined,
  setSelectedTimeRangeOption: () => {},
  labelOptions: [],
  selectedLabelOptions: [],
  setSelectedLabelOptions: () => {},
  labels: [],
});

type EnrichedApplication = Agreement & {
  producer?: { displayName: string; id: string } | null;
  store?: { displayName: string; id: string } | null;
};

interface SalesFilterProviderType {
  children: ReactNode;
  cacheKey: string;
  sales: (SalesGold | SalesStaging)[];
  salesLabelKey?: string;
  acceptedAgreements?: EnrichedApplication[];
  applicationLabelKey: "producerId" | "storeId";
}

export const SalesFilterProvider = ({
  children,
  cacheKey,
  sales,
  salesLabelKey,
  acceptedAgreements,
  applicationLabelKey,
}: SalesFilterProviderType) => {
  const [customDatePickerOpen, setCustomDatePickerOpen] = useState(false);
  const [customDateRange, setCustomDateRange] = useState({
    startDate: new Date(),
    endDate: new Date(),
  });
  const [selectedLabelOptions = [], setSelectedLabelOptions] = useLocalStorage<
    SelectOption[]
  >(`${cacheKey}.LineChartSales.selectedLabelOptions`, []);
  const [
    selectedTimeRangeOption = timeRangeOptions[0].options[0],
    setSelectedTimeRangeOption,
  ] = useLocalStorage<SelectOption>(
    `${cacheKey}.LineChartSales.selectedTimeRangeOption`
  );

  const salesLabels = useMemo(
    () => (salesLabelKey ? getSalesLabels(sales, salesLabelKey) : []),
    [sales, salesLabelKey]
  );
  const applicationsLabels = useMemo(
    () => getApplicationLabels(acceptedAgreements || []),
    [acceptedAgreements]
  );

  const labelOptions = useMemo(
    () => getlabelOptions(salesLabels, applicationsLabels),
    [salesLabels, applicationsLabels]
  );

  const selectedLabels = useMemo(
    () =>
      filterSelectedLabels(
        [...salesLabels, ...applicationsLabels],
        selectedLabelOptions!
      ),
    [salesLabels, applicationsLabels, selectedLabelOptions]
  );

  const handleChangeSelectedTimeRangeOption = (option: SelectOption) => {
    if (option.value === DateRangeKeys.CUSTOM) {
      setCustomDatePickerOpen(true);
    } else {
      setSelectedTimeRangeOption(option);
    }
  };

  const {
    filteredSales,
    chartData,
    pieChartDataByLabelOption,
    pieChartDataByInterval,
  } = useMemo(
    () =>
      filterSalesData(
        sales,
        filterSelectedLabels(applicationsLabels, selectedLabelOptions),
        applicationLabelKey,
        selectedTimeRangeOption.value,
        filterSelectedLabels(salesLabels, selectedLabelOptions),
        salesLabelKey,
        customDateRange
      ),
    [
      sales,
      applicationsLabels,
      selectedLabelOptions,
      applicationLabelKey,
      selectedTimeRangeOption.value,
      salesLabels,
      salesLabelKey,
      customDateRange,
    ]
  );

  return (
    <SalesFiltersContext.Provider
      value={{
        filteredSales,
        chartData,
        pieChartDataByLabelOption,
        pieChartDataByInterval,
        timeRangeOptions,
        selectedTimeRangeOption,
        setSelectedTimeRangeOption: handleChangeSelectedTimeRangeOption,
        labelOptions,
        selectedLabelOptions,
        setSelectedLabelOptions,
        labels: selectedLabels,
      }}
    >
      {children}
      <Dialog
        title="Choose date range"
        open={customDatePickerOpen}
        onOpenChange={setCustomDatePickerOpen}
      >
        <Form
          fields={[
            {
              name: "startDate",
              label: "Start date",
              component: FormFieldComponents.DATE_PICKER,
            },
            {
              name: "endDate",
              label: "End date",
              component: FormFieldComponents.DATE_PICKER,
            },
          ]}
          labelSubmit="Submit"
          onSubmit={(form) => {
            setCustomDateRange(form);
            setCustomDatePickerOpen(false);
            setSelectedTimeRangeOption({
              value: DateRangeKeys.CUSTOM,
              label: `${form.startDate.toLocaleDateString()} - ${form.endDate.toLocaleDateString()}`,
            });
          }}
          schema={yup.object().shape({
            startDate: yup
              .date()
              .required("Start date is required")
              .max(new Date(), "Start date must be in the past"),
            endDate: yup
              .date()
              .required("End date is required")
              .min(yup.ref("startDate"), "End date must be after start date"),
          })}
        />
      </Dialog>
    </SalesFiltersContext.Provider>
  );
};

export const useSalesFiltersContext = () => {
  const context = useContext(SalesFiltersContext);

  if (context === undefined) {
    throw new Error(
      "useSalesFiltersContext must be used within a SalesFilterProvider"
    );
  }

  return context;
};
