"use client";

import { FirebaseCollections } from "@/constants/firebaseCollections";
import { useFirestoreTypedRefs } from "@/hooks/useFirestoreTypedRefs";
import { Producer } from "@/types/collections/producerInterfaces";
import { Store } from "@/types/collections/storeInterfaces";
import { UserRootAccount } from "@/types/collections/userRootAccountInterfaces";
import { chunkArray } from "@/utils/chunkArray";
import { getDocs, query, where } from "firebase/firestore";
import {
  createContext,
  FC,
  ReactNode,
  useContext,
  useEffect,
  useMemo,
  useState,
} from "react";
import { useFirestoreDocData, useUser } from "reactfire";

interface UserContextType extends UserRootAccount {
  status: "loading" | "success" | "error";
  loadingDocs: boolean;
  storeDocs: Store[];
  producerDocs: Producer[];
}

const UserContext = createContext<UserContextType | undefined>(undefined);

export const UserProvider: FC<{ children: ReactNode }> = ({ children }) => {
  const { data: user } = useUser();
  const { getTypedDocumentRef, getTypedCollectionRef } =
    useFirestoreTypedRefs();

  const [storeDocs, setStoreDocs] = useState<Store[]>([]);
  const [producerDocs, setProducerDocs] = useState<Producer[]>([]);
  const [loadingDocs, setLoadingDocs] = useState(true);

  const userAccountDocRef = useMemo(
    () =>
      getTypedDocumentRef<UserRootAccount>(
        "userRootAccounts",
        user?.uid || "TODO Laurie"
      ),
    [getTypedDocumentRef, user?.uid]
  );

  const { status, data: userAccount } = useFirestoreDocData(userAccountDocRef);

  useEffect(() => {
    const calls: Promise<void>[] = [];

    if (!userAccount) {
      return;
    }

    const storeIds = Object.keys(userAccount?.stores);

    if (storeIds.length === 0) {
      setStoreDocs([]);
    } else {
      const fetchStores = async () => {
        const chunked = chunkArray(storeIds, 10);
        const allStoreDocs: Store[] = [];

        for (const chunk of chunked) {
          const storesRef = getTypedCollectionRef<Store>(
            FirebaseCollections.STORES
          );
          const storesQuery = query(storesRef, where("__name__", "in", chunk));
          const snapshot = await getDocs(storesQuery);
          snapshot.forEach((doc) => {
            allStoreDocs.push({ ...doc.data(), id: doc.id });
          });
        }

        setStoreDocs(allStoreDocs);
      };

      calls.push(fetchStores());
    }

    const producerIds = Object.keys(userAccount?.producers);

    if (producerIds.length === 0) {
      setProducerDocs([]);
    } else {
      const fetchProducers = async () => {
        const chunked = chunkArray(producerIds, 10);
        const allProducerDocs: Producer[] = [];

        for (const chunk of chunked) {
          const producersRef = getTypedCollectionRef<Producer>(
            FirebaseCollections.PRODUCERS
          );
          const producersQuery = query(
            producersRef,
            where("__name__", "in", chunk)
          );
          const snapshot = await getDocs(producersQuery);
          snapshot.forEach((doc) => {
            allProducerDocs.push({ ...doc.data(), id: doc.id });
          });
        }

        setProducerDocs(allProducerDocs);
      };

      calls.push(fetchProducers());
    }

    const fetchAll = async () => {
      await Promise.all(calls);
      setLoadingDocs(false);
    };

    fetchAll();
  }, [getTypedCollectionRef, userAccount]);

  return (
    <UserContext.Provider
      value={{
        ...userAccount,
        status,
        loadingDocs,
        storeDocs,
        producerDocs,
      }}
    >
      {children}
    </UserContext.Provider>
  );
};

export const useUserData = () => {
  const context = useContext(UserContext);
  if (context === undefined) {
    throw new Error("useUser must be used within a UserProvider");
  }
  return context;
};
