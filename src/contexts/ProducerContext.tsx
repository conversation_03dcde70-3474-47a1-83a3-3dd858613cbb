"use client";

import { Producer } from "@/types/collections/producerInterfaces";
import { createContext, ReactNode, useContext } from "react";

interface ProducerContextType {
  producer: Producer;
}

export const ProducerContext = createContext<ProducerContextType>({
  producer: {} as Producer,
});

interface ProducerProviderType extends ProducerContextType {
  children: ReactNode;
}

export const ProducerProvider = ({
  children,
  producer,
}: ProducerProviderType) => {
  return (
    <ProducerContext.Provider value={{ producer }}>
      {children}
    </ProducerContext.Provider>
  );
};

export const useProducerContext = () => {
  const context = useContext(ProducerContext);

  if (context === undefined) {
    throw new Error(
      "useProducerContext must be used within a ProducerProvider"
    );
  }

  return context;
};
