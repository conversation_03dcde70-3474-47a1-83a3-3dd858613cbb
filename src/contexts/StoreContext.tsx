"use client";

import { Store } from "@/types/collections/storeInterfaces";
import { createContext, ReactNode, useContext } from "react";

interface StoreContextType {
  store: Store;
}

export const StoreContext = createContext<StoreContextType>({
  store: {} as Store,
});

interface StoreProviderType extends StoreContextType {
  children: ReactNode;
}

export const StoreProvider = ({ children, store }: StoreProviderType) => {
  return (
    <StoreContext.Provider value={{ store }}>{children}</StoreContext.Provider>
  );
};

export const useStoreContext = () => {
  const context = useContext(StoreContext);

  if (context === undefined) {
    throw new Error("useStoreContext must be used within a StoreProvider");
  }

  return context;
};
