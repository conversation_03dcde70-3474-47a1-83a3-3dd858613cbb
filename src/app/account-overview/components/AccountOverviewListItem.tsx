import styled from "styled-components";

export const AccountOverviewListItemLi = styled.li`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  background-color: ${({ theme }) => theme.colors.mutedBg};
  border-radius: ${({ theme }) => theme.sizes.borderRadius};

  p {
    margin: 0;
  }
`;

export const AccountOverviewListItemInnerSide = styled.div`
  display: flex;
  align-items: center;
  gap: 1rem;
`;

export const AccountOverviewListItemEmail = styled.p`
  font-size: ${({ theme }) => theme.sizes.fonts.sm};
  color: ${({ theme }) => theme.colors.textMuted};
`;
