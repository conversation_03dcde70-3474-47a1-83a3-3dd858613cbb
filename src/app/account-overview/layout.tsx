"use client";

import { TabsTriggerLink } from "@/components/ga-ui";
import { PageTitle } from "@/components/ga-ui/PageTitle";
import { ProtectedRoute } from "@/components/ProtectedRoute";
import { useUserData } from "@/contexts/UserContext";
import { LoadingOverlay, Tabs, TabsList } from "@gaco/gaco-library";
import { usePathname } from "next/navigation";
import { PropsWithChildren, useMemo } from "react";
import styled from "styled-components";

enum TabIds {
  PRODUCERS = "producers",
  STORES = "stores",
}

const Content = styled.div`
  flex: 1;
  min-height: 0;
`;

const TabsContainer = styled.div`
  flex: 1;
  overflow-y: hidden;
`;

const AccountOverviewLayout = ({ children }: PropsWithChildren) => {
  const pathname = usePathname();
  const { status } = useUserData();

  const rootUrl = `/account-overview/`;

  const tabFromUrl = useMemo(
    () =>
      Object.values(TabIds).find((tab) => pathname.includes(tab)) ||
      TabIds.PRODUCERS,
    [pathname]
  );

  if (status === "loading") {
    return <LoadingOverlay>Loading accounts</LoadingOverlay>;
  }

  return (
    <ProtectedRoute>
      <PageTitle>Your Accounts</PageTitle>
      <TabsContainer>
        <Tabs defaultValue={tabFromUrl}>
          <TabsList>
            <TabsTriggerLink
              href={`${rootUrl}/artists`}
              value={TabIds.PRODUCERS}
            >
              Artist Accounts
            </TabsTriggerLink>
            <TabsTriggerLink href={`${rootUrl}/stores`} value={TabIds.STORES}>
              Store Accounts
            </TabsTriggerLink>
          </TabsList>

          <Content>{children}</Content>
        </Tabs>
      </TabsContainer>
    </ProtectedRoute>
  );
};

export default AccountOverviewLayout;
