"use client";

import { ButtonLink } from "@/components/ga-ui";
import { acceptedCountries } from "@/constants/acceptedCountryCodes";
import { useFirebaseFunctions } from "@/hooks/useFirebaseFunctions";
import { callCreateProducerApi } from "@/lib/api";
import {
  Alert,
  Card,
  CardSections,
  Form,
  FormFieldComponents,
} from "@gaco/gaco-library";
import { ArrowLeft } from "lucide-react";
import { useRouter } from "next/navigation";
import { useMemo, useState } from "react";
import countryList from "react-select-country-list";
import styled from "styled-components";
import * as yup from "yup";

const BackLink = styled(ButtonLink)`
  padding: 0;

  svg {
    height: 1rem;
  }
`;

const Page = () => {
  const functions = useFirebaseFunctions();
  const router = useRouter();

  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState<boolean>(false);

  const countryOptions = useMemo(
    () =>
      countryList()
        .getData()
        .filter(({ value }) => acceptedCountries.includes(value)),
    []
  );

  const handleSubmit = async (payload: {
    displayName: string;
    email: string;
    taxA2: string;
  }) => {
    setLoading(true);

    try {
      await callCreateProducerApi(payload, functions, {
        hideToast: true,
      });

      router.push("/account-overview/artists");
    } catch (error) {
      setError("Error adding artist");
      console.error("Error adding artist:", error);
    }

    setLoading(false);
  };

  return (
    <>
      <BackLink href="/account-overview/artists" variant="ghost">
        <ArrowLeft />
        Back
      </BackLink>
      <Card>
        <CardSections.Title>Add new artist</CardSections.Title>

        <CardSections.Content>
          {error && <Alert type="error">{error}</Alert>}
          <p>Enter the details for your new artist here.</p>

          <Form
            onSubmit={handleSubmit}
            labelSubmit="Add artist"
            isLoading={loading}
            fields={[
              {
                name: "displayName",
                label: "Name",
                component: FormFieldComponents.TEXT,
              },
              {
                name: "email",
                type: "email",
                label: "Email",
                component: FormFieldComponents.TEXT,
              },
              {
                name: "taxA2",
                label: "Country",
                component: FormFieldComponents.SELECT,
                options: countryOptions,
              },
            ]}
            schema={yup.object().shape({
              displayName: yup.string().required(),
              email: yup.string().email().required(),
              taxA2: yup.string().required(),
            })}
          />
        </CardSections.Content>
      </Card>
    </>
  );
};

export default Page;
