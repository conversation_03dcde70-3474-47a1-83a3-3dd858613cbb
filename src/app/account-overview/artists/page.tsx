"use client";

import { ButtonLink } from "@/components/ga-ui/ButtonLink";
import { DialogDeleteProducer } from "@/components/organisms/DialogDeleteProducer";
import { useUserData } from "@/contexts/UserContext";
import { Producer } from "@/types/collections/producerInterfaces";
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
  Button,
  Card,
  CardSections,
  DropdownMenu,
} from "@gaco/gaco-library";
import {
  EllipsisVertical,
  Palette as PaletteRoot,
  PlusCircle,
} from "lucide-react";
import { useRouter } from "next/navigation";
import { useState } from "react";
import styled from "styled-components";
import {
  AccountOverviewListItemEmail,
  AccountOverviewListItemInnerSide,
  AccountOverviewListItemLi,
} from "../components";

const List = styled.ul`
  list-style: none;
  padding: 0;
  margin: 0 0 1rem 0;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
`;

const Palette = styled(PaletteRoot)`
  height: 1rem;
  width: 1rem;
`;

export default function Page() {
  const { producerDocs, status } = useUserData();

  return (
    <Card
      loading={status === "loading" ? "Loading user's artist accounts" : false}
    >
      <CardSections.Title>Artist Accounts</CardSections.Title>

      <CardSections.Content>
        <List>
          {producerDocs?.map((producer) => (
            <ProducerListItem key={producer.id} {...producer} />
          ))}
        </List>

        <ButtonLink href="/account-overview/artists/add">
          <PlusCircle /> Add artist
        </ButtonLink>
      </CardSections.Content>
    </Card>
  );
}

const ProducerListItem = (producer: Producer) => {
  const router = useRouter();
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);

  return (
    <>
      <AccountOverviewListItemLi>
        <AccountOverviewListItemInnerSide>
          <Avatar>
            <AvatarImage src={"TODO Laurie"} alt={producer.displayName} />
            <AvatarFallback>
              <Palette />
            </AvatarFallback>
          </Avatar>
          <div>
            <p>{producer.displayName}</p>
            <AccountOverviewListItemEmail>
              {producer.email}
            </AccountOverviewListItemEmail>
          </div>
        </AccountOverviewListItemInnerSide>
        <AccountOverviewListItemInnerSide>
          <ButtonLink variant="outline" href={`/artists/${producer.id}`}>
            Dashboard
          </ButtonLink>
          <DropdownMenu
            trigger={<Button variant="ghost" icon={<EllipsisVertical />} />}
            dialogOpen={showDeleteDialog}
            items={[
              {
                content: "Delete",
                onSelect: () => setShowDeleteDialog(true),
              },
              {
                content: "Edit",
                onSelect: () => router.push(`/artists/${producer.id}/settings`),
              },
            ]}
          />
        </AccountOverviewListItemInnerSide>
      </AccountOverviewListItemLi>

      {showDeleteDialog && (
        <DialogDeleteProducer
          producer={producer}
          onOpenChange={setShowDeleteDialog}
        />
      )}
    </>
  );
};
