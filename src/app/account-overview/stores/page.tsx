"use client";

import { ButtonLink } from "@/components/ga-ui/ButtonLink";
import { DialogDeleteStore } from "@/components/organisms/DialogDeleteStore";
import { useUserData } from "@/contexts/UserContext";
import { Store } from "@/types/collections/storeInterfaces";
import {
  Avatar,
  AvatarFallback,
  Button,
  Card,
  CardSections,
  DropdownMenu,
} from "@gaco/gaco-library";
import {
  EllipsisVertical,
  PlusCircle,
  Store as StoreIconRoot,
} from "lucide-react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useState } from "react";
import styled from "styled-components";
import {
  AccountOverviewListItemEmail,
  AccountOverviewListItemInnerSide,
  AccountOverviewListItemLi,
} from "../components";

const List = styled.ul`
  list-style: none;
  padding: 0;
  margin: 0 0 1rem 0;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
`;

const StoreIcon = styled(StoreIconRoot)`
  height: 1rem;
  width: 1rem;
`;

export default function Page() {
  const { storeDocs, status } = useUserData();

  return (
    <Card
      loading={status === "loading" ? "Loading user's store accounts" : false}
    >
      <CardSections.Title>Store Accounts</CardSections.Title>

      <CardSections.Content>
        <List>
          {storeDocs?.map((store) => (
            <StoreListItem key={store.id} {...store} />
          ))}
        </List>

        <ButtonLink href="/account-overview/stores/add">
          <PlusCircle /> Add store
        </ButtonLink>
      </CardSections.Content>
    </Card>
  );
}

const StoreListItem = (store: Store) => {
  const router = useRouter();
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);

  return (
    <>
      <AccountOverviewListItemLi>
        <AccountOverviewListItemInnerSide>
          <Avatar>
            {store.profilePictureUrl && (
              <Image src={store.profilePictureUrl} alt="Profile" fill />
            )}
            <AvatarFallback>
              <StoreIcon />
            </AvatarFallback>
          </Avatar>
          <div>
            <p>{store.displayName}</p>
            <AccountOverviewListItemEmail>
              {store.email}
            </AccountOverviewListItemEmail>
          </div>
        </AccountOverviewListItemInnerSide>
        <AccountOverviewListItemInnerSide>
          <ButtonLink variant="outline" href={`/stores/${store.id}`}>
            Dashboard
          </ButtonLink>
          <DropdownMenu
            trigger={<Button variant="ghost" icon={<EllipsisVertical />} />}
            dialogOpen={showDeleteDialog}
            items={[
              {
                content: "Delete",
                onSelect: () => setShowDeleteDialog(true),
              },
              {
                content: "Edit",
                onSelect: () => router.push(`/stores/${store.id}/settings`),
              },
            ]}
          />
        </AccountOverviewListItemInnerSide>
      </AccountOverviewListItemLi>

      {showDeleteDialog && (
        <DialogDeleteStore
          store={store}
          onClose={() => setShowDeleteDialog(false)}
        />
      )}
    </>
  );
};
