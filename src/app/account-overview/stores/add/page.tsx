"use client";

import { ButtonLink } from "@/components/ga-ui";
import { acceptedCountries } from "@/constants/acceptedCountryCodes";
import { useFirebaseFunctions } from "@/hooks/useFirebaseFunctions";
import { callCreateStoreApi } from "@/lib/api";
import {
  Alert,
  Card,
  CardSections,
  Form,
  FormFieldComponents,
} from "@gaco/gaco-library";
import { ArrowLeft } from "lucide-react";
import { useRouter } from "next/navigation";
import { useMemo, useState } from "react";
import countryList from "react-select-country-list";
import styled from "styled-components";
import * as yup from "yup";

const BackLink = styled(ButtonLink)`
  padding: 0;

  svg {
    height: 1rem;
  }
`;

const Page = () => {
  const functions = useFirebaseFunctions();
  const router = useRouter();

  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState<boolean>(false);

  const countryOptions = useMemo(
    () =>
      countryList()
        .getData()
        .filter(({ value }) => acceptedCountries.includes(value)),
    []
  );

  const handleSubmit = async ({
    shopifyApiKey,
    shopName,
    ...payload
  }: {
    shopifyApiKey: string;
    shopName: string;
    displayName: string;
    email: string;
    taxA2: string;
  }) => {
    setLoading(true);

    try {
      callCreateStoreApi(
        {
          ...payload,
          shopifyApiKeyObject: {
            shopName,
            shopifyApiKey,
          },
        },
        functions,
        {
          hideToast: true,
        }
      );

      router.push("/account-overview/stores");
    } catch (error) {
      setError("Error adding store");
      console.error("Error adding store:", error);
    }

    setLoading(false);
  };

  return (
    <>
      <BackLink href="/account-overview/stores" variant="ghost">
        <ArrowLeft />
        Back
      </BackLink>

      <Card loading={loading}>
        <CardSections.Title>Add new store</CardSections.Title>

        <CardSections.Content>
          {error && <Alert type="error">{error}</Alert>}
          <p>Enter the details for your new store here.</p>

          <Form
            onSubmit={handleSubmit}
            labelSubmit="Add store"
            fields={[
              {
                name: "displayName",
                label: "Name",
                component: FormFieldComponents.TEXT,
              },
              {
                name: "email",
                label: "Email",
                component: FormFieldComponents.TEXT,
                type: "email",
              },
              {
                name: "taxA2",
                label: "Country",
                component: FormFieldComponents.SELECT,
                options: countryOptions,
              },
              {
                name: "shopName",
                label: "Shopify Store Name",
                component: FormFieldComponents.TEXT,
              },
              {
                name: "shopifyApiKey",
                label: "Shopify Secret API Key",
                component: FormFieldComponents.TEXT,
                type: "password",
              },
            ]}
            schema={yup.object().shape({
              displayName: yup.string().required(),
              email: yup.string().email().required(),
              taxA2: yup.string().required(),
              shopName: yup.string().required(),
              shopifyApiKey: yup.string().required(),
            })}
          />
        </CardSections.Content>
      </Card>
    </>
  );
};

export default Page;
