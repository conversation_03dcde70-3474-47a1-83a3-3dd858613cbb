"use client";

import { ButtonLink, PageTitle } from "@/components/ga-ui";
import { useUserData } from "@/contexts/UserContext";
import { useStores } from "@/hooks/useStores";
import {
  Button,
  ButtonGroup as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  Card,
  CardList,
  CardSections,
  H2,
  Spin<PERSON>,
} from "@gaco/gaco-library";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useSigninCheck } from "reactfire";
import styled from "styled-components";

const IntroductionContainer = styled.section`
  margin: 0 auto 4rem;
  text-align: center;
  max-width: 50rem;

  p {
    margin-bottom: 2rem;
  }
`;

const ButtonGroup = styled(ButtonGroupRoot)`
  justify-content: center;
`;

const StoreCardRightColumn = styled.div`
  display: flex;
  height: 100%;
  flex-direction: column-reverse;
`;

const GridSection = styled.section`
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;

  margin-bottom: 2rem;
`;

const SpinnerContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
`;

export default function App() {
  const router = useRouter();
  const { stores, status } = useStores();
  const { storeDocs, producerDocs } = useUserData();
  const { status: signInCheckStatus, data: signInCheckResult } =
    useSigninCheck();

  return (
    <>
      <IntroductionContainer>
        <PageTitle>GACO</PageTitle>

        <p>
          GACO is a consignment management platform built by creatives, for
          creatives.
          <br /> Our mission is to help creatives find each other, keep track of
          sales, boost profitability and maintain fruitful connections.
        </p>

        {signInCheckStatus !== "loading" ? (
          signInCheckResult.signedIn ? (
            <ButtonLink
              href={`/account-overview${stores?.length ? "/stores" : "/artists"}`}
            >
              Account overview
            </ButtonLink>
          ) : (
            <ButtonGroup>
              <ButtonLink href="/login">Login</ButtonLink>
              <ButtonLink href="/signup">Register</ButtonLink>
            </ButtonGroup>
          )
        ) : (
          <Spinner />
        )}
      </IntroductionContainer>

      {!!storeDocs.length && (
        <>
          <H2>Your stores</H2>
          <GridSection>
            {storeDocs.map((store) => (
              <Card key={store.id}>
                <CardSections.Title>{store.displayName}</CardSections.Title>
                <CardSections.Actions>
                  <Button
                    variant="outline"
                    onClick={() => router.push(`/stores/${store.id}`)}
                  >
                    Dashboard
                  </Button>
                </CardSections.Actions>
              </Card>
            ))}
          </GridSection>
        </>
      )}

      {!!producerDocs.length && (
        <>
          <H2>Your artists</H2>
          <GridSection>
            {producerDocs.map((producer) => (
              <Card key={producer.id}>
                <CardSections.Title>{producer.displayName}</CardSections.Title>
                <CardSections.Actions>
                  <Button
                    variant="outline"
                    onClick={() => router.push(`/artists/${producer.id}`)}
                  >
                    Dashboard
                  </Button>
                </CardSections.Actions>
              </Card>
            ))}
          </GridSection>
        </>
      )}

      <section>
        <H2>Discover stores</H2>

        {status === "success" ? (
          <CardList
            cards={stores.map((result) => ({
              id: result.id,
              title: result.displayName,
              content: (
                <Link href={`MAILTO:${result?.email}`}>{result?.email}</Link>
              ),
              rightColumn: (
                <StoreCardRightColumn>
                  <Button
                    variant="outline"
                    onClick={() => router.push(`/stores/${result.id}`)}
                  >
                    View Store
                  </Button>
                </StoreCardRightColumn>
              ),
            }))}
          />
        ) : (
          <SpinnerContainer>
            <Spinner />
          </SpinnerContainer>
        )}
      </section>
    </>
  );
}
