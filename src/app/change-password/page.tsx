"use client";

import { PageTitle } from "@/components/ga-ui";
import { ProtectedRoute } from "@/components/ProtectedRoute";
import {
  Alert,
  Card as CardRoot,
  CardSections,
  Form,
  FormFieldComponents,
} from "@gaco/gaco-library";
import {
  EmailAuthProvider,
  reauthenticateWithCredential,
  updatePassword,
} from "firebase/auth";
import { useState } from "react";
import { useUser } from "reactfire";
import styled from "styled-components";
import * as yup from "yup";

const Card = styled(CardRoot)`
  min-height: 200px;
`;

export default function Page() {
  const { data: user } = useUser();
  const [error, setError] = useState("");
  const [success, setSuccess] = useState("");
  const [loading, setLoading] = useState(false);

  const checkCurrentPassword = async (currentPassword: string) => {
    if (!user?.email || !currentPassword) {
      return false;
    }

    try {
      const userCredential = EmailAuthProvider.credential(
        user.email,
        currentPassword
      );

      await reauthenticateWithCredential(user, userCredential);

      return true;
    } catch (err) {
      setError("Current password is incorrect.");
      console.error(err);
      return false;
    }
  };

  const onSubmit = async ({
    newPassword,
    currentPassword,
  }: {
    newPassword: string;
    currentPassword: string;
  }) => {
    setError("");

    const isCurrentPasswordValid = await checkCurrentPassword(currentPassword);
    if (!isCurrentPasswordValid) {
      setLoading(false);
      return;
    }

    if (!newPassword || !user) {
      return;
    }

    setLoading(true);

    try {
      await updatePassword(user, newPassword);
      setSuccess("Password updated successfully.");
    } catch (err) {
      setError("Failed to update password.");
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <ProtectedRoute>
      <PageTitle>Account Settings</PageTitle>
      <Card loading={loading}>
        <CardSections.Title>Change password</CardSections.Title>

        <CardSections.Content>
          {error && <Alert type="error">{error}</Alert>}
          {success && <Alert type="success">{success}</Alert>}

          <Form
            onSubmit={onSubmit}
            fields={[
              {
                name: "currentPassword",
                label: "Current password",
                component: FormFieldComponents.TEXT,
                type: "password",
              },
              {
                name: "newPassword",
                label: "New password",
                component: FormFieldComponents.TEXT,
                type: "password",
              },
            ]}
            schema={yup.object().shape({
              currentPassword: yup.string().required(),
              newPassword: yup.string().required(),
            })}
          />
        </CardSections.Content>
      </Card>
    </ProtectedRoute>
  );
}
