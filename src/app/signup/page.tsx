"use client";

import { PageTitle } from "@/components/ga-ui";
import { RedirectIfAuthenticatedRoute } from "@/components/RedirectIfAuthenticatedRoute";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Card as CardRoot,
  CardSections,
  Form,
  FormFieldComponents,
} from "@gaco/gaco-library";
import {
  Auth,
  createUserWithEmailAndPassword,
  getAuth,
  GoogleAuthProvider,
  sendEmailVerification,
  signInWithPopup,
} from "firebase/auth";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useState } from "react";
import styled from "styled-components";
import * as yup from "yup";

const CenteredContainer = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
`;

const Card = styled(CardRoot)`
  width: 100%;
  max-width: 400px;

  button {
    align-self: stretch;
  }
`;

const CardTitle = styled(CardSections.Title)`
  text-align: center;
`;

const CardContent = styled(CardSections.Content)`
  display: flex;
  flex-direction: column;
  gap: 1rem;
`;

const Footer = styled.div`
  text-align: center;

  font-size: ${({ theme }) => theme.sizes.fonts.sm};
  color: ${({ theme }) => theme.colors.textMuted};
`;

const signUpWithGoogle = async (auth: Auth) => {
  const provider = new GoogleAuthProvider();

  await signInWithPopup(auth, provider);
};

export default function PortalSignUp() {
  const auth = getAuth();
  const router = useRouter();

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");

  const handleSignup = async ({
    password,
    confirmPassword,
    email,
  }: {
    email: string;
    password: string;
    confirmPassword: string;
  }) => {
    setLoading(true);

    if (password !== confirmPassword) {
      setError("Passwords don't match");
      return;
    }

    try {
      await createUserWithEmailAndPassword(auth, email, password).then(
        (userCredential) => {
          const user = userCredential.user;

          sendEmailVerification(user);
          router.push("/account-overview/artists");
        }
      );
    } catch (error) {
      console.error("Error creating producer account:", error);
      setError("Failed to create account. Please try again.");
    }

    setLoading(false);
  };

  return (
    <RedirectIfAuthenticatedRoute>
      <CenteredContainer>
        <Link href="/">
          <PageTitle>GACO</PageTitle>
        </Link>

        <Card loading={loading ? "Signing up" : false}>
          <CardTitle>Create your account</CardTitle>
          <CardContent>
            {error && <Alert type="error">{error}</Alert>}

            <Form
              fields={[
                {
                  name: "email",
                  type: "email",
                  label: "Email",
                  component: FormFieldComponents.TEXT,
                },
                {
                  name: "password",
                  type: "password",
                  label: "Password",
                  component: FormFieldComponents.TEXT,
                },
                {
                  name: "confirmPassword",
                  type: "password",
                  label: "Confirm Password",
                  component: FormFieldComponents.TEXT,
                },
              ]}
              schema={yup.object().shape({
                email: yup.string().email().required(),
                password: yup.string().required().min(6),
                confirmPassword: yup
                  .string()
                  .required()
                  .min(6)
                  .oneOf([yup.ref("password")], "Passwords must match"),
              })}
              onSubmit={handleSignup}
              labelSubmit="Sign Up"
            />

            <Button variant="outline" onClick={() => signUpWithGoogle(auth)}>
              Sign up with Google
            </Button>

            <Footer>
              Already have an account? <Link href="/login">Log in</Link>
            </Footer>
          </CardContent>
        </Card>
      </CenteredContainer>
    </RedirectIfAuthenticatedRoute>
  );
}
