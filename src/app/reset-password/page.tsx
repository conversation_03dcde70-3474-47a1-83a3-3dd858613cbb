"use client";

import { PageTitle } from "@/components/ga-ui";
import {
  Alert,
  Card as CardRoot,
  CardSections,
  Form,
  FormFieldComponents,
} from "@gaco/gaco-library";
import { sendPasswordResetEmail } from "firebase/auth";
import { useState } from "react";
import { useAuth } from "reactfire";
import styled from "styled-components";
import * as yup from "yup";

const CenteredContainer = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
`;

const Card = styled(CardRoot)`
  width: 100%;
  max-width: 400px;
`;

export default function Page() {
  const auth = useAuth();

  const [error, setError] = useState("");
  const [success, setSuccess] = useState("");
  const [loading, setLoading] = useState(false);

  const onSubmit = async ({ email }: { email: string }) => {
    setError("");
    setSuccess("");

    if (!email) {
      return;
    }

    setLoading(true);

    try {
      await sendPasswordResetEmail(auth, email);
      setSuccess(
        "An email has been sent to the provided email address with instructions on how to reset your password."
      );
    } catch (err) {
      setError("Failed to send reset password email.");
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <CenteredContainer>
      <PageTitle>Forgot your password?</PageTitle>
      <Card loading={loading}>
        <CardSections.Content>
          {error && <Alert type="error">{error}</Alert>}
          {success && <Alert type="success">{success}</Alert>}

          <Form
            onSubmit={onSubmit}
            labelSubmit="Send reset email"
            fields={[
              {
                name: "email",
                label: "Email",
                component: FormFieldComponents.TEXT,
                type: "email",
              },
            ]}
            schema={yup.object().shape({
              email: yup.string().email().required(),
            })}
          />
        </CardSections.Content>
      </Card>
    </CenteredContainer>
  );
}
