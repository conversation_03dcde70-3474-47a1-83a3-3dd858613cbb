"use client";

import { BarChartResponsive } from "@/components/molecules/BarCharts";
import { ControlsSales } from "@/components/molecules/ControlsSales/ControlsSales";
import { LineChartResponsive } from "@/components/molecules/LineCharts";
import { TooltipContentCurrency } from "@/components/molecules/TooltipContentCurrency/TooltipContentCurrency";
import { CardSalesListProducer } from "@/components/organisms/CardSalesListProducer";
import { useProducerContext } from "@/contexts/ProducerContext";
import {
  SalesFilterProvider,
  useSalesFiltersContext,
} from "@/contexts/SalesFiltersContext";
import { useAgreements } from "@/hooks/useAgreements";
import { useSales } from "@/hooks/useSales";
import { SalesGold } from "@/types/collections/salesInterfaces";
import { numberToCurrency } from "@/utils/currencyUtils";
import { Card, CardSections, media } from "@gaco/gaco-library";
import { Bar<PERSON>hart as BarChartIcon, Package } from "lucide-react";
import { useMemo } from "react";
import styled from "styled-components";

const Container = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;

  overflow-y: auto;
  height: 100%;
  padding-bottom: 1rem;
`;

const WidgetCards = styled.div`
  display: flex;
  gap: 1rem;

  flex-shrink: 0;
  flex-direction: row;
`;

const CardSalesWidgetsWithFilteredSales = () => {
  const { filteredSales } = useSalesFiltersContext();

  return (
    <WidgetCards>
      <Card>
        <CardSections.Title>
          Total Revenue
          <BarChartIcon />
        </CardSections.Title>
        <CardSections.Content>
          {numberToCurrency(
            filteredSales
              .map((sale) => sale.totalPrice)
              .reduce((a, b) => a + b, 0),
            "EUR"
          )}
        </CardSections.Content>
      </Card>

      <Card>
        <CardSections.Title>
          Total items sold
          <Package />
        </CardSections.Title>
        <CardSections.Content>{filteredSales.length}</CardSections.Content>
      </Card>
    </WidgetCards>
  );
};

const ChartCards = styled.div`
  display: flex;
  gap: 1rem;

  flex-shrink: 0;
  flex-direction: column;

  ${media.lg`
    flex-direction: row;
    `};
`;

interface CardSalesChartsProps {
  status: "loading" | "error" | "success";
}

const CardSalesCharts = ({ status }: CardSalesChartsProps) => {
  const { chartData, labels } = useSalesFiltersContext();

  return (
    <ChartCards>
      <Card
        error={status === "error" && "Error loading sales"}
        loading={status === "loading" && "Loading sales"}
        info={
          status === "success" && !chartData.length && "No sales data available"
        }
      >
        <LineChartResponsive
          data={chartData}
          labels={labels}
          tooltipContent={TooltipContentCurrency}
        />
      </Card>

      <Card
        error={status === "error" && "Error loading sales"}
        loading={status === "loading" && "Loading sales"}
        info={
          status === "success" && !chartData.length && "No sales data available"
        }
      >
        <BarChartResponsive
          data={chartData}
          labels={labels.map(({ dataKey, label, stroke }) => ({
            dataKey,
            label,
            color: stroke,
          }))}
          tooltipContent={TooltipContentCurrency}
          height={300}
        />
      </Card>
    </ChartCards>
  );
};

const CardSalesListWithFilteredSales = () => {
  const { filteredSales } = useSalesFiltersContext();

  const filteredGoldSales = filteredSales.filter(
    (sale): sale is SalesGold =>
      "producerDisplayName" in sale && !!sale.producerDisplayName
  );

  return <CardSalesListProducer sales={filteredGoldSales} title="Sales" />;
};

const Page = () => {
  const { producer } = useProducerContext();
  const { salesGoldStatus, salesGold } = useSales({
    producerId: producer.id,
  });
  const {
    acceptedAgreements,
    status: applicationStatus,
    enrichAgreementWithStore,
  } = useAgreements({
    accountRole: "producer",
    producerId: producer.id,
  });

  const enrichedAcceptedAgreements = useMemo(
    () =>
      acceptedAgreements.map((agreement) =>
        enrichAgreementWithStore(agreement)
      ),
    [acceptedAgreements, enrichAgreementWithStore]
  );

  const status =
    salesGoldStatus === "loading" || applicationStatus === "loading"
      ? "loading"
      : salesGoldStatus === "error" || applicationStatus === "error"
        ? "error"
        : "success";

  return (
    <Container>
      <SalesFilterProvider
        cacheKey={`${producer.id}-sales`}
        sales={salesGold}
        acceptedAgreements={enrichedAcceptedAgreements}
        applicationLabelKey="storeId"
      >
        <ControlsSales multiSelectLabel="Select stores" />

        <CardSalesWidgetsWithFilteredSales />

        <CardSalesCharts status={status} />

        <CardSalesListWithFilteredSales />
      </SalesFilterProvider>
    </Container>
  );
};

export default Page;
