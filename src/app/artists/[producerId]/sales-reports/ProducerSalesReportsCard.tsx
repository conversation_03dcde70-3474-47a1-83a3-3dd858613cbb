import { ButtonLink } from "@/components/ga-ui";
import { SalesReportModel } from "@/types/collections/salesReportInterfaces";
import { Store } from "@/types/collections/storeInterfaces";
import { numberToCurrency } from "@/utils/currencyUtils";
import { getFirebaseDateTime } from "@/utils/timeUtils";
import {
  ButtonGroup as ButtonGroupRoot,
  Card,
  CardSections,
  CardSize,
  DetailsList,
} from "@gaco/gaco-library";
import Link from "next/link";
import styled from "styled-components";

const ButtonGroup = styled(ButtonGroupRoot)`
  margin-top: 1rem;
  justify-content: flex-end;
`;

const RightColumnInner = styled.div`
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  justify-content: space-between;
  text-align: right;
`;

interface ProducerSalesReportCardProps extends SalesReportModel {
  store?: Store;
}

export const ProducerSalesReportCard = ({
  id,
  store,
  salesReportId,
  createdAt,
  producerLevel,
  uri,
  saleIds,
}: ProducerSalesReportCardProps) => {
  return (
    <Card size={CardSize.sm}>
      <CardSections.Title>{salesReportId}</CardSections.Title>

      <DetailsList>
        <dd>Store</dd>
        <dt>
          <Link href={`/store/${store?.id}`}>{store?.displayName}</Link>
        </dt>
        <dd>Gross Income</dd>
        <dt>
          {numberToCurrency(producerLevel.producerTotalGrossPayout, "EUR")}
        </dt>
        <dd>Items sold</dd>
        <dt>{saleIds.length}</dt>
      </DetailsList>

      <CardSections.RightColumn>
        <RightColumnInner>
          <div>{getFirebaseDateTime(createdAt)}</div>

          <ButtonGroup>
            {uri && (
              <ButtonLink
                variant="outline"
                href={uri}
                rel="noopener noreferrer"
                target="_blank"
                size="sm"
              >
                Download
              </ButtonLink>
            )}

            <ButtonLink href={`sales-reports/${id}`} size="sm">
              View
            </ButtonLink>
          </ButtonGroup>
        </RightColumnInner>
      </CardSections.RightColumn>
    </Card>
  );
};
