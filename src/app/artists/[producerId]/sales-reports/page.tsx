"use client";

import { useProducerContext } from "@/contexts/ProducerContext";
import { useSalesReports } from "@/hooks/useSalesReports";
import { <PERSON><PERSON>, CardList, H2, TextInput } from "@gaco/gaco-library";
import { Search } from "lucide-react";
import { useMemo, useState } from "react";
import styled from "styled-components";
import { ProducerSalesReportCard } from "./ProducerSalesReportsCard";

const Container = styled.div`
  height: 100%;
  padding-top: 0.5rem;

  overflow-y: auto;
  padding-bottom: 1rem;

  ${H2} {
    margin-bottom: 0;
  }
`;

const SearchInput = styled(TextInput)`
  padding-top: 0;
`;

const SalesReportsListHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
`;

const Page = () => {
  const { producer } = useProducerContext();
  const { enrichSalesReportWithStore, salesReports } = useSalesReports({
    producerId: producer.id,
  });
  const [searchTerm, setSearchTerm] = useState("");

  const enrichedSalesReports = useMemo(
    () =>
      salesReports?.map((salesReport) =>
        enrichSalesReportWithStore(salesReport)
      ),
    [salesReports, enrichSalesReportWithStore]
  );

  const filteredSalesReports = enrichedSalesReports?.filter((report) =>
    [
      report.store?.displayName.toLowerCase(),
      report.salesReportId.toLowerCase(),
    ].find((term) => {
      if (!term) return false;
      return term.includes(searchTerm.toLowerCase());
    })
  );

  return (
    <Container>
      <SalesReportsListHeader>
        <H2>Sales Reports</H2>
        <SearchInput
          id="searchSalesReports"
          type="text"
          label="Search sale reports..."
          icon={<Search />}
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
        />
      </SalesReportsListHeader>
      {filteredSalesReports?.length ? (
        <CardList>
          {filteredSalesReports?.map((enrichedSalesReport) => (
            <ProducerSalesReportCard
              key={enrichedSalesReport.id}
              {...enrichedSalesReport}
            />
          ))}
        </CardList>
      ) : (
        <Alert type="info">No sales reports found</Alert>
      )}
    </Container>
  );
};

export default Page;
