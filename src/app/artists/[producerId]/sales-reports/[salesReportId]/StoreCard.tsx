import { useStore } from "@/hooks/useStore";
import { CardSections, DetailsList, LoadingOverlay } from "@gaco/gaco-library";
import { Store } from "lucide-react";
import Link from "next/link";

export const StoreCard = ({ storeId }: { storeId: string }) => {
  const { store, status } = useStore({
    storeId,
  });

  return (
    <>
      {status === "loading" ? (
        <LoadingOverlay>Loading store</LoadingOverlay>
      ) : (
        <>
          <CardSections.Title>
            Store
            <Store />
          </CardSections.Title>
          <DetailsList>
            <dd>Name</dd>
            <dt>
              <Link href={`/store/${store?.id}`}>{store?.displayName}</Link>
            </dt>
            <dd>Email</dd>
            <dt>
              <a href={`mailto:${store?.email}`}>{store?.email}</a>
            </dt>
          </DetailsList>
        </>
      )}
    </>
  );
};
