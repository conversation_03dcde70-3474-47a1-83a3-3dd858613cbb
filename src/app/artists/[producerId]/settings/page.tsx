"use client";

import { ButtonFileUpload } from "@/components/ga-ui/ButtonFileUpload";
import { FirebaseCollections } from "@/constants/firebaseCollections";
import { useProducerContext } from "@/contexts/ProducerContext";
import { Al<PERSON>, Card, CardSections, H4 } from "@gaco/gaco-library";
import { doc, setDoc } from "firebase/firestore";
import { getDownloadURL, ref, uploadBytes } from "firebase/storage";
import Image from "next/image";
import { useState } from "react";
import { useFirestore, useStorage } from "reactfire";
import styled from "styled-components";

const ProfilePictureMask = styled.div`
  width: 10rem;
  height: 10rem;
  border-radius: 50%;
  overflow: hidden;
  margin-bottom: 1rem;
  position: relative;
  background-color: ${({ theme }) => theme.colors.background};
`;

const Page = () => {
  const { producer } = useProducerContext();
  const storage = useStorage();
  const firestore = useFirestore();
  const producerDocRef = doc(
    firestore,
    FirebaseCollections.PRODUCERS,
    producer.id
  );

  const [error, setError] = useState("");
  const [success, setSuccess] = useState("");

  const handleProfilePictureUpload = async (files: FileList | null) => {
    setSuccess("");
    setError("");

    if (files && files.length > 0) {
      const file = files[0];

      try {
        const storageRef = ref(
          storage,
          `producers/${producer.id}/media/${file.name}`
        );

        const snapshot = await uploadBytes(storageRef, file);
        const downloadURL = await getDownloadURL(snapshot.ref);

        await setDoc(
          producerDocRef,
          { profilePictureUrl: downloadURL },
          { merge: true }
        );
        setSuccess("Profile picture uploaded successfully");
      } catch (error) {
        setError("Error uploading profile picture");
        console.error("Error uploading profile picture:", error);
      }
    }
  };

  return (
    <Card>
      <CardSections.Title>Settings</CardSections.Title>
      {success && <Alert type="success">{success}</Alert>}
      {error && <Alert type="error">{error}</Alert>}
      <CardSections.Content>
        <H4>Profile picture</H4>
        {producer.profilePictureUrl && (
          <ProfilePictureMask>
            <Image
              src={producer.profilePictureUrl}
              alt="Profile"
              layout="fill"
              objectFit="cover"
            />
          </ProfilePictureMask>
        )}
        <ButtonFileUpload onUpload={handleProfilePictureUpload}>
          Upload new profile picture
        </ButtonFileUpload>
      </CardSections.Content>
    </Card>
  );
};

export default Page;
