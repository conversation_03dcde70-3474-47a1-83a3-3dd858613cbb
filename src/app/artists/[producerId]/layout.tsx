"use client";

import { PageTitle } from "@/components/ga-ui/PageTitle";
import { TabsTriggerLink } from "@/components/ga-ui/TabsTriggerLink";
import { ProducerProvider } from "@/contexts/ProducerContext";
import { useUserData } from "@/contexts/UserContext";
import { useProducer } from "@/hooks/useProducer";
import {
  Alert,
  Button,
  ButtonGroup,
  DropdownMenu,
  LoadingOverlay,
  Small,
  TabsList,
  Tabs as TabsRoot,
} from "@gaco/gaco-library";
import { EllipsisVertical } from "lucide-react";
import { usePathname, useRouter } from "next/navigation";
import { PropsWithChildren, useMemo } from "react";
import styled from "styled-components";
import { PublicView } from "./PublicView";

enum TabIds {
  OVERVIEW = "overview",
  SALES = "sales",
  SALES_REPORTS = "sales-reports",
  AGREEMENTS = "agreements",
}

const Tabs = styled(TabsRoot)`
  height: 100%;
`;

const Content = styled.div`
  flex: 1;
  min-height: 0;
`;

const TitleWrapper = styled.div`
  width: 100%;
  display: flex;
  align-items: center;
`;

const TitleActions = styled.div`
  margin-left: auto;
`;

const TabsContainer = styled.div`
  flex: 1;
  overflow-y: hidden;
`;

interface ProducerLayoutProps extends PropsWithChildren {
  params: { producerId: string };
}

const ProducerLayout = ({ children, params }: ProducerLayoutProps) => {
  const { producerId } = params;
  const router = useRouter();
  const pathname = usePathname();
  const { status, producer } = useProducer({ producerId });
  const { producers: userDataProducers } = useUserData();

  const isUserDataProducer = useMemo(
    () => !!userDataProducers?.[producerId],
    [userDataProducers, producerId]
  );

  const tabFromUrl = useMemo(
    () =>
      Object.values(TabIds).find(
        (tab) =>
          pathname.endsWith(`/artists/${producerId}/${tab}`) ||
          pathname.includes(`/artists/${producerId}/${tab}/`)
      ) || TabIds.OVERVIEW,
    [pathname, producerId]
  );

  const rootUrl = `/artists/${producerId}`;

  const showTablist = useMemo(
    () =>
      !pathname.includes(`${rootUrl}/settings`) &&
      !pathname.includes(`${rootUrl}/import`),
    [pathname, rootUrl]
  );

  const showHeader = useMemo(
    () => !pathname.includes(`${rootUrl}/sales-reports/`),
    [pathname, rootUrl]
  );

  if (status === "loading") {
    return <LoadingOverlay>Loading artist</LoadingOverlay>;
  }

  if (status === "error" || !producer) {
    return <Alert type="error">404: Producer not found</Alert>;
  }

  if (!isUserDataProducer) {
    return <PublicView producer={producer} />;
  }

  return (
    <ProducerProvider producer={producer}>
      {showHeader && (
        <TitleWrapper>
          <PageTitle>
            {producer?.displayName} <Small>Dashboard</Small>
          </PageTitle>

          <TitleActions>
            {!showTablist ? (
              <Button variant="ghost" onClick={() => router.back()}>
                Back
              </Button>
            ) : (
              <ButtonGroup>
                <DropdownMenu
                  align="end"
                  trigger={
                    <Button variant="ghost" icon={<EllipsisVertical />} />
                  }
                  items={[
                    {
                      content: "Settings",
                      onSelect: () => {
                        router.push(`${rootUrl}/settings`);
                      },
                    },
                  ]}
                />
              </ButtonGroup>
            )}
          </TitleActions>
        </TitleWrapper>
      )}
      <TabsContainer>
        <Tabs defaultValue={tabFromUrl} value={tabFromUrl}>
          {showTablist && (
            <TabsList>
              <TabsTriggerLink href={rootUrl} value={TabIds.OVERVIEW}>
                Overview
              </TabsTriggerLink>
              <TabsTriggerLink
                href={`${rootUrl}/${TabIds.SALES}`}
                value={TabIds.SALES}
              >
                Sales
              </TabsTriggerLink>
              <TabsTriggerLink
                href={`${rootUrl}/${TabIds.AGREEMENTS}`}
                value={TabIds.AGREEMENTS}
              >
                Agreements
              </TabsTriggerLink>
              <TabsTriggerLink
                href={`${rootUrl}/${TabIds.SALES_REPORTS}`}
                value={TabIds.SALES_REPORTS}
              >
                Sales Reports
              </TabsTriggerLink>
            </TabsList>
          )}

          <Content>{children}</Content>
        </Tabs>
      </TabsContainer>
    </ProducerProvider>
  );
};

export default ProducerLayout;
