import { DialogAgreementCreateForm } from "@/components/organisms/DialogAgreementCreateForm";
import { Store } from "@/types/collections/storeInterfaces";
import {
  Avatar,
  AvatarFallback,
  Button,
  Card as CardRoot,
  CardSections,
  Small,
} from "@gaco/gaco-library";
import Image from "next/image";
import Link from "next/link";
import { useState } from "react";
import styled from "styled-components";
import { CreateAgreementRequest } from "types/requests/agreementsInterfaces";

interface StoreListItemProps {
  store: Store;
  onOfferSubmit: (request: CreateAgreementRequest) => Promise<unknown>;
  className?: string;
  disabled?: boolean;
}

const LeftColumnContainer = styled.div`
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
`;

const Card = styled(CardRoot)`
  color: ${({ theme }) => theme.colors.textMuted};
`;

export const StoreListItem = ({
  store,
  onOfferSubmit,
  className,
  disabled,
}: StoreListItemProps) => {
  const [showSendOfferDialog, setShowOfferDialog] = useState(false);

  return (
    <Card className={className}>
      <CardSections.LeftColumn>
        <LeftColumnContainer>
          <Avatar>
            {store.profilePictureUrl && (
              <Image src={store.profilePictureUrl} alt="Profile" fill />
            )}
            <AvatarFallback>
              {store.displayName.slice(0, 2).toUpperCase()}
            </AvatarFallback>
          </Avatar>
        </LeftColumnContainer>
      </CardSections.LeftColumn>
      <CardSections.Title>
        <Link
          href={`/stores/${store?.id}`}
          title={`${store.displayName} profile page`}
        >
          {store.displayName}
        </Link>
      </CardSections.Title>
      <CardSections.Content>
        <Small>{store.email}</Small>
      </CardSections.Content>
      <CardSections.RightColumn
        style={{
          alignItems: "flex-end",
          display: "flex",
          flexDirection: "column",
          alignSelf: "flex-end",
        }}
      >
        <Button disabled={disabled} onClick={() => setShowOfferDialog(true)}>
          Send offer
        </Button>

        <DialogAgreementCreateForm
          open={showSendOfferDialog}
          setOpen={setShowOfferDialog}
          onSubmit={onOfferSubmit}
          role="producer"
        />
      </CardSections.RightColumn>
    </Card>
  );
};
