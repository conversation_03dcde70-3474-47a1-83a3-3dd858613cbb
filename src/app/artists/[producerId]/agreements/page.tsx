"use client";

import { Map as MapRoot } from "@/components/ga-ui";
import { AgreementList as AgreementListRoot } from "@/components/organisms/AgreementList";
import { useProducerContext } from "@/contexts/ProducerContext";
import { useAgreements } from "@/hooks/useAgreements";
import { useStores } from "@/hooks/useStores";
import { AgreementStatus } from "@/types/collections/agreementInterfaces";
import { formatAddressToOneLine } from "@/utils/addressUtils";
import {
  CardList,
  H2,
  Spinner,
  <PERSON><PERSON>,
  TabsContent,
  TabsList,
  TabsTrigger,
  TextInput,
} from "@gaco/gaco-library";
import { Search } from "lucide-react";
import { useMemo, useState } from "react";
import styled from "styled-components";
import { StoreListItem } from "./StoreListItem";

type TabItems = "list" | "map" | "graph";

const TabsListContainer = styled.div`
  display: flex;
  justify-content: space-between;
`;

const AgreementList = styled(AgreementListRoot)`
  margin-bottom: 2rem;
`;

const Container = styled.div`
  overflow-y: auto;
  height: 100%;

  ${H2}:first-child {
    margin-top: 0;
  }
`;

const SearchInput = styled(TextInput)`
  padding-top: 0;
`;

const Map = styled(MapRoot)`
  height: 600px;
  border-radius: ${({ theme }) => theme.sizes.borderRadius};
`;

const Page = () => {
  const { producer } = useProducerContext();
  const { stores, status } = useStores();
  const {
    outboundAgreements,
    status: agreementsLoading,
    createDraftAgreement,
  } = useAgreements({
    accountRole: "producer",
    producerId: producer.id,
  });

  const [searchTerm, setSearchTerm] = useState("");
  const [selectedTab, setSelectedTab] = useState<TabItems>("list");

  const searchResults = useMemo(() => {
    if (!stores?.length || agreementsLoading !== "success") {
      return [];
    }

    return stores.filter(
      (store) =>
        store.displayName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        store?.email.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [stores, agreementsLoading, searchTerm]);

  const handleSearch = (term: string) => {
    setSearchTerm(term);
  };

  const markersAddresses = useMemo(
    () =>
      searchResults
        .filter(({ address }) => !!address)
        .map((store) => formatAddressToOneLine(store.address!)),
    [searchResults]
  );

  return (
    <Container>
      <H2>Agreements</H2>
      <AgreementList accountId={producer?.id} role="producer" />

      <H2>Discover stores</H2>
      <Tabs
        defaultValue={selectedTab}
        onValueChange={(value) => setSelectedTab(value as TabItems)}
      >
        <TabsListContainer>
          <TabsList inline>
            <TabsTrigger value="list">List</TabsTrigger>
            <TabsTrigger value="map">Map</TabsTrigger>
            <TabsTrigger value="graph">Graph</TabsTrigger>
          </TabsList>
          {selectedTab === "list" && (
            <SearchInput
              id="searchStores"
              type="text"
              label="Search stores..."
              icon={<Search />}
              value={searchTerm}
              onChange={(e) => handleSearch(e.target.value)}
            />
          )}
        </TabsListContainer>

        <TabsContent value="list">
          {status === "loading" ? (
            <>
              <Spinner />
            </>
          ) : (
            <CardList>
              {searchResults?.length > 0 ? (
                searchResults?.map((store) => (
                  <StoreListItem
                    key={store.id}
                    store={store}
                    disabled={
                      !!outboundAgreements.find(
                        (a) =>
                          a.storeId === store.id &&
                          a.status === AgreementStatus.DRAFT
                      )
                    }
                    onOfferSubmit={(payload) =>
                      createDraftAgreement({
                        ...payload,
                        producerId: producer.id,
                        storeId: store.id,
                      })
                    }
                  />
                ))
              ) : (
                <>No stores found</>
              )}
            </CardList>
          )}
        </TabsContent>
        <TabsContent value="map">
          <Map markersAddresses={markersAddresses} />
        </TabsContent>
      </Tabs>
    </Container>
  );
};

export default Page;
