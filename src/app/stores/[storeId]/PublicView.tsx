import { Map, PageTitle } from "@/components/ga-ui";
import { Store } from "@/types/collections/storeInterfaces";
import { formatAddressToOneLine } from "@/utils/addressUtils";
import { Card as CardRoot, CardSections } from "@gaco/gaco-library";
import Image from "next/image";
import { useMemo } from "react";
import styled from "styled-components";

interface PublicViewProps {
  store?: Store;
}

const Address = styled.p`
  font-size: 0.8rem;
  margin-top: 1rem;
`;

const Container = styled.div`
  overflow-y: auto;
  height: 100%;
`;

const ProfilePictureContainer = styled.div`
  height: 10rem;
  margin-bottom: 1rem;

  position: relative;
`;

const Card = styled(CardRoot)`
  margin-bottom: 1rem;
`;

const MapCard = styled(CardRoot)`
  height: 420px;
  margin-bottom: 1rem;

  > * {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
  }
`;

export const PublicView = ({ store }: PublicViewProps) => {
  const address = useMemo(
    () => (store?.address ? formatAddressToOneLine(store?.address) : undefined),
    [store]
  );

  return (
    <Container>
      <PageTitle>{store?.displayName}</PageTitle>
      {store?.profilePictureUrl && (
        <ProfilePictureContainer>
          <Image
            src={store?.profilePictureUrl}
            alt="Profile"
            fill
            style={{
              objectFit: "contain",
            }}
          />
        </ProfilePictureContainer>
      )}
      <Card>
        {address && <Address>{address}</Address>}

        <CardSections.Content>
          <p>
            {store
              ? store.description
              : "This store has not been set up yet. Please check back later."}
          </p>
        </CardSections.Content>

        <CardSections.RightColumn>
          {store?.email && <a href={`MAILTO:${store.email}`}>{store.email}</a>}
        </CardSections.RightColumn>
      </Card>

      {address && (
        <MapCard>
          <Map markersAddresses={[address || ""]} />
        </MapCard>
      )}
    </Container>
  );
};
