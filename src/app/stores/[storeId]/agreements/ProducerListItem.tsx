import { DialogAgreementCreateForm } from "@/components/organisms/DialogAgreementCreateForm";
import { Producer } from "@/types/collections/producerInterfaces";
import {
  Avatar,
  AvatarFallback,
  <PERSON><PERSON>,
  Card as CardRoot,
  CardSections,
  Small,
} from "@gaco/gaco-library";
import Image from "next/image";
import { useState } from "react";
import styled from "styled-components";
import { CreateAgreementRequest } from "types/requests/agreementsInterfaces";

const LeftColumnContainer = styled.div`
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
`;

const Card = styled(CardRoot)`
  color: ${({ theme }) => theme.colors.textMuted};
`;

interface ProducerListItemProps {
  defaultcommission?: number;
  producer: Producer;
  onOfferSubmit: (request: CreateAgreementRequest) => Promise<unknown>;
  className?: string;
  disabled?: boolean;
}

export const ProducerListItem = ({
  defaultcommission,
  producer,
  onOfferSubmit,
  className,
  disabled,
}: ProducerListItemProps) => {
  const [showSendOfferDialog, setShowOfferDialog] = useState(false);

  return (
    <>
      <Card className={className}>
        <CardSections.LeftColumn>
          <LeftColumnContainer>
            <Avatar>
              {producer.profilePictureUrl && (
                <Image src={producer.profilePictureUrl} alt="Profile" fill />
              )}
              <AvatarFallback>
                {producer.displayName.slice(0, 2).toUpperCase()}
              </AvatarFallback>
            </Avatar>
          </LeftColumnContainer>
        </CardSections.LeftColumn>
        <CardSections.Title>
          <a
            href={`/artists/${producer?.id}`}
            title={`${producer.displayName} profile page`}
          >
            {producer.displayName}
          </a>
        </CardSections.Title>
        <CardSections.Content>
          <Small>{producer.email}</Small>
        </CardSections.Content>
        <CardSections.RightColumn
          style={{
            alignItems: "flex-end",
            display: "flex",
            flexDirection: "column",
            alignSelf: "flex-end",
          }}
        >
          <Button disabled={disabled} onClick={() => setShowOfferDialog(true)}>
            Send offer
          </Button>
        </CardSections.RightColumn>
      </Card>

      <DialogAgreementCreateForm
        open={showSendOfferDialog}
        setOpen={setShowOfferDialog}
        initialValues={{
          commission: defaultcommission,
        }}
        onSubmit={onOfferSubmit}
        role="store"
      />
    </>
  );
};
