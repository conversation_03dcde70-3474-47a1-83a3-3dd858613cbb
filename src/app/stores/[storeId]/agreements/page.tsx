"use client";

import { AgreementList as AgreementListRoot } from "@/components/organisms/AgreementList";
import { useStoreContext } from "@/contexts/StoreContext";
import { useAgreements } from "@/hooks/useAgreements";
import { useProducers } from "@/hooks/useProducers";
import { AgreementStatus } from "@/types/collections/agreementInterfaces";
import { CardList, H2, Spinner, TextInput } from "@gaco/gaco-library";
import { Search } from "lucide-react";
import { useMemo, useState } from "react";
import styled from "styled-components";
import { ProducerListItem } from "./ProducerListItem";

const HeaderContainer = styled.div`
  display: flex;
  justify-content: space-between;
`;

const AgreementList = styled(AgreementListRoot)`
  margin-bottom: 2rem;
`;

const Container = styled.div`
  overflow-y: auto;
  height: 100%;

  ${H2}:first-child {
    margin-top: 0;
  }
`;

const SearchInput = styled(TextInput)`
  padding-top: 0;
  padding-bottom: 0.5rem;
`;

const Page = () => {
  const { store } = useStoreContext();
  const { producers, status } = useProducers();
  const {
    outboundAgreements,
    status: applicationsLoading,
    createDraftAgreement,
  } = useAgreements({
    accountRole: "store",
    storeId: store.id,
  });

  const [searchTerm, setSearchTerm] = useState("");

  const searchResults = useMemo(() => {
    if (!producers?.length || applicationsLoading !== "success") {
      return [];
    }

    return producers.filter(
      (producer) =>
        producer.displayName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        producer?.email.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [producers, applicationsLoading, searchTerm]);

  return (
    <Container>
      <H2>Agreements</H2>
      <AgreementList accountId={store.id} role="store" />

      <HeaderContainer>
        <H2>Discover artists</H2>

        <SearchInput
          id="searchArtists"
          type="text"
          label="Search artists..."
          icon={<Search />}
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
        />
      </HeaderContainer>

      {status === "loading" ? (
        <>
          <Spinner />
        </>
      ) : (
        <CardList>
          {searchResults?.length > 0 ? (
            searchResults?.map((producer) => (
              <ProducerListItem
                key={producer.id}
                producer={producer}
                disabled={
                  !!outboundAgreements.find(
                    (a) =>
                      a.producerId === producer.id &&
                      a.status === AgreementStatus.DRAFT
                  )
                }
                onOfferSubmit={(payload) =>
                  createDraftAgreement({
                    ...payload,
                    producerId: producer.id,
                    storeId: store.id,
                  })
                }
                defaultcommission={store.defaultCommission || undefined}
              />
            ))
          ) : (
            <>No artists found</>
          )}
        </CardList>
      )}
    </Container>
  );
};

export default Page;
