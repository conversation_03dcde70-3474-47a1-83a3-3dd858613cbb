"use client";

import { PageTitle } from "@/components/ga-ui/PageTitle";
import { TabsTriggerLink } from "@/components/ga-ui/TabsTriggerLink";
import { StoreProvider } from "@/contexts/StoreContext";
import { useUserData } from "@/contexts/UserContext";
import { useStore } from "@/hooks/useStore";
import {
  Alert,
  Button,
  ButtonGroup,
  DropdownMenu,
  LoadingOverlay,
  Small,
  TabsList,
  Tabs as TabsRoot,
} from "@gaco/gaco-library";
import { EllipsisVertical, Import, Settings } from "lucide-react";
import { usePathname, useRouter } from "next/navigation";
import { PropsWithChildren, useMemo } from "react";
import styled from "styled-components";
import { PublicView } from "./PublicView";

enum TabIds {
  OVERVIEW = "overview",
  SALES = "sales",
  SALES_REPORTS = "sales-reports",
  AGREEMENTS = "agreements",
}

const Tabs = styled(TabsRoot)`
  height: 100%;
`;

const Content = styled.div`
  flex: 1;
  min-height: 0;
`;

const TitleWrapper = styled.div`
  width: 100%;
  display: flex;
  align-items: center;
`;

const TitleActions = styled.div`
  margin-left: auto;
`;

const TabsContainer = styled.div`
  flex: 1;
  overflow-y: hidden;
`;

interface StoreLayoutProps extends PropsWithChildren {
  params: { storeId: string };
}

const StoreLayout = ({ children, params }: StoreLayoutProps) => {
  const { storeId } = params;
  const router = useRouter();
  const pathname = usePathname();
  const { status, store } = useStore({ storeId });
  const { stores: userDataStores } = useUserData();

  const isUserStore = useMemo(
    () => !!userDataStores?.[storeId],
    [userDataStores, storeId]
  );

  const tabFromUrl = useMemo(
    () =>
      Object.values(TabIds).find(
        (tab) =>
          pathname.endsWith(`/stores/${storeId}/${tab}`) ||
          pathname.includes(`/stores/${storeId}/${tab}/`)
      ) || TabIds.OVERVIEW,
    [pathname, storeId]
  );

  const rootUrl = `/stores/${storeId}`;

  const showTablist = useMemo(
    () =>
      !pathname.includes(`${rootUrl}/settings`) &&
      !pathname.includes(`${rootUrl}/import`) &&
      !pathname.includes(`${rootUrl}/sales-reports/`),
    [pathname, rootUrl]
  );

  const showHeader = useMemo(
    () => !pathname.includes(`${rootUrl}/sales-reports/`),
    [pathname, rootUrl]
  );

  if (status === "loading") {
    return <LoadingOverlay>Loading store</LoadingOverlay>;
  }

  if (status === "error" || !store) {
    return <Alert type="error">404: Store not found</Alert>;
  }

  if (!isUserStore) {
    return <PublicView store={store} />;
  }

  return (
    <StoreProvider store={store}>
      {showHeader && (
        <TitleWrapper>
          <PageTitle>
            {store?.displayName} <Small>Dashboard</Small>
          </PageTitle>

          <TitleActions>
            {!showTablist ? (
              <Button variant="ghost" onClick={() => router.back()}>
                Back
              </Button>
            ) : (
              <ButtonGroup>
                <DropdownMenu
                  align="end"
                  trigger={
                    <Button variant="ghost" icon={<EllipsisVertical />} />
                  }
                  items={[
                    {
                      content: (
                        <>
                          <Settings />
                          Settings
                        </>
                      ),
                      onSelect: () => {
                        router.push(`${rootUrl}/settings`);
                      },
                    },
                    {
                      content: (
                        <>
                          <Import />
                          Import data
                        </>
                      ),
                      onSelect: () => {
                        router.push(`${rootUrl}/import`);
                      },
                    },
                  ]}
                />
              </ButtonGroup>
            )}
          </TitleActions>
        </TitleWrapper>
      )}
      <TabsContainer>
        <Tabs defaultValue={tabFromUrl} value={tabFromUrl}>
          {showTablist && (
            <TabsList>
              <TabsTriggerLink href={rootUrl} value={TabIds.OVERVIEW}>
                Overview
              </TabsTriggerLink>
              <TabsTriggerLink
                href={`${rootUrl}/${TabIds.SALES}`}
                value={TabIds.SALES}
              >
                Sales
              </TabsTriggerLink>
              <TabsTriggerLink
                href={`${rootUrl}/${TabIds.AGREEMENTS}`}
                value={TabIds.AGREEMENTS}
              >
                Agreements
              </TabsTriggerLink>
              <TabsTriggerLink
                href={`${rootUrl}/${TabIds.SALES_REPORTS}`}
                value={TabIds.SALES_REPORTS}
              >
                Sales Reports
              </TabsTriggerLink>
            </TabsList>
          )}
          <Content>{children}</Content>
        </Tabs>
      </TabsContainer>
    </StoreProvider>
  );
};

export default StoreLayout;
