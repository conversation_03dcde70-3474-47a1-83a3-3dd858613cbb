import { ButtonLink } from "@/components/ga-ui";
import { callDeleteSalesReport } from "@/lib/api";
import { Producer } from "@/types/collections/producerInterfaces";
import { SalesReportModel } from "@/types/collections/salesReportInterfaces";
import { numberToCurrency } from "@/utils/currencyUtils";
import { getFirebaseDateTime } from "@/utils/timeUtils";
import {
  Button,
  ButtonGroup as ButtonGroupRoot,
  Card,
  CardSections,
  CardSize,
  DetailsList,
  Dialog,
} from "@gaco/gaco-library";
import Link from "next/link";
import { useState } from "react";
import { useFunctions } from "reactfire";
import styled from "styled-components";

const ButtonGroup = styled(ButtonGroupRoot)`
  margin-top: 1rem;
  justify-content: flex-end;
`;

const RightColumnInner = styled.div`
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  justify-content: space-between;
  text-align: right;
`;

interface StoreSalesReportCardProps extends SalesReportModel {
  producer?: Producer;
}

export const StoreSalesReportCard = ({
  id,
  producer,
  salesReportId,
  createdAt,
  producerLevel,
  uri,
  saleIds,
}: StoreSalesReportCardProps) => {
  const functions = useFunctions();
  const [isLoading, setIsLoading] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const handleDeleteSalesReport = async () => {
    setIsLoading(true);

    try {
      await callDeleteSalesReport(id, functions, {});

      setIsDialogOpen(false);
    } catch (error) {
      console.error("Error deleting sales report:", error);
    }

    setIsLoading(false);
  };

  return (
    <Card size={CardSize.sm}>
      <CardSections.Title>{salesReportId}</CardSections.Title>

      <DetailsList>
        <dd>Artist</dd>
        <dt>
          <Link href={`/artists/${producer?.id}`}>{producer?.displayName}</Link>
        </dt>
        <dd>Gross Payout</dd>
        <dt>
          {numberToCurrency(producerLevel.producerTotalGrossPayout, "EUR")}
        </dt>
        <dd>Items sold</dd>
        <dt>{saleIds.length}</dt>
      </DetailsList>

      <CardSections.RightColumn>
        <RightColumnInner>
          <div>{getFirebaseDateTime(createdAt)}</div>

          <ButtonGroup>
            {uri && (
              <ButtonLink
                variant="outline"
                href={uri}
                rel="noopener noreferrer"
                target="_blank"
                size="sm"
              >
                Download
              </ButtonLink>
            )}

            <ButtonLink href={`sales-reports/${id}`} size="sm">
              View
            </ButtonLink>

            <Dialog
              trigger={
                <Button variant="destructive" size="sm">
                  Delete
                </Button>
              }
              title="Delete Sales Report"
              open={isDialogOpen}
              onOpenChange={setIsDialogOpen}
              content="Are you sure you want to delete this sales report? This action cannot be undone."
              actions={
                <Button
                  variant="destructive"
                  onClick={handleDeleteSalesReport}
                  loading={isLoading}
                  disabled={isLoading}
                >
                  Delete
                </Button>
              }
            />
          </ButtonGroup>
        </RightColumnInner>
      </CardSections.RightColumn>
    </Card>
  );
};
