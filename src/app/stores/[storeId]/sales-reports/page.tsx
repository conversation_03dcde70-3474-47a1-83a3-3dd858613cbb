"use client";

import { CreateSalesReportsForm } from "@/components/organisms/CreateSalesReportsForm";
import { useStoreContext } from "@/contexts/StoreContext";
import { useSalesReports } from "@/hooks/useSalesReports";
import {
  <PERSON><PERSON>,
  CardList,
  Card as CardRoot,
  CardSections,
  H2,
  TextInput,
} from "@gaco/gaco-library";
import { Search } from "lucide-react";
import { useMemo, useState } from "react";
import styled from "styled-components";
import { StoreSalesReportCard } from "./StoreSalesReportCard";

const Container = styled.div`
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 1rem;

  overflow-y: auto;
  padding-bottom: 1rem;

  ${H2} {
    margin-bottom: 0;
  }
`;

const CardCreateSalesReport = styled(CardRoot)`
  flex-shrink: 0;
`;

const SalesReportsContainer = styled.div`
  flex: 1;
`;

const SalesReportsListHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
`;

const SearchInput = styled(TextInput)`
  padding-top: 0;
`;

const Page = () => {
  const { store } = useStoreContext();
  const { salesReports, enrichSalesReportWithProducer } = useSalesReports({
    storeId: store.id,
  });
  const [searchTerm, setSearchTerm] = useState("");

  const enrichedSalesReports = useMemo(
    () =>
      salesReports?.map((salesReport) =>
        enrichSalesReportWithProducer(salesReport)
      ),
    [salesReports, enrichSalesReportWithProducer]
  );

  const filteredSalesReports = enrichedSalesReports?.filter((report) =>
    [
      report.producer?.displayName.toLowerCase(),
      report.salesReportId.toLowerCase(),
    ].find((term) => {
      if (!term) return false;
      return term.includes(searchTerm.toLowerCase());
    })
  );

  return (
    <Container>
      <CardCreateSalesReport>
        <CardSections.Title>Create Sales Report</CardSections.Title>
        <CardSections.Content>
          <CreateSalesReportsForm storeId={store.id} role="store" />
        </CardSections.Content>
      </CardCreateSalesReport>

      <SalesReportsContainer>
        <SalesReportsListHeader>
          <H2>Sales Reports</H2>
          <SearchInput
            id="searchSalesReports"
            type="text"
            label="Search sales reports..."
            icon={<Search />}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </SalesReportsListHeader>
        {filteredSalesReports?.length ? (
          <CardList>
            {filteredSalesReports?.map((enrichedSalesReport) => (
              <StoreSalesReportCard
                key={enrichedSalesReport.id}
                {...enrichedSalesReport}
              />
            ))}
          </CardList>
        ) : (
          <Alert type="info">No sales reports found</Alert>
        )}
      </SalesReportsContainer>
    </Container>
  );
};

export default Page;
