import { useProducer } from "@/hooks/useProducer";
import { CardSections, DetailsList, LoadingOverlay } from "@gaco/gaco-library";
import { Brush } from "lucide-react";
import Link from "next/link";

export const ProducerCard = ({ producerId }: { producerId: string }) => {
  const { producer, status } = useProducer({
    producerId,
  });

  return (
    <>
      {status === "loading" ? (
        <LoadingOverlay>Loading artist</LoadingOverlay>
      ) : (
        <>
          <CardSections.Title>
            Artist
            <Brush />
          </CardSections.Title>
          <DetailsList>
            <dd>Name</dd>
            <dt>
              <Link href={`/artist/${producer?.id}`}>
                {producer?.displayName}
              </Link>
            </dt>
            <dd>Email</dd>
            <dt>
              <a href={`mailto:${producer?.email}`}>{producer?.email}</a>
            </dt>
          </DetailsList>
        </>
      )}
    </>
  );
};
