"use client";

import { ButtonLink, PageTitle } from "@/components/ga-ui";
import { useSalesReport } from "@/hooks/useSalesReport";
import { numberToCurrency } from "@/utils/currencyUtils";
import { getFirebaseDate, getFirebaseDateTime } from "@/utils/timeUtils";
import {
  <PERSON><PERSON>,
  ButtonGroup,
  CardList,
  Card as CardRoot,
  CardSections,
  CardSize,
  DetailsList,
  H2,
  media,
  Small,
} from "@gaco/gaco-library";
import { NotebookPen } from "lucide-react";
import styled from "styled-components";
import { ProducerCard } from "./ProducerCard";

const Container = styled.div`
  overflow-y: auto;
  height: 100%;
`;

const Grid = styled.div`
  padding-bottom: 1rem;

  display: grid;
  grid-template-columns: repeat(6, 1fr);
  grid-template-rows: auto;
  grid-column-gap: 0.5rem;
  grid-row-gap: 0.5rem;

  ${media.md`
      grid-template-rows: auto 1fr;
      grid-column-gap: 1rem;
      grid-row-gap: 1rem;
  `}
`;

const GridItemSalesList = styled.div`
  overflow: hidden;
  grid-area: 3 / 1 / 3 / 7;

  ${media.md`
    grid-area: 2 / 1 / 3 / 7;
  `}

  ${H2} {
    margin-top: 0rem;
  }
`;

const CardSalesReportDetails = styled(CardRoot)`
  grid-area: 1 / 1 / 2 / 7;

  ${media.md`
    grid-area: 1 / 1 / 2 / 4;
  `};
`;

const CardSalesReportStoreDetails = styled(CardRoot)`
  grid-area: 2 / 1 / 3 / 7;

  ${media.md`
    grid-area: 1 / 4 / 2 / 7;
  `};
`;

const CardSalesList = styled(CardList)`
  height: 100%;
  min-height: 300px;
`;

const TitleWrapper = styled.div`
  width: 100%;
  display: flex;
  align-items: center;

  margin-bottom: 0.25rem;

  small {
    margin-left: 0.5rem;
  }

  span {
    display: none;
  }

  ${media.md`
    span {
      margin-bottom: 1rem;
      display: inline;
    }
  `}
`;

const TitleActions = styled.div`
  margin-left: auto;
`;

interface PageProps {
  params: { salesReportId: string };
}

const Page = ({ params: { salesReportId } }: PageProps) => {
  const { data, status } = useSalesReport({
    salesReportId,
  });

  return (
    <Container>
      <TitleWrapper>
        <PageTitle>
          <span>Sales Report</span>
          {data?.salesReportId && <Small>#{data.salesReportId}</Small>}
        </PageTitle>
        <TitleActions>
          <ButtonGroup>
            {data?.downloadUrl && (
              <ButtonLink target="_blank" href={data?.downloadUrl}>
                Download PDF
              </ButtonLink>
            )}
            <Button variant="outline" onClick={window.print}>
              Print
            </Button>
          </ButtonGroup>
        </TitleActions>
      </TitleWrapper>
      <Grid>
        <CardSalesReportDetails loading={status === "loading"}>
          <CardSections.Title>
            Summary
            <NotebookPen />
          </CardSections.Title>
          {data?.createdAt && (
            <DetailsList>
              <dd>Date</dd>
              <dt>{getFirebaseDate(data.createdAt)}</dt>
              <dd>Items sold</dd>
              <dt>{data.salesLevelData.length}</dt>
              <dd>Gross Income</dd>
              <dt>
                {numberToCurrency(
                  data.producerLevel.producerTotalGrossPayout,
                  "EUR"
                )}
              </dt>
              <dd>Gross Payout</dd>
              <dt>
                {numberToCurrency(
                  data.producerLevel.storeTotalGrossPayout,
                  "EUR"
                )}
              </dt>
              <dd>Subtotal</dd>
              <dt>{numberToCurrency(data.producerLevel.subtotal, "EUR")}</dt>
            </DetailsList>
          )}
        </CardSalesReportDetails>

        <CardSalesReportStoreDetails loading={status === "loading"}>
          {data?.producerId && <ProducerCard producerId={data.producerId} />}
        </CardSalesReportStoreDetails>

        <GridItemSalesList>
          <H2>Items</H2>
          <CardSalesList
            loading={status === "loading" && "Loading sales report"}
            cards={data?.salesLevelData.map((sale) => ({
              id: sale.saleId,
              size: CardSize.sm,
              content: (
                <>
                  <DetailsList>
                    <dd>Item</dd>
                    <dt>{sale.title}</dt>
                    {sale.variantTitle && (
                      <>
                        <dd>Variant</dd>
                        <dt>{sale.variantTitle}</dt>
                      </>
                    )}
                    <dd>Sale Price</dd>
                    <dt>{numberToCurrency(sale.subtotal, "EUR")}</dt>
                    <dd>Store Comission</dd>
                    <dt>{sale.commission}%</dt>
                    <dd>Gross Payout</dd>
                    <dt>{numberToCurrency(sale.producerGrossPayout, "EUR")}</dt>
                    <dd>Gross Income</dd>
                    <dt>
                      {numberToCurrency(
                        sale.subtotal - sale.producerGrossPayout,
                        "EUR"
                      )}
                    </dt>
                  </DetailsList>
                </>
              ),
              rightColumn: getFirebaseDateTime(sale.updatedAt),
            }))}
          />
        </GridItemSalesList>
      </Grid>
    </Container>
  );
};

export default Page;
