"use client";

import { ButtonFileUpload } from "@/components/ga-ui/ButtonFileUpload";
import { acceptedCountries } from "@/constants/acceptedCountryCodes";
import { FirebaseCollections } from "@/constants/firebaseCollections";
import { useStoreContext } from "@/contexts/StoreContext";
import {
  Alert,
  Card,
  CardSections,
  Form,
  FormFieldComponents,
  H4,
} from "@gaco/gaco-library";
import { doc, setDoc } from "firebase/firestore";
import { getDownloadURL, ref, uploadBytes } from "firebase/storage";
import Image from "next/image";
import { useMemo, useState } from "react";
import countryList from "react-select-country-list";
import { useFirestore, useStorage } from "reactfire";
import styled from "styled-components";
import { Address } from "types/collections/storeInterfaces";
import * as yup from "yup";

const Container = styled.div`
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  height: 100%;
  padding-bottom: 1rem;
`;

const ProfilePictureMask = styled.div`
  width: 10rem;
  height: 10rem;
  border-radius: 50%;
  overflow: hidden;
  margin-bottom: 1rem;
  position: relative;
  background-color: ${({ theme }) => theme.colors.background};
`;

const Page = () => {
  const firestore = useFirestore();
  const { store } = useStoreContext();
  const storage = useStorage();

  const [error, setError] = useState("");
  const [success, setSuccess] = useState("");
  const [loading, setLoading] = useState(false);
  const storeDocRef = doc(firestore, FirebaseCollections.STORES, store.id);

  const countries = useMemo(
    () =>
      countryList()
        .getData()
        .filter(({ value }) => acceptedCountries.includes(value)),
    []
  );

  const handleStoreInfoSubmit = async (payload: {
    displayName: string;
    description: string | null;
    address: Address | null;
    defaultCommission: number | null;
  }) => {
    setLoading(true);
    setError("");
    setSuccess("");

    try {
      await setDoc(storeDocRef, payload, { merge: true });
      setSuccess("Store information saved successfully");
    } catch (error) {
      setError("Error saving store info to Firestore");
      console.error("Error saving store info to Firestore:", error);
    }

    setLoading(false);
  };

  const handleProfilePictureUpload = async (files: FileList | null) => {
    setSuccess("");
    setError("");

    if (files && files.length > 0) {
      const file = files[0];

      try {
        const storageRef = ref(
          storage,
          `stores/${store.id}/media/${file.name}`
        );

        const snapshot = await uploadBytes(storageRef, file);
        const downloadURL = await getDownloadURL(snapshot.ref);

        await setDoc(
          storeDocRef,
          { profilePictureUrl: downloadURL },
          { merge: true }
        );
        setSuccess("Profile picture uploaded successfully");
      } catch (error) {
        setError("Error uploading profile picture");
        console.error("Error uploading profile picture:", error);
      }
    }
  };

  return (
    <Container>
      {success && <Alert type="success">{success}</Alert>}
      {error && <Alert type="error">{error}</Alert>}
      <Card loading={loading}>
        <CardSections.Title>Settings</CardSections.Title>
        <CardSections.Content>
          <Form
            onSubmit={handleStoreInfoSubmit}
            labelSubmit="Save Store Information"
            initialValues={store}
            fields={[
              {
                title: "Store information",
                columns: 2,
                fields: [
                  {
                    name: "displayName",
                    label: "Display name",
                    component: FormFieldComponents.TEXT,
                  },

                  {
                    name: "defaultCommission",
                    label: "Default commission percentage",
                    component: FormFieldComponents.TEXT,
                    type: "number",
                  },
                  {
                    name: "description",
                    label: "Description",
                    component: FormFieldComponents.TEXT_AREA,
                  },
                ],
              },
              {
                title: "Address",
                columns: 2,
                fields: [
                  {
                    name: "address.streetNumber",
                    label: "Street number",
                    component: FormFieldComponents.TEXT,
                  },
                  {
                    name: "address.streetName",
                    label: "Street name",
                    component: FormFieldComponents.TEXT,
                  },
                  {
                    name: "address.zipCode",
                    label: "Zip code",
                    component: FormFieldComponents.TEXT,
                  },
                  {
                    name: "address.city",
                    label: "City",
                    component: FormFieldComponents.TEXT,
                  },
                  {
                    name: "address.state",
                    label: "State",
                    component: FormFieldComponents.TEXT,
                  },
                  {
                    name: "address.country",
                    label: "Country",
                    component: FormFieldComponents.SELECT,
                    options: countries,
                  },
                ],
              },
            ]}
            schema={yup.object().shape({
              displayName: yup.string().required(),
              defaultCommission: yup.number().required().max(100).min(0),
              description: yup.string().required(),

              address: yup.object().shape({
                streetNumber: yup.string().required(),
                streetName: yup.string().required(),
                zipCode: yup.string().required(),
                city: yup.string().required(),
                state: yup.string().nullable(),
                country: yup.string().required(),
              }),
            })}
          />
          <H4>Profile picture</H4>

          {store.profilePictureUrl && (
            <ProfilePictureMask>
              <Image
                src={store.profilePictureUrl}
                alt="Profile"
                layout="fill"
                objectFit="cover"
              />
            </ProfilePictureMask>
          )}
          <ButtonFileUpload onUpload={handleProfilePictureUpload}>
            Upload new profile picture
          </ButtonFileUpload>
        </CardSections.Content>
      </Card>
    </Container>
  );
};

export default Page;
