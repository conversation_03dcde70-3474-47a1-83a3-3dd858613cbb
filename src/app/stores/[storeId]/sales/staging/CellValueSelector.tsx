import { CreatableSelect as CreatableSelectRoot } from "@gaco/gaco-library";
import { useMemo } from "react";
import styled from "styled-components";

const CreatableSelect = styled(CreatableSelectRoot)`
  > div {
    padding-top: 0;
  }

  .react-select__control {
    background-color: ${({ theme }) => theme.colors.contentBg};
  }

  .react-select__input-container {
    height: 2rem;
    margin: 0;
    padding: 0 0 0 0.75rem;
  }

  .react-select__single-value {
    height: 2rem;
    padding-left: 0.75rem;
    display: flex;
    align-items: center;
  }

  input {
    height: 2rem;
  }
`;

export const CellValueSelector = ({
  value,
  setValue,
  options = [],
  getValidationErrors,
  ...props
}: {
  value: string;
  setValue: (value: string) => void;
  options?: { label: string; value: string }[];
  getValidationErrors?: (value: string) => string[] | null;
}) => {
  const validationErrors = useMemo(() => {
    if (!getValidationErrors) return;

    const errors = getValidationErrors(value);

    return errors && errors.length > 0 ? errors.join(", ") : undefined;
  }, [getValidationErrors, value]);

  return (
    <CreatableSelect
      id="vendor-select"
      error={validationErrors}
      options={options}
      defaultInputValue={value}
      onChange={(option) => {
        setValue(option?.value || "");
      }}
      onCreateOption={(inputValue) => setValue(inputValue)}
      createOptionPosition="first"
      filterOption={() => true} // Allow all options to be selectable
      className="cell-value-selector"
      {...props}
    />
  );
};
