"use client";

import { useStoreContext } from "@/contexts/StoreContext";
import { useAgreements } from "@/hooks/useAgreements";
import { useFirebaseFunctions } from "@/hooks/useFirebaseFunctions";
import { useSales } from "@/hooks/useSales";
import { numberToCurrency } from "@/utils/currencyUtils";
import { getFirebaseDate } from "@/utils/timeUtils";
import { themeQuartz } from "ag-grid-community";

import { callSanitizeSalesWithExistingAgreement } from "@/lib/api";
import {
  Button,
  Card,
  CardSections,
  Dialog,
  LoadingOverlay,
  media,
  TextInput,
} from "@gaco/gaco-library";
import type { ColDef } from "ag-grid-community";
import { AllCommunityModule, ModuleRegistry } from "ag-grid-community";
import { AgGridReact, CustomCellRendererProps } from "ag-grid-react";
import { parse, subDays } from "date-fns";
import { Search } from "lucide-react";
import { useEffect, useMemo, useRef, useState } from "react";
import { useUser } from "reactfire";
import styled from "styled-components";
import { AddProducerForm } from "./AddProducerForm";
import { CellValueSelector } from "./CellValueSelector";

const myTheme = themeQuartz.withParams({
  wrapperBorder: false,
  rowHeight: 36,
});

const Container = styled.div`
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 1rem;

  overflow-y: auto;
  padding-bottom: 1rem;
`;

const GridAndSearchContainer = styled.div`
  flex: 1;

  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  align-items: flex-end;

  button {
    align-self: flex-end;
    flex-shrink: 0;
  }
`;

const GridCard = styled(Card)`
  min-height: 300px;
  flex: 1;
  padding: 0;

  .react-select__control {
    background-color: transparent;
  }

  ${media.md`
    min-height: unset;
  `}
`;

const SearchInput = styled(TextInput)`
  display: inline-block;
  padding-top: 0;
`;

interface SaleRow {
  title: string;
  variantDisplayName: string;
  totalPrice: string;
  updatedAt: string;
  vendor: string;
  saleId: string;
}

ModuleRegistry.registerModules([AllCommunityModule]);

const Page = () => {
  const functions = useFirebaseFunctions();
  const { data: user } = useUser();
  const { store } = useStoreContext();
  const gridRef = useRef<AgGridReact<SaleRow>>(null);
  const { acceptedAgreements, enrichAgreementWithProducer } = useAgreements({
    accountRole: "store",
    storeId: store.id,
  });
  const { status, salesStaging } = useSales({
    storeId: store.id,
  });
  const [createArtistDialogOpen, setCreateArtistDialogOpen] = useState(false);
  const [createArtistStagingSale, setCreateArtistStagingSale] =
    useState<SaleRow>();
  const [searchTerm, setSearchTerm] = useState("");
  const [loading, setLoading] = useState(false);
  const [rowData, setRowData] = useState<SaleRow[]>([]);

  useEffect(() => {
    const handleResize = () => {
      const isMd = window.innerWidth > 900;
      gridRef.current?.api?.setColumnsVisible(["variantDisplayName"], isMd);
    };

    window.addEventListener("resize", handleResize);

    handleResize();

    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, []);

  useEffect(() => {
    if (!salesStaging) return;

    setRowData(
      salesStaging.map((sale) => ({
        title: sale.title || "No title",
        variantDisplayName: sale.variantDisplayName || "No variant",
        totalPrice: numberToCurrency(sale.totalPrice, sale.currency),
        updatedAt: getFirebaseDate(sale.updatedAt),
        vendor: sale.vendor || "",
        saleId: sale.id,
      }))
    );
  }, [salesStaging, setRowData]);

  const enrichedAgreements = useMemo(
    () => acceptedAgreements.map(enrichAgreementWithProducer),
    [acceptedAgreements, enrichAgreementWithProducer]
  );

  const agreementOptions = useMemo(
    () =>
      enrichedAgreements.map((agreement) => ({
        label: agreement?.producer.displayName || "",
        value: agreement?.id || "",
      })),
    [enrichedAgreements]
  );

  const colDefs: ColDef<SaleRow>[] = useMemo(
    () => [
      { field: "updatedAt", headerName: "Date", width: 110 },
      { field: "title", flex: 1 },
      { field: "variantDisplayName", headerName: "Variant", flex: 2 },
      { field: "totalPrice", headerName: "Total", flex: 1 },
      {
        field: "vendor",
        headerName: "Vendor",
        cellStyle: {
          padding: 0,
        },
        validate: true,
        context: {
          validate: true,
        },
        cellRendererParams: (cell: CustomCellRendererProps) => {
          return {
            getValidationErrors: (value: string) => {
              if (!agreementOptions.find((opt) => opt.value === value)) {
                return ["There must be an active agreement with this vendor"];
              }

              return null;
            },
            options: agreementOptions,
            onCreateOption: () => handleNewProducer(cell),
            onValueChange: (value: string) => {
              setRowData((prev) =>
                prev.map((row) =>
                  row.vendor === value ? { ...row, vendor: value } : row
                )
              );
            },
          };
        },
        cellRenderer: CellValueSelector,
      },
    ],
    [agreementOptions]
  );

  const handleNewProducer = (cell: CustomCellRendererProps) => {
    if (!cell?.data) {
      return;
    }

    const newOption = {
      label: cell.data.title,
      value: cell.data.title,
    };
    setCreateArtistStagingSale(cell.data);
    setCreateArtistDialogOpen(true);

    return newOption;
  };

  if (status === "loading") {
    return <LoadingOverlay>Loading sales</LoadingOverlay>;
  }

  if (status === "error") {
    return <div>Error loading sales</div>;
  }

  const handleSubmit = async () => {
    const updateCalls = Object.entries(
      rowData
        .filter((row) => {
          const relevantSale = salesStaging.find(
            (sale) => sale.id === row.saleId
          );

          if (!relevantSale) {
            return false;
          }

          if (row.vendor === "" || relevantSale.vendor === row.vendor) {
            return false;
          }

          return true;
        })
        .reduce(
          (acc, row) => {
            const agreementId = row.vendor;
            if (!acc[agreementId]) {
              acc[agreementId] = [];
            }
            acc[agreementId].push(row.saleId);
            return acc;
          },
          {} as Record<string, string[]>
        )
    ).map(([agreementId, saleIds]) => {
      callSanitizeSalesWithExistingAgreement(
        {
          agreementId,
          saleIds,
        },
        functions,
        {}
      );
    });
    setLoading(true);
    try {
      await Promise.all(updateCalls);
    } catch (error) {
      console.error("Error sanitizing sales:", error);
    }
    setLoading(false);
  };

  return (
    <>
      <Dialog
        title="Add missing artist"
        open={createArtistDialogOpen}
        onOpenChange={setCreateArtistDialogOpen}
        content={
          user &&
          createArtistStagingSale && (
            <AddProducerForm
              functions={functions}
              user={user}
              onSuccess={() => setCreateArtistDialogOpen(false)}
              saleId={createArtistStagingSale?.saleId}
              storeId={store.id}
              initialValues={{
                displayName: createArtistStagingSale?.vendor,
                commission: store.defaultCommission || undefined,
                effectiveDate: createArtistStagingSale?.updatedAt
                  ? subDays(
                      parse(
                        createArtistStagingSale.updatedAt,
                        "dd/MM/yyyy",
                        new Date()
                      ),
                      1
                    )
                  : undefined,
              }}
            />
          )
        }
      />

      <Container>
        <Card>
          <CardSections.Title>
            {salesStaging.length} incomplete sales
          </CardSections.Title>
          <CardSections.Content>
            The following sales are missing vendor information. Add the missing
            information to ensure they are added to the correct reporting and
            invoicing data.
          </CardSections.Content>
        </Card>
        <GridAndSearchContainer>
          <SearchInput
            id="searchSales"
            type="text"
            label="Search sales..."
            icon={<Search />}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
          <GridCard>
            <AgGridReact
              ref={gridRef}
              rowData={rowData}
              columnDefs={colDefs}
              theme={myTheme}
              quickFilterText={searchTerm}
              singleClickEdit
            />
          </GridCard>
          <Button onClick={handleSubmit} loading={loading}>
            Submit
          </Button>
        </GridAndSearchContainer>
      </Container>
    </>
  );
};

export default Page;
