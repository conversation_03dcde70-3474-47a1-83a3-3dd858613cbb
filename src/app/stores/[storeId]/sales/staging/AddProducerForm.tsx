import { acceptedCountries } from "@/constants/acceptedCountryCodes";
import { DefaultApiResponse } from "@/lib/api/types";
import { callHttpsCallableApi } from "@/lib/api/util";
import { Form, FormFieldComponents } from "@gaco/gaco-library";
import { User } from "firebase/auth";
import { Functions, httpsCallable } from "firebase/functions";
import { useMemo, useState } from "react";
import countryList from "react-select-country-list";
import { SanitizeSalesStagingWithAgreementRequest } from "types/requests/sanitizationRequestInterfaces";
import * as yup from "yup";

interface AddProducerFormProps {
  functions: Functions;
  user: User;
  onSuccess?: () => void;
  initialValues?: {
    displayName?: string;
    email?: string;
    country?: string;
    commission?: number;
    effectiveDate?: Date;
  };
  saleId: string;
  storeId: string;
}

export const AddProducerForm = ({
  functions,
  user,
  initialValues,
  saleId,
  storeId,
  onSuccess,
}: AddProducerFormProps) => {
  const [loading, setLoading] = useState<boolean>(false);
  const countryOptions = useMemo(
    () =>
      countryList()
        .getData()
        .filter(({ value }) => acceptedCountries.includes(value)),
    []
  );

  const handleSubmit = async ({
    displayName,
    email,
    country,
    commission,
    effectiveDate,
  }: {
    displayName: string;
    email: string;
    country: string;
    commission: number;
    effectiveDate: Date;
  }) => {
    if (!user) {
      throw new Error("User must be authenticated to add an account");
    }

    setLoading(true);

    try {
      await callHttpsCallableApi(
        httpsCallable<
          SanitizeSalesStagingWithAgreementRequest,
          DefaultApiResponse
        >(functions, "sanitize_sales_staging_with_new_agreement"),
        {
          storeId,
          displayName,
          email,
          commission,
          saleId,
          taxA2: country,
          effectiveDate,
        },
        {}
      );

      onSuccess?.();
    } catch (error) {
      console.error("Error adding artist:", error);
    }

    setLoading(false);
  };

  return (
    <>
      <p>Enter the details for the new artist here.</p>

      <Form
        onSubmit={handleSubmit}
        labelSubmit="Add artist"
        initialValues={initialValues}
        isLoading={loading}
        fields={[
          {
            name: "displayName",
            label: "Name",
            component: FormFieldComponents.TEXT,
          },
          {
            name: "email",
            type: "email",
            label: "Email",
            component: FormFieldComponents.TEXT,
          },
          {
            name: "country",
            label: "Country",
            component: FormFieldComponents.SELECT,
            options: countryOptions,
          },
          {
            name: "commission",
            label: "Comission",
            component: FormFieldComponents.TEXT,
            type: "number",
            min: 0,
            max: 100,
          },
          {
            name: "effectiveDate",
            label: "Contract start date",
            component: FormFieldComponents.DATE_PICKER,
          },
        ]}
        schema={yup.object().shape({
          displayName: yup.string().required(),
          email: yup.string().email().required(),
          country: yup.string().required(),
          commission: yup
            .number()
            .min(0, "Comission must be between 0 and 100")
            .max(100, "Comission must be between 0 and 100")
            .required(),
        })}
      />
    </>
  );
};
