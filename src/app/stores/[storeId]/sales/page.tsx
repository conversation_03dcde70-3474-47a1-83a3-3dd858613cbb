"use client";

import { BarChartResponsive } from "@/components/molecules/BarCharts";
import { ControlsSales } from "@/components/molecules/ControlsSales/ControlsSales";
import { LineChartResponsive } from "@/components/molecules/LineCharts";
import { PieChartResponsive } from "@/components/molecules/PieCharts";
import { TooltipContentCurrency } from "@/components/molecules/TooltipContentCurrency/TooltipContentCurrency";
import { CardSalesListStore } from "@/components/organisms/CardSalesListStore";
import {
  SalesFilterProvider,
  useSalesFiltersContext,
} from "@/contexts/SalesFiltersContext";
import { useStoreContext } from "@/contexts/StoreContext";
import { useAgreements } from "@/hooks/useAgreements";
import { useSales } from "@/hooks/useSales";
import { numberToCurrency } from "@/utils/currencyUtils";
import {
  Alert as AlertRoot,
  Card,
  CardSections,
  Tabs,
  TabsContent,
  Ta<PERSON>List as Ta<PERSON>ListRoot,
  TabsTrigger as TabsTriggerRoot,
  media,
} from "@gaco/gaco-library";
import {
  BarChart as BarChartIcon,
  Calendar,
  ChartColumnIncreasing,
  ChartSpline,
  Package,
  Users,
} from "lucide-react";
import Link from "next/link";
import { useMemo } from "react";
import styled from "styled-components";

const Container = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;

  overflow-y: auto;
  height: 100%;
  padding-bottom: 1rem;
`;

const WidgetCards = styled.div`
  display: flex;
  gap: 1rem;

  flex-shrink: 0;
  flex-direction: row;
`;

const CardSalesWidgetsWithFilteredSales = () => {
  const { filteredSales } = useSalesFiltersContext();

  return (
    <WidgetCards>
      <Card>
        <CardSections.Title>
          Total Revenue
          <BarChartIcon />
        </CardSections.Title>
        <CardSections.Content>
          {numberToCurrency(
            filteredSales
              .map((sale) => sale.totalPrice)
              .reduce((a, b) => a + b, 0),
            "EUR"
          )}
        </CardSections.Content>
      </Card>

      <Card>
        <CardSections.Title>
          Total items sold
          <Package />
        </CardSections.Title>
        <CardSections.Content>{filteredSales.length}</CardSections.Content>
      </Card>
    </WidgetCards>
  );
};

const ChartCards = styled.div`
  display: flex;
  gap: 1rem;

  flex-shrink: 0;
  flex-direction: column;

  ${media.lg`
    flex-direction: row;
  `};
`;

const TabsList = styled(TabsListRoot)`
  margin-left: auto;

  svg {
    height: 1rem;
  }
`;

const TabsTrigger = styled(TabsTriggerRoot)`
  border: 2px solid ${({ theme }) => theme.colors.grayLighter};
`;

interface CardSalesChartsProps {
  status: "loading" | "error" | "success";
}

const CardSalesCharts = ({ status }: CardSalesChartsProps) => {
  const {
    chartData,
    pieChartDataByLabelOption,
    pieChartDataByInterval = [],
    labels,
  } = useSalesFiltersContext();

  return (
    <ChartCards>
      <Card
        error={status === "error" && "Error loading sales"}
        loading={status === "loading" && "Loading sales"}
        info={
          status === "success" && !chartData.length && "No sales data available"
        }
      >
        <Tabs defaultValue="lineChart">
          <TabsList inline>
            <TabsTrigger value="lineChart">
              <ChartSpline />
            </TabsTrigger>
            <TabsTrigger value="barChart">
              <ChartColumnIncreasing />
            </TabsTrigger>
          </TabsList>

          <TabsContent value="lineChart">
            <LineChartResponsive
              data={chartData}
              labels={labels}
              tooltipContent={TooltipContentCurrency}
            />
          </TabsContent>
          <TabsContent value="barChart">
            <BarChartResponsive
              data={chartData}
              labels={labels.map(({ dataKey, stroke, label }) => ({
                dataKey,
                label,
                color: stroke,
              }))}
              tooltipContent={TooltipContentCurrency}
            />
          </TabsContent>
        </Tabs>
      </Card>

      <Card
        error={status === "error" && "Error loading sales"}
        loading={status === "loading" && "Loading sales"}
        info={
          status === "success" && !chartData.length && "No sales data available"
        }
      >
        <Tabs defaultValue="pieChartLabelOption">
          <TabsList inline>
            <TabsTrigger value="pieChartLabelOption">
              <Users />
            </TabsTrigger>
            <TabsTrigger value="pieChartInterval">
              <Calendar />
            </TabsTrigger>
          </TabsList>

          <TabsContent value="pieChartLabelOption">
            <PieChartResponsive
              data={pieChartDataByLabelOption}
              tooltipContent={TooltipContentCurrency}
            />
          </TabsContent>
          <TabsContent value="pieChartInterval">
            <PieChartResponsive
              data={pieChartDataByInterval}
              tooltipContent={TooltipContentCurrency}
            />
          </TabsContent>
        </Tabs>
      </Card>
    </ChartCards>
  );
};

const CardSalesListWithFilteredSales = () => {
  const { filteredSales } = useSalesFiltersContext();

  return <CardSalesListStore sales={filteredSales} title="Sales" />;
};

const Alert = styled(AlertRoot)`
  margin: 0;
`;

const Page = () => {
  const { store } = useStoreContext();
  const {
    status: salesStatus,
    sales,
    salesStaging,
  } = useSales({
    storeId: store.id,
  });
  const {
    acceptedAgreements,
    status: agreementStatus,
    enrichAgreementWithProducer,
  } = useAgreements({
    accountRole: "store",
    storeId: store.id,
  });

  const enrichedAcceptedAgreements = useMemo(
    () =>
      acceptedAgreements.map((agreement) =>
        enrichAgreementWithProducer(agreement)
      ),
    [acceptedAgreements, enrichAgreementWithProducer]
  );

  const status =
    salesStatus === "loading" || agreementStatus === "loading"
      ? "loading"
      : salesStatus === "error" || agreementStatus === "error"
        ? "error"
        : "success";

  return (
    <Container>
      {!!salesStaging?.length && (
        <Alert type="warning">
          <Link href={`/stores/${store.id}/sales/staging`}>
            You have {salesStaging.length} sales items that are missing a linked
            artist
          </Link>
        </Alert>
      )}

      {sales?.length + salesStaging?.length === 0 ? (
        <Alert type="warning">
          Your store currently has no sales.{" "}
          <Link href={`/stores/${store.id}/import`}>Import sales</Link>
        </Alert>
      ) : (
        <></>
      )}

      <SalesFilterProvider
        cacheKey={`${store.id}-sales`}
        sales={sales}
        salesLabelKey="vendor"
        acceptedAgreements={enrichedAcceptedAgreements}
        applicationLabelKey="producerId"
      >
        <ControlsSales multiSelectLabel="Select artists" />

        <CardSalesWidgetsWithFilteredSales />

        <CardSalesCharts status={status} />

        <CardSalesListWithFilteredSales />
      </SalesFilterProvider>
    </Container>
  );
};

export default Page;
