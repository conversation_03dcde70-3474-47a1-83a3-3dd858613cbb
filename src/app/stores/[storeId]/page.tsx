"use client";

import { LineChartTotalSales } from "@/components/molecules/LineCharts/LineChartTotalSales";
import { CardSalesListStoreCardList } from "@/components/organisms/CardSalesListStore";
import { useStoreContext } from "@/contexts/StoreContext";
import { useAgreements } from "@/hooks/useAgreements";
import { useSales } from "@/hooks/useSales";
import { numberToCurrency } from "@/utils/currencyUtils";
import { Card as CardRoot, CardSections, media } from "@gaco/gaco-library";
import { BarChart, Package, Users } from "lucide-react";
import styled from "styled-components";

const OverviewGrid = styled.div`
  height: 100%;
  padding-bottom: 1rem;

  display: grid;
  grid-template-columns: repeat(6, 1fr);
  grid-template-rows: auto 1fr;
  grid-column-gap: 0.5rem;
  grid-row-gap: 0.5rem;

  ${media.sm`
      padding-bottom: 2rem;
      grid-column-gap: 1rem;
      grid-row-gap: 1rem;
  `}
`;

const GridItemTotalSalesList = styled.div`
  overflow: hidden;
  grid-area: 3 / 1 / 3 / 7;

  ${media.md`
    grid-area: 2 / 4 / 3 / 7;
  `}
`;

const CardTotalSalesGraph = styled(CardRoot)`
  grid-area: 2 / 1 / 3 / 7;
  min-height: 25rem;

  ${media.md`
    grid-area: 2 / 1 / 3 / 4;
  `}
`;

const Card = styled(CardRoot)`
  .lucide {
    display: none;
  }
  ${media.md`
    .lucide {
      display: inline;
    }
  `}
`;

const CardSalesList = styled(CardRoot)`
  height: 100%;
  min-height: 300px;
`;

const Page = () => {
  const { store } = useStoreContext();
  const { status, sales, totalSales, totalRevenue } = useSales({
    storeId: store.id,
  });

  const { acceptedAgreements } = useAgreements({
    accountRole: "store",
    storeId: store.id,
  });

  return (
    <OverviewGrid>
      <Card style={{ gridArea: "1 / 1 / 2 / 3" }}>
        <CardSections.Title>
          Total Revenue
          <BarChart />
        </CardSections.Title>
        <CardSections.Content>
          {numberToCurrency(totalRevenue, "EUR")}
        </CardSections.Content>
      </Card>

      <Card style={{ gridArea: "1 / 3 / 2 / 5" }}>
        <CardSections.Title>
          Partnered artists
          <Users />
        </CardSections.Title>
        <CardSections.Content>{acceptedAgreements.length}</CardSections.Content>
      </Card>

      <Card style={{ gridArea: "1 / 5 / 2 / 7" }}>
        <CardSections.Title>
          Total items sold
          <Package />
        </CardSections.Title>
        <CardSections.Content>{totalSales}</CardSections.Content>
      </Card>

      <CardTotalSalesGraph
        error={status === "error" && "Error loading sales"}
        loading={status === "loading" && "Loading sales"}
        info={
          status === "success" && !sales.length && "No sales data available"
        }
      >
        <CardSections.Title>Total Sales</CardSections.Title>

        <LineChartTotalSales sales={sales} cacheKey="storeTotalSales" />
      </CardTotalSalesGraph>

      <GridItemTotalSalesList>
        <CardSalesList
          error={status === "error" && "Error loading sales"}
          loading={status === "loading" && "Loading sales"}
          info={
            status === "success" && !sales.length && "No sales data available"
          }
        >
          <CardSections.Title>Recent sales</CardSections.Title>
          <CardSections.Content>
            <CardSalesListStoreCardList sales={sales} />
          </CardSections.Content>
        </CardSalesList>
      </GridItemTotalSalesList>
    </OverviewGrid>
  );
};

export default Page;
