"use client";

import { PageTitle } from "@/components/ga-ui";
import { RedirectIfAuthenticatedRoute } from "@/components/RedirectIfAuthenticatedRoute";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Card as CardRoot,
  CardSections,
  Form,
  FormFieldComponents,
} from "@gaco/gaco-library";
import {
  Auth,
  getAuth,
  GoogleAuthProvider,
  signInWithEmailAndPassword,
  signInWithPopup,
} from "firebase/auth";
import Link from "next/link";
import { useState } from "react";
import styled from "styled-components";
import * as yup from "yup";

const CenteredContainer = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
`;

const Card = styled(CardRoot)`
  width: 100%;
  max-width: 400px;

  button {
    align-self: stretch;
  }
`;

const CardTitle = styled(CardSections.Title)`
  text-align: center;
`;

const CardContent = styled(CardSections.Content)`
  display: flex;
  flex-direction: column;
  gap: 1rem;
`;

const Footer = styled.div`
  text-align: center;
  font-size: ${({ theme }) => theme.sizes.fonts.sm};
  color: ${({ theme }) => theme.colors.textMuted};

  div {
    margin-top: 0.25rem;
  }
`;

const logInWithGoogle = async (auth: Auth) => {
  const provider = new GoogleAuthProvider();

  await signInWithPopup(auth, provider);
};

export default function Login() {
  const auth = getAuth();

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");

  const handleUserLogin = async (form: { email: string; password: string }) => {
    setLoading(true);
    setError("");

    try {
      await signInWithEmailAndPassword(auth, form.email, form.password);
    } catch (error) {
      console.error("Error logging in:", error);
      setError(
        "Failed to log in. Please check your credentials and try again."
      );
    }

    setLoading(false);
  };

  return (
    <RedirectIfAuthenticatedRoute>
      <CenteredContainer>
        <Link href="/">
          <PageTitle>GACO</PageTitle>
        </Link>

        <Card loading={loading ? "Logging in" : false}>
          <CardTitle>Log in to your account</CardTitle>

          <CardContent>
            {error && <Alert type="error">{error}</Alert>}

            <Form
              onSubmit={handleUserLogin}
              labelSubmit="Log in"
              fields={[
                {
                  name: "email",
                  label: "Email",
                  component: FormFieldComponents.TEXT,
                  type: "email",
                },
                {
                  name: "password",
                  label: "Password",
                  component: FormFieldComponents.TEXT,
                  type: "password",
                },
              ]}
              schema={yup.object().shape({
                email: yup.string().email().required(),
                password: yup.string().required(),
              })}
            />

            <Button variant="outline" onClick={() => logInWithGoogle(auth)}>
              Log in with Google
            </Button>

            <Footer>
              <Link href="/reset-password">Forgot your password?</Link>
              <div>
                Don&apos;t have an account? <Link href="/signup">Sign up</Link>
              </div>
            </Footer>
          </CardContent>
        </Card>
      </CenteredContainer>
    </RedirectIfAuthenticatedRoute>
  );
}
