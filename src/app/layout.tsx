import { FirebaseComponents } from "@/components/FirebaseComponents";
import { LayoutCenteredWithNav } from "@/components/layouts/LayoutCenteredWithNav";
import { ThemeClient } from "@/contexts/ThemeClient";
import { UserProvider } from "@/contexts/UserContext";
import { figtree } from "@/lib/fonts";
import StyledComponentsRegistry from "@/lib/StyledComponentsRegistry";
import { AllCommunityModule, ModuleRegistry } from "ag-grid-community";
import type { Metadata } from "next";
import { ReactNode } from "react";
import { ToastContainer } from "react-toastify";

// Register all Community features
ModuleRegistry.registerModules([AllCommunityModule]);

export const metadata: Metadata = {
  title: "GACO",
  description: "Connecting stores and artists!",
};

const RootLayout = ({
  children,
}: Readonly<{
  children: ReactNode;
}>) => {
  return (
    <html lang="en">
      <body className={figtree.className}>
        <StyledComponentsRegistry>
          <FirebaseComponents>
            <UserProvider>
              <ThemeClient>
                <LayoutCenteredWithNav>{children}</LayoutCenteredWithNav>
                <ToastContainer />
              </ThemeClient>
            </UserProvider>
          </FirebaseComponents>
        </StyledComponentsRegistry>
      </body>
    </html>
  );
};

export { RootLayout };
export default RootLayout;
