import { Functions, httpsCallable } from "firebase/functions";
import {
  CreateSalesReportRequest,
  DeleteSalesReportRequest,
} from "types/requests/salesReportRequestInterfaces";
import { callHttpsCallableApiSettings, DefaultApiResponse } from "./types";
import { callHttpsCallableApi } from "./util";

export const callCreateSalesReports = (
  payload: CreateSalesReportRequest,
  functions: Functions,
  settings: callHttpsCallableApiSettings = {}
) =>
  callHttpsCallableApi(
    httpsCallable<CreateSalesReportRequest, DefaultApiResponse>(
      functions,
      "create_sales_reports"
    ),
    payload,
    settings
  );

export const callDeleteSalesReport = (
  salesReportId: string,
  functions: Functions,
  settings: callHttpsCallableApiSettings = {}
) =>
  callHttpsCallableApi(
    httpsCallable<DeleteSalesReportRequest, DefaultApiResponse>(
      functions,
      "delete_sales_report"
    ),
    { salesReportId },
    settings
  );
