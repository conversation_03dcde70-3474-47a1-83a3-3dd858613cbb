import { Functions, httpsCallable } from "firebase/functions";
import {
  ApproveAgreementRequest,
  CreateAgreementRequest,
  DeleteAgreementRequest,
  RejectAgreementRequest,
  SubmitAgreementForApprovalRequest,
  TerminateAgreementRequest,
  UpdateDraftAgreementRequest,
} from "types/requests/agreementsInterfaces";
import { callHttpsCallableApiSettings, DefaultApiResponse } from "./types";
import { callHttpsCallableApi } from "./util";

export const callDeleteAgreementApi = (
  payload: { agreementId: string },
  functions: Functions,
  settings: callHttpsCallableApiSettings = {}
) =>
  callHttpsCallableApi(
    httpsCallable<DeleteAgreementRequest, DefaultApiResponse>(
      functions,
      "delete_agreement"
    ),
    payload,
    settings
  );

export const callSubmitDraftAgreementApi = (
  payload: SubmitAgreementForApprovalRequest,
  functions: Functions,
  settings: callHttpsCallableApiSettings = {}
) =>
  callHttpsCallableApi(
    httpsCallable<SubmitAgreementForApprovalRequest, DefaultApiResponse>(
      functions,
      "submit_agreement_for_approval"
    ),
    payload,
    settings
  );

export const callUpdateDraftAgreementApi = (
  payload: UpdateDraftAgreementRequest,
  functions: Functions,
  settings: callHttpsCallableApiSettings = {}
) =>
  callHttpsCallableApi(
    httpsCallable<UpdateDraftAgreementRequest, DefaultApiResponse>(
      functions,
      "update_draft_agreement"
    ),
    payload,
    settings
  );

export const callCreateDraftAgreementApi = async (
  payload: CreateAgreementRequest,
  functions: Functions,
  settings: callHttpsCallableApiSettings = {}
) =>
  callHttpsCallableApi(
    httpsCallable<CreateAgreementRequest, DefaultApiResponse>(
      functions,
      "create_agreement"
    ),
    payload,
    settings
  );

export const callApproveAgreementApi = async (
  payload: ApproveAgreementRequest,
  functions: Functions,
  settings: callHttpsCallableApiSettings = {}
) =>
  callHttpsCallableApi(
    httpsCallable<ApproveAgreementRequest, DefaultApiResponse>(
      functions,
      "approve_agreement"
    ),
    payload,
    settings
  );

export const callTerminateAgreementApi = async (
  payload: TerminateAgreementRequest,
  functions: Functions,
  settings: callHttpsCallableApiSettings = {}
) =>
  callHttpsCallableApi(
    httpsCallable<TerminateAgreementRequest, DefaultApiResponse>(
      functions,
      "terminate_agreement"
    ),
    payload,
    settings
  );

export const callRejectAgreementApi = async (
  payload: RejectAgreementRequest,
  functions: Functions,
  settings: callHttpsCallableApiSettings = {}
) =>
  callHttpsCallableApi(
    httpsCallable<RejectAgreementRequest, DefaultApiResponse>(
      functions,
      "reject_agreement"
    ),
    payload,
    settings
  );
