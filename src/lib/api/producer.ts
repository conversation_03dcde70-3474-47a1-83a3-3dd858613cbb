import { Functions, httpsCallable } from "firebase/functions";
import {
  CreateProducerRequest,
  DeleteProducerRequest,
} from "types/requests/producerRequestInterfaces";
import { callHttpsCallableApiSettings, DefaultApiResponse } from "./types";
import { callHttpsCallableApi } from "./util";

export const callCreateProducerApi = async (
  payload: CreateProducerRequest,
  functions: Functions,
  settings: callHttpsCallableApiSettings = {}
) =>
  callHttpsCallableApi(
    httpsCallable<CreateProducerRequest, DefaultApiResponse>(
      functions,
      "create_producer"
    ),
    payload,
    settings
  );

export const callDeleteProducerApi = async (
  payload: DeleteProducerRequest,
  functions: Functions,
  settings: callHttpsCallableApiSettings = {}
) =>
  callHttpsCallableApi(
    httpsCallable<DeleteProducerRequest, DefaultApiResponse>(
      functions,
      "delete_producer"
    ),
    payload,
    settings
  );
