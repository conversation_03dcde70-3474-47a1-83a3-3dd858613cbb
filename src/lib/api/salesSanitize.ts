import { Functions, httpsCallable } from "firebase/functions";
import { SanitizeSalesStagingWithExistingAgreementRequest } from "types/requests/agreementsInterfaces";
import { callHttpsCallableApiSettings, DefaultApiResponse } from "./types";
import { callHttpsCallableApi } from "./util";

export const callSanitizeSalesWithExistingAgreement = (
  payload: SanitizeSalesStagingWithExistingAgreementRequest,
  functions: Functions,
  settings: callHttpsCallableApiSettings = {}
) =>
  callHttpsCallableApi(
    httpsCallable<
      SanitizeSalesStagingWithExistingAgreementRequest,
      DefaultApiResponse
    >(functions, "sanitize_sales_staging_with_existing_agreement"),
    payload,
    settings
  );
