import { Functions, httpsCallable } from "firebase/functions";
import { GetShopifyOrdersWithDynamicQueryRequest } from "types/requests/shopifyRequestInterfaces";
import {
  CreateStoreRequest,
  DeleteStoreRequest,
} from "types/requests/storeRequestInterfaces";
import { callHttpsCallableApiSettings, DefaultApiResponse } from "./types";
import { callHttpsCallableApi } from "./util";

export const callCreateStoreApi = async (
  payload: CreateStoreRequest,
  functions: Functions,
  settings: callHttpsCallableApiSettings = {}
) =>
  callHttpsCallableApi(
    httpsCallable<CreateStoreRequest, DefaultApiResponse>(
      functions,
      "create_store"
    ),
    payload,
    settings
  );

export const callDeleteStoreApi = async (
  payload: DeleteStoreRequest,
  functions: Functions,
  settings: callHttpsCallableApiSettings = {}
) =>
  callHttpsCallableApi(
    httpsCallable<DeleteStoreRequest, DefaultApiResponse>(
      functions,
      "delete_store"
    ),
    payload,
    settings
  );

export const CallGetShopifySalesApi = async (
  payload: GetShopifyOrdersWithDynamicQueryRequest,
  functions: Functions,
  settings: callHttpsCallableApiSettings = {}
) =>
  callHttpsCallableApi(
    httpsCallable<GetShopifyOrdersWithDynamicQueryRequest, DefaultApiResponse>(
      functions,
      "get_shopify_orders_with_dynamic_query"
    ),
    payload,
    settings
  );
