import { HttpsCallableResult } from "firebase/functions";
import { toast } from "react-toastify";
import { callHttpsCallableApiSettings } from "./types";

export const callHttpsCallableApi = async <T>(
  httpsCallable: (payload: T) => Promise<
    HttpsCallableResult<{
      success: boolean;
      message?: string;
      data?: unknown;
      code?: number;
    }>
  >,
  payload: T,
  settings: callHttpsCallableApiSettings = {}
) => {
  const response = await httpsCallable(payload);

  if (!response?.data || response?.data?.success === false) {
    if (!settings.hideToast)
      toast.error(`${response?.data?.code} - Error calling API`);
    throw new Error(
      "Error in API call: " + JSON.stringify(response.data.message)
    );
  } else {
    return response.data;
  }
};
