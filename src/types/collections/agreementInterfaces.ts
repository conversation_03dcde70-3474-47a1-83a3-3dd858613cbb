import { Timestamp } from "firebase/firestore";
import { Agreement as AgreementRoot } from "types/collections/agreementInterfaces";

export interface Agreement
  extends Omit<
    AgreementRoot,
    "createdAt" | "effectiveDate" | "expirationDate"
  > {
  id: string;
  createdAt: Timestamp;
  effectiveDate: Timestamp;
  expirationDate: Timestamp;
}

export enum AgreementStatus {
  DRAFT = "draft",
  ACTIVE = "active",
  REJECTED = "rejected",
  PENDING = "pending_approval",
  EXPIRED = "expired",
  TERMINATED = "terminated",
}
