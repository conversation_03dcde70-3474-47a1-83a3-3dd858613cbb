import { Timestamp } from "firebase-admin/firestore";
import {
  Sale as SaleRoot,
  SalesGold as SalesGoldRoot,
  SalesStaging as SalesStagingRoot,
} from "types/collections/salesInterfaces";

export interface Sale extends Omit<SaleRoot, "updatedAt"> {
  id: string;
  updatedAt: Timestamp;
}

export interface SalesGold extends Omit<SalesGoldRoot, "updatedAt"> {
  id: string;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface SalesStaging extends Omit<SalesStagingRoot, "updatedAt"> {
  id: string;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}
