import { TabsTrigger } from "@gaco/gaco-library";
import { useRouter } from "next/navigation";
import { ComponentProps } from "react";

interface TabsTriggerLinkProps extends ComponentProps<typeof TabsTrigger> {
  href: string;
}

export const TabsTriggerLink = ({
  href,
  children,
  ...props
}: TabsTriggerLinkProps) => {
  const router = useRouter();
  return (
    <TabsTrigger onClick={() => router.push(href)} {...props}>
      {children}
    </TabsTrigger>
  );
};
