import { lora } from "@/lib/fonts";
import { PageTitle as PageTitleRoot } from "@gaco/gaco-library";
import { ComponentProps } from "react";
import styled from "styled-components";

const StyledPageTitle = styled(PageTitleRoot)`
  margin-top: 0;
  margin-bottom: 0.25rem;
`;

export const PageTitle = ({
  style,
  ...props
}: ComponentProps<typeof PageTitleRoot>) => {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars, @typescript-eslint/no-explicit-any
  const { ref, ...restProps } = props as any;

  return (
    <StyledPageTitle
      {...restProps}
      style={{
        ...style,
        ...lora.style,
      }}
    />
  );
};
