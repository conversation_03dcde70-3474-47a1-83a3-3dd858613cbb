import { Button, ButtonProps } from "@gaco/gaco-library";
import Link from "next/link";

interface ButtonLinkProps extends ButtonProps {
  href: string;
  className?: string;
  target?: string;
  rel?: string;
}

export const ButtonLink = ({
  href,
  children,
  target,
  rel = "noopener noreferrer",
  ...props
}: ButtonLinkProps) => {
  return (
    <Link href={href} target={target} rel={rel}>
      <Button {...props}>{children}</Button>
    </Link>
  );
};
