import { Button, ButtonProps, Small } from "@gaco/gaco-library";
import { useState } from "react";
import styled from "styled-components";

const FileNames = styled(Small)`
  display: block;
  margin-bottom: 0.5rem;
`;

interface ButtonFileUploadProps extends ButtonProps {
  onUpload: (files: FileList | null) => void;
}

export const ButtonFileUpload = ({
  onUpload,
  ...props
}: ButtonFileUploadProps) => {
  const [files, setFiles] = useState<FileList>();
  const [loading, setLoading] = useState(false);

  const handleFileChange = async (fileList: FileList | null) => {
    if (fileList && fileList.length > 0) {
      setFiles(fileList);
      setLoading(true);

      try {
        await onUpload(fileList);
      } catch (error) {
        console.error("Error uploading file:", error);
      } finally {
        setLoading(false);
      }
    }
  };

  return (
    <>
      {files?.length && (
        <FileNames>
          {Array.from(files)
            .map((file) => file.name)
            .join(", ")}
        </FileNames>
      )}
      <Button
        type="file"
        loading={loading}
        onFileChange={handleFileChange}
        {...props}
      />
    </>
  );
};
