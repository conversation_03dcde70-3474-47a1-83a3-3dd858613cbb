"use client";

import { firebaseConfig } from "@/firebase/config";
import { connectAuthEmulator, getAuth } from "firebase/auth";
import { connectFirestoreEmulator, getFirestore } from "firebase/firestore";
import { connectFunctionsEmulator, getFunctions } from "firebase/functions";
import { connectStorageEmulator, getStorage } from "firebase/storage";
import { PropsWithChildren } from "react";
import {
  AuthProvider,
  FirebaseAppProvider,
  FirestoreProvider,
  FunctionsProvider,
  StorageProvider,
  useFirebaseApp,
} from "reactfire";

export const FirebaseComponents = ({ children }: PropsWithChildren) => {
  return (
    <FirebaseAppProvider firebaseConfig={firebaseConfig}>
      <FirebaseComponentsInner>{children}</FirebaseComponentsInner>
    </FirebaseAppProvider>
  );
};

export const FirebaseComponentsInner = ({ children }: PropsWithChildren) => {
  const app = useFirebaseApp();

  const auth = getAuth(app);
  const firestoreInstance = getFirestore(app);
  const storageInstance = getStorage(app);
  const functionsInstance = getFunctions(app, "europe-west3");

  if (process.env.NEXT_PUBLIC_FIRESTORE_EMULATOR === "true") {
    connectFirestoreEmulator(firestoreInstance, "localhost", 9000);
    connectStorageEmulator(storageInstance, "localhost", 9199);
    connectFunctionsEmulator(functionsInstance, "localhost", 5001);
    connectAuthEmulator(auth, "http://localhost:9099");
  }

  return (
    <AuthProvider sdk={auth}>
      <FunctionsProvider sdk={functionsInstance}>
        <StorageProvider sdk={storageInstance}>
          <FirestoreProvider sdk={firestoreInstance}>
            {children}
          </FirestoreProvider>
        </StorageProvider>
      </FunctionsProvider>
    </AuthProvider>
  );
};
