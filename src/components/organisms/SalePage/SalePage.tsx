import { useSale } from "@/hooks/useSale";
import { numberToCurrency } from "@/utils/currencyUtils";
import { getFirebaseDateTime } from "@/utils/timeUtils";
import {
  Card,
  CardSections,
  DetailsList,
  H1,
  LoadingOverlay,
  media,
} from "@gaco/gaco-library";
import styled from "styled-components";
import { SalePageProducerCard } from "./SalePageProducerCard";
import { SalePageStoreCard } from "./SalePageStoreCard";

const Columns = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 1rem;

  > div {
    flex: 1;
  }

  ${media.md`
    flex-direction: row;
  `}
`;

const Header = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
  margin-bottom: 1rem;

  > div {
    flex: 1;
  }

  i {
    font-size: ${({ theme }) => theme.sizes.fonts.sm};
  }

  ${H1} {
    margin: 0;
  }

  ${media.md`
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  `}
`;

const CardSale = styled(Card)`
  margin-bottom: 1rem;
`;

interface SalePageProps {
  saleId: string;
}

export const SalePage = ({ saleId }: SalePageProps) => {
  const { sale, status } = useSale({ saleId });

  if (status === "loading") {
    return <LoadingOverlay>Loading sale</LoadingOverlay>;
  }

  return (
    <>
      <Header>
        <H1>{sale.title}</H1>
        <i>{getFirebaseDateTime(sale.updatedAt)}</i>
      </Header>
      <CardSale>
        <CardSections.Content>
          <DetailsList>
            <dd>Product</dd>
            <dt>{sale.title}</dt>
            <dd>Variant</dd>
            <dt>{sale.variantDisplayName}</dt>
            <dd>Quantity</dd>
            <dt>{sale.quantity}</dt>
            {!!sale.discount && (
              <>
                <dd>Subtotal</dd>
                <dt>{numberToCurrency(sale.subtotal, sale.currency)}</dt>
                <dd>Discount</dd>
                <dt>{numberToCurrency(sale.discount, sale.currency)}</dt>
              </>
            )}
            <dd>Total</dd>
            <dt>
              <strong>
                {numberToCurrency(sale.totalPrice, sale.currency)}
              </strong>
            </dt>
          </DetailsList>
        </CardSections.Content>
      </CardSale>
      <Columns>
        <SalePageProducerCard sale={sale} />

        <SalePageStoreCard storeId={sale.storeId} />
      </Columns>
    </>
  );
};
