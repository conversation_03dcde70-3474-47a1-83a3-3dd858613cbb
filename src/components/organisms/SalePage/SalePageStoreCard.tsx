import { Map } from "@/components/ga-ui/Map";
import { useStore } from "@/hooks/useStore";
import { formatAddressToOneLine } from "@/utils/addressUtils";
import {
  Card,
  CardSections,
  DetailsList,
  LoadingOverlay,
} from "@gaco/gaco-library";
import { ExternalLink } from "lucide-react";
import Link from "next/link";
import { useMemo } from "react";
import styled from "styled-components";

const MAP_HEIGHT = "20rem";

const CardStore = styled(Card)`
  padding-bottom: calc(${MAP_HEIGHT} + 1.5rem);
`;

const MapContainer = styled.div`
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;

  height: ${MAP_HEIGHT};
`;

const CardSectionsTitle = styled(CardSections.Title)`
  a {
    color: ${({ theme }) => theme.colors.text};
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;

    svg {
      width: 1rem;
      margin-bottom: 0.25rem;
    }
  }
`;

export const SalePageStoreCard = ({ storeId }: { storeId: string }) => {
  const { store, status } = useStore({
    storeId,
  });

  const address = useMemo(
    () => (store?.address ? formatAddressToOneLine(store?.address) : undefined),
    [store]
  );

  if (status === "loading") {
    return <LoadingOverlay>Loading store</LoadingOverlay>;
  }

  return (
    <CardStore>
      <CardSectionsTitle>
        <Link href={`/stores/${store.id}`} target="_blank">
          Store <ExternalLink />
        </Link>
      </CardSectionsTitle>
      <DetailsList>
        <dd>Name</dd>
        <dt>{store.displayName}</dt>
        <dd>Address</dd>
        <dt>{address || "No address provided"}</dt>
        <dd>Email</dd>
        <dt>
          <a href={`MAILTO:${store.email}`} title={`email ${store.email}`}>
            {store.email}
          </a>
        </dt>
      </DetailsList>
      <MapContainer>
        <Map markersAddresses={[address || ""]} />
      </MapContainer>
    </CardStore>
  );
};
