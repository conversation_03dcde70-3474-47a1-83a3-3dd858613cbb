import { useUserData } from "@/contexts/UserContext";
import { useProducer } from "@/hooks/useProducer";
import { SalesGold, SalesStaging } from "@/types/collections/salesInterfaces";
import { isSalesGold } from "@/utils/typeGuardUtils";
import {
  Alert,
  Card,
  CardSections,
  DetailsList,
  LoadingOverlay,
} from "@gaco/gaco-library";
import { ExternalLink } from "lucide-react";
import Link from "next/link";
import styled from "styled-components";

const CardSectionsTitle = styled(CardSections.Title)`
  a {
    color: ${({ theme }) => theme.colors.text};
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;

    svg {
      width: 1rem;
      margin-bottom: 0.25rem;
    }
  }
`;

export const SalePageProducerCard = ({
  sale,
}: {
  sale: SalesGold | SalesStaging;
}) => {
  if (!sale) {
    return <LoadingOverlay>Loading sale</LoadingOverlay>;
  }

  const hasProducerId = isSalesGold(sale);

  return (
    <Card>
      {hasProducerId ? (
        <SalePageProducerCardInner producerId={sale.producerId} />
      ) : (
        <SalePageProducerCardInnerNoProducer sale={sale} />
      )}
    </Card>
  );
};

interface SalePageProducerCardInnnerProps {
  producerId: string;
}

const SalePageProducerCardInner = ({
  producerId,
}: SalePageProducerCardInnnerProps) => {
  const { producer, status } = useProducer({
    producerId,
  });

  if (status === "loading") {
    return <LoadingOverlay>Loading artist</LoadingOverlay>;
  }

  return (
    <>
      <CardSectionsTitle>
        <Link href={`/artists/${producer.id}`} target="_blank">
          Artist <ExternalLink />
        </Link>
      </CardSectionsTitle>
      <DetailsList>
        <dd>Name</dd>
        <dt>{producer.displayName}</dt>
        <dd>Email</dd>
        <dt>
          <a
            href={`MAILTO:${producer.email}`}
            title={`email ${producer.email}`}
          >
            {producer.email}
          </a>
        </dt>
      </DetailsList>
    </>
  );
};

interface SalePageProducerCardInnerNoProducerProps {
  sale: SalesStaging;
}

const SalePageProducerCardInnerNoProducer = ({
  sale,
}: SalePageProducerCardInnerNoProducerProps) => {
  const { storeDocs } = useUserData();

  const isSaleOwner = storeDocs.some((store) => store.id === sale.storeId);

  return (
    <>
      <CardSectionsTitle>Artist</CardSectionsTitle>
      <DetailsList>
        <dd>Name</dd>
        <dt>{sale.vendor}</dt>
      </DetailsList>

      <Alert type="warning">
        This sale is not linked to an agreement.{" "}
        {isSaleOwner && (
          <Link href={`/stores/${sale.storeId}/sales/staging`}>Link sales</Link>
        )}
      </Alert>
    </>
  );
};
