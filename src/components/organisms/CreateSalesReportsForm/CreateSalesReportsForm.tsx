import { useAgreements } from "@/hooks/useAgreements";
import { useFirebaseFunctions } from "@/hooks/useFirebaseFunctions";
import { callCreateSalesReports } from "@/lib/api";
import {
  Alert,
  FormFieldComponents,
  Form as FormRoot,
  media,
} from "@gaco/gaco-library";
import { useMemo, useState } from "react";
import styled from "styled-components";
import { Role } from "types/collections/agreementInterfaces";
import * as yup from "yup";

interface CreateSalesReportsFormProps {
  storeId: string;
  role: Role;
}

const Form = styled(FormRoot)`
  width: 100%;

  flex-direction: column;

  ${media.md`
    flex-direction: row;
  `}
`;

export const CreateSalesReportsForm = ({
  storeId,
  role,
}: CreateSalesReportsFormProps) => {
  const functions = useFirebaseFunctions();
  const {
    expiredAgreements,
    acceptedAgreements,
    enrichAgreementWithProducer,
    enrichAgreementWithStore,
  } = useAgreements({
    storeId,
    accountRole: role,
  });

  const [success, setSuccess] = useState("");
  const [loading, setLoading] = useState(false);

  const handleCreateSalesReport = async ({
    startDate,
    endDate,
    producerId,
  }: {
    startDate: string;
    endDate: string;
    producerId: string;
  }) => {
    setSuccess("");
    setLoading(true);

    try {
      await callCreateSalesReports(
        {
          startDate: new Date(startDate),
          endDate: new Date(endDate),
          producerId,
          storeId,
        },
        functions,
        {}
      );

      setSuccess("Sales reports generated successfully!");
    } catch (error) {
      console.error("Error generating sales reports:", error);
    }

    setLoading(false);
  };

  const today = useMemo(() => new Date().toISOString().split("T")[0], []);

  const producersOptions = useMemo(() => {
    return [...acceptedAgreements, ...expiredAgreements]
      .map((agreement) => {
        if (role === "store") {
          const enrichedAgreement = enrichAgreementWithProducer(agreement);
          return {
            value: enrichedAgreement.producer.id,
            label: enrichedAgreement.producer.displayName,
          };
        } else if (role === "producer") {
          const enrichedAgreement = enrichAgreementWithStore(agreement);
          return {
            value: enrichedAgreement.store.id,
            label: enrichedAgreement.store.displayName,
          };
        }
      })
      .filter(
        (
          a
        ): a is {
          value: string;
          label: string;
        } => !!a
      );
  }, [
    acceptedAgreements,
    expiredAgreements,
    enrichAgreementWithProducer,
    enrichAgreementWithStore,
    role,
  ]);

  return (
    <>
      {success && <Alert type="success">{success}</Alert>}

      <Form
        inline
        onSubmit={({ startDate, endDate, producer }) =>
          handleCreateSalesReport({ startDate, endDate, producerId: producer })
        }
        labelSubmit="Create Sales Reports"
        fields={[
          {
            name: "startDate",
            component: FormFieldComponents.DATE_PICKER,
            label: "Start Date",
            max: today,
          },
          {
            name: "endDate",
            component: FormFieldComponents.DATE_PICKER,
            label: "End Date",
            max: today,
          },
          {
            name: "producer",
            component: FormFieldComponents.SELECT,
            label: "Artist",
            options: producersOptions,
          },
        ]}
        schema={yup.object().shape({
          startDate: yup.string().required(),
          endDate: yup.string().required(),
          producer: yup.string().required(),
        })}
        isLoading={loading}
      />
    </>
  );
};
