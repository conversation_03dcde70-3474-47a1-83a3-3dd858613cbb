"use client";

import { SalesGold, SalesStaging } from "@/types/collections/salesInterfaces";
import { <PERSON><PERSON>, CardList, H2, TextInput } from "@gaco/gaco-library";
import { Search } from "lucide-react";
import { useState } from "react";
import styled from "styled-components";
import { CardSaleStore } from "./CardSaleStore";

interface CardSalesListStoreProps {
  sales: (SalesGold | SalesStaging)[];
}

export const CardSalesListStoreCardList = ({
  sales,
}: CardSalesListStoreProps) => {
  return (
    <CardList>
      {sales.map((sale) => (
        <CardSaleStore key={sale.id} sale={sale} storeId={sale.storeId} />
      ))}
    </CardList>
  );
};

const SearchInput = styled(TextInput)`
  padding-top: 0;
`;

const TitleBar = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;

  ${H2} {
    margin: 0;
  }
`;

interface CardSalesListStoreProps {
  className?: string;
  sales: (SalesGold | SalesStaging)[];
  title?: string;
}

export const CardSalesListStore = ({
  sales,
  title = "Sales",
  ...props
}: CardSalesListStoreProps) => {
  const [searchTerm, setSearchTerm] = useState("");

  const filteredSales = sales.filter(
    (sale) =>
      sale.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      sale.variantDisplayName
        ?.toLowerCase()
        .includes(searchTerm.toLowerCase()) ||
      ("producerDisplayName" in sale ? sale.producerDisplayName : "unknown")
        .toLowerCase()
        .includes(searchTerm.toLowerCase())
  );

  return (
    <div {...props}>
      <TitleBar>
        {title && <H2>{title}</H2>}

        <SearchInput
          id="searchSales"
          type="text"
          label="Search sales..."
          icon={<Search />}
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
        />
      </TitleBar>

      {filteredSales.length === 0 ? (
        <Alert type="info">No sales found</Alert>
      ) : (
        <CardSalesListStoreCardList sales={filteredSales} />
      )}
    </div>
  );
};
