import { SalesGold, SalesStaging } from "@/types/collections/salesInterfaces";
import { numberToCurrency } from "@/utils/currencyUtils";
import { getFirebaseDate } from "@/utils/timeUtils";
import { Card, CardSections, CardSize, DetailsList } from "@gaco/gaco-library";
import Link from "next/link";
import styled from "styled-components";

interface CardSaleStoreProps {
  sale: SalesGold | SalesStaging;
  storeId: string;
}

const CardRightColumn = styled.div`
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: flex-end;
`;

export const CardSaleStore = ({ sale, storeId }: CardSaleStoreProps) => {
  return (
    <Card size={CardSize.sm}>
      <CardSections.Title>
        <Link href={`/stores/${storeId}/sales/${sale.id}`}>{sale.title}</Link>
      </CardSections.Title>
      <CardSections.RightColumn>
        <CardRightColumn>
          <span>{numberToCurrency(sale.totalPrice, sale.currency)}</span>
        </CardRightColumn>
      </CardSections.RightColumn>
      <CardSections.Content>
        <DetailsList>
          <dd>Artist</dd>
          <dt>
            {"producerDisplayName" in sale
              ? sale.producerDisplayName
              : "Unknown"}
          </dt>
          <dd>Variant</dd>
          <dt>{sale.variantDisplayName}</dt>
          <dd>Date</dd>
          <dt>{getFirebaseDate(sale.updatedAt)}</dt>
        </DetailsList>
      </CardSections.Content>
    </Card>
  );
};
