import {
  <PERSON><PERSON>,
  Button,
  Dialog,
  Form,
  FormFieldComponents,
} from "@gaco/gaco-library";
import { useState } from "react";
import { Agreement } from "src/types/collections/agreementInterfaces";
import styled from "styled-components";
import { UpdateDraftAgreementRequest } from "types/requests/agreementsInterfaces";
import * as yup from "yup";

const DeleteButton = styled(Button)`
  position: absolute;
  bottom: 1rem;
  right: 1rem;
`;

interface DialogAgreementUpdateFormProps {
  initialValues?: Partial<Agreement>;
  onSubmit: (values: UpdateDraftAgreementRequest) => void;
  onDelete?: () => void;
  open: boolean;
  setOpen: (open: boolean) => void;
}

export const DialogAgreementUpdateForm = ({
  initialValues,
  onSubmit,
  onDelete,
  open,
  setOpen,
}: DialogAgreementUpdateFormProps) => {
  const [error, setError] = useState("");
  const [loading, setLoading] = useState(false);
  const [deleteLoading, setDeleteLoading] = useState(false);

  const handleOfferSubmit = async (
    updateDraftAgreementRequest: UpdateDraftAgreementRequest
  ) => {
    setLoading(true);
    setError("");

    try {
      await onSubmit(updateDraftAgreementRequest);
      setOpen(false);
    } catch (error) {
      setError("Error updating agreement. Please try again.");
      console.error("Error sending agreement:", error);
    }

    setLoading(false);
  };

  const handleDelete = async () => {
    setDeleteLoading(true);
    setError("");

    try {
      if (onDelete) {
        await onDelete();
      }
      setOpen(false);
    } catch (error) {
      setError("Error deleting agreement. Please try again.");
      console.error("Error deleting agreement:", error);
    }

    setDeleteLoading(false);
  };

  return (
    <Dialog open={open} onOpenChange={setOpen} title="Edit Offer">
      {error && <Alert type="error">{error}</Alert>}
      <Form
        onSubmit={handleOfferSubmit}
        fields={[
          {
            fields: [
              {
                name: "title",
                component: FormFieldComponents.TEXT,
                label: "Title",
                type: "text",
              },
              {
                name: "commission",
                component: FormFieldComponents.TEXT,
                label: "Commission percentage",
                type: "number",
                min: 0,
                max: 100,
              },
            ],
          },
          {
            title: "Dates",
            columns: 2,
            fields: [
              {
                name: "effectiveDate",
                component: FormFieldComponents.DATE_PICKER,
                label: "Effective Date",
                type: "date",
              },
              {
                name: "expirationDate",
                component: FormFieldComponents.DATE_PICKER,
                label: "Expiration Date",
                type: "date",
              },
            ],
          },
        ]}
        isLoading={loading}
        labelSubmit="Update Offer"
        schema={yup.object().shape({
          title: yup.string().required("Title is required"),
          commission: yup
            .number()
            .min(0, "Commission must be at least 0%")
            .max(100, "Commission must be at most 100%")
            .required("Commission is required"),
          effectiveDate: yup.date().nullable(),
          expirationDate: yup.date().nullable(),
        })}
        initialValues={{
          ...initialValues,
          effectiveDate: initialValues?.effectiveDate?.toDate() || new Date(),
          expirationDate: initialValues?.expirationDate?.toDate(),
        }}
      />

      {onDelete && (
        <DeleteButton
          variant="destructive"
          onClick={handleDelete}
          loading={deleteLoading}
        >
          Delete
        </DeleteButton>
      )}
    </Dialog>
  );
};
