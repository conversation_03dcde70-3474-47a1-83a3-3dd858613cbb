"use client";

import { ResponsiveContainer } from "@/components/ga-ui";
import { ButtonLink as ButtonLinkRoot } from "@/components/ga-ui/ButtonLink";

import { useUserData } from "@/contexts/UserContext";
import { lora } from "@/lib/fonts";
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
  Button as ButtonRoot,
  DropdownMenu,
  H1,
  media,
} from "@gaco/gaco-library";
import { getAuth } from "firebase/auth";
import { LogOut, User, UserPen, UserPlus } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useMemo } from "react";
import { useUser } from "reactfire";
import styled, { css } from "styled-components";

export interface NavigationLink {
  name: string;
  href?: string;
  onClick?: () => void;
}

interface NavigationProps {
  links?: NavigationLink[];
}

const Brand = styled(H1)`
  color: ${({ theme }) => theme.colors.text};
  margin-right: 1rem;
  margin-bottom: 0;
  margin-top: 0;
`;

const Nav = styled.nav`
  box-shadow: ${({ theme }) => theme.shadows.card};
  background-color: ${({ theme }) => theme.colors.contentBg};
`;

const NavInner = styled(ResponsiveContainer)`
  display: flex;
  justify-content: space-between;

  padding: 0.5rem 1rem;

  ${media.sm`
      padding: 1rem 2rem;
  `}
`;

const NavItemsGroup = styled.div`
  display: flex;
  align-items: center;

  a {
    text-decoration: none;
  }
`;

const ButtonStyling = css`
  color: ${({ theme }) => theme.colors.text};
  &:hover {
    color: ${({ theme }) => theme.colors.text};
    background-color: ${({ theme }) => theme.colors.mutedBg};
  }
`;

const Button = styled(ButtonRoot)`
  ${ButtonStyling}
`;
const ButtonLink = styled(ButtonLinkRoot)`
  ${ButtonStyling}

  svg {
    margin-right: 0.5rem;
  }
`;
const AvatarButton = styled(Button)`
  &:hover {
    background-color: transparent;
  }
`;

export const Navigation = ({ links }: NavigationProps) => {
  const { data: user } = useUser();
  const auth = getAuth();
  const router = useRouter();
  const { storeDocs, producerDocs, loadingDocs, status } = useUserData();

  const handleSignOut = async () => {
    try {
      await auth.signOut();
      console.log("User signed out successfully");
    } catch (error) {
      console.error("Error signing out:", error);
    }
  };

  const dropdownMenuItems = useMemo(() => {
    if (!user) {
      return null;
    }

    switch ((storeDocs?.length || 0) + (producerDocs?.length || 0)) {
      case 0:
        return (
          <ButtonLink href="/account-overview/artists" variant="ghost">
            Account Overview
          </ButtonLink>
        );
      case 1:
        return (
          <>
            {!!storeDocs?.length && (
              <ButtonLink href={`/stores/${storeDocs[0].id}`} variant="ghost">
                Dashboard
              </ButtonLink>
            )}

            {!!producerDocs?.length && (
              <ButtonLink
                href={`/artists/${producerDocs[0].id}`}
                variant="ghost"
              >
                Dashboard
              </ButtonLink>
            )}
          </>
        );
      default:
        return (
          <>
            {!!storeDocs?.length && (
              <DropdownMenu
                trigger={<Button variant="ghost">Stores</Button>}
                align="end"
                items={storeDocs.map((store) => ({
                  content: store.displayName,
                  onSelect: () => {
                    router.push(`/stores/${store.id}`);
                  },
                }))}
              />
            )}

            {!!producerDocs?.length && (
              <DropdownMenu
                trigger={<Button variant="ghost">Artists</Button>}
                align="end"
                items={producerDocs.map((producer) => ({
                  content: producer.displayName,
                  onSelect: () => {
                    router.push(`/artists/${producer.id}`);
                  },
                }))}
              />
            )}
          </>
        );
    }
  }, [user, storeDocs, producerDocs, router]);

  return (
    <Nav>
      <NavInner>
        <NavItemsGroup>
          <Link href="/">
            <Brand style={lora.style}>GACO</Brand>
          </Link>
          {links?.map((link) =>
            link.href ? (
              <ButtonLink
                key={link.name}
                href={`/${link.href}`}
                variant="ghost"
              >
                {link.name}
              </ButtonLink>
            ) : (
              <Button key={link.name}>{link.name}</Button>
            )
          )}
        </NavItemsGroup>

        <NavItemsGroup>
          {!loadingDocs && status !== "loading" && dropdownMenuItems}

          {!!user ? (
            <DropdownMenu
              align="end"
              alignOffset={8}
              sideOffset={8}
              trigger={
                <AvatarButton variant="ghost">
                  <Avatar>
                    {user?.photoURL && (
                      <AvatarImage src={user.photoURL} alt="@shadcn" />
                    )}
                    <AvatarFallback>
                      {user?.email?.slice(0, 2).toUpperCase() || "U"}
                    </AvatarFallback>
                  </Avatar>
                </AvatarButton>
              }
              items={[
                {
                  content: (
                    <>
                      <User />
                      Account Overview
                    </>
                  ),
                  onSelect: () => {
                    router.push(
                      "/account-overview" +
                        (storeDocs?.length ? "/stores" : "/artists")
                    );
                  },
                },
                {
                  content: (
                    <>
                      <UserPen />
                      Change password
                    </>
                  ),
                  onSelect: () => {
                    router.push("/change-password");
                  },
                },
                "seperator",
                {
                  content: (
                    <>
                      <LogOut />
                      Sign out
                    </>
                  ),
                  onSelect: handleSignOut,
                },
              ]}
            />
          ) : (
            <ButtonLink href="/login" variant="ghost">
              <UserPlus />
              Login / Sign Up
            </ButtonLink>
          )}
        </NavItemsGroup>
      </NavInner>
    </Nav>
  );
};
