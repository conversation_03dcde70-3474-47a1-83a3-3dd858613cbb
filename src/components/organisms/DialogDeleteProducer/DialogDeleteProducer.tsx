import { useFirebaseFunctions } from "@/hooks/useFirebaseFunctions";
import { callDeleteProducerApi } from "@/lib/api";
import { Producer } from "@/types/collections/producerInterfaces";
import { Alert, Button, Dialog, TextInput } from "@gaco/gaco-library";
import { useState } from "react";

interface DialogDeleteProducerProps {
  producer: Producer;
  onOpenChange: (open: boolean) => void;
}

export const DialogDeleteProducer = ({
  producer,
  onOpenChange,
}: DialogDeleteProducerProps) => {
  const functions = useFirebaseFunctions();
  const [producerDisplayName, setProducerDisplayName] = useState("");
  const [error, setError] = useState("");
  const [loading, setLoading] = useState(false);

  const handleDelete = async () => {
    if (!producer || producer.displayName !== producerDisplayName) {
      return;
    }

    setError("");
    setLoading(true);

    try {
      await callDeleteProducerApi(
        {
          producerId: producer.id,
        },
        functions,
        {
          hideToast: true,
        }
      );

      onOpenChange(false);
    } catch (error) {
      console.error("Error deleting producer", error);
      setError("Error deleting producer. Please try again.");
    }

    setLoading(false);
  };

  return (
    <Dialog
      title="Delete Producer"
      onOpenChange={onOpenChange}
      open
      actions={
        <Button
          variant="destructive"
          onClick={handleDelete}
          disabled={
            !producer || producer.displayName !== producerDisplayName || loading
          }
          loading={loading}
        >
          Delete
        </Button>
      }
    >
      {error && <Alert type="error">{error}</Alert>}

      <p>
        Are you sure you want to delete the artist{" "}
        <strong>{producer.displayName}</strong>? This action cannot be undone.
      </p>
      <TextInput
        id="producerDisplayName"
        placeholder="Type the artist name to confirm"
        value={producerDisplayName}
        disabled={loading}
        onChange={(e) => setProducerDisplayName(e.target.value)}
      />
    </Dialog>
  );
};
