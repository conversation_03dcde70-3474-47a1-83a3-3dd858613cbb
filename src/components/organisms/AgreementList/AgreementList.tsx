import { useAgreements } from "@/hooks/useAgreements";
import { AgreementStatus } from "@/types/collections/agreementInterfaces";
import {
  Card,
  CardList,
  CardSections,
  Spinner,
  Tabs,
  TabsList,
  TabsTrigger,
} from "@gaco/gaco-library";
import { useMemo, useState } from "react";
import { Role } from "types/collections/agreementInterfaces";
import { AgreementListItem } from "./AgreementListItem";

interface ApplicationListProps {
  className?: string;
  accountId: string;
  role: Role;
}

export const AgreementList = ({
  className,
  accountId,
  role,
}: ApplicationListProps) => {
  const {
    outboundAgreements,
    inboundAgreements,
    enrichAgreementWithProducer,
    enrichAgreementWithStore,
    status: applicationsLoading,
  } = useAgreements({
    accountRole: role,
    storeId: role === "store" ? accountId : undefined,
    producerId: role === "producer" ? accountId : undefined,
  });
  const [statusFilter, setStatusFilter] = useState<string | undefined>("all");

  const enrichedInboundAgreements = useMemo(
    () =>
      inboundAgreements.map((agreement) =>
        role === "store"
          ? enrichAgreementWithProducer(agreement)
          : enrichAgreementWithStore(agreement)
      ),
    [
      inboundAgreements,
      role,
      enrichAgreementWithProducer,
      enrichAgreementWithStore,
    ]
  );
  const enrichedOutboundAgreements = useMemo(
    () =>
      outboundAgreements.map((agreement) => {
        console.log({ agreement, role });
        return role === "store"
          ? enrichAgreementWithProducer(agreement)
          : enrichAgreementWithStore(agreement);
      }),
    [
      outboundAgreements,
      role,
      enrichAgreementWithStore,
      enrichAgreementWithProducer,
    ]
  );

  const sortedAgreements = useMemo(() => {
    return [...enrichedInboundAgreements, ...enrichedOutboundAgreements].sort(
      (a, b) => {
        const statusOrder = [
          AgreementStatus.DRAFT,
          AgreementStatus.PENDING,
          AgreementStatus.ACTIVE,
          AgreementStatus.REJECTED,
          AgreementStatus.EXPIRED,
          AgreementStatus.TERMINATED,
        ];
        return (
          statusOrder.indexOf(a.status as AgreementStatus) -
            statusOrder.indexOf(b.status as AgreementStatus) ||
          a.createdAt.toMillis() - b.createdAt.toMillis()
        );
      }
    );
  }, [enrichedInboundAgreements, enrichedOutboundAgreements]);

  const filteredAgreements = statusFilter
    ? sortedAgreements.filter(
        (agreement) =>
          statusFilter === "all" || agreement.status === statusFilter
      )
    : sortedAgreements;

  return (
    <div className={className}>
      <Tabs value={statusFilter}>
        <TabsList inline>
          <TabsTrigger
            value="all"
            onClick={() => {
              setStatusFilter("all");
            }}
          >
            All
          </TabsTrigger>
          <TabsTrigger
            value={AgreementStatus.DRAFT}
            onClick={() => setStatusFilter(AgreementStatus.DRAFT)}
          >
            Draft
          </TabsTrigger>
          <TabsTrigger
            value={AgreementStatus.PENDING}
            onClick={() => setStatusFilter(AgreementStatus.PENDING)}
          >
            Pending
          </TabsTrigger>
          <TabsTrigger
            value={AgreementStatus.ACTIVE}
            onClick={() => setStatusFilter(AgreementStatus.ACTIVE)}
          >
            Active
          </TabsTrigger>
          <TabsTrigger
            value={AgreementStatus.REJECTED}
            onClick={() => setStatusFilter(AgreementStatus.REJECTED)}
          >
            Rejected
          </TabsTrigger>
        </TabsList>
      </Tabs>

      <CardList>
        {applicationsLoading === "loading" ? (
          <Spinner />
        ) : (
          <>
            {filteredAgreements.length > 0 ? (
              filteredAgreements.map((agreement) => (
                <AgreementListItem
                  key={agreement.id}
                  agreement={agreement}
                  senderId={accountId}
                  role={role}
                />
              ))
            ) : (
              <Card>
                <CardSections.Content>
                  <p>No agreements</p>
                </CardSections.Content>
              </Card>
            )}
          </>
        )}
      </CardList>
    </div>
  );
};
