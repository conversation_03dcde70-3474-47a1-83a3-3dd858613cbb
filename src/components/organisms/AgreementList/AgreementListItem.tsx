import { useAgreements } from "@/hooks/useAgreements";
import {
  Agreement,
  AgreementStatus,
} from "@/types/collections/agreementInterfaces";
import { getStatusBadge } from "@/utils/badgeUtils";
import { getFirebaseDate } from "@/utils/timeUtils";
import {
  Button,
  ButtonGroup,
  Card,
  CardSections,
  DropdownMenu,
  media,
  Small,
} from "@gaco/gaco-library";
import { EllipsisVertical, MoveDown } from "lucide-react";
import { useRouter } from "next/navigation";
import { useState } from "react";
import styled from "styled-components";
import { Role } from "types/collections/agreementInterfaces";
import { UpdateDraftAgreementRequest } from "types/requests/agreementsInterfaces";
import { DialogAgreementUpdateForm } from "../DialogAgreementUpdateForm";

const RightColumnInner = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: flex-end;
  height: 100%;
  gap: 0.5rem;
`;

const LeftColumnInner = styled.div`
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  align-items: center;
  justify-content: center;

  svg {
    stroke: ${({ theme }) => theme.colors.textMuted};
    width: 0.75rem;
  }
`;

const DateAndBadgeContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.5rem;

  ${media.md`
    flex-direction: row;
    align-items: center;
  `}
`;

const ContentInner = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
`;

interface ApplicationListItemProps {
  agreement:
    | (Agreement & {
        store: { displayName: string };
      })
    | (Agreement & {
        producer: { displayName: string };
      });
  senderId: string;
  role: Role;
  className?: string;
}

export const AgreementListItem = ({
  agreement,
  senderId,
  role,
  className,
}: ApplicationListItemProps) => {
  const router = useRouter();
  const {
    updateDraftAgreement,
    deleteAgreement,
    approveAgreement,
    submitDraftAgreement,
    rejectAgreement,
    terminateAgreement,
  } = useAgreements({
    accountRole: role,
    storeId: role === "store" ? senderId : undefined,
    producerId: role === "producer" ? senderId : undefined,
  });
  const [loading, setLoading] = useState<boolean>(false);
  const [showEditDialog, setShowEditDialog] = useState(false);

  const handleDelete = async () => {
    setLoading(true);
    try {
      await deleteAgreement(agreement.id);
    } catch (error) {
      console.error("Error deleting agreement", error);
    }
    setLoading(false);
  };

  const handleTerminate = async () => {
    setLoading(true);
    try {
      await terminateAgreement(agreement.id);
    } catch (error) {
      console.error("Error terminating agreement", error);
    }
    setLoading(false);
  };

  const handleSubmitDraftAgreement = async () => {
    setLoading(true);
    try {
      await submitDraftAgreement({
        agreementId: agreement.id,
        role,
      });
    } catch (error) {
      console.error("Error submitting draft agreement", error);
    }
    setLoading(false);
  };

  const handleOfferEdit = async (values: UpdateDraftAgreementRequest) => {
    setLoading(true);
    try {
      await updateDraftAgreement(agreement.id, values);
    } catch (error) {
      console.error("Error updating agreement", error);
    }
    setLoading(false);
  };

  const handleAgreementAccept = async () => {
    setLoading(true);
    try {
      await approveAgreement(agreement.id);
    } catch (error) {
      console.error("Error accepting agreement", error);
    }
    setLoading(false);
  };

  const handleAgreementReject = async () => {
    setLoading(true);
    try {
      await rejectAgreement(agreement.id);
    } catch (error) {
      console.error("Error rejecting agreement", error);
    }
    setLoading(false);
  };

  const dropdownMenuOptions = [
    {
      content: "Edit",
      disabled: agreement.status !== "draft",
      onSelect: (e: Event) => {
        e.preventDefault();
        setShowEditDialog(true);
      },
    },
    ...(agreement.status === AgreementStatus.ACTIVE
      ? [
          {
            content: "Terminate Agreement",
            onSelect: handleTerminate,
          },
        ]
      : []),
    ...(agreement.status === AgreementStatus.PENDING &&
    role !== agreement.createdByRole
      ? [
          {
            content: "Accept Offer",
            onSelect: handleAgreementAccept,
          },
          {
            content: "Reject Offer",
            onSelect: handleAgreementReject,
          },
        ]
      : []),
    {
      content: "Delete",
      onSelect: handleDelete,
    },
    {
      content: "View Profile",
      onSelect: () => {
        router.push(
          role === "store"
            ? `/artists/${agreement?.producerId}`
            : `/stores/${agreement?.storeId}`
        );
      },
    },
  ];

  return (
    <Card className={className} loading={loading}>
      <CardSections.LeftColumn>
        <LeftColumnInner>
          <Small>{getFirebaseDate(agreement.effectiveDate)}</Small>
          <MoveDown />
          <Small>
            {agreement.expirationDate
              ? getFirebaseDate(agreement.expirationDate)
              : agreement.status &&
                  [
                    AgreementStatus.EXPIRED,
                    AgreementStatus.TERMINATED,
                  ].includes(agreement.status as AgreementStatus)
                ? agreement.status
                : "ongoing"}
          </Small>
        </LeftColumnInner>
      </CardSections.LeftColumn>
      {agreement && <CardSections.Title>{agreement.title}</CardSections.Title>}

      <CardSections.Content>
        <ContentInner>
          {agreement ? (
            <a
              href={
                role === "store"
                  ? `/stores/${agreement?.storeId}`
                  : `/artists/${agreement?.producerId}`
              }
            >
              {role === "store"
                ? "producer" in agreement
                  ? agreement.producer.displayName
                  : "Account not found"
                : "store" in agreement
                  ? agreement.store.displayName
                  : "Account not found"}
            </a>
          ) : (
            <p> Account not found</p>
          )}

          <Small>Commision Rate: {agreement.commission || "0"}%</Small>
        </ContentInner>
      </CardSections.Content>

      <CardSections.RightColumn>
        <RightColumnInner>
          {role === "store" && (
            <DialogAgreementUpdateForm
              open={showEditDialog}
              setOpen={setShowEditDialog}
              initialValues={{
                ...agreement,
                effectiveDate: agreement.effectiveDate,
                expirationDate: agreement.expirationDate,
              }}
              onSubmit={handleOfferEdit}
              onDelete={handleDelete}
            />
          )}

          <ButtonGroup>
            {agreement.status === AgreementStatus.PENDING &&
              agreement.createdByRole !== role && (
                <Button variant="outline" onClick={handleAgreementAccept}>
                  Accept
                </Button>
              )}

            {agreement.status === "draft" &&
              agreement.createdByRole === role && (
                <Button variant="outline" onClick={handleSubmitDraftAgreement}>
                  Submit draft
                </Button>
              )}

            <DropdownMenu
              trigger={
                <Button variant="ghost" icon={<EllipsisVertical />}></Button>
              }
              items={dropdownMenuOptions}
            />
          </ButtonGroup>

          <DateAndBadgeContainer>
            <Small>{getFirebaseDate(agreement.createdAt)}</Small>
            {getStatusBadge(agreement.status)}
          </DateAndBadgeContainer>
        </RightColumnInner>
      </CardSections.RightColumn>
    </Card>
  );
};
