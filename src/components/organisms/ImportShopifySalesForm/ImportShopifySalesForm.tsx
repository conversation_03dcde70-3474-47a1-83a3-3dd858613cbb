import { useFirebaseFunctions } from "@/hooks/useFirebaseFunctions";
import { CallGetShopifySalesApi } from "@/lib/api";
import {
  Alert,
  Card,
  CardSections,
  Form,
  FormFieldComponents,
} from "@gaco/gaco-library";
import { useState } from "react";
import * as yup from "yup";

interface ImportShopifySalesFormProps {
  storeId: string;
}

export const ImportShopifySalesForm = ({
  storeId,
}: ImportShopifySalesFormProps) => {
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const functions = useFirebaseFunctions();

  const handleSubmit = async (formValues: {
    startDate: Date;
    storeName: string;
  }) => {
    setLoading(true);
    const today = new Date();

    const daysBackSinceStartDate = Math.ceil(
      (today.getTime() - formValues.startDate.getTime()) / (1000 * 3600 * 24)
    );

    try {
      await CallGetShopifySalesApi(
        {
          daysBack: daysBackSinceStartDate,
          storeId,
          type: "shopify",
        },
        functions,
        {}
      );
      setSuccess(true);
    } catch (error) {
      console.error("Error importing Shopify sales:", error);
      throw new Error("Failed to import Shopify sales. Please try again.");
    }

    setLoading(false);
  };

  return (
    <>
      {success && (
        <Alert type="info">
          Sales imported started. It may take a few minutes for sales to appear
          on your dashboard.
        </Alert>
      )}
      <Card>
        <CardSections.Title>Import Shopify Sales</CardSections.Title>

        <CardSections.Content>
          <Form
            onSubmit={handleSubmit}
            isLoading={loading}
            labelSubmit="Import from Shopify"
            initialValues={{}}
            fields={[
              {
                columns: 2,
                fields: [
                  {
                    name: "startDate",
                    label: "Start Date",
                    component: FormFieldComponents.DATE_PICKER,
                    type: "date",
                  },
                ],
              },
            ]}
            schema={yup.object().shape({
              startDate: yup.date().required("Start date is required"),
            })}
          />
        </CardSections.Content>
      </Card>
    </>
  );
};
