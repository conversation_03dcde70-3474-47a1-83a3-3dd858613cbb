import { SalesGold } from "@/types/collections/salesInterfaces";
import { numberToCurrency } from "@/utils/currencyUtils";
import { getFirebaseDate } from "@/utils/timeUtils";
import { Card, CardSections, CardSize } from "@gaco/gaco-library";
import Link from "next/link";
import styled from "styled-components";

interface CardSaleProducerProps {
  sale: SalesGold;
  producerId: string;
}

const CardRightColumn = styled.div`
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: flex-end;
`;

export const CardSaleProducer = ({
  sale,
  producerId,
}: CardSaleProducerProps) => {
  return (
    <Card size={CardSize.sm}>
      <CardSections.Title>
        <Link href={`/artists/${producerId}/sales/${sale.id}`}>
          {sale.title}
        </Link>
      </CardSections.Title>
      <CardSections.RightColumn>
        <CardRightColumn>
          <span>{numberToCurrency(sale.totalPrice, sale.currency)}</span>
        </CardRightColumn>
      </CardSections.RightColumn>
      <CardSections.Content>
        <div>Store: {sale.storeDisplayName || "Unknown"}</div>
        {sale.variantDisplayName && (
          <div>Variant: {sale.variantDisplayName}</div>
        )}
        <div>{getFirebaseDate(sale.updatedAt)}</div>
      </CardSections.Content>
    </Card>
  );
};
