"use client";

import { SalesGold } from "@/types/collections/salesInterfaces";
import { <PERSON><PERSON>, CardList, H2, TextInput } from "@gaco/gaco-library";
import { Search } from "lucide-react";
import { useState } from "react";
import styled from "styled-components";
import { CardSaleProducer } from "./CardSaleProducer";

interface CardSalesListProducerCardListProps {
  sales: SalesGold[];
}

export const CardSalesListProducerCardList = ({
  sales,
}: CardSalesListProducerCardListProps) => {
  return (
    <CardList>
      {sales.map((sale) => (
        <CardSaleProducer
          key={sale.id}
          sale={sale}
          producerId={sale.producerId}
        />
      ))}
    </CardList>
  );
};

const SearchInput = styled(TextInput)`
  padding-top: 0;
`;

const TitleBar = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;

  ${H2} {
    margin: 0;
  }
`;

interface CardSalesListProducerProps {
  className?: string;
  sales: SalesGold[];
  title?: string;
}

export const CardSalesListProducer = ({
  sales,
  title = "Sales",
  ...props
}: CardSalesListProducerProps) => {
  const [searchTerm, setSearchTerm] = useState("");

  const filteredSales = sales.filter(
    (sale) =>
      sale.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (sale.storeDisplayName || "unknown")
        .toLowerCase()
        .includes(searchTerm.toLowerCase()) ||
      sale.variantDisplayName?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div {...props}>
      <TitleBar>
        {title && <H2>{title}</H2>}

        <SearchInput
          id="searchSales"
          type="text"
          label="Search sales..."
          icon={<Search />}
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
        />
      </TitleBar>

      {filteredSales.length === 0 ? (
        <Alert type="info">No sales found</Alert>
      ) : (
        <CardSalesListProducerCardList sales={filteredSales} />
      )}
    </div>
  );
};
