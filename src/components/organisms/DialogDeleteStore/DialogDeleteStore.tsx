import { useFirebaseFunctions } from "@/hooks/useFirebaseFunctions";
import { callDeleteStoreApi } from "@/lib/api";
import { Store } from "@/types/collections/storeInterfaces";
import { Alert, Button, Dialog, TextInput } from "@gaco/gaco-library";
import { useState } from "react";

interface DialogDeleteStoreProps {
  store: Store;
  onClose: () => void;
}

export const DialogDeleteStore = ({
  store,
  onClose,
}: DialogDeleteStoreProps) => {
  const functions = useFirebaseFunctions();
  const [storeDisplayName, setStoreDisplayName] = useState("");
  const [error, setError] = useState("");
  const [loading, setLoading] = useState(false);

  const handleDelete = async () => {
    if (!store || store.displayName !== storeDisplayName) {
      return;
    }

    setError("");
    setLoading(true);

    try {
      await callDeleteStoreApi(
        {
          storeId: store.id,
        },
        functions,
        {
          hideToast: true,
        }
      );
      onClose();
    } catch (error) {
      console.error("Error deleting store", error);
      setError("Error deleting store. Please try again.");
    }

    setLoading(false);
  };

  return (
    <Dialog
      title="Delete Store"
      onOpenChange={() => onClose()}
      open
      actions={
        <Button
          variant="destructive"
          onClick={handleDelete}
          disabled={!store || store.displayName !== storeDisplayName || loading}
          loading={loading}
        >
          Delete
        </Button>
      }
    >
      {error && <Alert type="error">{error}</Alert>}

      <p>
        Are you sure you want to delete the store{" "}
        <strong>{store.displayName}</strong>? This action cannot be undone.
      </p>
      <TextInput
        id="storeDisplayName"
        placeholder="Type the store name to confirm"
        value={storeDisplayName}
        disabled={loading}
        onChange={(e) => setStoreDisplayName(e.target.value)}
      />
    </Dialog>
  );
};
