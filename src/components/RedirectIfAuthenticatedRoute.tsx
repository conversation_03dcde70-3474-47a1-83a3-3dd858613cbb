import { useUserData } from "@/contexts/UserContext";
import { useRouter } from "next/navigation";
import { FC, ReactNode, useEffect } from "react";
import { useSigninCheck } from "reactfire";

export const RedirectIfAuthenticatedRoute: FC<{
  children: ReactNode;
}> = ({ children }) => {
  const { status, data: signInCheckResult } = useSigninCheck();
  const router = useRouter();
  const { stores, producers, status: userDataStatus } = useUserData();

  useEffect(() => {
    if (
      status !== "loading" &&
      userDataStatus !== "loading" &&
      signInCheckResult.signedIn
    ) {
      if (!stores && !producers) {
        return;
      }
      if (
        (Object.keys(stores)?.length || 0) +
          (Object.keys(producers)?.length || 0) !==
        1
      ) {
        router.push(
          "/account-overview" + (stores?.length ? "/stores" : "/artists")
        );
      } else {
        router.push(
          stores.length ? `/stores/${stores[0]}` : `/artists/${producers[0]}`
        );
      }
    }
  }, [status, router, signInCheckResult, stores, producers, userDataStatus]);

  return children;
};
