import { useRouter } from "next/navigation";
import { FC, ReactNode, useEffect } from "react";
import { useSigninCheck } from "reactfire";

export const ProtectedRoute: FC<{
  children: ReactNode;
  publicChildren?: ReactNode;
}> = ({ children, publicChildren }) => {
  const { status, data: signInCheckResult } = useSigninCheck();
  const router = useRouter();

  useEffect(() => {
    if (
      status !== "loading" &&
      !signInCheckResult.signedIn &&
      !publicChildren
    ) {
      router.push("/");
    }
  }, [status, router, publicChildren, signInCheckResult]);

  return signInCheckResult?.signedIn ? children : publicChildren;
};
