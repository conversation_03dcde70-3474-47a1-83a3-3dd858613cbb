import { media, MultiSelect, Select } from "@gaco/gaco-library";

import { useSalesFiltersContext } from "@/contexts/SalesFiltersContext";
import { useMemo } from "react";
import styled from "styled-components";

const Controls = styled.div`
  display: flex;
  justify-content: space-between;
  gap: 0.5rem;

  > * {
    flex: 1;
  }

  ${media.lg`
    gap: 1rem;
  `}
`;

interface ControlsSalesProps {
  multiSelectLabel: string;
}

export const ControlsSales = ({ multiSelectLabel }: ControlsSalesProps) => {
  const {
    timeRangeOptions,
    setSelectedTimeRangeOption,
    selectedTimeRangeOption,
    labelOptions: labelOptionsRoot,
    setSelectedLabelOptions,
    selectedLabelOptions,
  } = useSalesFiltersContext();

  const labelOptions = useMemo(() => {
    const optionsWithOptions = labelOptionsRoot.filter(
      ({ options }) => options.length > 0
    );

    if (optionsWithOptions.length === 1) {
      return optionsWithOptions.map(({ options }) => options).flat();
    }

    return labelOptionsRoot;
  }, [labelOptionsRoot]);

  return (
    <>
      <Controls>
        <MultiSelect
          id="label"
          label={multiSelectLabel}
          options={labelOptions}
          value={selectedLabelOptions}
          onChange={setSelectedLabelOptions}
        />
        <Select
          id="time-range"
          label="Time range"
          options={timeRangeOptions}
          value={selectedTimeRangeOption}
          onChange={(val) => setSelectedTimeRangeOption(val!)}
        />
      </Controls>
    </>
  );
};
