import {
  LineChart as LineChartRoot,
  media,
  Select,
  SelectOption,
} from "@gaco/gaco-library";
import { useMemo } from "react";
import { ResponsiveContainerProps } from "recharts";

import { SalesGold, SalesStaging } from "@/types/collections/salesInterfaces";
import { DateRangeKeys } from "@/types/date";
import { safeColor } from "@/utils/colorUtils";
import { useLocalStorage } from "react-use";
import styled from "styled-components";
import { TooltipContentCurrency } from "../TooltipContentCurrency/TooltipContentCurrency";

const Controls = styled.div`
  display: flex;
  justify-content: space-between;
  gap: 2rem;
  margin-bottom: 1rem;

  > * {
    flex: 1;
  }
`;

const LineChart = styled(LineChartRoot)`
  min-height: 18rem;

  ${media.sm`
    min-height: 12rem;
    `};
`;

const timeRangeOptions = [
  { value: DateRangeKeys.LAST_MONTH, label: "Last month" },
  { value: DateRangeKeys.LAST_3_MONTHS, label: "Last 3 months" },
  { value: DateRangeKeys.LAST_6_MONTHS, label: "Last 6 months" },
  { value: DateRangeKeys.LAST_YEAR, label: "Last year" },
  { value: DateRangeKeys.CUSTOM, label: "Choose date range" },
];

const generateDataPoints = (
  sales: (SalesStaging | SalesGold)[],
  timeRange: string
) => {
  const reversedSales = sales.slice().reverse();
  if (!reversedSales.length) return [];

  const lastDayOfLastMonth = new Date(
    new Date().getFullYear(),
    new Date().getMonth(),
    0
  );

  const calculateDataPoints = (
    startDate: Date,
    endDate: Date,
    intervals: number
  ) => {
    return Array.from({ length: intervals }).map((_, index) => {
      const intervalStart = new Date(
        startDate.getTime() +
          ((endDate.getTime() - startDate.getTime()) * index) / intervals
      );
      const intervalEnd = new Date(
        startDate.getTime() +
          ((endDate.getTime() - startDate.getTime()) * (index + 1)) / intervals
      );

      return {
        name: `${intervalStart.toLocaleDateString()} - ${intervalEnd.toLocaleDateString()}`,
        total: reversedSales
          .filter(
            (sale) =>
              new Date(sale.updatedAt.toDate()) >= intervalStart &&
              new Date(sale.updatedAt.toDate()) < intervalEnd
          )
          .reduce((sum, sale) => sum + sale.totalPrice, 0),
      };
    });
  };

  switch (timeRange) {
    case DateRangeKeys.LAST_MONTH:
      const numOfDaysInLastMonth = lastDayOfLastMonth.getDate();
      return calculateDataPoints(
        new Date(new Date().getFullYear(), new Date().getMonth() - 1, 1),
        lastDayOfLastMonth,
        numOfDaysInLastMonth
      );
    case DateRangeKeys.LAST_3_MONTHS:
      return calculateDataPoints(
        new Date(new Date().getFullYear(), new Date().getMonth() - 3, 1),
        lastDayOfLastMonth,
        12
      );
    case DateRangeKeys.LAST_6_MONTHS:
      return calculateDataPoints(
        new Date(new Date().getFullYear(), new Date().getMonth() - 6, 1),
        lastDayOfLastMonth,
        12
      );
    case DateRangeKeys.LAST_YEAR:
      return calculateDataPoints(
        new Date(new Date().getFullYear() - 1, new Date().getMonth(), 1),
        lastDayOfLastMonth,
        12
      );
    default:
      return reversedSales;
  }
};

interface LineChartSalesProps
  extends Omit<ResponsiveContainerProps, "children"> {
  sales: (SalesGold | SalesStaging)[];
  cacheKey: string;
}

export const LineChartTotalSales = ({
  sales,
  cacheKey,
}: LineChartSalesProps) => {
  const [
    selectedTimeRangeOption = timeRangeOptions[0],
    setSelectedTimeRangeOption,
  ] = useLocalStorage<SelectOption>(
    `${cacheKey}.LineChartTotalSales.selectedTimeRangeOption`,
    timeRangeOptions[0]
  );

  const labels = useMemo(
    () => [
      {
        dataKey: "total",
        label: "Total items sold",
        stroke: safeColor.random(),
      },
    ],
    []
  );

  const data = useMemo(
    () => generateDataPoints(sales, selectedTimeRangeOption.value),
    [sales, selectedTimeRangeOption.value]
  );

  return (
    <>
      <Controls>
        <Select
          id="time-range"
          label="Time range"
          options={timeRangeOptions}
          value={selectedTimeRangeOption}
          onChange={(val) => setSelectedTimeRangeOption(val!)}
        />
      </Controls>
      <LineChart
        data={data}
        labels={labels}
        tooltipContent={TooltipContentCurrency}
        height="100%"
      />
    </>
  );
};
