"use client";

import { GlobalStyle, LoadingOverlay, media } from "@gaco/gaco-library";
import { PropsWithChildren } from "react";
import styled from "styled-components";
import { ResponsiveContainer } from "../ga-ui";
import { Navigation, NavigationLink } from "../organisms/Navigation";

const links: NavigationLink[] = [];

interface LayoutCenteredWithNavProps extends PropsWithChildren {
  loading?: boolean;
  loadingMessage?: string;
}

const Screen = styled.div`
  background: ${({ theme }) => theme.colors.bg};
  height: 100%;
  display: flex;
  flex-direction: column;

  ${media.sm`
     height: 100vh;
  `}
`;

const Main = styled(ResponsiveContainer)`
  flex: 1;
  padding: 0.5rem 1rem 0 1rem;

  display: flex;
  flex-direction: column;
  justify-content: stretch;

  ${media.md`
    overflow: hidden;
    padding: 1rem 2rem 0 2rem;
  `}
`;

export const LayoutCenteredWithNav = ({
  children,
  loading,
  loadingMessage,
}: LayoutCenteredWithNavProps) => {
  return (
    <Screen>
      <GlobalStyle />
      <Navigation links={links} />

      <Main>
        {(loading || loadingMessage) && (
          <LoadingOverlay>{loadingMessage}</LoadingOverlay>
        )}

        {children}
      </Main>
    </Screen>
  );
};
