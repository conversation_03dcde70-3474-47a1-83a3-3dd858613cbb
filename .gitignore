# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.
/tmp/

# dependencies
/node_modules
/.pnp
.pnp.js
.yarn/install-state.gz

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env*.local
.env

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts
.env

.firebase
firebase-debug.log
firestore-debug.log
packages/shared-types/node_modules
/tmp/*


/functions/typescript/test/*


# Python
functions/python/venv/
functions/python/__pycache__/
**/__pycache__/**
functions/python/*.pyc
functions/python/.pytest_cache/
functions/python/lib

functions/python/tests/tmp_data/*

**/service-account.json

**/test_output/*