{"name": "gaco-platform", "version": "0.1.0", "private": true, "workspaces": ["packages/*", "functions/*"], "scripts": {"build:shared-types": "yarn workspace @gaco/shared-types build", "dev": "next dev", "build:types:watch": "yarn workspace @gaco/shared-types watch", "deploy:functions": "firebase deploy --only functions", "deploy:hosting": "firebase deploy --only hosting", "build": "next build", "ci": "yarn install --frozen-lockfile", "start": "next start", "lint": "next lint"}, "dependencies": {"@apollo/client": "^3.11.10", "@gaco/gaco-library": "^0.33.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-label": "^2.1.0", "@react-pdf/renderer": "^4.3.0", "@types/react-select-country-list": "^2.2.3", "@types/three": "^0.169.0", "ag-grid-community": "^34.0.0", "ag-grid-react": "^34.0.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cross-fetch": "^4.0.0", "d3": "^7.9.0", "date-fns": "^4.1.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "firebase": "9.23.0", "firebase-admin": "^12.7.0", "firebase-functions": "^6.1.0", "lucide-react": "^0.441.0", "next": "14.2.12", "prettier": "^3.3.3", "react": "^18", "react-dom": "^18", "react-dropzone": "^14.2.10", "react-hook-form": "^7.54.2", "react-select-country-list": "^2.2.3", "react-toastify": "^11.0.5", "react-use": "^17.6.0", "reactfire": "^4.2.3", "recharts": "^2.12.7", "safecolor": "^1.0.1", "styled-components": "^6.1.13", "three": "^0.170.0", "yarn": "^1.22.22", "yup": "^1.6.1"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@typescript-eslint/eslint-plugin": "^8.22.0", "@typescript-eslint/parser": "^8.22.0", "eslint": "^8", "eslint-config-next": "14.2.12", "postcss": "^8", "typescript": "^5.7.3"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}