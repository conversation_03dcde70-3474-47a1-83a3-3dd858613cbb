rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {

    // Helper function to check if a user has a specific access right for a store
    function hasStoreAccess(storeId, requiredAccessLevels) {
      return request.auth.token.store_access_rights != null && // Check if claim exists
             storeId in request.auth.token.store_access_rights &&
             request.auth.token.store_access_rights[storeId] in requiredAccessLevels;
    }

    // Helper function to check if a user has a specific access right for a producer
    function hasProducerAccess(producerId, requiredAccessLevels) {
      return request.auth.token.producer_access_rights != null && // Check if claim exists
             producerId in request.auth.token.producer_access_rights &&
             request.auth.token.producer_access_rights[producerId] in requiredAccessLevels;
    }

    // --- User Root Accounts ---
    // Users can read and update their own userRootAccount.
    // Creating/deleting userRootAccounts is typically handled by backend functions.
    match /userRootAccounts/{userId} {
      allow read, update: if request.auth.uid == userId;
      allow create, delete, list: if false; // Managed by backend
    }

    // --- sales-gold Collection ---
    // Documents in sales-gold/{salesId} contain either a 'storeId' or 'producerId'
    match /sales-gold/{salesId} {
      // READ: 'viewer', 'editor', 'admin' can read via store OR producer rights.
      allow read: if 
                     // Store-based access
                     (resource.data.keys().hasAny(['storeId']) && 
                      hasStoreAccess(resource.data.storeId, ['viewer', 'editor', 'admin'])) 
                     || // OR
                     // Producer-based access
                     (resource.data.keys().hasAny(['producerId']) && 
                      hasProducerAccess(resource.data.producerId, ['viewer', 'editor', 'admin']));


      // CREATE: 'editor', 'admin' can create. storeId must be in the request.
      allow create: if (request.resource.data.keys().hasAny(['storeId']) && hasStoreAccess(request.resource.data.storeId, ['editor', 'admin'])) ||
                       (request.resource.data.keys().hasAny(['producerId']) && hasProducerAccess(request.resource.data.producerId, ['editor', 'admin']));

      // UPDATE: 'editor', 'admin' can update.
      // Assumes storeId on a document does not change. If it can, more complex rules are needed.
      allow update: if (resource.data.keys().hasAny(['storeId']) && hasStoreAccess(resource.data.storeId, ['editor', 'admin'])) ||
                       (resource.data.keys().hasAny(['producerId']) && hasProducerAccess(resource.data.producerId, ['editor', 'admin']));

      // DELETE: only 'admin' can delete.
      allow delete: if (resource.data.keys().hasAny(['storeId']) && hasStoreAccess(resource.data.storeId, ['admin'])) ||
                       (resource.data.keys().hasAny(['producerId']) && hasProducerAccess(resource.data.producerId, ['admin']));
    }

    // --- stores Collection ---
    match /stores/{storeDocId} {
      allow read: if true; // Allows everyone, authenticated or not, to read
      // ... other rules for write, update, delete would remain based on hasStoreAccess
    }
  }
}