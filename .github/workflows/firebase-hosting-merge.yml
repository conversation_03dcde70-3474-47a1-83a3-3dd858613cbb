# This file was auto-generated by the Firebase CLI
# https://github.com/firebase/firebase-tools

name: Deploy to Firebase Hosting on merge
on:
  push:
    branches:
      - main
jobs:
  build_and_deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20

      - name: Cache dependencies
        uses: actions/cache@v4
        with:
          path: |
            node_modules/
            .next/cache/
          key: ${{ runner.os }}-nextjs-${{ github.ref }}
          restore-keys: |
            ${{ runner.os }}-nextjs-

      - run: yarn ci

      - name: Deploy to firebase
        uses: FirebaseExtended/action-hosting-deploy@v0
        with:
          repoToken: ${{ secrets.GITHUB_TOKEN }}
          firebaseServiceAccount: ${{ secrets.FIREBASE_SERVICE_ACCOUNT_GAST_ART_PLATFORM_87104 }}
          channelId: live
          projectId: gast-art-platform-87104
        env:
          FIREBASE_CLI_EXPERIMENTS: webframeworks
          ACTIONS_RUNNER_DEBUG: true
          ACTIONS_STEP_DEBUG: true
          NEXT_PUBLIC_API_KEY: AIzaSyCdd7QKPhZeJpecKnrTgyhK8gyiT6cA7cc
          NEXT_PUBLIC_AUTH_DOMAIN: gast-art-platform-87104.firebaseapp.com
          NEXT_PUBLIC_DATABASE_URL: https://gast-art-platform-87104-default-rtdb.europe-west1.firebasedatabase.app
          NEXT_PUBLIC_PROJECT_ID: gast-art-platform-87104
          NEXT_PUBLIC_STORAGE_BUCKET: gast-art-platform-87104.appspot.com
          NEXT_PUBLIC_MESSAGING_SENDER_ID: ************
          NEXT_PUBLIC_APP_ID: 1:************:web:9d167a29aa003d179f297c
          NEXT_PUBLIC_MEASUREMENT_ID: G-KWPBZPQ89Z
          NEXT_PUBLIC_MAPTILER_API_KEY: SvCoAHuiPzjxGQVoqWog
          NEXT_PUBLIC_FIRESTORE_EMULATOR: false
