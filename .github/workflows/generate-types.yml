# .github/workflows/generate-types.yml
name: Generate TypeScript Types

on:
  push:
    paths:
      - "functions/python/models/**" # Run when Python models change
      - "functions/python/generate_types.py"
  workflow_dispatch: # Allow manual trigger from GitHub UI

jobs:
  generate-types:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v3

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: "3.x"

      - name: Set up Node
        uses: actions/setup-node@v3
        with:
          node-version: "20"

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install pydantic pydantic-to-typescript
          npm install -g json-schema-to-typescript

      - name: Generate TypeScript types
        run: |
          cd functions/python
          python generate_types.py

      - name: Create Pull Request
        uses: peter-evans/create-pull-request@v5
        with:
          commit-message: "chore: update typescript types"
          title: "chore: update typescript types"
          body: "Auto-generated TypeScript types from Python models"
          branch: update-typescript-types
          base: main # or your default branch
