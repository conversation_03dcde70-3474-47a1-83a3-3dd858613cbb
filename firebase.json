{"functions": [{"runtime": "nodejs20", "source": "functions/typescript", "codebase": "default", "region": "europe-west3", "ignore": ["node_modules", ".git", "firebase-debug.log", "firebase-debug.*.log", "*.local", "**/test/**"], "predeploy": ["yarn --cwd \"$RESOURCE_DIR\" lint", "yarn --cwd \"$RESOURCE_DIR\" build"]}, {"source": "functions/python", "codebase": "python", "region": "europe-west3", "runtime": "python311", "ignore": ["venv", ".git", "firebase-debug.log", "firebase-debug.*.log", "*.local", "__pycache__", ".pytest_cache", "**/test/**", "**/__pycache__/**"], "predeploy": ["cd \"$RESOURCE_DIR\" && [ -d venv ] || python3.11 -m venv venv", "source \"$RESOURCE_DIR/venv/bin/activate\" && python -m pip install -r \"$RESOURCE_DIR/requirements.txt\""]}], "firestore": {"rules": "firestore.rules"}, "emulators": {"hosting": {"port": 3000}, "auth": {"port": 9099}, "functions": {"port": 5001}, "firestore": {"port": 8089}, "storage": {"port": 9199}, "ui": {"enabled": true}, "singleProjectMode": true}, "storage": {"rules": "storage.rules"}, "hosting": {"source": ".", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "frameworksBackend": {"region": "europe-west3"}}}