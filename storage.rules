rules_version = '2';

service firebase.storage {
  match /b/{bucket}/o {
    // This is the root match, we'll keep it restrictive
    match /{allPaths=**} {
      allow read, write: if false;
    }

    // Helper function to check store access rights from claims for storage paths
    function hasStoreStorageAccess(storeId, requiredAccessLevels) {
      return request.auth != null &&
             request.auth.token.store_access_rights != null &&
             storeId in request.auth.token.store_access_rights &&
             request.auth.token.store_access_rights[storeId] in requiredAccessLevels;
    }

    // Helper function to check producer access rights from claims for storage paths
    function hasProducerStorageAccess(producerId, requiredAccessLevels) {
      return request.auth != null &&
             request.auth.token.producer_access_rights != null &&
             producerId in request.auth.token.producer_access_rights &&
             request.auth.token.producer_access_rights[producerId] in requiredAccessLevels;
    }

    // --- Store-specific paths: stores/{storeId}/... ---
    match /stores/{storeId}/{allPaths=**} {
      // READ: 'viewer', 'editor', 'admin' can read files.
      allow read: if hasStoreStorageAccess(storeId, ['viewer', 'editor', 'admin']);

      // WRITE (create, update, delete): 'editor', 'admin' can write files.
      allow write: if hasStoreStorageAccess(storeId, ['editor', 'admin']);
    }

    // --- Producer-specific paths: producers/{producerId}/... ---
    match /producers/{producerId}/{allPaths=**} {
      // READ: 'viewer', 'editor', 'admin' can read files.
      allow read: if hasProducerStorageAccess(producerId, ['viewer', 'editor', 'admin']);

      // WRITE (create, update, delete): 'editor', 'admin' can write files.
      allow write: if hasProducerStorageAccess(producerId, ['editor', 'admin']);
    }

    // Example: User-specific private files (e.g., profile pictures)
    // match /users/{userId}/{allPaths=**} {
    //   allow read, write: if request.auth.uid == userId;
    // }

    // Default deny all access to other paths not explicitly matched
    // match /{allPaths=**} {
    //  allow read, write: if false;
    // }
  }
}