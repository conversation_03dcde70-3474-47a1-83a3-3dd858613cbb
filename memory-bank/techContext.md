# Tech Context: Gaco Platform

## 1. Technologies Used

*   **Primary Language:** Python (for Cloud Functions)
*   **Cloud Platform:** Google Cloud Platform (GCP)
    *   Cloud Functions for Firebase
    *   Firebase Authentication
    *   Firestore (NoSQL Database - inferred)
    *   Google Cloud Storage (for file-triggered events - inferred)
    *   Google Cloud Secret Manager (for API keys, secrets)
    *   Google Cloud Tasks (for background task queuing - inferred)
*   **Frontend Interface Definition:** TypeScript (used in `endpoints.md` to define request/response structures)
*   **Dependency Management (Python):**
    *   Poetry (`poetry.lock`, `pyproject.toml`)
    *   `requirements.txt` (likely generated by Poetry or for other purposes)
*   **Type Checking (Python):** MyPy (`mypy.ini`)
*   **Testing (Python):** Pytest (`pytest.ini`, `.pytest_cache/` directory)

## 2. Development Setup (Inferred)

*   Python development environment (version not specified, but contemporary Python 3.x expected).
*   Firebase CLI (`firebase-tools`) for deploying functions and managing Firebase projects.
*   Google Cloud SDK (`gcloud`) for interacting with GCP services.
*   Poetry for managing Python dependencies within the `functions/python/` directory.
*   Code editor with Python support (e.g., VS Code, PyCharm).
*   Access to a Firebase project and a GCP project.
*   Service account credentials for local development and testing requiring GCP/Firebase access.

## 3. Technical Constraints

*   **Authentication:** All callable cloud functions *must* be called by an authenticated user (Firebase Authentication). Exception: `delete_api_key` uses HTTP request authentication as per `endpoints.md`.
*   **Regional Deployment:** Functions are deployed to `europe-west3`.
*   **Function Resource Limits:** Individual functions have defined memory (e.g., 1GB, 4GB) and timeout (e.g., 60s, 300s, 600s) limits that must be respected.
*   **Statelessness:** Cloud functions are generally stateless, with persistent data stored in Firestore or other GCP services.
*   **Cold Starts:** Potential for cold starts in cloud functions, which might impact latency for infrequently used functions.
*   **API Quotas and Limits:** Subject to GCP and Firebase service quotas and limits (e.g., Firestore read/writes, Function invocations, Task Queue rates).
*   **Python Version:** The specific Python runtime version supported by Cloud Functions for Firebase at the time of deployment.

## 4. Key Dependencies (from `main.py` and `endpoints.md`)

*   **`firebase-admin`:** Essential Python SDK for interacting with Firebase services from the backend (e.g., initializing app, potentially interacting with Auth, Firestore).
*   **Specific `gaco_cloud_functions` modules:** `main.py` imports extensively from modules within this directory, indicating a strong internal dependency structure. Examples:
    *   `gaco_cloud_functions.gaco_store_manager_function`
    *   `gaco_cloud_functions.gaco_product_manager`
    *   `gaco_cloud_functions.gaco_agreements`
    *   `gaco_cloud_functions.gaco_email_manager`
    *   And many others as listed in `main.py` and the `gaco_cloud_functions` directory listing.
*   (Potentially many others listed in `requirements.txt` / `poetry.lock` for specific tasks like HTTP requests, data manipulation, etc. These are not detailed in `main.py` but are implied by a typical Python project structure).

## 5. Code Structure (Python Backend)

*   **`functions/python/main.py`:** Main entry point that initializes Firebase (`initialize_app()`) and exports all the cloud functions by importing them from their respective modules. The `__all__` list in this file serves as the definitive list of all exported Python cloud functions available in the deployment.
*   **`functions/python/gaco_cloud_functions/`:** Directory containing individual Python files, each responsible for one or more related cloud functions (e.g., `gaco_product_manager.py`, `gaco_agreements.py`).
*   **Supporting Directories:** The `functions/python/` directory also contains other subdirectories like `models/`, `services/`, `queries/`, etc., suggesting a structured approach to organizing code by concern (data models, service logic, database queries). 