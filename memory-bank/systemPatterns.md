# System Patterns: Gaco Platform

## 1. System Architecture

*   **Serverless Backend:** The platform is built primarily using Google Cloud Functions, providing a serverless, scalable backend.
*   **Firebase Integration:** Leverages Firebase services, most notably:
    *   **Firebase Authentication:** For securing all cloud function endpoints.
    *   **Cloud Functions for Firebase:** As the runtime environment for the Python backend logic.
    *   **Firestore:** (Strongly inferred) As the primary NoSQL database for storing application data (e.g., stores, producers, products, sales data, agreements). This is supported by mentions of Firestore triggers (`on_sales_staging_created`, `on_sales_silver_updated` etc.).
*   **Google Cloud Platform (GCP) Services:**
    *   **Cloud Storage:** (Inferred) Used for event-driven processing, such as `on_storage_shopify_orders_file_added` which processes files uploaded to a storage bucket.
    *   **Secret Manager:** (Mentioned in `endpoints.md`) For securely storing and accessing sensitive data like API keys.
    *   **Cloud Tasks:** (Inferred from `endpoints.md` discussion of background tasks and queues, e.g., `continueGettingOrders` and `sendEmail` background processing) Used for managing asynchronous execution of background tasks, ensuring reliability and retries.
*   **API Gateway:** Implicitly, Google Cloud Functions endpoints act as the API gateway for the frontend.

## 2. Key Technical Decisions

*   **Python for Backend Logic:** Cloud functions are implemented in Python.
*   **Region Specificity:** Functions are deployed in `europe-west3` region, as specified in `endpoints.md`.
*   **Defined Function Configurations:** Each function has specified memory and timeout configurations (e.g., 1GB RAM, 60s timeout for many functions), indicating performance and resource management considerations.
*   **HTTPS Callable Functions:** The primary mode of interaction for frontend-initiated actions.
*   **Event-Driven Functions:** Utilization of triggers (Firestore, Cloud Storage) to automate backend processes.
*   **Modular Code Structure:** Backend logic is organized into modules within the `functions/python/gaco_cloud_functions/` directory (e.g., `gaco_store_manager_function.py`, `gaco_product_manager.py`). `main.py` serves as an aggregator and explicitly exports the callable functions via its `__all__` list.
*   **Standardized Request/Response:** Adherence to a common JSON-based response format (`success`, `message`, `code`, `data`). Request formats are defined, often referencing TypeScript interfaces.

## 3. Design Patterns

*   **Facade Pattern:** Each cloud function often acts as a facade, simplifying complex underlying logic or interactions with multiple services/database collections into a single API endpoint.
*   **Manager/Service Layer:** The Python modules (e.g., `ProductManager`, `AgreementManager` - inferred from module names and `endpoints.md` descriptions like "Based on `agreement_manager.py`") suggest the use of manager or service classes encapsulating business logic for specific domains.
*   **Asynchronous Task Processing / Task Queues:**
    *   Used for operations like sending emails (`sendEmail` background task) and fetching Shopify orders in multiple pages (`continueGettingOrders` background task).
    *   This pattern improves responsiveness of initial API calls and handles potentially long-running or retryable operations reliably.
*   **Event-Driven Architecture:**
    *   **Firestore Triggers:** Functions like `on_sales_staging_created`, `on_sales_staging_updated`, `on_sales_silver_created`, `on_sales_silver_updated` are triggered by database changes to process data through a pipeline (e.g., raw sales to sanitized sales to gold-level aggregated data).
    *   **Cloud Storage Triggers:** `on_storage_shopify_orders_file_added` initiates processing when new Shopify order files arrive.
*   **Data Transfer Objects (DTOs):** While not explicitly named DTOs, the TypeScript interfaces for request and response formats (referenced in `endpoints.md`) serve a similar purpose, defining the structure of data exchanged between client and server.
*   **Workflow/Lifecycle Management:** The Agreement Management section in `endpoints.md` details a stateful lifecycle for agreements (Draft -> Pending Approval -> Approved -> Active -> Terminated/Expired), managed through various functions. This is a form of workflow pattern.

## 4. Component Relationships

```mermaid
graph TD
    FrontendApp[Frontend Application] -->|HTTPS Calls Authenticated| GCFR[Google Cloud Functions Python]

    subgraph GCFR
        direction LR
        MainPy[main.py __all__ exports] --> SpecificFunctions[`gaco_cloud_functions` modules]
        SpecificFunctions --> FirebaseServices[Firebase Services]
        SpecificFunctions --> GCPServices[Other GCP Services]
    end

    FirebaseServices --> FirebaseAuth[Firebase Authentication]
    FirebaseServices --> Firestore[Firestore Database]
    FirebaseServices --> CloudTasks[Cloud Tasks Task Queues]

    GCPServices --> SecretManager[Google Cloud Secret Manager]
    GCPServices --> CloudStorage[Google Cloud Storage]

    Firestore -- Triggers --> EventDrivenFunctions[Event-Driven Cloud Functions]
    CloudStorage -- Triggers --> EventDrivenFunctions

    EventDrivenFunctions --> SpecificFunctions
```

*   The **Frontend Application** is the primary consumer of the callable cloud functions.
*   **`main.py`** acts as the entry point, defining and explicitly exporting (via `__all__`) all available cloud functions.
*   Modules within **`gaco_cloud_functions/`** contain the actual business logic for each function or group of related functions.
*   These functions interact with **Firebase Services** (Authentication, Firestore, Cloud Tasks) and other **GCP Services** (Secret Manager, Cloud Storage).
*   **Firestore** and **Cloud Storage** also trigger separate **Event-Driven Cloud Functions** for background processing and data pipelines. 