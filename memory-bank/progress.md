# Progress: Gaco Platform Development

## 1. Overall Project Status

The Gaco Platform has a substantial set of core features implemented, primarily delivered as Google Cloud Functions. The backend provides a range of functionalities for e-commerce management, focusing on interactions between stores, producers, and their associated data (products, agreements, sales). The system relies on Firebase for authentication and Firestore (inferred) for data persistence, with event-driven processing for automation.

## 2. Implemented Modules & Features

The platform's capabilities are organized into the following modules/functional areas, with key implemented features:

### 2.1. Authentication
*   **Implemented:** All cloud functions require Firebase Authentication.
*   **Python Modules:** Implicitly handled by Firebase SDK and function invocation settings.

### 2.2. Store Management
*   **Implemented:**
    *   Create new stores (`create_store`).
    *   Delete stores (`delete_store`), with an option for hard delete.
*   **Python Module:** `gaco_cloud_functions.gaco_store_manager_function`

### 2.3. Producer Management
*   **Implemented:**
    *   Create new producers (`create_producer`).
    *   Delete producers (`delete_producer`).
    *   Internal "force producer" creation logic for specific flows (e.g., `ForceActivePartnershipFlow`).
*   **Python Module:** `gaco_cloud_functions.gaco_producer_manager_function`

### 2.4. Agreement Management
*   **Implemented:**
    *   Comprehensive contract lifecycle management:
        *   Create draft agreements (`create_agreement`).
        *   Update draft agreements (`update_draft_agreement`).
        *   Submit agreements for approval (`submit_agreement_for_approval`).
        *   Approve agreement steps by store/producer (`approve_agreement`).
        *   Internal activation of agreements upon final approval, leading to partnership creation.
        *   Terminate active agreements (`terminate_agreement`).
        *   Delete agreements (any state) (`delete_agreement`).
        *   (Negotiation and Renewal flows are documented in `endpoints.md` diagrams, suggesting they are planned or implemented aspects of `AgreementManager`).
*   **Python Module:** `gaco_cloud_functions.gaco_agreements` (likely wrapping an `AgreementManager` class).

### 2.5. Partnership Management
*   **Implemented:**
    *   Automatic creation of partnerships upon agreement activation.
    *   Delete partnerships (and related agreements) (`delete_partnership`).
    *   Automatic termination of partnerships upon agreement termination.
*   **Python Module:** `gaco_cloud_functions.gaco_delete_partnership` (and logic within `AgreementManager` / `gaco_agreements`).

### 2.6. Product Management
*   **Implemented:**
    *   Create products with variants (`create_product`).
    *   Update product information (`update_product`).
    *   Delete products (optionally with variants) (`delete_product`).
    *   Create product variants (`create_variant`).
    *   Update product variants (`update_variant`).
    *   Delete product variants (`delete_variant`).
    *   Various getter functions (`get_product`, `get_variant`, etc.) are implemented for internal backend use. They are not documented in `endpoints.md` as the frontend accesses data directly via `react-fire`.
*   **Python Module:** `gaco_cloud_functions.gaco_product_manager`

### 2.7. Product Allocation Management
*   **Implemented:**
    *   Create product allocations from producer to store (`create_allocation`).
    *   Update allocation status (e.g., PENDING_SHIPMENT, IN_TRANSIT, DELIVERED) (`update_allocation_status`).
*   **Python Module:** `gaco_cloud_functions.gaco_product_allocator`

### 2.8. Product Request Management
*   **Implemented:**
    *   Create store product requests to producers (`create_store_product_request`).
    *   Approve store product requests by producer (which creates an allocation) (`approve_store_product_request`).
    *   Reject store product requests by producer (`reject_store_product_request`).
    *   Cancel store product requests by store (`cancel_store_product_request`).
*   **Python Module:** `gaco_cloud_functions.gaco_product_requester`

### 2.9. Sales Report Management
*   **Implemented:**
    *   Create sales reports for a store/producer within a date range (`create_sales_reports`).
    *   Delete sales reports and revert related changes (`delete_sales_report`).
*   **Python Modules:** `gaco_cloud_functions.gaco_create_sales_report`, `gaco_cloud_functions.gaco_delete_sales_report`

### 2.10. Shopify Integration
*   **Implemented:**
    *   Fetch Shopify orders for a store (initial page + background processing for more pages) (`get_shopify_orders`).
    *   Continue fetching subsequent pages of Shopify orders (background task) (`continueGettingOrders`).
    *   Fetch Shopify orders with dynamically determined date ranges (`get_shopify_orders_with_dynamic_query`).
    *   Parse Shopify order files added to Cloud Storage (event-driven) (`on_storage_shopify_orders_file_added`).
    *   Create product in Shopify (`create_product_in_shopify`).
    *   Initialize Shopify product import (`initialize_shopify_product_import`).
    *   Process Shopify product files added to Cloud Storage (event-driven) (`on_storage_shopify_products_file_added`).
*   **Python Modules:** `gaco_cloud_functions.gaco_get_shopify_orders`, `gaco_cloud_functions.gaco_shopify_orders_background`, `gaco_cloud_functions.gaco_get_shopify_orders_with_dynamic_query`, `gaco_cloud_functions.gaco_shopify_orders_parser`, `gaco_cloud_functions.gaco_create_product_in_shopify`, `gaco_cloud_functions.gaco_shopify_product_import_initialization`, `gaco_cloud_functions.gaco_shopify_products_parser`.

### 2.11. Sales Data Processing
*   **Implemented:**
    *   Event-driven sanitization of sales data in `sales-staging` upon creation/update (`on_sales_staging_created`, `on_sales_staging_updated`).
    *   Process `sales-staging` data with a new producer/agreement context (`sanitize_sales_staging_with_new_agreement`).
    *   Event-driven processing of `sales-silver` data to `sales-gold` (likely for aggregated reports) upon creation/update (`on_sales_silver_created`, `on_sales_silver_updated`).
*   **Python Modules:** `gaco_cloud_functions.gaco_sanitize_sales_on_update`, `gaco_cloud_functions.gaco_sanitize_sales_staging_with_new_agreement`, `gaco_cloud_functions.gaco_update_sales_silver_to_gold`.

### 2.12. Email Management
*   **Implemented:**
    *   Enqueue emails for sending via a generic `enqueue_send_email` function. The type of email is determined by parameters in the request payload (e.g., `template_id`).
    *   Background task for sending queued emails (`sendEmail`).
*   **Python Module:** `gaco_cloud_functions.gaco_email_manager`

### 2.13. Secret Management
*   **Implemented:**
    *   Store Shopify API keys securely (`store_api_key`).
    *   Retrieve API keys (`get_api_key`). This is exported in `main.py` but intended for internal backend use, not direct frontend calls.
    *   Delete stored API keys (`delete_api_key`). This is not a standard callable function and is not intended for frontend use.
*   **Python Modules:** `gaco_cloud_functions.gaco_store_secret_function`, `gaco_cloud_functions.gaco_delete_secret_function`.

### 2.14. Invoice Management (Partial/Commented Out)
*   **Status:** The `delete_invoice` function is exported in `main.py` but is not documented in `endpoints.md`. This confirms it is not intended for frontend use and the overall feature is likely incomplete or deprecated.
*   **Implemented (Exported for internal use):** `delete_invoice`.
*   **Python Modules:** (Potentially `gaco_cloud_functions.gaco_create_invoices_function`, `gaco_cloud_functions.gaco_delete_invoice_function`)

## 3. What's Left to Build / Future Enhancements (Examples)

*   **P1. Expansion of Shopify Integration:** Syncing products from Gaco to Shopify, real-time inventory sync, etc.
*   **Inventory Management:** More granular inventory tracking across producer and store locations, stock adjustments, etc.
*   **Real-time Inventory Updates & Synchronization:**
    *   Automatic Decrements on Sales: Does the inventory_quantity automatically decrease when a sale is made (e.g., synced from Shopify or through an internal sales recording mechanism)? -> Solved by further shopify integration.
    *   Automatic Increments on Received Allocations: When an allocation is marked DELIVERED or RECEIVED, does the store's (or relevant entity's) stock for those product variants automatically increase?
    *   Returns Processing: How are customer returns handled in terms of adding items back to inventory?
    *   Inventory Ledger/Movement History:
        *   A detailed log for each product variant showing all stock movements:
            *   Initial stock levels.
            *   Stock received (e.g., from an allocation).
            *   Stock shipped/sold.
            *   Manual adjustments (see next point).
            *   Reasons for changes.
            *   This provides auditability and helps track down discrepancies.
        *   Manual Stock Adjustments:
            *   Functions to manually adjust inventory levels for reasons other than sales or allocations, such as:
                *   Stocktakes/Cycle Counts: Correcting discrepancies found during physical inventory counts.
                *   Damaged or Expired Goods: Writing off unsellable stock.
                *   Internal Transfers: Moving stock between different internal locations if that level of detail is needed (e.g., within a large store or producer warehouse).
        *   ~~Multi-Location Inventory (within a single Store/Producer):~~
            *   ~~If a store or producer has multiple physical locations (e.g., different warehouses, backroom vs. shop floor), the current system might not distinguish where the inventory_quantity resides. Granular tracking would allow managing stock per location -> each store have to be unique, cant create store in the same location.~~
        *   Committed Stock/Available Stock:
            *   Distinguishing between "total stock on hand" and "available stock." For example, stock might be committed to fulfilling an order but not yet shipped.
        *   Reorder Points & Low Stock Alerts:
            *    System-defined thresholds for product variants that trigger notifications or reports when stock levels fall low, prompting reordering or replenishment.
        *   Batch/Lot Tracking or Serial Number Tracking:
            *   For certain types of products (e.g., perishables, electronics, high-value items), tracking inventory by specific batches, lots, or individual serial numbers.
*   **User Roles & Permissions:** More detailed role-based access control beyond basic authentication if needed.
*   **Notifications System:** Expansion of the email system or addition of in-app notifications for various events.
*   **Payment Gateway Integration:** If the platform needs to handle direct payments.
*   **Testing:** Comprehensive test coverage (unit, integration, e2e) for all modules is crucial. `pytest.ini` and `tests/` directory suggest testing is in place or planned.
*   **Advanced Reporting & Analytics:** Beyond basic sales reports, more sophisticated analytics could be built on the `sales-gold` data.

## 4. Known Issues / Gaps (from Current Understanding)

*   **Clarity on Internal vs. External Functions:** The primary remaining task is to definitively confirm which functions exported in `main.py` but not listed in `endpoints.md` are truly for internal/backend use only. This ensures that the frontend-facing documentation (`endpoints.md`) is both accurate and complete from a developer's perspective.
*   **Detailed Error Handling Logic:** While `endpoints.md` specifies common error codes, the detailed error handling within each function isn't fully visible without code inspection.
*   **Scalability Testing:** While designed for scalability, actual load testing results are unknown.

## 5. Frontend Implementation Status

Based on an analysis of the frontend codebase in `src`, the following features have backend support but are not yet implemented in the frontend. The current goal is to implement these missing pieces.

### 5.1. Unimplemented Agreement Management
*   The entire Agreement Management lifecycle is missing. This includes creating, updating, submitting, approving, terminating, and deleting agreements.
    *   `create_agreement`
    *   `update_draft_agreement`
    *   `submit_agreement_for_approval`
    *   `approve_agreement`
    *   `terminate_agreement`
    *   `delete_agreement`

### 5.2. Unimplemented Partnership Management
*   No frontend component exists for deleting a partnership.
    *   `delete_partnership`

### 5.3. Unimplemented Product Management
*   The full suite of product and variant management is not implemented.
    *   `create_product`
    *   `update_product`
    *   `delete_product`
    *   `create_variant`
    *   `update_variant`
    *   `delete_variant`

### 5.4. Unimplemented Product Allocation Management
*   The product allocation workflow is not implemented.
    *   `create_allocation`
    *   `update_allocation_status`

### 5.5. Unimplemented Product Request Management
*   The product request workflow between stores and producers is not implemented.
    *   `create_store_product_request`
    *   `approve_store_product_request`
    *   `reject_store_product_request`
    *   `cancel_store_product_request`

### 5.6. Unimplemented Sales Report Management
*   The creation and deletion of sales reports are not implemented.
    *   `create_sales_reports`
    *   `delete_sales_report`

### 5.7. Unimplemented Shopify Integration Features
*   Key product integration features with Shopify are missing.
    *   `create_product_in_shopify`
    *   `initialize_shopify_product_import`

### 5.8. Unimplemented Email Management
*   The generic email sending function is not used.
    *   `enqueue_send_email`

### 5.9. Other Discrepancies
*   The frontend uses `create_invoices`, `sendApplyPending`, `sendAcceptApplication`, `sendRejectApplication`, and `uploadJson`, which are not documented in `endpoints.md`. This should be addressed later to ensure documentation is complete. 