# Project Brief: Gaco Platform

## 1. Project Goal

The Gaco Platform is a backend system designed to support a frontend application by providing a suite of cloud functions. These functions manage various aspects of an e-commerce ecosystem, including stores, producers, agreements, products, inventory, sales data, and integrations with external services like Shopify.

## 2. Core Requirements

*   **Modular Functionality:** The platform is built as a collection of discrete cloud functions, each responsible for specific operations. These are exported via `functions/python/main.py`.
*   **Authentication:** All cloud functions require user authentication via Firebase Authentication (with specific exceptions like `delete_api_key` which uses HTTP auth).
*   **Defined Interfaces:** Each function has a specific request and response format, primarily documented in `api-endpoints-doc/endpoints.md`.
*   **Scalability and Reliability:** Functions are designed with considerations for memory, timeout, and utilize background processing for long-running tasks (e.g., email sending, Shopify order fetching).
*   **Data Management:** The system manages data related to stores, producers, products, sales, agreements, and their lifecycle.
*   **Integration:** Provides integration with Shopify for order retrieval and potentially other e-commerce functionalities.
*   **Event-Driven Processing:** Utilizes event-driven functions (e.g., Firestore triggers, Cloud Storage triggers) for automated data processing workflows.

## 3. Scope

The platform encompasses management of:
*   Stores
*   Producers
*   Agreements & Contracts
*   Partnerships
*   Products & Variants
*   Product Allocations
*   Product Requests (Store to Producer)
*   Sales Reports
*   Shopify Order Integration
*   Sales Data Sanitization & Processing
*   Email Notifications
*   Secure API Key Management

## 4. Primary Source of Truth

The primary documentation for the *publicly intended* available cloud functions, their endpoints, request/response formats, and behavior is:
*   `api-endpoints-doc/endpoints.md`

The `functions/python/main.py` file's `__all__` list provides the definitive enumeration of all *exported* Python cloud functions, which includes publicly documented ones as well as potentially internal or alternatively invoked functions.

This document serves as the foundation for understanding the platform's capabilities. 