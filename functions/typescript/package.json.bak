{"name": "functions", "scripts": {"lint": "eslint --ext .js,.ts .", "build": "tsc --build && cp -r ../../src/shared lib/shared", "build:watch": "tsc --watch", "serve": "npm run build && firebase emulators:start --only functions", "shell": "npm run build && firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log"}, "engines": {"node": "18"}, "main": "lib/index.js", "dependencies": {"@gaco/shared-types": "1.0.0", "firebase-admin": "^12.1.0", "firebase-functions": "^5.0.0"}, "devDependencies": {"@gaco/shared-types": "1.0.0", "@typescript-eslint/eslint-plugin": "^5.12.0", "@typescript-eslint/parser": "^5.12.0", "eslint": "^8.9.0", "eslint-config-google": "^0.14.0", "eslint-plugin-import": "^2.25.4", "firebase-functions-test": "^3.1.0", "typescript": "^4.9.0"}, "private": true}