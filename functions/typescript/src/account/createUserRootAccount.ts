import * as admin from "firebase-admin";
import { Timestamp } from "firebase-admin/firestore";
import { UserRecord } from "firebase-admin/auth";

export const createUserRootAccount = async (user: UserRecord) => {
  try {
    const userUuid = user.uid;
    const email = user.email;

    if (!email) {
      console.error("User has no email address");
      return;
    }

    // Check if user root account already exists
    const userRootAccountRef = admin.firestore().collection("userRootAccounts").doc(userUuid);
    const doc = await userRootAccountRef.get();

    if (doc.exists) {
      console.log(`User root account already exists: ${userUuid}`);
      return;
    }

    // Create user root account
    const userRootAccountData = {
      createdAt: Timestamp.now(),
      email: email,
      stores: {},
      producers: {},
    };

    await userRootAccountRef.set(userRootAccountData);
    console.log(`Created user root account for: ${userUuid}`);

    // Initialize custom claims with empty access_rights maps
    const initialClaims = {
      store_access_rights: {},
      producer_access_rights: {},
    };

    await admin.auth().setCustomUserClaims(userUuid, initialClaims);
    console.log(`Initialized custom claims for user: ${userUuid} with empty access_rights.`);
  } catch (error) {
    console.error("Error creating user root account or setting initial claims:", error);
  }
};
