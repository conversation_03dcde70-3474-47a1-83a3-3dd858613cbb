import * as admin from "firebase-admin";
import { onDocumentWrittenWithAuthContext } from "firebase-functions/v2/firestore";

export const generateDownloadUrl = onDocumentWrittenWithAuthContext(
  {
    document: "sales-reports/{docId}",
    region: "europe-west3",
  },
  async (event) => {
    const { data } = event;

    if (!data) {
      console.log("No event data found");
      return;
    }

    // Handle deletion
    if (!data.after?.exists) {
      console.log("Document deleted, skipping");
      return;
    }

    const afterData = data.after.data();
    if (!afterData) {
      console.log("No document data found");
      return;
    }

    const { uri, gcsPath } = afterData;

    // Check if document has either uri or gcsPath
    if (!uri && !gcsPath) {
      console.log("Document has no uri or gcsPath field");
      return;
    }

    // Check if ONLY our function's fields were added/changed
    // Only process documents updated by Python backend
    const updatedByFunction = afterData.updatedByFunction;
    if (updatedByFunction !== "python") {
      console.log(
        `Document updated by ${updatedByFunction}, skipping (only process python updates)`
      );
      return;
    }

    try {
      let bucket;
      let filePath: string;

      if (gcsPath) {
        // Parse GCS path: gs://bucket-name/path/to/file
        const gcsMatch = gcsPath.match(/^gs:\/\/([^/]+)\/(.+)$/);
        if (!gcsMatch) {
          console.error("Invalid GCS path format:", gcsPath);
          return;
        }

        const bucketName = gcsMatch[1];
        filePath = gcsMatch[2];
        bucket = admin.storage().bucket(bucketName);
      } else if (uri) {
        // Parse HTTP URI: https://storage.googleapis.com/bucket-name/path/to/file
        const uriMatch = uri.match(/^https:\/\/storage\.googleapis\.com\/([^/]+)\/(.+)$/);
        if (!uriMatch) {
          console.error("Invalid URI format:", uri);
          return;
        }

        const bucketName = uriMatch[1];
        filePath = uriMatch[2];
        bucket = admin.storage().bucket(bucketName);
      } else {
        console.error("Neither uri nor gcsPath provided");
        return;
      }

      // Generate a signed URL (valid for 14 days)
      const file = bucket.file(filePath);
      const [url] = await file.getSignedUrl({
        action: "read",
        expires: Date.now() + 14 * 24 * 60 * 60 * 1000, // 14 days from now
      });

      // Update the document with the download URL
      await data.after.ref.update({
        downloadUrl: url,
        downloadUrlGeneratedAt: admin.firestore.FieldValue.serverTimestamp(),
        updatedByFunction: "typescript",
      });

      console.log("Download URL generated successfully for:", event.params.docId);
    } catch (error) {
      console.error("Error generating download URL:", error);

      // Update document with error info
      await data.after.ref.update({
        downloadUrlError: error instanceof Error ? error.message : "Unknown error",
        downloadUrlGeneratedAt: admin.firestore.FieldValue.serverTimestamp(),
        updatedByFunction: "typescript",
      });
    }
  }
);
