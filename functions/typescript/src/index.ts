import * as admin from "firebase-admin";
// import * as functions from "firebase-functions";
import { createUserRootAccount } from "./account/createUserRootAccount";
import { generateDownloadUrl } from "./collection/generateDownloadUrl";

// Use require for v1 functions to avoid type issues
// eslint-disable-next-line @typescript-eslint/no-var-requires
const functionsV1 = require("firebase-functions/v1");

// Initialize Firebase Admin SDK
admin.initializeApp();

// Export the v2 firestore function
export { generateDownloadUrl };

// Add this export with your other exports - using v1 syntax
export const onCreateUserAccount = functionsV1
  .region("europe-west3")
  .auth.user()
  .onCreate(createUserRootAccount);
