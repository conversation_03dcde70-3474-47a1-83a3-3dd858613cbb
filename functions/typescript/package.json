{"name": "functions", "version": "0.0.1", "scripts": {"lint": "eslint --fix --ext .js,.ts .", "build": "npx tsc", "build:watch": "tsc --watch", "serve": "npm run build && firebase emulators:start --only functions", "shell": "npm run build && firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log"}, "engines": {"node": "20"}, "main": "lib/index.js", "dependencies": {"@google-cloud/secret-manager": "^5.6.0", "axios": "^1.7.9", "cors": "^2.8.5", "cross-fetch": "^4.0.0", "firebase-admin": "^12.1.0", "firebase-functions": "^6.0.1", "graphql": "^16.9.0", "graphql-request": "^7.1.2", "mailgun.js": "^10.2.3"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^5.12.0", "@typescript-eslint/parser": "^5.12.0", "eslint": "^8.9.0", "eslint-config-google": "^0.14.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.25.4", "eslint-plugin-prettier": "^5.2.1", "firebase-functions-test": "^3.1.0", "prettier": "^3.3.3", "ts-node": "^10.9.2", "typescript": "^4.9.0"}, "private": true, "license": "MIT"}