{"compilerOptions": {"module": "commonjs", "noImplicitReturns": true, "noUnusedLocals": true, "outDir": "./lib", "sourceMap": true, "strict": true, "target": "es2017", "esModuleInterop": true, "moduleResolution": "node", "baseUrl": ".", "composite": true, "rootDir": "src", "paths": {"@gaco/shared-types": ["../../packages/shared-types/src"]}, "skipLibCheck": true, "typeRoots": ["./node_modules/@types"]}, "compileOnSave": true, "include": ["src/**/*.ts", "src/**/*.d.ts"], "exclude": ["node_modules"], "references": [{"path": "../../packages/shared-types", "composite": true}]}