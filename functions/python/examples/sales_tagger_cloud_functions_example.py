"""
Example usage of Sales Staging Tagger Cloud Functions

This example demonstrates how to use the cloud functions for sales tagging
with task-based background processing for optimal cost and performance.
"""

import time
import requests
from typing import Dict, Any


class SalesTaggingClient:
    """
    Client for interacting with Sales Staging Tagger Cloud Functions
    """
    
    def __init__(self, base_url: str, auth_token: str):
        """
        Initialize the client
        
        Args:
            base_url: Base URL for the cloud functions
            auth_token: Firebase auth token for authentication
        """
        self.base_url = base_url.rstrip('/')
        self.headers = {
            'Authorization': f'Bearer {auth_token}',
            'Content-Type': 'application/json'
        }
    
    def initialize_tagging(self, tag_request: Dict[str, Any]) -> Dict[str, Any]:
        """
        Initialize a sales tagging operation
        
        Args:
            tag_request: Tagging configuration
            
        Returns:
            Response with operation details
        """
        url = f"{self.base_url}/initializeSalesTagging"
        response = requests.post(url, json=tag_request, headers=self.headers)
        return response.json()
    
    def get_tagging_status(self, operation_id: str) -> Dict[str, Any]:
        """
        Get the status of a tagging operation
        
        Args:
            operation_id: ID of the operation to check
            
        Returns:
            Response with operation status
        """
        url = f"{self.base_url}/getSalesTaggingStatus"
        response = requests.post(
            url, 
            json={"operation_id": operation_id}, 
            headers=self.headers
        )
        return response.json()
    
    def wait_for_completion(
        self, 
        operation_id: str, 
        timeout_seconds: int = 300,
        poll_interval: int = 10
    ) -> Dict[str, Any]:
        """
        Wait for a tagging operation to complete
        
        Args:
            operation_id: ID of the operation to wait for
            timeout_seconds: Maximum time to wait
            poll_interval: How often to check status
            
        Returns:
            Final operation status
        """
        start_time = time.time()
        
        while time.time() - start_time < timeout_seconds:
            status_response = self.get_tagging_status(operation_id)
            
            if not status_response.get("success"):
                return status_response
            
            status_data = status_response["data"]
            status = status_data["status"]
            
            print(f"Operation {operation_id}: {status} "
                  f"({status_data['progress_percentage']:.1f}% complete)")
            
            if status in ["completed", "error"]:
                return status_response
            
            time.sleep(poll_interval)
        
        return {
            "success": False,
            "message": f"Operation timed out after {timeout_seconds} seconds",
            "code": 408
        }


def example_basic_tagging():
    """
    Example of basic sales tagging with producer assignment
    """
    print("=== Basic Sales Tagging Example ===")
    
    # Initialize client (replace with your actual values)
    client = SalesTaggingClient(
        base_url="https://your-region-your-project.cloudfunctions.net",
        auth_token="your-firebase-auth-token"
    )
    
    # Define tagging configuration
    tag_request = {
        "storeId": "store_abc123",
        "fields": ["title"],  # Search in title field
        "tagRule": {
            "producer_artisan_001": ["artisan", "handmade", "craft"],
            "producer_vintage_002": ["vintage", "antique", "retro"],
            "producer_organic_003": ["organic", "natural", "eco-friendly"]
        },
        "overrideVendor": True,        # Always override vendor with producer display name
        "updateVendorIfNull": False    # Not needed since override is True
    }
    
    # Initialize tagging operation
    print("Initializing sales tagging operation...")
    init_response = client.initialize_tagging(tag_request)
    
    if not init_response.get("success"):
        print(f"Failed to initialize: {init_response.get('message')}")
        return
    
    operation_id = init_response["data"]["operation_id"]
    total_documents = init_response["data"]["total_documents"]
    estimated_time = init_response["data"]["estimated_processing_time"]
    
    print(f"Operation {operation_id} initialized successfully!")
    print(f"Total documents to process: {total_documents}")
    print(f"Estimated processing time: {estimated_time}")
    
    # Wait for completion
    print("\nWaiting for operation to complete...")
    final_status = client.wait_for_completion(operation_id)
    
    if final_status.get("success"):
        data = final_status["data"]
        print(f"\n✅ Operation completed successfully!")
        print(f"Documents processed: {data['documents_processed']}")
        print(f"Documents tagged: {data['documents_tagged']}")
        print(f"Success rate: {(data['documents_tagged']/data['documents_processed']*100):.1f}%")
    else:
        print(f"\n❌ Operation failed: {final_status.get('message')}")


def example_vendor_update_scenarios():
    """
    Example showing different vendor update scenarios
    """
    print("\n=== Vendor Update Scenarios ===")
    
    client = SalesTaggingClient(
        base_url="https://your-region-your-project.cloudfunctions.net",
        auth_token="your-firebase-auth-token"
    )
    
    scenarios = [
        {
            "name": "Override All Vendors",
            "config": {
                "storeId": "store_123",
                "fields": ["title", "vendor"],
                "tagRule": {"producer_001": ["special", "premium"]},
                "overrideVendor": True,
                "updateVendorIfNull": False
            },
            "description": "Always replaces vendor field with producer display name"
        },
        {
            "name": "Fill Empty Vendors Only",
            "config": {
                "storeId": "store_123", 
                "fields": ["title"],
                "tagRule": {"producer_002": ["handmade", "artisan"]},
                "overrideVendor": False,
                "updateVendorIfNull": True
            },
            "description": "Only sets vendor when it's currently null or empty"
        },
        {
            "name": "No Vendor Updates",
            "config": {
                "storeId": "store_123",
                "fields": ["title"],
                "tagRule": {"producer_003": ["vintage", "classic"]},
                "overrideVendor": False,
                "updateVendorIfNull": False
            },
            "description": "Only tags with producer_id, doesn't modify vendor field"
        }
    ]
    
    for scenario in scenarios:
        print(f"\n--- {scenario['name']} ---")
        print(f"Description: {scenario['description']}")
        
        # Initialize operation
        response = client.initialize_tagging(scenario['config'])
        
        if response.get("success"):
            operation_id = response["data"]["operation_id"]
            print(f"Operation {operation_id} started")
            
            # In a real scenario, you'd wait for completion here
            # For this example, we'll just show the initialization
        else:
            print(f"Failed to initialize: {response.get('message')}")


def example_monitoring_progress():
    """
    Example of monitoring operation progress in real-time
    """
    print("\n=== Progress Monitoring Example ===")
    
    client = SalesTaggingClient(
        base_url="https://your-region-your-project.cloudfunctions.net",
        auth_token="your-firebase-auth-token"
    )
    
    # Start a tagging operation
    tag_request = {
        "storeId": "large_store_456",
        "fields": ["title", "vendor"],
        "tagRule": {
            "producer_A": ["keyword1", "keyword2"],
            "producer_B": ["keyword3", "keyword4"],
            "producer_C": ["keyword5", "keyword6"]
        },
        "overrideVendor": False,
        "updateVendorIfNull": True
    }
    
    init_response = client.initialize_tagging(tag_request)
    
    if not init_response.get("success"):
        print(f"Failed to initialize: {init_response.get('message')}")
        return
    
    operation_id = init_response["data"]["operation_id"]
    print(f"Monitoring operation {operation_id}...")
    
    # Monitor progress with detailed output
    start_time = time.time()
    
    while True:
        status_response = client.get_tagging_status(operation_id)
        
        if not status_response.get("success"):
            print(f"Error getting status: {status_response.get('message')}")
            break
        
        data = status_response["data"]
        elapsed = time.time() - start_time
        
        print(f"\n[{elapsed:.0f}s] Status: {data['status']}")
        print(f"Progress: {data['progress_percentage']:.1f}%")
        print(f"Batches: {data['batches_completed']}/{data['total_batches']}")
        print(f"Documents: {data['documents_processed']}/{data['total_documents']}")
        print(f"Tagged: {data['documents_tagged']}")
        
        if data['status'] in ['completed', 'error']:
            break
        
        time.sleep(5)  # Check every 5 seconds
    
    print(f"\nOperation finished in {elapsed:.0f} seconds")


def example_error_handling():
    """
    Example of proper error handling
    """
    print("\n=== Error Handling Example ===")
    
    client = SalesTaggingClient(
        base_url="https://your-region-your-project.cloudfunctions.net",
        auth_token="your-firebase-auth-token"
    )
    
    # Example with invalid configuration
    invalid_request = {
        "storeId": "",  # Invalid: empty store ID
        "fields": [],   # Invalid: no fields specified
        "tagRule": {},  # Invalid: no tag rules
        "overrideVendor": True,
        "updateVendorIfNull": False
    }
    
    print("Testing with invalid configuration...")
    response = client.initialize_tagging(invalid_request)
    
    if not response.get("success"):
        print(f"Expected error: {response.get('message')} (Code: {response.get('code')})")
    
    # Example with non-existent operation
    print("\nTesting status check for non-existent operation...")
    status_response = client.get_tagging_status("nonexistent_operation_id")
    
    if not status_response.get("success"):
        print(f"Expected error: {status_response.get('message')} (Code: {status_response.get('code')})")


if __name__ == "__main__":
    print("Sales Staging Tagger Cloud Functions Examples")
    print("=" * 50)
    
    # Note: These examples require actual cloud function URLs and auth tokens
    # Replace the placeholder values with your actual deployment details
    
    print("\n⚠️  Note: Update the base_url and auth_token in the examples")
    print("    before running with actual cloud functions.")
    
    # Uncomment the examples you want to run:
    # example_basic_tagging()
    # example_vendor_update_scenarios()
    # example_monitoring_progress()
    # example_error_handling()
    
    print("\nExamples ready to run with proper configuration!")
