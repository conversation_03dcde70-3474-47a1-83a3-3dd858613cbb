import pandas as pd
from typing import Dict, List, Any
import json
from datetime import datetime
from columns.shopify_raw_order_columns import (
    ShopifyOrderColumnNames, 
    ShopifyOrderJsonKeys,
    ShopifyOrderAdditionalColumnNames
)

class ShopifyOrderParser:
    @staticmethod
    def parse_line_item(line_item: Dict[str, Any]) -> Dict[str, Any]:
        """Parse a single line item from an order."""
        node = line_item[ShopifyOrderJsonKeys.NODE.value]
        
        # Helper function to safely convert string to float
        def safe_float(value: Any) -> float:
            try:
                return float(value) if value is not None else None
            except (ValueError, TypeError):
                return None
                
        return {
            ShopifyOrderColumnNames.LINE_ITEM_ID.value: node.get(ShopifyOrderJsonKeys.ID.value),
            ShopifyOrderColumnNames.PRODUCT_ID.value: node.get(
                ShopifyOrderJsonKeys.PRODUCT.value, 
                {}
            ).get(ShopifyOrderJsonKeys.ID.value) if node.get(ShopifyOrderJsonKeys.PRODUCT.value) else None,
            ShopifyOrderColumnNames.TITLE.value: node.get(ShopifyOrderJsonKeys.TITLE.value),
            ShopifyOrderColumnNames.VARIANT_TITLE.value: node.get(ShopifyOrderJsonKeys.VARIANT_TITLE.value),
            ShopifyOrderColumnNames.VARIANT_DISPLAY_NAME.value: node.get(
                ShopifyOrderJsonKeys.VARIANT.value, 
                {}
            ).get(ShopifyOrderJsonKeys.DISPLAY_NAME.value) if node.get(ShopifyOrderJsonKeys.VARIANT.value) else None,
            ShopifyOrderColumnNames.QUANTITY.value: node.get(ShopifyOrderJsonKeys.QUANTITY.value, 0),
            ShopifyOrderColumnNames.VENDOR.value: node.get(ShopifyOrderJsonKeys.VENDOR.value, ''),
            ShopifyOrderColumnNames.UNIT_PRICE.value: safe_float(
                node.get(ShopifyOrderJsonKeys.ORIGINAL_UNIT_PRICE_SET.value, {})
                .get(ShopifyOrderJsonKeys.SHOP_MONEY.value, {})
                .get(ShopifyOrderJsonKeys.AMOUNT.value)
            ),
            ShopifyOrderColumnNames.TOTAL_PRICE.value: safe_float(
                node.get(ShopifyOrderJsonKeys.ORIGINAL_TOTAL_SET.value, {})
                .get(ShopifyOrderJsonKeys.PRESENTMENT_MONEY.value, {})
                .get(ShopifyOrderJsonKeys.AMOUNT.value)
            ),
            ShopifyOrderColumnNames.CURRENCY.value: node.get(ShopifyOrderJsonKeys.ORIGINAL_TOTAL_SET.value, {})
                .get(ShopifyOrderJsonKeys.PRESENTMENT_MONEY.value, {})
                .get(ShopifyOrderJsonKeys.CURRENCY_CODE.value, 'EUR')  # Default to EUR if not specified
        }

    @staticmethod
    def parse_order(order: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Parse a single order and its line items."""
        node = order[ShopifyOrderJsonKeys.NODE.value]
        
        # Only process closed and fully paid orders
        if not (node[ShopifyOrderJsonKeys.CLOSED.value] and node[ShopifyOrderJsonKeys.FULLY_PAID.value]):
            return []
        
        base_order_data = {
            ShopifyOrderColumnNames.ORDER_ID.value: node[ShopifyOrderJsonKeys.ID.value],
            ShopifyOrderColumnNames.UPDATED_AT.value: datetime.fromisoformat(
                node[ShopifyOrderJsonKeys.UPDATED_AT.value]
                .replace('Z', '+00:00')
            ),
            ShopifyOrderColumnNames.SUBTOTAL.value: float(
                node[ShopifyOrderJsonKeys.CURRENT_SUBTOTAL_PRICE_SET.value]
                [ShopifyOrderJsonKeys.SHOP_MONEY.value]
                [ShopifyOrderJsonKeys.AMOUNT.value]
            ),
            ShopifyOrderColumnNames.CURRENCY.value: (
                node[ShopifyOrderJsonKeys.CURRENT_SUBTOTAL_PRICE_SET.value]
                [ShopifyOrderJsonKeys.SHOP_MONEY.value]
                [ShopifyOrderJsonKeys.CURRENCY_CODE.value]
            ),
            ShopifyOrderColumnNames.DISCOUNT.value: float(
                node[ShopifyOrderJsonKeys.CART_DISCOUNT_AMOUNT_SET.value]
                [ShopifyOrderJsonKeys.SHOP_MONEY.value]
                [ShopifyOrderJsonKeys.AMOUNT.value]
            ) if node[ShopifyOrderJsonKeys.CART_DISCOUNT_AMOUNT_SET.value] else 0.0
        }
        
        flattened_items = []
        for line_item in node[ShopifyOrderJsonKeys.LINE_ITEMS.value][ShopifyOrderJsonKeys.EDGES.value]:
            item_data = ShopifyOrderParser.parse_line_item(line_item)
            flattened_items.append({**base_order_data, **item_data})
        
        return flattened_items

    @staticmethod
    def parse_shopify_orders(json_data: str, store_id: str) -> pd.DataFrame:
        """Parse Shopify raw orders JSON into a flat pandas DataFrame and optionally save as parquet.
        
        Args:
            json_data: JSON string or dict containing Shopify orders data
        
        Returns:
            Pandas DataFrame containing parsed order data
        """
        data = json.loads(json_data)
        
        flattened_data = []
        for order in data[ShopifyOrderJsonKeys.ORDERS.value][ShopifyOrderJsonKeys.EDGES.value]:
            flattened_data.extend(ShopifyOrderParser.parse_order(order))
        
        df = pd.DataFrame(flattened_data)
        df = df[[col.value for col in ShopifyOrderColumnNames]]
        df[ShopifyOrderAdditionalColumnNames.STORE_ID.value] = store_id

        return df