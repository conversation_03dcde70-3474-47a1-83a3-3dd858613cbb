import json
from typing import Dict, List, Any, Optional
from models.products import Product, ProductVariant, products_collection, product_variants_collection
from hasher.shopify_gid import shopify_gid_to_hash # Import the hashing function
from firebase_functions import logger

# It is assumed that the db instance passed will have a similar interface to firestore.Client
# e.g., from google.cloud.firestore import Client

class ShopifyProductParser:
    @staticmethod
    def parse_variant(variant_edge_data: Dict[str, Any], product_id: str) -> Optional[ProductVariant]:
        """Parse a single variant edge from a product into a ProductVariant object."""
        variant_node_data = variant_edge_data.get('node')


        if not variant_node_data:
            logger.warning(f"Variant edge data missing node for product {product_id}")
            # TODO: Consider logging this case if a variant edge is expected to always have a node
            return None
        
        try:
            # Add the parent product_id to the variant data before parsing
            # This ensures the ProductVariant model can link back to its parent Product
            variant_data_with_product_id = variant_node_data.copy()
            variant_data_with_product_id[ProductVariant.PRODUCT_ID_FIELD] = product_id
            variant_data_with_product_id[ProductVariant.PRODUCT_VARIANT_ID_FIELD] = (
                variant_node_data.get('id')
            )
            
            # Pydantic will use the aliases defined in ProductVariant
            # (e.g., 'id' maps to 'product_variant_id')
            return ProductVariant.model_validate(variant_data_with_product_id)
        except Exception as e:
            # TODO: Implement proper logging (e.g., using Python's logging module)
            variant_gid = variant_node_data.get('id', 'Unknown Variant ID')
            print(f"Error parsing variant node {variant_gid} for product {product_id}: {e}")
            print(f"Problematic variant data: {variant_node_data}")
            return None

    @staticmethod
    def parse_product(product_edge_data: Dict[str, Any], 
                      store_id: Optional[str] = None, 
                      store_display_name: Optional[str] = None,
                      producer_id: Optional[str] = None,
                      producer_display_name: Optional[str] = None) -> Optional[Product]:
        """Parse a single product edge into a Product object, including its variants."""
        product_node_data = product_edge_data.get('node')
        if not product_node_data:
            # TODO: Consider logging
            return None

        product_gid = product_node_data.get('id')

        if not product_gid:
            # TODO: Implement proper logging
            print(f"Product node missing ID. Data: {product_node_data}")
            logger.error(f"Product node missing ID. Data: {product_node_data}")
            return None

        parsed_variants: List[ProductVariant] = []
        # Safely access nested variant data
        variants_data_container = product_node_data.get('variants', {})
        if isinstance(variants_data_container, dict):
            variant_edges = variants_data_container.get('edges', [])
            if isinstance(variant_edges, list):
                for variant_edge in variant_edges:
                    if isinstance(variant_edge, dict):
                        parsed_variant = ShopifyProductParser.parse_variant(variant_edge, product_gid)
                        if parsed_variant:
                            parsed_variants.append(parsed_variant)
                    else:
                        # TODO: Log unexpected variant_edge structure
                        print(f"Warning: Unexpected variant edge format for product {product_gid}: {variant_edge}")
            else:
                # TODO: Log unexpected structure for variant_edges
                print(f"Warning: 'edges' in variants is not a list for product {product_gid}.")
        else:
            # TODO: Log unexpected structure for variants_data_container
            print(f"Warning: 'variants' data is not a dictionary for product {product_gid}.")
            
        try:
            # Prepare data for Product model instantiation
            # Pydantic will use aliases from Product model (e.g. 'id' to 'product_id')
            product_data_for_model = product_node_data.copy()
            product_data_for_model['variants'] = parsed_variants # Assign the list of parsed ProductVariant objects
            product_data_for_model[Product.PRODUCT_ID_FIELD] = product_gid
            
            # Add store context if provided
            if store_id:
                product_data_for_model[Product.STORE_ID_FIELD] = store_id
            if store_display_name:
                product_data_for_model[Product.STORE_DISPLAY_NAME_FIELD] = store_display_name
            
            # Add producer context if provided
            if producer_id:
                product_data_for_model[Product.PRODUCER_ID_FIELD] = producer_id
            if producer_display_name:
                product_data_for_model[Product.PRODUCER_DISPLAY_NAME_FIELD] = producer_display_name

            return Product.model_validate(product_data_for_model)
        except Exception as e:
            # TODO: Implement proper logging
            print(f"Error parsing product node {product_gid}: {e}")
            print(f"Problematic product data (after adding context): {product_data_for_model}")
            return None

    @staticmethod
    def parse_shopify_products(json_string_data: str, 
                               store_id: Optional[str] = None, 
                               store_display_name: Optional[str] = None,
                               producer_id: Optional[str] = None,
                               producer_display_name: Optional[str] = None,
                               db: Optional[Any] = None,
                               hash_salt: str = 'shopify_gid') -> List[Product]:
        """Parse a JSON string of Shopify products into a list of Product objects.
           Optionally, if a db instance is provided, create/update documents in Firestore
           using hashed GIDs as document IDs in a batch operation.

        Args:
            json_string_data: JSON string containing Shopify products data.
            store_id: Optional store ID to associate with the products.
            store_display_name: Optional store display name.
            producer_id: Optional producer ID to associate with the products.
            producer_display_name: Optional producer display name.
            db: Optional Firestore client instance (or an object with a compatible interface).
            hash_salt: Optional salt for hashing GIDs. Defaults to 'shopify_gid'.

        Returns:
            A list of Product objects.
            
        Raises:
            ValueError: If neither (store_id AND store_display_name) 
                        NOR (producer_id AND producer_display_name) are sufficiently provided.
                        This is now handled by printing a warning and skipping the product.
        """
        try:
            raw_data = json.loads(json_string_data)
        except json.JSONDecodeError as e:
            # TODO: Implement proper logging
            print(f"Error decoding JSON string: {e}")
            return []

        products_list: List[Product] = []
        
        products_container = None
        # Try to get products from {"data": {"products": ...}} structure
        data_level = raw_data.get('data')
        if isinstance(data_level, dict):
            products_container = data_level.get('products')
        
        # If not found in "data" or "data" key doesn't exist, try root level {"products": ...}
        if not isinstance(products_container, dict):
            products_container = raw_data.get('products')

        if not isinstance(products_container, dict):
            logger.warn("Warning: 'products' key not found or not a dictionary in the expected locations (root or under 'data').")
            return []

        product_edges = products_container.get('edges', [])
        if not isinstance(product_edges, list):
            logger.warn("Warning: 'edges' under 'products' is not a list or is missing.")
            return []

        # Lists to hold data for batch Firestore write
        products_to_upload = []
        variants_to_upload = []

        for product_edge in product_edges:
            if isinstance(product_edge, dict):
                # Validation for context (store or producer)
                store_info_present = store_id is not None and store_display_name is not None
                producer_info_present = producer_id is not None and producer_display_name is not None

                if not (store_info_present or producer_info_present):
                    product_gid_for_error = product_edge.get('node', {}).get('id', 'Unknown Product ID')
                    logger.warn(f"Warning: Product GID {product_gid_for_error} is missing required context. "
                          f"Either (store_id AND store_display_name) OR (producer_id AND producer_display_name) must be provided. "
                          f"Skipping this product.")
                    continue # Skip this product_edge

                parsed_product = ShopifyProductParser.parse_product(
                    product_edge, 
                    store_id, 
                    store_display_name,
                    producer_id,
                    producer_display_name
                )
                if parsed_product:
                    products_list.append(parsed_product)
                    
                    if db: # If db instance is provided, prepare data for batch upload
                        product_doc_id = shopify_gid_to_hash(parsed_product.product_id, salt=hash_salt)
                        # Ensure producer fields are included if present in the model, even if they are None
                        product_data_for_firestore = parsed_product.model_dump(exclude={'variants'}, exclude_none=False)
                        
                        products_to_upload.append({
                            "doc_id": product_doc_id,
                            "data": product_data_for_firestore,
                            "gid": parsed_product.product_id # For error logging
                        })

                        for variant in parsed_product.variants:
                            variant_doc_id = shopify_gid_to_hash(variant.product_variant_id, salt=hash_salt)
                            variant_data_for_firestore = variant.model_dump(exclude_none=False)
                            variants_to_upload.append({
                                "doc_id": variant_doc_id,
                                "data": variant_data_for_firestore,
                                "gid": variant.product_variant_id # For error logging
                            })
            else:
                # TODO: Log unexpected product_edge structure
                logger.warn(f"Warning: Unexpected product edge format: {product_edge}")

        if db and (products_to_upload or variants_to_upload):
            batch = db.batch()
            try:
                for product_item in products_to_upload:
                    doc_ref = db.collection(products_collection).document(product_item["doc_id"])
                    batch.set(doc_ref, product_item["data"], merge=True)
                
                for variant_item in variants_to_upload:
                    doc_ref = db.collection(product_variants_collection).document(variant_item["doc_id"])
                    batch.set(doc_ref, variant_item["data"], merge=True)
                
                batch.commit()
                # print(f"Successfully batched upsert of {len(products_to_upload)} products and {len(variants_to_upload)} variants to Firestore.")

            except Exception as e:
                # TODO: Implement more specific error handling/logging for DB batch operations
                logger.error(f"Error during Firestore batch commit: {e}")


        return products_list
