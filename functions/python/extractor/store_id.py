def extract_store_id(file_path: str) -> str:
    """Extract store ID from a file path.
    
    Args:
        file_path (str): Path string in format 'stores/store-id/...'
        
    Returns:
        str: The extracted store ID
        
    Raises:
        ValueError: If path is empty, None, or in invalid format
    """
    # test_path = "stores/test-store-123/orders/2024/03/20/shopify_raw_orders.json"
    file_path = file_path.split("/")
        
    if not file_path:
        raise ValueError("Empty path")
    
    return file_path[1]