import re
import pytest
from python.constants.gaco_path_patterns import shopify_orders_pattern

SHOPIFY_ORDERS_PATTERN = shopify_orders_pattern

@pytest.mark.parametrize("file_path,expected", [
    # Valid paths
    (
        "stores/vBONpFXTdm2rwBIP7ON4/orders/2025/04/20/shopify_orders_vBONpFXTdm2rwBIP7ON4_20250420_211028/page_10.json", 
        True
    ),
    ("stores/STORE1/orders/2024/12/31/shopify_orders_23_59_59.json", False),
    ("stores/test_store/orders/2025/1/1/shopify_orders_test_store_0_0_0/page_1.json", True),
    
    # Invalid paths
    ("stores/test-store/orders/2024/3/15/shopify_raw_orders_9_5_30.json", False),  # Invalid store ID (contains hyphen)
    ("stores/abc123/wrong/2024/3/15/shopify_raw_orders_9_5_30.json", False),  # Wrong middle path
    ("stores/abc123/orders/24/3/15/shopify_raw_orders_9_5_30.json", False),  # Invalid year format
    ("stores/abc123/orders/2024/3/15/wrong_prefix_9_5_30.json", False),  # Wrong file prefix
    (
        "stores/vBONpFXTdm2rwBIP7ON4/products/2025/04/20/shopify_products_vBONpFXTdm2rwBIP7ON4_20250420_211028/page_10.json", 
        False
    ),
])
def test_shopify_orders_pattern(file_path: str, expected: bool):
    """Test if the pattern correctly matches valid Shopify order file paths."""
    match = re.match(SHOPIFY_ORDERS_PATTERN, file_path)
    assert bool(match) == expected, f"Failed for path: {file_path}"

def test_pattern_groups():
    """Test if we can extract the correct components from a valid path."""
    test_path = "stores/abc123/orders/2024/3/15/shopify_orders_abc123_20240315_000000/page_1.json"
    match = re.match(SHOPIFY_ORDERS_PATTERN, test_path)
    assert match is not None
    
    # The entire match should be the complete path
    assert match.group(0) == test_path