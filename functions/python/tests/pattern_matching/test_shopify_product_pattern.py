import re
import pytest
from python.constants.gaco_path_patterns import shopify_products_pattern

SHOPIFY_PRODUCTS_PATTERN = shopify_products_pattern

@pytest.mark.parametrize("file_path,expected", [
    # Valid paths
    (
        "stores/vBONpFXTdm2rwBIP7ON4/products/2025/04/20/shopify_products_vBONpFXTdm2rwBIP7ON4_20250420_211028/page_10.json", 
        True
    ),
    (
        "producers/testproducer/products/2025/04/20/shopify_products_testproducer_20250420_211028/page_10.json", 
        True
    ),
    (
        "stores/test-store-prod-import-96abf7/products/2025/05/22/shopify_products_test-store-prod-import-96abf7_20250522_095448_802951/page_1.json",
        False
    ),
    
])
def test_shopify_products_pattern(file_path: str, expected: bool):
    """Test if the pattern correctly matches valid Shopify product file paths."""
    match = re.match(SHOPIFY_PRODUCTS_PATTERN, file_path)
    assert bool(match) == expected, f"Failed for path: {file_path}"

