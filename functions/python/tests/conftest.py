import sys
import os
from pathlib import Path
import pytest
from google.cloud import secretmanager
from datetime import datetime, timezone
from firebase_admin import firestore, credentials, initialize_app
from google.api_core import retry
from .utils.cloud_function_tester import CloudFunctionTester
from .utils.firestore_rest_client_helper import FirestoreRestClientHelper
from .utils.storage_rest_client_helper import StorageRestClientHelper
from google.auth.credentials import Credentials
import uuid
# Add the parent directory (functions/python) to the Python path
root_dir = Path(__file__).parent.parent
sys.path.append(str(root_dir))

from services.user_root_account_manager import UserRootAccountManager
from services.store_manager import StoreManager
from models.requests.store_requests import CreateStoreRequest, ShopifyStoreCredentials, Address, DeleteStoreRequest
from gaco_secrets.secret_manager import SecretManager
from constants.gaco_values import project_id
from time import sleep
from firebase_admin import auth
import time
from services.producer_manager import ProducerManager
from models.requests.producer_requests import CreateProducerRequest
from datetime import timedelta
import random
from models.sales import SalesStaging
from flows.force_active_partnership import ForceActivePartnershipFlow
from models.requests.sanitization_request import ForceAgreementRequest
from firebase_functions import logger
from models.agreement import Agreement, AgreementStatus, ApprovalStep, ApprovalStatus, Role
from models.access_right import AccessRight
from services.partner_manager import PartnershipManager
from gaco_framework.auth import AuthContext

@pytest.fixture(scope="session")
def firebase_app():
    try:
        # For emulator use, set environment variables first
        cred_path = os.getenv("LOCAL_GOOGLE_APPLICATION_CREDENTIALS")
        if cred_path is not None:
            cred = credentials.Certificate(cred_path)  # Use dummy creds
            return initialize_app(credential=cred)
        return initialize_app()
    except ValueError as e:
        if "The default Firebase app already exists" in str(e):
            return firestore.client().app
        raise

@pytest.fixture(scope="session")
def db(firebase_app):
    client = firestore.client()
    client._transaction_retry = retry.Retry(
        initial=1.0,
        maximum=300.0,
        multiplier=1.5,
        deadline=300.0
    )
    client._retry_timeout = retry.Retry(
        initial=1.0,
        maximum=300.0,
        multiplier=1.5,
        deadline=300.0
    )
    return client

@pytest.fixture(scope="session")
def storage_bucket(firebase_app):
    from firebase_admin import storage
    from constants.gaco_values import bucket_name
    return storage.bucket(bucket_name)
    
@pytest.fixture(scope="session")
def project_id_fixture():
    from constants.gaco_values import project_id
    return project_id

@pytest.fixture(scope="session")
def secret_manager_client(project_id_fixture):
    return secretmanager.SecretManagerServiceClient()

@pytest.fixture(scope="function")
def unique_id():
    return f"test-{datetime.now(timezone.utc).timestamp()}"

@pytest.fixture
def cloud_function_tester():
    return CloudFunctionTester()

@pytest.fixture
def firestore_rest_client_helper():
    return FirestoreRestClientHelper()

@pytest.fixture
def storage_rest_client_helper():
    return StorageRestClientHelper()

@pytest.fixture(scope="function")
def test_user(firebase_app):
    unique_timestamp = int(time.time())
    email = f'test-integration-{unique_timestamp}@example.com'
    password = 'testpassword123'
    try:
        user = auth.create_user(
            email=email,
            password=password,
            display_name='Test Shopify Integration User'
        )
        yield user
    finally:
        try:
            auth.delete_user(user.uid)
            print(f"Deleted test user: {user.uid}")
        except auth.UserNotFoundError:
            print(f"User {user.uid} already deleted or not found")
        except Exception as e:
            print(f"Error during test user cleanup: {e}")

@pytest.fixture
def auth_context(test_user):
    """Creates a Gaco AuthContext for the test user."""
    return AuthContext(user_id=test_user.uid, email=test_user.email, email_verified=True)

@pytest.fixture
def user_with_root_account(db, test_user):
    yield test_user
    user_root_account_manager = UserRootAccountManager(db)
    user_root_account_manager.delete_user_root_account(test_user.uid)

@pytest.fixture
def test_user_b(db):
    unique_suffix = int(time.time())
    email = f'test-integration-b-{unique_suffix}@example.com'
    password = 'testpassword123'
    user = auth.create_user(email=email, password=password)
    yield user
    auth.delete_user(user.uid)

@pytest.fixture
def test_store(db, user_with_root_account, auth_context):
    store_manager = StoreManager(db, auth_context=auth_context)
    unique_suffix = int(time.time())
    shop_name = os.getenv("SHOPIFY_TEST_SHOP_NAME")
    shopify_api_key = os.getenv("SHOPIFY_TEST_ACCESS_TOKEN")
    
    store_data = CreateStoreRequest(
        display_name=f"Test Store {unique_suffix}",
        email=f"test-store-{unique_suffix}@example.com",
        tax_a2="US",
        default_commission=10,
        description="Test store for Shopify integration",
        shopify_api_key_object=ShopifyStoreCredentials(
            shop_name=shop_name,
            shopify_api_key=shopify_api_key
        ),
        address=Address(
            street_number="123",
            street_name="Test Street",
            zip_code="12345",
            city="Test City",
            country="US"
        ),
        access_right=AccessRight.ADMIN
    )
    
    store_id = store_manager.create_store(store_data)
    
    yield store_id
    
    sleep(5)
    try:
        delete_request = DeleteStoreRequest(
            store_id=store_id, 
            hard_delete=True
        )
        store_manager.delete_store(delete_request)
    except Exception as e:
        logger.warn(f"Cleanup failed for store {store_id}: {e}")


@pytest.fixture
def sales_staging_test_document(db, test_store):
    sales_id = str(uuid.uuid4())
    sales_staging = SalesStaging(
        document_id=sales_id,
        store_id=test_store,
        order_id=f"order_{random.randint(10000, 99999)}",
        line_item_id=f"item_{random.randint(10000, 99999)}",
        product_id=f"prod_{random.randint(10000, 99999)}",
        title="Test Product",
        variant_title="Test Variant",
        variant_display_name="Test Product - Test Variant",
        quantity=1,
        unit_price=100,
        subtotal=100,
        total_price=100,
        currency="USD",
        discount=0,
        vendor="Unknown Vendor",  # This is why we need to link it to a producer
        updated_at=datetime.now(timezone.utc),
        store_display_name="Test Store"
    )

    sales_staging_ref = db.collection("sales-staging").document(sales_id)
    sales_staging_ref.set(sales_staging.model_dump())
    
    yield sales_staging_ref
    
    sales_staging_ref.delete()


@pytest.fixture
def sample_agreement(db, test_store, test_producer, test_user):
    agreement = Agreement(
        store_id=test_store,
        producer_id=test_producer,
        effective_date=datetime.now(timezone.utc),
        expiration_date=datetime.now(timezone.utc) + timedelta(days=365),
        commission=69,
        status=AgreementStatus.ACTIVE,
        approval_workflow=[
            ApprovalStep(
                role=Role.SYSTEM, 
                approver_id=test_user.uid, 
                status=ApprovalStatus.APPROVED
            )
        ],
        created_by=test_user.uid,
        created_by_role=Role.SYSTEM,
        created_at=datetime.now(timezone.utc),
        updated_by=test_user.uid,
        updated_at=datetime.now(timezone.utc),
    )

    ref = db.collection("agreements").document()
    ref.set(agreement.model_dump())

    yield ref
    
    ref.delete()
    
@pytest.fixture
def test_store_NL(db, user_with_root_account, auth_context):
    store_manager = StoreManager(db, auth_context=auth_context)
    unique_suffix = int(time.time())
    shop_name = os.getenv("SHOPIFY_TEST_SHOP_NAME")
    shopify_api_key = os.getenv("SHOPIFY_TEST_ACCESS_TOKEN")
    
    store_data = CreateStoreRequest(
        display_name=f"Test Store {unique_suffix}",
        email=f"test-store-{unique_suffix}@example.com",
        tax_a2="NL",
        default_commission=10,
        description="Test store for Shopify integration",
        shopify_api_key_object=ShopifyStoreCredentials(
            shop_name=shop_name,
            shopify_api_key=shopify_api_key
        ),
        address=Address(
            street_number="123",
            street_name="Test Street",
            zip_code="12345",
            city="Test City",
            country="US"
        ),
        access_right=AccessRight.ADMIN
    )
    
    store_id = store_manager.create_store(store_data)
    
    yield store_id
    
    sleep(5)
    delete_request = DeleteStoreRequest(
        store_id=store_id, 
        hard_delete=True
    )
    try:
        store_manager.delete_store(delete_request)
    except Exception as e:
        logger.warn(f"Cleanup failed for store {store_id}: {e}")

@pytest.fixture
def test_producer(db, user_with_root_account):
    """Create a test producer"""
    sleep(10) #for custom claim to propagate
    auth_context = AuthContext(user_id=user_with_root_account.uid)
    producer_manager = ProducerManager(db, auth_context)
    unique_suffix = int(time.time())
    request = CreateProducerRequest(
        display_name=f"Test Producer {unique_suffix}",
        email=f"test-producer-{unique_suffix}@example.com",
        tax_a2="US",
        access_right=AccessRight.ADMIN.value
    )
    producer_id = producer_manager.create_producer(request)
    yield producer_id

    try:
        # Cleanup - delete the producer
        producer_manager.producer_collection.document(producer_id).delete()
    except Exception as e:
        logger.warn(f"Cleanup failed for producer {producer_id}: {e}")

@pytest.fixture
def test_n():
    return 5

@pytest.fixture
def test_expiration_start_date():
    return datetime(2023, 1, 1)

@pytest.fixture
def test_expiration_end_date():
    return datetime(2023, 12, 1)

@pytest.fixture
def test_start_date():
    return datetime(2024, 1, 1)

@pytest.fixture
def test_end_date():
    return datetime(2024, 12, 1)

@pytest.fixture
def test_email(test_user):
    return f"test-{test_user.uid}@test.gaco.com"

@pytest.fixture
def test_force_create_active_partnership(
    db, test_store_NL, test_user, test_start_date, test_end_date, test_email
):
    sleep(10) # wait for the token update

    # Create an AuthContext for the test user
    auth_context = AuthContext(user_id=test_user.uid)
    flow = ForceActivePartnershipFlow(db, auth_context)

    effective_date = (test_start_date - timedelta(days=1)).isoformat()
    expiration_date = (test_end_date + timedelta(days=10)).isoformat()


    request = ForceAgreementRequest(
        store_id=test_store_NL,
        title="test-title",
        effective_date=effective_date,
        expiration_date=expiration_date,
        commission=100,
        document_url="document_url",
        display_name="test-display-name",
        email=test_email,
        tax_a2="NL"
    )

    # wait for the agreement to be created and data to be propagated
    sleep(10)

    # this is where authentication have to be handled, stop baking it in the logic.

    result = flow.force_create_active_partnership(request)
    yield result

    try:
        partnership_manager = PartnershipManager(db)
        deleted_partnership_id = partnership_manager.delete_partnership_and_agreement(result["partnership_id"])
        assert deleted_partnership_id == result["partnership_id"]
        
        db.collection("producers").document(result["producer_id"]).delete()
    except Exception as e:
        logger.warn(f"Cleanup failed for agreement {result['agreement_id']}: {e}")


@pytest.fixture
def n_sales_stagin_to_gold(
    db, test_n, test_start_date, test_end_date, 
    test_store_NL, test_force_create_active_partnership
    ):

    def _date_picker(start_date, end_date):
        """Returns a random date between start_date and end_date."""
        time_delta = end_date - start_date
        random_days = random.randint(0, time_delta.days)
        random_date = start_date + timedelta(days=random_days)
        return random_date.replace(tzinfo=timezone.utc)

    producer_id = test_force_create_active_partnership["producer_id"]

    batch = db.batch()
    document_ids = []
    for i in range(test_n):
        document_id = f"test-{i}"
        product_id = f"product_id_{i}"

        document_ids.append(document_id)

        sales_staging = SalesStaging(
            document_id=document_id,
            store_id=test_store_NL,
            order_id=f"order_id_{i}",
            line_item_id=f"line_item_id_{i}",
            title=f"title_{i}",
            variant_title=f"variant_title_{i}",
            variant_display_name=f"variant_display_name_{i}",
            quantity=1,
            unit_price=100,
            subtotal=100,
            total_price=100,
            currency="EUR",
            discount=0,
            vendor=producer_id,
            product_id=product_id,
            updated_at=_date_picker(test_start_date, test_end_date),
            store_display_name=f"test-store-{test_store_NL}",
        )

        batch.set(
            db.collection("sales-staging").document(document_id), 
            sales_staging.model_dump()
        )

    batch.commit()

    # wait till the on create function picks up
    sleep(10)

    yield document_ids

    try:
        batch = db.batch()
        for document_id in document_ids:
            doc_ref = db.collection("sales-staging").document(document_id)
            batch.delete(doc_ref)
            silver_doc_ref = db.collection("sales-silver").document(document_id)
            batch.delete(silver_doc_ref)
            gold_doc_ref = db.collection("sales-gold").document(document_id)
            batch.delete(gold_doc_ref)
        batch.commit()
        logger.info(f"Batch deleted {len(document_ids)} documents from sales-staging.")
    except Exception as e:
        logger.warn(f"Batch cleanup failed for document_ids {document_ids}: {e}")


@pytest.fixture
def old_start_date():
    return datetime(1996, 1, 1)

@pytest.fixture
def old_end_date():
    return datetime(1999, 12, 1)

import random
@pytest.fixture
def n_sales_stagin(
    db, 
    test_store,
    old_start_date,
    old_end_date
):
    def _date_picker(start_date, end_date):
        """Returns a random date between start_date and end_date."""
        time_delta = end_date - start_date
        random_days = random.randint(0, time_delta.days)
        random_date = start_date + timedelta(days=random_days)
        return random_date.replace(tzinfo=timezone.utc)

    # procuers_count = 3
    # total_sales_count = 15

    def _get_producer_key_word(i):
        less_than_5 = i < 5 
        more_than_5_less_than_10 = i >= 5 and i < 10
        more_than_10_less_than_15 = i >= 10
        match (less_than_5, more_than_5_less_than_10, more_than_10_less_than_15):
            case (True, _, _):
                return random.choice(["producer_1", "producer 1"])
            case (_, True, _):
                return random.choice(["producer_2", "producer 2"])
            case (_, _, True):
                return random.choice(["producer_3", "producer 3"])


    batch = db.batch()
    document_ids = []
    for i in range(15):
        document_id = f"test-{i}"
        product_id = f"product_id_{i}"

        document_ids.append(document_id)

        sales_staging = SalesStaging(
            document_id=document_id,
            store_id=test_store,
            order_id=f"order_id_{i}",
            line_item_id=f"line_item_id_{i}", 
            title=f"title_{i} {_get_producer_key_word(i)}", # here, search by key word
            variant_title=f"variant_title_{i} {_get_producer_key_word(i)}", # here, search by key word
            variant_display_name=f"variant_display_name_{i} {_get_producer_key_word(i)}", # here, search by key word
            quantity=1,
            unit_price=100,
            subtotal=100,
            total_price=100,
            currency="EUR",
            discount=0,
            vendor=f'{_get_producer_key_word(i)}', # here, search by key word
            product_id=product_id,
            updated_at=_date_picker(old_start_date, old_end_date),
            store_display_name=f"test-store-{test_store}",
        )

        batch.set(
            db.collection("sales-staging").document(document_id), 
            sales_staging.model_dump()
        )

    batch.commit()

    # wait till the on create function picks up
    sleep(10)

    yield document_ids

    try:
        batch = db.batch()
        for document_id in document_ids:
            doc_ref = db.collection("sales-staging").document(document_id)
            batch.delete(doc_ref)
            silver_doc_ref = db.collection("sales-silver").document(document_id)
            batch.delete(silver_doc_ref)
            gold_doc_ref = db.collection("sales-gold").document(document_id)
            batch.delete(gold_doc_ref)
        batch.commit()
        logger.info(f"Batch deleted {len(document_ids)} documents from sales-staging.")
    except Exception as e:
        logger.warn(f"Batch cleanup failed for document_ids {document_ids}: {e}")


@pytest.fixture
def test_create_terminated_agreement(
    db, test_store_NL, test_user, test_expiration_start_date, test_expiration_end_date,
    test_force_create_active_partnership
    ):
    auth_context = AuthContext(user_id=test_user.uid)
    flow = ForceActivePartnershipFlow(db, auth_context)
    effective_date = (test_expiration_start_date - timedelta(days=1)).isoformat()
    expiration_date = (test_expiration_end_date + timedelta(days=10)).isoformat()

    producer_id = test_force_create_active_partnership["producer_id"]

    # create terminated agreement
    agreement = Agreement(
        store_id=test_store_NL,
        producer_id=producer_id,
        effective_date=effective_date,
        expiration_date=expiration_date,
        status=AgreementStatus.TERMINATED,
        commission=40,
        approval_workflow=[ApprovalStep(role=Role.SYSTEM, approver_id=test_user.uid, status=ApprovalStatus.APPROVED)],
        created_by=test_user.uid,
        created_by_role=Role.SYSTEM,
        created_at=datetime.now(timezone.utc),
        updated_by=test_user.uid,
        updated_at=datetime.now(timezone.utc),
        renewed_by=None,
        version="1.0",
        document_url="document_url",
        title="test-title",
        partnership_id=None,
    )

    ref = db.collection("agreements").document()
    ref.set(agreement.model_dump())

    yield ref, producer_id

    try:
        ref.delete()
    except Exception as e:
        logger.warn(f"Cleanup failed for agreement {ref.id}: {e}")


@pytest.fixture
def test_create_terminated_n_sales_stagin(
    db, test_n, test_expiration_start_date, test_expiration_end_date, 
    test_store_NL, test_force_create_active_partnership
    ):

    def _date_picker(start_date, end_date):
        """Returns a random date between start_date and end_date."""
        time_delta = end_date - start_date
        random_days = random.randint(0, time_delta.days)
        random_date = start_date + timedelta(days=random_days)
        return random_date.replace(tzinfo=timezone.utc)

    producer_id = test_force_create_active_partnership["producer_id"]

    batch = db.batch()
    document_ids = []
    for i in range(test_n):
        document_id = f"test-{i}"
        product_id = f"product_id_{i}"

        document_ids.append(document_id)

        sales_staging = SalesStaging(
            document_id=document_id,
            store_id=test_store_NL,
            order_id=f"order_id_{i}",
            line_item_id=f"line_item_id_{i}",
            title=f"title_{i}",
            variant_title=f"variant_title_{i}",
            variant_display_name=f"variant_display_name_{i}",
            quantity=1,
            unit_price=100,
            subtotal=100,
            total_price=100,
            currency="EUR",
            discount=0,
            vendor=producer_id,
            product_id=product_id,
            updated_at=_date_picker(test_expiration_start_date, test_expiration_end_date),
            store_display_name=f"test-store-{test_store_NL}",
        )

        batch.set(
            db.collection("sales-staging").document(document_id), 
            sales_staging.model_dump()
        )

    batch.commit()

    # wait till the on_create function picks up
    sleep(10)

    yield document_ids

    try:
        batch = db.batch()
        for document_id in document_ids:
            doc_ref = db.collection("sales-staging").document(document_id)
            batch.delete(doc_ref)
            silver_doc_ref = db.collection("sales-silver").document(document_id)
            batch.delete(silver_doc_ref)
            gold_doc_ref = db.collection("sales-gold").document(document_id)
            batch.delete(gold_doc_ref)
        batch.commit()
        logger.info(f"Batch deleted {len(document_ids)} documents from sales-staging.")
    except Exception as e:
        logger.warn(f"Batch cleanup failed for document_ids {document_ids}: {e}")


@pytest.fixture
def firestore_rest_client(test_user):
    """
    Provides a client for interacting with Firestore via its REST API,
    authenticated as the test_user. Uses CloudFunctionTester to get ID token.
    """
    # CloudFunctionTester.get_id_token is a static method, so we can call it directly.
    id_token = CloudFunctionTester.get_id_token(test_user.uid)
    
    client_instance = FirestoreRestClientHelper(id_token=id_token)
    yield client_instance