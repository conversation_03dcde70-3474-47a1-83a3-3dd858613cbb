import pytest
from pathlib import Path
import json
from typing import List
from datetime import datetime, timezone

# Adjust imports based on your project structure
# This assumes 'parsers' and 'models' are subdirectories of 'python'
# and that 'python' is in your PYTHONPATH or you run pytest from the project root.
from parsers.shopify_raw_products import ShopifyProductParser
from models.products import Product, ProductVariant
# from models.base import DateTime # DateTime import removed, using datetime directly for isinstance checks

@pytest.fixture
def sample_products_json_string() -> str:
    """Provides a sample Shopify products JSON string for testing from a local file."""
    # Load from the specific sample file provided by the user
    sample_file_path = Path(__file__).parent.parent / 'raw_data' / 'shopify_raw_products.json'
    if not sample_file_path.exists():
        # If the primary sample file is missing, raise an error.
        pytest.fail(f"Test sample file not found: {sample_file_path}. Please ensure it exists and contains sample product data.")
    
    with open(sample_file_path, 'r') as f:
        return f.read()


@pytest.fixture
def sample_single_product_json_string() -> str:
    """Provides a sample Shopify products JSON string for testing from a local file."""
    # Load from the specific sample file provided by the user
    sample_file_path = Path(__file__).parent.parent / 'raw_data' / 'shopify_raw_single_product.json'
    if not sample_file_path.exists():
        # If the primary sample file is missing, raise an error.
        pytest.fail(f"Test sample file not found: {sample_file_path}. Please ensure it exists and contains sample product data.")
    
    with open(sample_file_path, 'r') as f:
        return f.read()

    

def test_parse_shopify_products(sample_products_json_string):
    """Test parsing of the Shopify products JSON string based on shopify_raw_products.json."""
    store_id_to_test = "test-gaco-store" # Example store ID
    store_name_to_test = "Gaco Test Store" # Example store name

    parsed_products: List[Product] = ShopifyProductParser.parse_shopify_products(
        sample_products_json_string,
        store_id=store_id_to_test,
        store_display_name=store_name_to_test
    )

    assert isinstance(parsed_products, list)
    # Based on the provided shopify_raw_products.json, there are 2 product edges.
    assert len(parsed_products) == 2, "Should parse two products from the sample JSON"

    # --- Test Product 1: Brass Wallet Chain ---
    product1 = parsed_products[0]
    assert isinstance(product1, Product)
    assert product1.product_id == "gid://shopify/Product/8492199510347"
    assert product1.title == "Brass Wallet Chain"
    assert product1.vendor == "Chaincult"
    assert product1.handle == "brass-wallet-chain"
    assert product1.product_type == "" # As per sample data, Pydantic should handle "" if field is str
    assert product1.status == "ACTIVE"
    assert isinstance(product1.created_at, datetime)
    assert product1.created_at == datetime(2023, 5, 13, 12, 52, 46, tzinfo=timezone.utc)
    assert isinstance(product1.updated_at, datetime)
    assert product1.updated_at == datetime(2024, 11, 19, 7, 26, 28, tzinfo=timezone.utc)
    assert product1.store_id == store_id_to_test
    assert product1.store_display_name == store_name_to_test

    assert isinstance(product1.variants, list)
    assert len(product1.variants) == 1, "Product 1 should have one variant"

    # Test Variant 1.1 of Product 1
    variant1_1 = product1.variants[0]
    assert isinstance(variant1_1, ProductVariant)
    assert variant1_1.product_variant_id == "gid://shopify/ProductVariant/46562587083083"
    assert variant1_1.title == "Default Title"
    assert variant1_1.sku == ""
    assert isinstance(variant1_1.price, float)
    assert variant1_1.price == 69.00
    assert variant1_1.compare_at_price is None # As per sample data (null becomes None)
    assert variant1_1.inventory_quantity == 0
    assert variant1_1.barcode == ""
    assert isinstance(variant1_1.created_at, datetime)
    assert variant1_1.created_at == datetime(2023, 5, 13, 12, 52, 46, tzinfo=timezone.utc)
    assert isinstance(variant1_1.updated_at, datetime)
    assert variant1_1.updated_at == datetime(2024, 5, 4, 13, 32, 25, tzinfo=timezone.utc)
    assert variant1_1.product_id == product1.product_id, "Variant's product_id should match parent"

    # --- Test Product 2: "Kaltes Klares" Steel Wallet Chain ---
    product2 = parsed_products[1]
    assert isinstance(product2, Product)
    assert product2.product_id == "gid://shopify/Product/8492201804107"
    # JSON \u201c and \u201d are opening/closing double quotes
    assert product2.title == "\u201cKaltes Klares\u201d Steel Wallet Chain" 
    assert product2.vendor == "Chaincult"
    assert product2.handle == "steel-wallet-chain"
    assert product2.product_type == ""
    assert product2.status == "ACTIVE"
    assert isinstance(product2.created_at, datetime)
    assert product2.created_at == datetime(2023, 5, 13, 12, 57, 36, tzinfo=timezone.utc)
    assert isinstance(product2.updated_at, datetime)
    assert product2.updated_at == datetime(2024, 10, 16, 13, 10, 28, tzinfo=timezone.utc)
    assert product2.store_id == store_id_to_test
    assert product2.store_display_name == store_name_to_test

    assert isinstance(product2.variants, list)
    assert len(product2.variants) == 2, "Product 2 should have two variants"

    # Test Variant 2.1 of Product 2
    variant2_1 = product2.variants[0]
    assert isinstance(variant2_1, ProductVariant)
    assert variant2_1.product_variant_id == "gid://shopify/ProductVariant/50576588538187"
    assert variant2_1.title == "30cm"
    assert variant2_1.sku is None # As per sample data (null)
    assert isinstance(variant2_1.price, float)
    assert variant2_1.price == 49.00
    assert variant2_1.compare_at_price is None
    assert variant2_1.inventory_quantity == 1
    assert variant2_1.barcode is None
    assert isinstance(variant2_1.created_at, datetime)
    assert variant2_1.created_at == datetime(2024, 10, 12, 12, 16, 46, tzinfo=timezone.utc)
    assert isinstance(variant2_1.updated_at, datetime)
    assert variant2_1.updated_at == datetime(2024, 10, 13, 13, 26, 10, tzinfo=timezone.utc)
    assert variant2_1.product_id == product2.product_id

    # Test Variant 2.2 of Product 2
    variant2_2 = product2.variants[1]
    assert isinstance(variant2_2, ProductVariant)
    assert variant2_2.product_variant_id == "gid://shopify/ProductVariant/50576588570955"
    assert variant2_2.title == "50cm"
    assert variant2_2.sku is None
    assert isinstance(variant2_2.price, float)
    assert variant2_2.price == 59.00
    assert variant2_2.compare_at_price is None
    assert variant2_2.inventory_quantity == 0
    assert variant2_2.barcode is None
    assert isinstance(variant2_2.created_at, datetime)
    assert variant2_2.created_at == datetime(2024, 10, 12, 12, 16, 46, tzinfo=timezone.utc)
    assert isinstance(variant2_2.updated_at, datetime)
    assert variant2_2.updated_at == datetime(2024, 10, 16, 13, 7, 12, tzinfo=timezone.utc)
    assert variant2_2.product_id == product2.product_id


def test_parse_single_product(sample_single_product_json_string):
    """Test parsing of the Shopify single product JSON string based on shopify_raw_single_product.json."""
    store_id_to_test = "test-gaco-store" # Example store ID
    store_name_to_test = "Gaco Test Store" # Example store name

    parsed_products: List[Product] = ShopifyProductParser.parse_shopify_products(
        sample_single_product_json_string,
        store_id=store_id_to_test,
        store_display_name=store_name_to_test
    )


    assert isinstance(parsed_products, list)
    assert len(parsed_products) == 1, "Should parse one product from the sample JSON"




def test_parse_empty_and_malformed_json():
    """Test parser behavior with empty or malformed JSON."""
    # Empty JSON string
    assert ShopifyProductParser.parse_shopify_products("{}", "s1", "n1") == []
    # Malformed JSON string
    assert ShopifyProductParser.parse_shopify_products("{\"data\": malformed}", "s1", "n1") == []
    # JSON without data.products.edges
    assert ShopifyProductParser.parse_shopify_products('{"data": {"products": {}}}', "s1", "n1") == []
    # JSON with products.edges not being a list
    assert ShopifyProductParser.parse_shopify_products('{"data": {"products": {"edges": "not_a_list"}}}', "s1", "n1") == []
    # JSON where a product node is not a dict
    assert ShopifyProductParser.parse_shopify_products('{"data": {"products": {"edges": ["not_a_dict"]}}}', "s1", "n1") == []
    # JSON where a variant node is not a dict
    faulty_variant_json = """
    {
        "data": {
            "products": {
                "edges": [
                    {
                        "node": {
                            "id": "gid://shopify/Product/123",
                            "title": "P1", "status": "ACTIVE",
                            "createdAt": "2023-01-01T00:00:00Z", "updatedAt": "2023-01-01T00:00:00Z",
                            "variants": {"edges": ["not_a_dict"]}
                        }
                    }
                ]
            }
        }
    }
    """
    result_faulty_variant = ShopifyProductParser.parse_shopify_products(faulty_variant_json, "s1", "n1")
    assert len(result_faulty_variant) == 1
    assert len(result_faulty_variant[0].variants) == 0


# def test_parse_shopify_products_with_hash_salt(sample_products_json_string, db):
#     """Test parsing of the Shopify products JSON string based on shopify_raw_products.json."""
#     store_id_to_test = "test-gaco-store" # Example store ID
#     store_name_to_test = "Gaco Test Store" # Example store name

#     parsed_products: List[Product] = ShopifyProductParser.parse_shopify_products(
#         sample_products_json_string,
#         store_id=store_id_to_test,
#         store_display_name=store_name_to_test,
#         db=db
#     )

#     import pdb; pdb.set_trace()
    
