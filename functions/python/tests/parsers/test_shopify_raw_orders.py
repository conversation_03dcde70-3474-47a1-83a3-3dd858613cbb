import pytest
import pandas as pd
from pathlib import Path
from python.parsers.shopify_raw_orders import ShopifyOrderParser
from python.columns.shopify_raw_order_columns import ShopifyOrderColumnNames, ShopifyOrderAdditionalColumnNames

@pytest.fixture
def sample_orders_json():
    json_path = (
        Path(__file__)
        .parent
        .parent
        / 'raw_data'
        / 'shopify_raw_order.json'
    )
    with open(json_path, 'r') as f:
        return f.read()

def test_parse_shopify_orders(sample_orders_json):

    # Parse the JSON data
    df = ShopifyOrderParser.parse_shopify_orders(
        sample_orders_json, 
        "test-store-123"
    )

    # Basic DataFrame checks
    assert isinstance(df, pd.DataFrame)
    assert not df.empty
    assert len(df) == 11  # Number of line items in the sample data
    
    # Check if all required columns are present
    expected_columns = [col.value for col in ShopifyOrderColumnNames] 
    assert all(col in df.columns for col in expected_columns)
    
    # Test specific order values
    first_order = df.iloc[0]
    assert first_order[ShopifyOrderColumnNames.ORDER_ID.value] == "gid://shopify/Order/6281005891915"
    assert first_order[ShopifyOrderColumnNames.LINE_ITEM_ID.value] == "gid://shopify/LineItem/15952700801355"
    assert first_order[ShopifyOrderColumnNames.TITLE.value] == "Taisei ring"
    assert first_order[ShopifyOrderColumnNames.QUANTITY.value] == 1
    assert first_order[ShopifyOrderColumnNames.UNIT_PRICE.value] == 35.0
    assert first_order[ShopifyOrderColumnNames.TOTAL_PRICE.value] == 35.0
    assert first_order[ShopifyOrderColumnNames.CURRENCY.value] == "EUR"
    
    # Test an order with product and variant information
    spoon_ring_order = df[df[ShopifyOrderColumnNames.ORDER_ID.value] == "gid://shopify/Order/6291435356491"].iloc[0]
    assert spoon_ring_order[ShopifyOrderColumnNames.PRODUCT_ID.value] == "gid://shopify/Product/8548400038219"
    assert spoon_ring_order[ShopifyOrderColumnNames.VARIANT_TITLE.value] == "Medium"
    assert spoon_ring_order[ShopifyOrderColumnNames.VARIANT_DISPLAY_NAME.value] == "Spoon Ring - Medium"
    assert spoon_ring_order[ShopifyOrderColumnNames.VENDOR.value] == "Laurie"
    
    # Test order with multiple quantity
    multiple_quantity_order = df[df[ShopifyOrderColumnNames.ORDER_ID.value] == "gid://shopify/Order/6293558985035"].iloc[0]
    assert multiple_quantity_order[ShopifyOrderColumnNames.QUANTITY.value] == 2
    assert multiple_quantity_order[ShopifyOrderColumnNames.UNIT_PRICE.value] == 35.0
    assert multiple_quantity_order[ShopifyOrderColumnNames.TOTAL_PRICE.value] == 70.0

    # Test store ID column
    assert df[ShopifyOrderAdditionalColumnNames.STORE_ID.value].iloc[0] == "test-store-123"
