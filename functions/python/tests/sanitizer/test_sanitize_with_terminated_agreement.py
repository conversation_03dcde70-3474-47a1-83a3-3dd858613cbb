import pytest

def test_sanitize_with_terminated_agreement(
    db, test_create_terminated_agreement, test_create_terminated_n_sales_stagin,
    test_store_NL
):
    terminated_agreement_ref, producer_id = test_create_terminated_agreement
    terminated_agreement_id = terminated_agreement_ref.id

    agreements = db.collection("agreements").where("storeId", "==", test_store_NL).where("producerId", "==", producer_id).get()
    agreements[1].id
    agreements[0].id
    agreements_dict = [agreement.to_dict() for agreement in agreements if agreement.to_dict()['status'] == 'terminated']

    terminated_agreement_dict = agreements_dict[0]


    agreement = db.collection("agreements").document(terminated_agreement_id).get()
    agreement_dict = agreement.to_dict()

    # its ok that terminated agreement does not have a partnershipId
    partnership_doc = db.collection("partnerships").document(agreement_dict["partnershipId"]).get()

    partnership_doc.exists

    sales_staging_ids = test_create_terminated_n_sales_stagin
    assert db.collection('sales-silver').document(sales_staging_ids[0]).get().to_dict()['agreementId'] == terminated_agreement_id
    assert db.collection('sales-gold').document(sales_staging_ids[1]).get().to_dict()['agreementId'] == terminated_agreement_id
    assert db.collection('sales-gold').document(sales_staging_ids[2]).get().to_dict()['commission'] == terminated_agreement_dict['commission']



