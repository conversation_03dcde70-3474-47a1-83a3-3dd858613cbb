import pytest
from gaco_framework.auth import AuthContext
from flows.force_active_partnership import ForceActivePartnershipFlow
from sanitizer.sales_sanitizer_impl import SalesSanitizerImpl
from services.user_root_account_manager import UserRootAccountManager
from models.access_right import AccessRight
from models.requests.agreements_requests import (
    CreateAgreementRequest,
    SanitizeSalesStagingWithActiveAgreementRequest,
)
from datetime import datetime, timedelta, timezone
from models.agreement import Role
from models.agreement import AgreementStatus
from constants.collections import partnership_collection


@pytest.fixture
def user_root_account_manager(db):
    return UserRootAccountManager(db)


@pytest.fixture
def store_id():
    return "aEwJD5kT9CQJcI2EmFnz"


@pytest.fixture
def user_id():
    return "6YOpcD3Pt1aJa75ujubAPCI5x1H3"


@pytest.mark.skip(reason="This test was used to simulate the manual process")
def test_add_user_to_a_store(db, user_root_account_manager, store_id, user_id):
    # taisei_user = "6YOpcD3Pt1aJa75ujubAPCI5x1H3"
    taisei_user = user_id

    store_doc = db.collection("storesV2").document(store_id).get()
    user_root_account_manager.add_store_with_access_right(
        taisei_user, 
        store_id, 
        AccessRight.EDITOR.value
    )

    user_doc = db.collection("userRootAccounts").document(taisei_user).get()

    import pdb; pdb.set_trace()

    user_doc.to_dict()


def test_sanitize_a_sale_with_agreement(db, store_id, user_id):
    # goal: sanitize a sale with a new agreement but pre-existing producer
    # use chaincult as example

    sales_staging_query = db.collection("sales-staging")\
        .where("storeId", "==", store_id)\
        .where("vendor", "==", "Chaincult")\
        .get()


    sale_ids = [sale_staging_doc.id for sale_staging_doc in sales_staging_query]

    import pdb; pdb.set_trace()
    # since the producer already exists, we can use the first sale to test the new flow
    sample_sale_id = sale_ids[0]

    # get the active partnership
    sales_sanitizer = SalesSanitizerImpl(db)

    active_partnership = sales_sanitizer.fetch_active_partnerships(store_id)
    active_partnership 


    chaincult_producer_id = [i['producerId'] for i in active_partnership if i['producer_display_name'] == "Chaincult"][0]

    # agreements, producer_id, producer_display_name
    chaincult_partnerships = [(i['producerId'], i['producer_display_name'], i['agreementId']) for i in active_partnership if i['producer_display_name'] == "Chaincult"]
    chaincult_partnerships
    # dang, more than 1 acitve partnership for chaincult, this can't be right
    
    # Q: where is this being queried from ? --> partnership collection
    # clean up partnership collection
    for partnership in chaincult_partnerships:
        agreement_id = partnership[2]

        # check if the agreement exists
        doc = db.collection('agreements').document(agreement_id)
        if not doc.get().exists:
            partnership_doc = db.collection(partnership_collection).where("agreementId", "==", agreement_id)
            for partner_doc in partnership_doc.get():
                partner_doc.id
                # db.collection(partnership_collection).dchain_cult_agreementsocument(partner_doc.id).delete()
                partner_doc.reference.delete()
            



    import pdb; pdb.set_trace()
    chain_cult_agreements = db.collection("agreements").where("storeId", "==", store_id) .where("producerId", "==", chaincult_producer_id).get()
    chain_cult_agreements[3].to_dict()
    chain_cult_agreements[3].reference.delete()
    # for i in chain_cult_agreements:

    #     i.reference.delete()
    #     pass
        
    len(chain_cult_agreements)

    active_agreement_id = [i.id for i in chain_cult_agreements if i.to_dict()['status'] == 'active']

    agreement_doc = db.collection("agreements").document(active_agreement_id[0])
    agreement_doc.get().exists
    # agreement_doc.delete()

    chaincult_new_agreement_request = CreateAgreementRequest(
        store_id=store_id,
        producer_id=chaincult_producer_id,
        title="Chaincult New Agreement",
        effective_date=datetime.now(timezone.utc) - timedelta(days=3),
        expiration_date=datetime.now(timezone.utc),
        commission=40,
        document_url="https://www.google.com",
        created_by_role=Role.STORE.value
    )

    # create new agreement
    auth_context = AuthContext(user_id=user_id)
    flow = ForceActivePartnershipFlow(db, auth_context)
    agreement_id = flow.force_create_active_agreement(chaincult_new_agreement_request)

    sanitized_sales_staging = sales_sanitizer.force_sanitize_sales_staging_with_agreement(
        sale_staging_id=sample_sale_id,
        agreement_id=agreement_id
    )

    import pdb; pdb.set_trace()
    sanitized_sales_staging
