import pytest
from flows.force_active_partnership import ForceActivePartnershipFlow
from models.requests.sanitization_request import ForceAgreementRequest
from models.partner import PartnershipStatus
from models.agreement import AgreementStatus, Agreement
from models.partner import Partnership
from datetime import datetime, timezone, timedelta
import uuid


def test_force_create_active_agreement(db, test_store, test_user):
    """
    Test the force create active agreement flow.
    """
    flow = ForceActivePartnershipFlow(db)
    effective_date = datetime.now(timezone.utc).isoformat()
    expiration_date = (datetime.now(timezone.utc) + timedelta(days=365)).isoformat()

    request = ForceAgreementRequest(
        store_id=test_store,
        title="test-title",
        effective_date=effective_date,
        expiration_date=expiration_date,
        commission=100,
        document_url="document_url",
        display_name=f"test-display-name-{str(uuid.uuid4())}",
        email=f"test-email-{str(uuid.uuid4())}@test.gaco.com",
        tax_a2="NL"
    )

    result = flow.force_create_active_partnership(request, test_user.uid)
    agreement_doc = db.collection("agreements").document(result["agreement_id"]).get()

    assert agreement_doc.exists
    assert agreement_doc.to_dict()["status"] == AgreementStatus.ACTIVE.value

    partnership_doc = db.collection("partnerships") \
        .where(Partnership.AGREEMENT_ID_FIELD, "==", result["agreement_id"]) \
        .get()

    assert len(partnership_doc) == 1

    partnership_id = partnership_doc[0].id

    partnership_doc = Partnership.model_validate(partnership_doc[0].to_dict())
    assert partnership_doc.status == PartnershipStatus.ACTIVE.value


    # clean up !
    db.collection("partnerships").document(partnership_id).delete()
    db.collection("agreements").document(result["agreement_id"]).delete()



