from models.requests.shopify_requests import GetShopifyOrdersWithDynamicQueryRequest
import pytest


@pytest.mark.skip(reason="Skipping test due store with id k8mqH8BBv8zpV7enshI0 is not available")
def test_dynamic_query_function(db, cloud_function_tester, user_with_root_account):
  """Test the dynamic query function"""
  # Create a test store
  request = GetShopifyOrdersWithDynamicQueryRequest(
    store_id='k8mqH8BBv8zpV7enshI0',
    force_refresh=True,
    days_back=360
  )

  request_data = request.model_dump()

  try:
    status_code, result = cloud_function_tester.call_function(
      function_name='get_shopify_orders_with_dynamic_query',
      data=request_data,
      user_id=user_with_root_account.uid
    )
  except Exception as e:
    pytest.skip(f"Skipping test due to issue calling function: {e}")


  assert status_code == 200
  assert result["success"] is True
  assert result["code"] == 200
  assert 'Started getting orders from Shopify' in result["message"]
  assert result["data"] is not None
  
@pytest.mark.skip(reason="Skipping test due store with id k8mqH8BBv8zpV7enshI0 is not available")
def test_dynamic_query_function_only_store_id(db, cloud_function_tester, user_with_root_account):
  """Test the dynamic query function"""
  # Create a test store
  request = GetShopifyOrdersWithDynamicQueryRequest(
    store_id='k8mqH8BBv8zpV7enshI0'
  )

  request_data = request.model_dump()

  try:
    status_code, result = cloud_function_tester.call_function(
      function_name='get_shopify_orders_with_dynamic_query',
      data=request_data,
      user_id=user_with_root_account.uid
    )
  except Exception as e:
    pytest.skip(f"Skipping test due to issue calling function: {e}")


  assert status_code == 200
  assert result["success"] is True
  assert result["code"] == 200
  assert 'Started getting orders from Shopify' in result["message"]
  assert result["data"] is not None
  