import pytest
import uuid
from datetime import datetime, timezone
from time import sleep

from firebase_admin import firestore

from models.products import Product, ProductVariant, products_collection, product_variants_collection
from models.requests.product_requests import (
    GetProductRequest,
    UpdateProductRequest,
    DeleteProductRequest,
    GetVariantRequest,
    UpdateVariantRequest,
    DeleteVariantRequest,
    GetVariantsForProductRequest
)
from models.response import ResponseData
from services.product_manager import ProductManager, SHOPIFY_PRODUCT_GID_PREFIX, SHOPIFY_VARIANT_GID_PREFIX

# --- Helper ID Generators (similar to unit tests for consistency if needed) ---
def new_test_shopify_product_gid() -> str:
    return f"{SHOPIFY_PRODUCT_GID_PREFIX}{uuid.uuid4().hex}"

def new_test_shopify_variant_gid() -> str:
    return f"{SHOPIFY_VARIANT_GID_PREFIX}{uuid.uuid4().hex}"

def new_test_custom_product_id(prefix: str = "custom_prod_integ") -> str:
    return f"{prefix}_{uuid.uuid4().hex}"

def new_test_custom_variant_id(prefix: str = "custom_var_integ") -> str:
    return f"{prefix}_{uuid.uuid4().hex}"

# --- Fixtures ---

@pytest.fixture
def product_manager_service(db: firestore.Client) -> ProductManager:
    """Provides a ProductManager service instance for direct interaction or cleanup."""
    return ProductManager(db=db)

@pytest.fixture
def sample_product_payload_factory():
    """Factory to create product data payloads for endpoint requests."""
    def _factory(
        product_id_type: str = "gid", # "gid", "custom", or None to let manager generate
        num_variants: int = 0,
        with_store_context: bool = True
    ) -> dict:
        now_iso = datetime.now(timezone.utc).isoformat()
        
        product_logical_id: str | None = None
        if product_id_type == "gid":
            product_logical_id = new_test_shopify_product_gid()
        elif product_id_type == "custom":
            product_logical_id = new_test_custom_product_id()
        # If None, manager will generate

        variants_payload = []
        if num_variants > 0:
            temp_parent_id_for_variants = product_logical_id or f"temp_parent_{uuid.uuid4().hex}"
            for i in range(num_variants):
                variants_payload.append({
                    "product_variant_id": new_test_shopify_variant_gid() if i % 2 == 0 else new_test_custom_variant_id(),
                    "product_id": temp_parent_id_for_variants, # Manager should align this
                    "title": f"Test Variant {i+1}",
                    "sku": f"VAR-SKU-{uuid.uuid4().hex[:6]}",
                    "price": 20.00 + i,
                    "inventory_quantity": 50 + i * 10,
                    "created_at": now_iso,
                    "updated_at": now_iso,
                })

        payload = {
            "product_id": product_logical_id,
            "title": f"Integration Test Product {uuid.uuid4().hex[:6]}",
            "vendor": "Integ Test Vendor",
            "handle": f"integ-test-product-{uuid.uuid4().hex[:6]}",
            "product_type": "Integ Test Type",
            "status": "ACTIVE",
            "created_at": now_iso,
            "updated_at": now_iso,
            "variants": variants_payload
        }
        if with_store_context:
            payload["store_id"] = f"store_integ_{uuid.uuid4().hex[:6]}"
            payload["store_display_name"] = "Integration Test Store"
        else: # Add producer context if no store context for validation
            payload["producer_id"] = f"producer_integ_{uuid.uuid4().hex[:6]}"
            payload["producer_display_name"] = "Integration Test Producer"
        return payload
    return _factory

@pytest.fixture
def created_product_via_endpoint(
    cloud_function_tester,
    sample_product_payload_factory,
    user_with_root_account,
    product_manager_service: ProductManager # For cleanup
):
    """Creates a product via the endpoint and yields its logical ID."""
    product_payload = sample_product_payload_factory(product_id_type="gid", num_variants=1)
    
    _, response_data_json = cloud_function_tester.call_function(
        function_name="create_product",
        data=product_payload,
        user_id=user_with_root_account.uid
    )
    response_data = ResponseData.model_validate(response_data_json)
    assert response_data.success is True
    assert response_data.data is not None
    created_product_model = Product.model_validate(response_data.data)
    logical_product_id = created_product_model.product_id
    assert logical_product_id is not None

    yield logical_product_id, created_product_model # yield ID and the full model

    # Cleanup
    try:
        product_manager_service.delete_product(logical_product_id, delete_variants=True)
    except Exception as e:
        print(f"Error during cleanup of product {logical_product_id}: {e}")


# --- Product Endpoint Tests ---

def test_create_product_endpoint(
    cloud_function_tester, sample_product_payload_factory, user_with_root_account, product_manager_service
):
    product_payload_gid = sample_product_payload_factory(product_id_type="gid", num_variants=2)
    logical_id_gid = None
    try:
        _, response_data_json = cloud_function_tester.call_function(
            "create_product", product_payload_gid, user_id=user_with_root_account.uid
        )
        response = ResponseData.model_validate(response_data_json)
        assert response.success is True
        assert response.code == 201
        assert response.data is not None
        created_product = Product.model_validate(response.data)
        logical_id_gid = created_product.product_id
        assert logical_id_gid == product_payload_gid["product_id"]
        assert len(created_product.variants) == 2

        # Verify in DB
        db_product = product_manager_service.get_product(logical_id_gid)
        assert db_product is not None
        assert db_product.title == product_payload_gid["title"]
        assert len(db_product.variants) == 2
    finally:
        if logical_id_gid:
            product_manager_service.delete_product(logical_id_gid, delete_variants=True)


def test_update_product_endpoint(cloud_function_tester, created_product_via_endpoint, user_with_root_account, product_manager_service):
    logical_product_id, _ = created_product_via_endpoint
    update_payload_dict = {"title": "Updated Title via Endpoint", "vendor": "Updated Vendor"}
    
    request_payload = UpdateProductRequest(
        logical_product_id=logical_product_id,
        update_data=update_payload_dict
    ).model_dump()

    _, response_data_json = cloud_function_tester.call_function(
        "update_product", request_payload, user_id=user_with_root_account.uid
    )
    response = ResponseData.model_validate(response_data_json)

    assert response.success is True
    assert response.code == 200
    assert response.data is not None
    updated_product_model = Product.model_validate(response.data)
    assert updated_product_model.title == update_payload_dict["title"]
    assert updated_product_model.vendor == update_payload_dict["vendor"]

    # Verify in DB
    db_product = product_manager_service.get_product(logical_product_id)
    assert db_product is not None
    assert db_product.title == update_payload_dict["title"]

    product_manager_service.delete_product(logical_product_id, delete_variants=True)


def test_delete_product_endpoint(cloud_function_tester, created_product_via_endpoint, user_with_root_account, product_manager_service):
    logical_product_id, product_model = created_product_via_endpoint
    
    request_payload = DeleteProductRequest(
        logical_product_id=logical_product_id,
        delete_variants=True # Default, but explicit
    ).model_dump()

    sleep(10)
    _, response_data_json = cloud_function_tester.call_function(
        "delete_product", request_payload, user_id=user_with_root_account.uid
    )
    response = ResponseData.model_validate(response_data_json)
    assert response.success is True
    assert response.code == 200

    # Verify in DB
    sleep(10)

    assert product_manager_service.get_product(logical_product_id) is None
    if product_model.variants:
        for var in product_model.variants:
            assert product_manager_service.get_variant(var.product_variant_id) is None
    
    # To prevent cleanup error in the fixture as we've already deleted it
    product_manager_service.delete_product(logical_product_id, delete_variants=True)


# --- Product Variant Fixtures & Tests ---

@pytest.fixture
def sample_variant_payload_factory(sample_product_payload_factory):
    """Factory to create variant data payloads, ensuring a parent product exists."""
    def _factory(parent_product_logical_id: str, variant_id_type: str = "gid") -> dict:
        now_iso = datetime.now(timezone.utc).isoformat()
        variant_logical_id = new_test_shopify_variant_gid() if variant_id_type == "gid" else new_test_custom_variant_id()
        return {
            "product_variant_id": variant_logical_id,
            "product_id": parent_product_logical_id,
            "title": f"Test Variant for {parent_product_logical_id}",
            "sku": f"VAR-SKU-{uuid.uuid4().hex[:4]}",
            "price": 25.99,
            "inventory_quantity": 75,
            "created_at": now_iso,
            "updated_at": now_iso,
        }
    return _factory

@pytest.fixture
def created_variant_via_endpoint(
    cloud_function_tester,
    created_product_via_endpoint, # Depends on a product being created first
    sample_variant_payload_factory,
    user_with_root_account,
    product_manager_service: ProductManager
):
    parent_logical_product_id, _ = created_product_via_endpoint
    variant_payload = sample_variant_payload_factory(parent_logical_product_id, variant_id_type="gid")

    _, response_data_json = cloud_function_tester.call_function(
        function_name="create_variant",
        data=variant_payload,
        user_id=user_with_root_account.uid
    )
    response_data = ResponseData.model_validate(response_data_json)
    assert response_data.success is True
    created_variant_model = ProductVariant.model_validate(response_data.data)
    logical_variant_id = created_variant_model.product_variant_id
    assert logical_variant_id is not None

    yield logical_variant_id, parent_logical_product_id, created_variant_model

    # Cleanup variant (parent product cleanup is handled by its own fixture)
    try:
        product_manager_service.delete_variant(logical_variant_id)
    except Exception as e:
        print(f"Error during cleanup of variant {logical_variant_id}: {e}")


def test_create_variant_endpoint(
    cloud_function_tester, created_product_via_endpoint, sample_variant_payload_factory,
    user_with_root_account, product_manager_service
):
    parent_logical_id, _ = created_product_via_endpoint
    variant_payload = sample_variant_payload_factory(parent_logical_id, variant_id_type="custom")
    logical_variant_id = None
    try:
        _, response_data_json = cloud_function_tester.call_function(
            "create_variant", variant_payload, user_id=user_with_root_account.uid
        )
        response = ResponseData.model_validate(response_data_json)
        assert response.success is True
        assert response.code == 201
        created_variant = ProductVariant.model_validate(response.data)
        logical_variant_id = created_variant.product_variant_id
        assert logical_variant_id == variant_payload["product_variant_id"]
        assert created_variant.product_id == parent_logical_id

        db_variant = product_manager_service.get_variant(logical_variant_id)
        assert db_variant is not None
        assert db_variant.title == variant_payload["title"]
    finally:
        if logical_variant_id:
            product_manager_service.delete_variant(logical_variant_id)


def test_update_variant_endpoint(cloud_function_tester, created_variant_via_endpoint, user_with_root_account, product_manager_service):
    logical_variant_id, _, _ = created_variant_via_endpoint
    update_payload_dict = {
        ProductVariant.PRICE_FIELD: 199.99,
        ProductVariant.INVENTORY_QUANTITY_FIELD: 5
    }
    
    request_payload = UpdateVariantRequest(
        logical_variant_id=logical_variant_id,
        update_data=update_payload_dict
    ).model_dump()

    _, response_data_json = cloud_function_tester.call_function(
        "update_variant", request_payload, user_id=user_with_root_account.uid
    )
    response = ResponseData.model_validate(response_data_json)
    assert response.success is True


    product_manager_service.get_variant(logical_variant_id)

    updated_variant = ProductVariant.model_validate(response.data)
    assert updated_variant.price == update_payload_dict[
        ProductVariant.PRICE_FIELD
    ]
    assert updated_variant.inventory_quantity == update_payload_dict[
        ProductVariant.INVENTORY_QUANTITY_FIELD
    ]

    db_variant = product_manager_service.get_variant(logical_variant_id)
    assert db_variant is not None
    assert db_variant.price == update_payload_dict["price"]


def test_delete_variant_endpoint(cloud_function_tester, created_variant_via_endpoint, user_with_root_account, product_manager_service):
    logical_variant_id, _, _ = created_variant_via_endpoint
    request_payload = DeleteVariantRequest(logical_variant_id=logical_variant_id).model_dump()

    sleep(10)
    _, response_data_json = cloud_function_tester.call_function(
        "delete_variant", request_payload, user_id=user_with_root_account.uid
    )
    response = ResponseData.model_validate(response_data_json)

    assert response.success is True
    assert response.code == 200
    assert product_manager_service.get_variant(logical_variant_id) is None
    
    # To prevent cleanup error in the fixture
    product_manager_service.delete_variant(logical_variant_id)


def test_create_product_unauthenticated(cloud_function_tester, sample_product_payload_factory):
    product_payload = sample_product_payload_factory()
    _, response_data_json = cloud_function_tester.call_function(
        "create_product", product_payload, user_id=None # Unauthenticated
    )
    response = ResponseData.model_validate(response_data_json)
    assert response.success is False
    assert response.code == 401
