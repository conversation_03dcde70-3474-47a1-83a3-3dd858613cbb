import pytest
from models.requests.partnership_requests import DeletePartnershipRequest


@pytest.fixture
def test_delete_partnership_request(test_force_create_active_partnership, db):
    agreement_id = test_force_create_active_partnership["agreement_id"]
    partnership_id = db.collection("partnerships").where(
        "agreementId", "==", agreement_id
    ).get()[0].id

    return DeletePartnershipRequest(partnership_id=partnership_id)


def test_delete_partnership(test_delete_partnership_request, cloud_function_tester, user_with_root_account):

    try:
        status_code, response = cloud_function_tester.call_function(
            "delete_partnership",
            test_delete_partnership_request.model_dump(),
            user_id=user_with_root_account.uid
        )
    except Exception as e:
        pytest.skip(f"Skipping test due to issue calling function: {e}")


    assert status_code == 200
    assert response["success"] is True
    assert "Partnership deleted successfully" in response["message"]
    assert response["data"]["partnership_id"] == test_delete_partnership_request.partnership_id
