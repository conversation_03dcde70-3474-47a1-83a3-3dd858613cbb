import pytest
import json
from datetime import datetime, timezone, timedelta
from firebase_admin import firestore
from firebase_functions import logger

# Import the endpoint functions directly
from gaco_framework.auth import AuthContext

# Import necessary models and services for setup/assertions
from models.response import ResponseData
from models.agreement import (
    Agreement, AgreementStatus, ApprovalStatus, Role, 
     agreement_collection # Assuming agreement_collection = "agreementsV1"
)
from models.requests.agreements_requests import (
     UpdateDraftAgreementRequest, 
     SubmitAgreementForApprovalRequest,
     ApproveAgreementRequest, 
     TerminateAgreementRequest, 
     DeleteAgreementRequest,
     CreateAgreementRequest,
     RejectAgreementRequest
)
from models.partner import PartnershipStatus, Partnership
from services.agreement_manager import AgreementManager # For cleanup/verification
from services.user_root_account_manager import UserRootAccountManager # For role checks setup if needed
from models.partner import partnership_collection


@pytest.fixture
def agreement_manager_for_cleanup(db):
    """Provides an AgreementManager instance purely for cleanup."""
    return AgreementManager(db)

@pytest.fixture
def create_agreement_request_data(test_store, test_producer):
    """Provides data dict for the create_agreement endpoint."""
    effective_date = (datetime.now(timezone.utc) - timedelta(days=1)).isoformat()
    expiration_date = (datetime.now(timezone.utc) + timedelta(days=367)).isoformat()
    return {
        CreateAgreementRequest.STORE_ID_FIELD: test_store,
        CreateAgreementRequest.PRODUCER_ID_FIELD: test_producer,
        CreateAgreementRequest.TITLE_FIELD: "Test Endpoint Agreement",
        CreateAgreementRequest.EFFECTIVE_DATE_FIELD: effective_date,
        CreateAgreementRequest.EXPIRATION_DATE_FIELD: expiration_date,
        CreateAgreementRequest.COMMISSION_FIELD: 18,
        CreateAgreementRequest.DOCUMENT_URL_FIELD: "http://example.com/endpoint_doc.pdf",
        CreateAgreementRequest.CREATED_BY_ROLE_FIELD: Role.STORE.value
    }

@pytest.fixture
def created_draft_agreement_via_endpoint(
    cloud_function_tester,
    create_agreement_request_data, 
    user_with_root_account, 
    agreement_manager_for_cleanup
):
    """Creates a draft agreement via the endpoint and returns its ID."""
    status_code, response_data = cloud_function_tester.call_function(
        function_name="create_agreement",
        data=create_agreement_request_data,
        user_id=user_with_root_account.uid
    )
    
    assert status_code == 200
    # Validate the structure of the *content* inside the response
    response_content = ResponseData.model_validate(response_data) 
    assert response_content.success is True
    agreement_id = response_content.data["agreement_id"]
    
    yield agreement_id # Provide the ID to the test
    
    # Cleanup
    try:
        agreement_manager_for_cleanup.agreements_collection.document(agreement_id).delete()
        logger.info(f"Cleaned up agreement via endpoint fixture: {agreement_id}")
    except Exception as e:
         logger.warn(f"Cleanup failed for endpoint-created agreement {agreement_id}: {e}")

@pytest.fixture
def created_pending_approval_agreement_via_endpoint(
    cloud_function_tester,
    created_draft_agreement_via_endpoint, 
    user_with_root_account,
    agreement_manager_for_cleanup # Keep for cleanup
):
    """Submits the draft agreement for approval via the endpoint."""
    agreement_id = created_draft_agreement_via_endpoint
    submit_data = SubmitAgreementForApprovalRequest(
        agreement_id=agreement_id, 
        role=Role.STORE.value
    ).model_dump()
    
    status_code, response_data = cloud_function_tester.call_function(
        function_name="submit_agreement_for_approval",
        data=submit_data,
        user_id=user_with_root_account.uid
    )
    
    assert status_code == 200
    response_content = ResponseData.model_validate(response_data)
    assert response_content.success is True
    
    yield agreement_id # Still yield the same ID, now in pending state

    # Cleanup is handled by the fixture that created the draft initially

@pytest.fixture
def created_active_agreement_via_endpoint(
    db, # Need db for direct verification
    cloud_function_tester,
    created_pending_approval_agreement_via_endpoint, 
    user_with_root_account,
    test_store, # Need store/producer ID for approval context
    test_producer,
    agreement_manager_for_cleanup # Keep for cleanup
):
    """Approves the pending agreement from both sides via the endpoint."""
    agreement_id = created_pending_approval_agreement_via_endpoint
    
    # --- Approve as Store ---
    # Assume user_with_root_account is part of test_store for role check
    approve_data_producer = ApproveAgreementRequest(
        agreement_id=agreement_id,
        store_or_producer_id=test_store, # ID used by function to determine role
        role=Role.PRODUCER.value,
        comments="Producer approves via endpoint"
    ).model_dump()
    
    status_code_store, response_data_store = cloud_function_tester.call_function(
        function_name="approve_agreement",
        data=approve_data_producer,
        user_id=user_with_root_account.uid
    )
    assert status_code_store == 200
    response_store = ResponseData.model_validate(response_data_store)
    assert response_store.success is True
    assert response_store.data["status"] == "approved"


    # --- Verify directly in DB ---
    doc = db.collection(agreement_collection).document(agreement_id).get()
    assert doc.exists
    agreement_data = Agreement.model_validate(doc.to_dict())
    assert agreement_data.status == AgreementStatus.ACTIVE.value
    assert agreement_data.partnership_id is not None
    partnership_id = agreement_data.partnership_id

    yield agreement_id, partnership_id # Yield agreement and partnership ID

    # Cleanup agreement (handled by draft fixture) and partnership
    try:
        db.collection(Partnership.collection_name).document(partnership_id).delete()
        logger.info(f"Cleaned up partnership via endpoint fixture: {partnership_id}")
    except Exception as e:
        logger.warn(f"Cleanup failed for endpoint-created partnership {partnership_id}: {e}")


# -------------------- Test Cases: Endpoint Calls --------------------

def test_create_agreement_endpoint(db, cloud_function_tester, create_agreement_request_data, user_with_root_account):
    """Test the create_agreement endpoint."""
    status_code, response_data = cloud_function_tester.call_function(
        function_name="create_agreement",
        data=create_agreement_request_data,
        user_id=user_with_root_account.uid
    )

    assert status_code == 200

    response = ResponseData.model_validate(response_data)
    assert response.success is True
    assert "agreement_id" in response.data
    agreement_id = response.data["agreement_id"]

    # Verify in Firestore
    doc_ref = db.collection(agreement_collection).document(agreement_id)
    doc = doc_ref.get()
    assert doc.exists
    data = Agreement.model_validate(doc.to_dict())
    assert data.title == create_agreement_request_data["title"]
    assert data.status == AgreementStatus.DRAFT.value

    # Cleanup
    doc_ref.delete()

def test_update_draft_agreement_endpoint(db, cloud_function_tester, created_draft_agreement_via_endpoint, user_with_root_account):
    """Test the update_draft_agreement endpoint."""
    agreement_id = created_draft_agreement_via_endpoint
    update_data_dict = {
        UpdateDraftAgreementRequest.AGREEMENT_ID_FIELD: agreement_id,
        UpdateDraftAgreementRequest.TITLE_FIELD: "Updated Title via Endpoint",
        UpdateDraftAgreementRequest.EFFECTIVE_DATE_FIELD: (datetime.now(timezone.utc) + timedelta(days=5)).isoformat(),
        UpdateDraftAgreementRequest.EXPIRATION_DATE_FIELD: (datetime.now(timezone.utc) + timedelta(days=400)).isoformat(),
        UpdateDraftAgreementRequest.COMMISSION_FIELD: 22,
        UpdateDraftAgreementRequest.DOCUMENT_URL_FIELD: "http://example.com/updated_doc.pdf"
    }
    # Ensure dict matches Pydantic model if needed for validation before sending
    UpdateDraftAgreementRequest.model_validate(update_data_dict) 
    
    status_code, response_data = cloud_function_tester.call_function(
        function_name="update_draft_agreement",
        data=update_data_dict,
        user_id=user_with_root_account.uid
    )

    assert status_code == 200

    response = ResponseData.model_validate(response_data)
    assert response.success is True

    # Verify in Firestore
    doc = db.collection(agreement_collection).document(agreement_id).get()
    data = Agreement.model_validate(doc.to_dict())
    assert data.title == update_data_dict[UpdateDraftAgreementRequest.TITLE_FIELD]
    assert data.commission == update_data_dict[UpdateDraftAgreementRequest.COMMISSION_FIELD]

def test_submit_agreement_for_approval_endpoint(
        db, 
        cloud_function_tester, 
        created_draft_agreement_via_endpoint, 
        user_with_root_account
    ):
    """Test the submit_agreement_for_approval endpoint."""
    agreement_id = created_draft_agreement_via_endpoint

    doc = db.collection(agreement_collection).document(agreement_id).get()
    data = Agreement.model_validate(doc.to_dict())
    assert data.status == AgreementStatus.DRAFT.value

    submit_data = SubmitAgreementForApprovalRequest(
        agreement_id=agreement_id,
        role=Role.STORE.value
    ).model_dump()

    status_code, response_data = cloud_function_tester.call_function(
        function_name="submit_agreement_for_approval",
        data=submit_data,
        user_id=user_with_root_account.uid
    )

    assert status_code == 200
    response = ResponseData.model_validate(response_data)
    assert response.success is True

    # Verify status change in Firestore
    doc = db.collection(agreement_collection).document(agreement_id).get()
    data = Agreement.model_validate(doc.to_dict())
    assert data.status == AgreementStatus.PENDING_APPROVAL.value

def test_approve_agreement_endpoint_partial(
    db, 
    cloud_function_tester,
    created_pending_approval_agreement_via_endpoint, 
    user_with_root_account,
    test_store # ID used by function to determine role
):
    """Test the approve_agreement endpoint for partial (store) approval."""
    agreement_id = created_pending_approval_agreement_via_endpoint

    approve_data = ApproveAgreementRequest(
        agreement_id=agreement_id,
        store_or_producer_id=test_store, 
        role=Role.PRODUCER.value,
        comments="Store approves via endpoint test"
    ).model_dump()

    status_code, response_data = cloud_function_tester.call_function(
        function_name="approve_agreement",
        data=approve_data,
        user_id=user_with_root_account.uid
    )

    assert status_code == 200
    response = ResponseData.model_validate(response_data)
    assert response.success is True
    assert response.data["status"] == "approved"

    # Verify workflow step in Firestore
    doc = db.collection(agreement_collection).document(agreement_id).get()
    data = Agreement.model_validate(doc.to_dict())
    assert data.status == AgreementStatus.ACTIVE.value # Still pending
    store_step = next((s for s in data.approval_workflow if s.role == Role.STORE), None)
    assert store_step is not None
    assert store_step.status == ApprovalStatus.APPROVED.value

@pytest.mark.skip(reason="above is the full approval test")
def test_approve_agreement_endpoint_full(
    db, 
    cloud_function_tester,
    created_pending_approval_agreement_via_endpoint, 
    user_with_root_account,
    test_store, 
    test_producer
):
    """Test the approve_agreement endpoint for full approval."""
    agreement_id = created_pending_approval_agreement_via_endpoint
    
    # --- Store Approval ---
    approve_data_store = ApproveAgreementRequest(
        agreement_id=agreement_id, 
        store_or_producer_id=test_store, 
        role=Role.STORE.value,
        comments="Store OK"
    ).model_dump()
    status_code_store, response_data_store = cloud_function_tester.call_function(
        function_name="approve_agreement",
        data=approve_data_store,
        user_id=user_with_root_account.uid
    )
    assert status_code_store == 200
    assert ResponseData.model_validate(response_data_store).data["status"] == "partial"

    # --- Producer Approval ---
    approve_data_producer = ApproveAgreementRequest(
        agreement_id=agreement_id, 
        store_or_producer_id=test_producer, 
        role=Role.PRODUCER.value,
        comments="Producer OK"
    ).model_dump()
    status_code_producer, response_data_producer = cloud_function_tester.call_function(
        "approve_agreement", approve_data_producer, user_with_root_account.uid)
    assert status_code_producer == 200
    assert ResponseData.model_validate(response_data_producer).data["status"] == "approved"

    # Verify final status and partnership in Firestore
    doc = db.collection(agreement_collection).document(agreement_id).get()
    data = Agreement.model_validate(doc.to_dict())
    assert data.status == AgreementStatus.ACTIVE.value
    assert data.partnership_id is not None
    partnership_doc = db.collection(partnership_collection).document(data.partnership_id).get()
    assert partnership_doc.exists
    
    # Cleanup partnership created during test
    db.collection(partnership_collection).document(data.partnership_id).delete()


def test_terminate_agreement_endpoint(db, cloud_function_tester, created_active_agreement_via_endpoint, user_with_root_account):
    """Test the terminate_agreement endpoint."""
    agreement_id, partnership_id = created_active_agreement_via_endpoint

    agreement_doc = db.collection('agreements').document(agreement_id).get().to_dict()
    assert agreement_doc["status"] != AgreementStatus.TERMINATED.value
    

    terminate_data = TerminateAgreementRequest(agreement_id=agreement_id).model_dump()
    
    status_code, response_data = cloud_function_tester.call_function(
        function_name="terminate_agreement",
        data=terminate_data,
        user_id=user_with_root_account.uid
    )

    assert status_code == 200
    response = ResponseData.model_validate(response_data)
    assert response.success is True

    # Verify agreement status in Firestore
    doc = db.collection(agreement_collection).document(agreement_id).get()
    data = Agreement.model_validate(doc.to_dict())
    assert data.status == AgreementStatus.TERMINATED.value

    # Verify partnership status in Firestore
    partnership_doc = db.collection(partnership_collection).document(partnership_id).get()
    assert not partnership_doc.exists


def test_reject_agreement_endpoint(
        db, 
        cloud_function_tester, 
        created_pending_approval_agreement_via_endpoint, 
        user_with_root_account,
        test_store
    ):
    """Test the reject_agreement endpoint."""
    agreement_id = created_pending_approval_agreement_via_endpoint

    reject_data = RejectAgreementRequest(
        agreement_id=agreement_id, 
        role=Role.PRODUCER.value, 
        comments="Producer rejects via endpoint"
    ).model_dump()
    status_code, response_data = cloud_function_tester.call_function(
        function_name="reject_agreement",
        data=reject_data,
        user_id=user_with_root_account.uid
    )

    assert status_code == 200
    response = ResponseData.model_validate(response_data)
    assert response.success is True
    assert response.data['agreement_id'] == agreement_id

    # Verify agreement status in Firestore
    doc = db.collection(agreement_collection).document(agreement_id).get()
    data = Agreement.model_validate(doc.to_dict())
    assert data.status == AgreementStatus.REJECTED.value


def test_delete_agreement_endpoint(db, cloud_function_tester, created_draft_agreement_via_endpoint, user_with_root_account):
    """Test the delete_agreement endpoint (for draft)."""
    agreement_id = created_draft_agreement_via_endpoint

    delete_data = DeleteAgreementRequest(agreement_id=agreement_id).model_dump()
    
    status_code, response_data = cloud_function_tester.call_function(
        function_name="delete_agreement",
        data=delete_data,
        user_id=user_with_root_account.uid
    )

    assert status_code == 200
    response = ResponseData.model_validate(response_data)
    assert response.success is True

    # Verify deletion in Firestore
    doc = db.collection(agreement_collection).document(agreement_id).get()
    assert not doc.exists
    
    # The fixture cleanup will now fail (expectedly), logging a warning.
    # This is okay, as the test confirmed deletion.


def test_create_agreement_endpoint_unauthenticated(cloud_function_tester, create_agreement_request_data):
    """Test create_agreement endpoint without authentication."""
    status_code, response_data = cloud_function_tester.call_function(
        function_name="create_agreement",
        data=create_agreement_request_data,
        user_id=None # No auth
    )
    
    response = ResponseData.model_validate(response_data)
    assert response.success is False
    # assert response.code == 401 # Check specific code if your ResponseData includes it
    assert 401 == response.code

@pytest.mark.skip(reason="need to check deeper")
def test_update_non_draft_agreement_endpoint(
        db,
        cloud_function_tester, 
        created_pending_approval_agreement_via_endpoint, 
        user_with_root_account
    ):
    """Test updating a non-draft agreement via endpoint should fail."""
    agreement_id = created_pending_approval_agreement_via_endpoint
    update_data_dict = {"agreement_id": agreement_id, "title": "Attempt Update"}
    
    status_code, response_data = cloud_function_tester.call_function(
        function_name="update_draft_agreement",
        data=update_data_dict,
        user_id=user_with_root_account.uid
    )

    db.collection(agreement_collection).document(agreement_id).get().to_dict()

    response = ResponseData.model_validate(response_data)
    assert response.success is False
    assert 409 == response.code

def test_update_active_agreement_endpoint(
        db,
        cloud_function_tester, 
        created_active_agreement_via_endpoint, 
        user_with_root_account
    ):
    """Test updating an active agreement via endpoint should fail."""
    agreement_id, partnership_id = created_active_agreement_via_endpoint
    update_data_dict = {"agreement_id": agreement_id, "title": "Attempt Update"}
    
    status_code, response_data = cloud_function_tester.call_function(
        function_name="update_draft_agreement",
        data=update_data_dict,
        user_id=user_with_root_account.uid
    )

    response = ResponseData.model_validate(response_data)
    assert response.success is False
    assert 409 == response.code

def test_renew_agreement_by_approve_agreement(
        db, 
        cloud_function_tester, 
        created_active_agreement_via_endpoint, 
        user_with_root_account, 
        test_store, 
        test_producer
    ):
    """Test renewing an agreement by activating a new one."""
    agreement_id, partnership_id = created_active_agreement_via_endpoint

    create_agreement_request_data = CreateAgreementRequest.model_validate({
        CreateAgreementRequest.STORE_ID_FIELD: test_store,
        CreateAgreementRequest.PRODUCER_ID_FIELD: test_producer,
        CreateAgreementRequest.EFFECTIVE_DATE_FIELD: datetime.now(timezone.utc),
        CreateAgreementRequest.COMMISSION_FIELD: 20,
        CreateAgreementRequest.DOCUMENT_URL_FIELD: "http://example.com/renewal_doc.pdf",
        CreateAgreementRequest.CREATED_BY_ROLE_FIELD: Role.STORE.value
    })

    auth_context = AuthContext(user_id=user_with_root_account.uid)
    agreement_manager = AgreementManager(db, auth_context)
    new_agreement_id = agreement_manager.create_draft_agreement(
        create_agreement_request_data
    )
    agreement_manager.submit_for_approval(new_agreement_id, Role.STORE.value)

    activate_store_data = ApproveAgreementRequest(
        agreement_id=new_agreement_id,
        store_or_producer_id=test_store,
        role=Role.PRODUCER.value,
        comments="Store approves via endpoint test"
    ).model_dump()


    status_code, response_data = cloud_function_tester.call_function(
        function_name="approve_agreement",
        data=activate_store_data,
        user_id=user_with_root_account.uid
    )

    assert status_code == 200
    response_data = ResponseData.model_validate(response_data)
    assert response_data.success is True

    # check old agreement is terminated
    doc = db.collection(agreement_collection).document(agreement_id).get()
    data = Agreement.model_validate(doc.to_dict())
    assert data.status == AgreementStatus.TERMINATED.value

    assert data.renewed_by == new_agreement_id

    # check new agreement is active
    doc = db.collection(agreement_collection).document(new_agreement_id).get()
    data = Agreement.model_validate(doc.to_dict())
    assert data.status == AgreementStatus.ACTIVE.value

