import pytest
import time
import json
from time import sleep
from datetime import datetime, timedelta, timezone
from services.store_manager import StoreManager
from models.requests.store_requests import (
  CreateStoreRequest, 
  ShopifyStoreCredentials, 
  Address, 
  DeleteStoreRequest,
)
from models.shopify_get_operation import ShopifyGetOperationStatus
from models.requests.shopify_requests import GetShopifyOrdersRequest
from gaco_secrets.secret_manager import SecretManager
from constants.gaco_values import project_id
from queries.stores_query_builder_v2 import StoresQueryBuilderV2
from gaco_framework.auth import AuthContext
import os
from firebase_functions import logger


@pytest.fixture
def test_store(db, user_with_root_account):
    """Create a test store with Shopify credentials"""
    # Initialize store manager
    auth_context = AuthContext(user_id=user_with_root_account.uid)
    store_manager = StoreManager(db, auth_context)
    secret_manager = SecretManager(project_id=project_id)
    
    # Create a unique store name
    unique_suffix = int(time.time())
    shop_name = os.getenv("SHOPIFY_TEST_SHOP_NAME")
    shopify_api_key = os.getenv("SHOPIFY_TEST_ACCESS_TOKEN")
    
    # Create store with Shopify credentials
    store_data = CreateStoreRequest(
        display_name=f"Test Store {unique_suffix}",
        email=f"test-store-{unique_suffix}@example.com",
        tax_a2="US",
        default_commission=10,
        description="Test store for Shopify integration",
        shopify_api_key_object=ShopifyStoreCredentials(
            shop_name=shop_name,
            shopify_api_key=shopify_api_key
        ),
        address=Address(
            street_number="123",
            street_name="Test Street",
            zip_code="12345",
            city="Test City",
            country="US"
        )
    )
    
    # Create the store and get store ID
    store_id = store_manager.create_store(
        store_data,
    )
    
    # Create a test API key in customer_api_keys collection
    
    # Return store ID for tests
    yield store_id
    
    # Cleanup - delete the store
    # sleep so I can trigger the order processor
    try:
        sleep(30)
        delete_request = DeleteStoreRequest(store_id=store_id)
        store_manager.delete_store(delete_request)
    except Exception as e:
        logger.warn(f"Cleanup failed for store {store_id}: {e}")


def test_get_shopify_orders_function(db, user_with_root_account, test_store, cloud_function_tester):
    """Test getting Shopify orders through the deployed Cloud Function"""

    # check if the store exists
    query_builder = StoresQueryBuilderV2(db)
    store_doc = query_builder.for_id(test_store).build().get()
    assert store_doc.exists, f"Store {test_store} does not exist"

    # Set up data for the Shopify orders fetch request
    today = datetime.now(timezone.utc)
    thirty_days_ago = today - timedelta(days=365)
    
    fetch_request = GetShopifyOrdersRequest(
        store_id=test_store,
        start_date=thirty_days_ago.strftime('%Y-%m-%d'),
        end_date=today.strftime('%Y-%m-%d')
    )
    fetch_request = fetch_request.model_dump()
    print(fetch_request)
    
    
    # Call the function using the tester
    try:
        status_code, result = cloud_function_tester.call_function(
            function_name="get_shopify_orders",
            data=fetch_request,
            user_id=user_with_root_account.uid
        )
    except Exception as e:
        pytest.skip(f"Skipping test due to issue calling function: {e}")
    
    # Assert response
    assert status_code == 200, f"Failed to call function: {result}"

    assert result["success"] is True, f"Function call failed: {result}"

    
    # Extract fetch operation ID from response
    order_getter_id = result["data"]["fetchId"]
    assert order_getter_id is not None
    
    # Verify that a download URL is provided for the first page
    assert "filePath" in result["data"]
    assert result["data"]["filePath"].startswith("stores/")
    
    # Verify that a fetch operation was created in Firestore
    fetch_doc = db.collection('shopify_fetch_operations').document(order_getter_id).get()
    assert fetch_doc.exists, f"Fetch operation {order_getter_id} not found in Firestore"
    
    # Verify fetch operation data
    fetch_data = fetch_doc.to_dict()
    assert fetch_data["storeId"] == test_store
    assert fetch_data["status"] in [
        ShopifyGetOperationStatus.IN_PROGRESS.value,
        ShopifyGetOperationStatus.COMPLETED.value
    ]


def test_get_shopify_orders_unauthorized(cloud_function_tester):
    """Test getting Shopify orders without authentication"""
    # Set up data for the Shopify orders fetch request
    today = datetime.now(timezone.utc)
    thirty_days_ago = today - timedelta(days=30)
    
    fetch_request = {
        "shopId": "test-store-id",
        "startDate": thirty_days_ago.strftime("%Y-%m-%d"),
        "endDate": today.strftime("%Y-%m-%d")
    }
    
    # Call the function without authentication
    status_code, result = cloud_function_tester.call_function(
        function_name="get_shopify_orders",
        data=fetch_request,
        user_id=None  # No authentication
    )
    
    # Assert response
    assert status_code == 200  # HTTP call succeeds but function returns auth error
    assert result["success"] is False
    assert "User must be authenticated" in result["message"]
    assert result["code"] == 401


def test_get_shopify_orders_invalid_store(db, user_with_root_account, cloud_function_tester):
    """Test getting Shopify orders with an invalid store ID"""
    # Set up data for the Shopify orders fetch request with non-existent store
    today = datetime.now(timezone.utc)
    thirty_days_ago = today - timedelta(days=30)
    
    fetch_request = {
        "shopId": "non-existent-store-id",
        "startDate": thirty_days_ago.strftime("%Y-%m-%d"),
        "endDate": today.strftime("%Y-%m-%d")
    }
    
    # Call the function
    status_code, result = cloud_function_tester.call_function(
        function_name="get_shopify_orders",
        data=fetch_request,
        user_id=user_with_root_account.uid
    )
    
    # Assert response - should fail with error about store not found
    assert status_code == 200  # HTTP call succeeds but function returns error
    assert result["success"] is False
    assert result["code"] == 400
