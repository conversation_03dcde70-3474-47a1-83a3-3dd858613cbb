import pytest
from models.requests.sales_report_request import CreateSalesReportRequest, DeleteSalesReportRequest
from models.response import ResponseData
from services.sales_report_manager import SalesReportManager
from models.sales_report import sales_report_collection
from datetime import datetime


@pytest.fixture
def test_create_sales_report(
    test_store_NL, test_start_date, test_end_date, test_force_create_active_partnership, 
    n_sales_stagin_to_gold
):
    return CreateSalesReportRequest(
        store_id=test_store_NL,
        producer_id=test_force_create_active_partnership["producer_id"],
        start_date=test_start_date,
        end_date=test_end_date
    )


@pytest.fixture
def test_create_sales_report_no_producer(
    test_store_NL, test_start_date, test_end_date, test_force_create_active_partnership, 
    n_sales_stagin_to_gold
):
    return CreateSalesReportRequest(
        store_id=test_store_NL,
        start_date=test_start_date,
        end_date=test_end_date
    )


@pytest.mark.parametrize(
        "test_create_sales_report_request", 
        [
            "test_create_sales_report", 
            "test_create_sales_report_no_producer"
        ]
)
def test_create_sales_report_integration(
    db, test_create_sales_report_request, 
    cloud_function_tester, user_with_root_account, request
    ):

    test_create_sales_report_name = request.getfixturevalue(test_create_sales_report_request)

    start_date = test_create_sales_report_name.start_date.isoformat()
    end_date = test_create_sales_report_name.end_date.isoformat()

    data = {
      CreateSalesReportRequest.STORE_ID_FIELD: test_create_sales_report_name.store_id,
      CreateSalesReportRequest.PRODUCER_ID_FIELD: (
          test_create_sales_report_name.producer_id 
          if test_create_sales_report_name.producer_id 
          else None
      ),
      CreateSalesReportRequest.START_DATE_FIELD: start_date,
      CreateSalesReportRequest.END_DATE_FIELD: end_date
    }

    status_code, response_data = cloud_function_tester.call_function(
        function_name="create_sales_reports",
        data=data,
        user_id=user_with_root_account.uid
    )

    assert status_code == 200

    response = ResponseData.model_validate(response_data)

    assert response.success

    report_id = f"{test_create_sales_report_name.store_id}-{datetime.now().year}-{1}"
    assert response.data['sales_reports'][0]['sales_report_id'] == report_id



def test_delete_sales_report_integration(
    db, test_create_sales_report, cloud_function_tester, user_with_root_account
):

    sales_report_manager = SalesReportManager(db)
    result = sales_report_manager.create_sales_reports(test_create_sales_report)


    sales_report_id = result[0]['sales_report_id']

    data = {
      DeleteSalesReportRequest.SALES_REPORT_ID_FIELD: sales_report_id
    }

    status_code, response_data = cloud_function_tester.call_function(
        function_name="delete_sales_report",
        data=data,
        user_id=user_with_root_account.uid
    )

    assert status_code == 200

    response = ResponseData.model_validate(response_data)

    assert response.success

    assert response.data['result_data']['sales_report_doc_id'] == sales_report_id

    # Check that the sales report document was deleted
    sales_report_ref = db.collection(sales_report_collection).document(sales_report_id)
    assert not sales_report_ref.get().exists
