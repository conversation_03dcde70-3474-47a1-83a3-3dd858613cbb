import pytest
import time
import json
from firebase_admin import auth
from models.requests.producer_requests import DeleteProducerRequest
from services.producer_manager import ProducerManager
from services.user_root_account_manager import UserRootAccountManager
from models.requests.producer_requests import CreateProducerRequest
from time import sleep


def test_create_producer_function(db, user_with_root_account, cloud_function_tester):
    """Test creating a producer through the deployed Cloud Function"""
    # Set up data for the producer creation request
    # wait till root user account is created
    sleep(10)
    unique_suffix = int(time.time())

    producer_request = CreateProducerRequest(
        display_name=f"Test Producer {unique_suffix}",
        email=f"test-producer-{unique_suffix}@example.com",
        tax_a2="US"
    )
    
    # Call the function using the tester
    try:
        status_code, result = cloud_function_tester.call_function(
            function_name="create_producer",
            data=producer_request.model_dump(),
            user_id=user_with_root_account.uid
        )
    except Exception as e:
        pytest.skip(f"Skipping test due to issue calling function: {e}")
    
    # Assert response
    assert status_code == 200, f"Failed to call function: {result}"
    assert result["success"] is True, f"Function call failed: {result}"

    assert "Producer created successfully" in result["message"], f"Unexpected message: {result['message']}"
    
    # Extract the producer ID from the response message
    producer_id = result["message"].split("id: ")[1]
    
    # Verify producer was created in Firestore
    producer_doc = db.collection('producersV2').document(producer_id).get()
    assert producer_doc.exists, f"Producer {producer_id} not found in Firestore"
    
    # Verify producer was added to user's root account
    user_doc = db.collection('userRootAccounts').document(user_with_root_account.uid).get()
    user_data = user_doc.to_dict()
    assert producer_id in user_data.get('producers', []), f"Producer {producer_id} not found in user's producers list"
    
    # Cleanup - delete the producer
    producer_manager = ProducerManager(db)
    delete_request = DeleteProducerRequest(
        producer_id=producer_id
    )
    producer_manager.delete_producer( delete_request)


def test_create_duplicate_producer_function(db, user_with_root_account, cloud_function_tester):
    """Test creating a duplicate producer through the deployed Cloud Function"""
    # Set up data for the producer creation request
    sleep(10)
    unique_suffix = int(time.time())
    
    producer_request = CreateProducerRequest(
        display_name=f"Test Duplicate Producer {unique_suffix}",
        email=f"test-duplicate-{unique_suffix}@example.com",
        tax_a2="US"
    )
    
    request_data = producer_request.model_dump()
    
    # First request - should succeed
    try:
        first_status, first_result = cloud_function_tester.call_function(
            function_name="create_producer",
            data=request_data,
            user_id=user_with_root_account.uid
        )
    except Exception as e:
        pytest.skip(f"Skipping test due to issue calling function: {e}")
    
    assert first_status == 200
    assert first_result["success"] is True
    producer_id = first_result["message"].split("id: ")[1]
    
    sleep(3)  # Wait a bit to ensure first request completes
    
    # Second request with the same data - should fail with duplicate error
    second_status, second_result = cloud_function_tester.call_function(
        function_name="create_producer",
        data=request_data,
        user_id=user_with_root_account.uid
    )
    
    # Assert the error response
    assert second_status == 200  # The HTTP call succeeds, but result contains error
    assert second_result["success"] is False

    assert "Producer with this email already exists" in second_result["message"]
    assert second_result["code"] == 409  # Bad request code
    
    # Cleanup - delete the producer
    producer_manager = ProducerManager(db)
    delete_request = DeleteProducerRequest(producer_id=producer_id)
    producer_manager.delete_producer(delete_request)


def test_delete_producer_function(db, user_with_root_account, cloud_function_tester, test_producer):
    """Test deleting a producer through the deployed Cloud Function"""
    # Set up data for the producer creation request
    delete_request = DeleteProducerRequest(
        producer_id=test_producer
    )
    sleep(10)
    try:
        status_code, result = cloud_function_tester.call_function(
            function_name="delete_producer",
            data=delete_request.model_dump(),
            user_id=user_with_root_account.uid
        )
    except Exception as e:
        pytest.skip(f"Skipping test due to issue calling function: {e}")


    assert status_code == 200
    assert result["success"] is True
    assert "Producer deleted successfully" in result["message"]