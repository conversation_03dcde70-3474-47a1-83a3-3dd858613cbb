import pytest
import time
import json
import os
from firebase_admin import firestore, auth, storage
import requests
from models.requests.store_requests import DeleteStoreRequest
from services.store_manager import StoreManager
from services.user_root_account_manager import UserRootAccountManager
from models.requests.store_requests import CreateStoreRequest, ShopifyStoreCredentials, Address
from models.store import StoreV2
from time import sleep
from models.sanitized_sales import sales_staging_collection
from models.sales import sales_gold_collection
from constants.gaco_values import bucket_name
from models.access_right import AccessRight
from gaco_framework import AuthContext

def test_create_store_function(db, user_with_root_account, cloud_function_tester):
    """Test creating a store through the deployed Cloud Function"""
    # Get the ID token for authentication

    # Set up data for the store creation request
    unique_suffix = int(time.time())

    sleep(5)

    shopify_api_key = os.getenv('SHOPIFY_TEST_ACCESS_TOKEN')
    # shopify_api_key = os.getenv('BROKEN_SHOPIFY_TEST_ACCESS_TOKEN')

    store_data = CreateStoreRequest(
        display_name=f"Test Store {unique_suffix}",
        email=f"test-store-{unique_suffix}@example.com",
        tax_a2="US",
        default_commission=10,
        description="Test store description from API test",
        shopify_api_key_object=ShopifyStoreCredentials(
            shop_name=os.getenv('SHOPIFY_TEST_SHOP_NAME'),
            shopify_api_key=shopify_api_key
        ),
        address=Address(
            street_number="123",
            street_name="Test Street",
            zip_code="12345",
            city="Test City",
            country="US"
        )
    )

    try:
        status_code, result = cloud_function_tester.call_function(
            function_name="create_store",
            data=store_data.model_dump(),
            user_id=user_with_root_account.uid
        )
    except Exception as e:
        pytest.skip(f"Skipping test due to issue calling function: {e}")

    # take grab some tea while waiting for the background process to finish
    sleep(30)

    # Assert response
    assert status_code == 200, f"Failed to call function: {result}"
    
    # Parse the response

    # Parse the JSON string result into a Python dictionary
    if isinstance(result, str):
        result = json.loads(result)

    assert result["success"] is True, f"Function call failed: {result}"
    assert "Store created successfully" in result["message"], f"Unexpected message: {result['message']}"
    
    # Extract the store ID from the response message
    store_id = result["message"].split("id: ")[1]
    
    # Verify store was created in Firestore
    store_doc = db.collection('storesV2').document(store_id).get()
    assert store_doc.exists, f"Store {store_id} not found in Firestore"
    
    # Verify store was added to user's root account
    user_doc = db.collection('userRootAccounts').document(user_with_root_account.uid).get()
    user_data = user_doc.to_dict()
    assert store_id in user_data.get('stores', []), f"Store {store_id} not found in user's stores list"

    # verify if the data is created in the sales-staging collection
    sales_staging_docs = db.collection(sales_staging_collection).where('storeId', '==', store_id).get()
    assert len(sales_staging_docs) > 0, f"Sales staging data for store {store_id} not found in Firestore"
    
    # Cleanup - delete the store
    auth_context = AuthContext(user_id=user_with_root_account.uid)
    store_manager = StoreManager(db, auth_context)
    delete_request = DeleteStoreRequest(store_id=store_id, hard_delete=True)
    store_manager.delete_store(delete_request)


def test_create_store_with_empty_fields(db, user_with_root_account, cloud_function_tester):
    """Test creating a store with empty address, description and default_commission"""
    # Get the ID token for authentication
    
    # Set up data for the store creation request
    user_doc = db.collection('userRootAccounts').document(user_with_root_account.uid).get()
    user_data = user_doc.to_dict()
    print(user_data)

    unique_suffix = int(time.time())

    store_data = CreateStoreRequest(
        display_name=f"Test Store {unique_suffix}",
        email=f"test-store-{unique_suffix}@example.com",
        tax_a2="US", # No Default Commission
        description=None,  # Empty description
        shopify_api_key_object=ShopifyStoreCredentials(
            shop_name=os.getenv('SHOPIFY_TEST_SHOP_NAME'),
            shopify_api_key=os.getenv('SHOPIFY_TEST_ACCESS_TOKEN')
        ),
        address=None  # Empty address
    )


    time.sleep(5)
    
    try:
        status_code, result = cloud_function_tester.call_function(
            function_name="create_store",
            data=store_data.model_dump(),
            user_id=user_with_root_account.uid
        )
    except Exception as e:
        pytest.skip(f"Skipping test due to issue calling function: {e}")
    
    
    
    # Assert response
    assert status_code == 200, f"Failed to call function: {result}"
    
    # Parse the response

    # Parse the JSON string result into a Python dictionary

    assert result["success"] is True, f"Function call failed: {result}"
    assert "Store created successfully" in result["message"], f"Unexpected message: {result['message']}"
    
    # Extract the store ID from the response message
    store_id = result["message"].split("id: ")[1]
    
    # Verify store was created in Firestore
    store_doc = db.collection('storesV2').document(store_id).get()
    assert store_doc.exists, f"Store {store_id} not found in Firestore"
    store_data = store_doc.to_dict()
    
    # Verify the empty fields
    assert store_data.get("default_commission") is None, "default_commission should be None"
    assert store_data.get("description") is None, "description should be None"
    assert store_data.get("address") is None, "address should be None"
    
    # Verify store was added to user's root account
    user_doc = db.collection('userRootAccounts').document(user_with_root_account.uid).get()
    user_data = user_doc.to_dict()
    assert store_id in user_data.get('stores', []), f"Store {store_id} not found in user's stores list"
    
    # Cleanup - delete the store
    auth_context = AuthContext(user_id=user_with_root_account.uid)
    store_manager = StoreManager(db, auth_context)
    delete_request = DeleteStoreRequest(store_id=store_id)
    store_manager.delete_store(delete_request)


def test_create_store_with_invalid_api_key(db, user_with_root_account, cloud_function_tester):
    """Test creating a store with empty address, description and default_commission"""
    # Get the ID token for authentication
    
    # Set up data for the store creation request
    user_doc = db.collection('userRootAccounts').document(user_with_root_account.uid).get()
    user_data = user_doc.to_dict()
    print(user_data)

    unique_suffix = int(time.time())

    store_data = CreateStoreRequest(
        display_name=f"Test Store {unique_suffix}",
        email=f"test-store-{unique_suffix}@example.com",
        tax_a2="US", # No Default Commission
        description=None,  # Empty description
        shopify_api_key_object=ShopifyStoreCredentials(
            shop_name="invalid_shop_name",
            shopify_api_key="invalid_api_key"
        ),
        address=None  # Empty address
    )


    time.sleep(5)
    
    try:
        status_code, result = cloud_function_tester.call_function(
            function_name="create_store",
            data=store_data.model_dump(),
            user_id=user_with_root_account.uid
        )
    except Exception as e:
        pytest.skip(f"Skipping test due to issue calling function: {e}")
    
    
    
    # Assert response
    assert status_code == 200, f"Failed to call function: {result}"
    
    # Parse the response

    # Parse the JSON string result into a Python dictionary

    assert result["success"] is False, f"Function call failed: {result}"
    assert "However, there was an issue fetching initial orders from Shopify" in result["message"], f"Unexpected message: {result['message']}"


def test_create_duplicate_store_function(
        db, 
        user_with_root_account, 
        cloud_function_tester, 
        test_store,
        firebase_app,
        storage_bucket
    ):
    """Test creating a duplicate store through the deployed Cloud Function"""
    # Get the ID token for authentication
    
    
    # Set up data for the store creation request

    the_store = db.collection('storesV2').document(test_store).get()
    the_store_data = the_store.to_dict()
    the_store = StoreV2.model_validate(the_store_data)


    store_data = CreateStoreRequest(
        display_name=the_store.display_name,
        email=the_store.email,
        tax_a2=the_store.tax_a2,
        default_commission=the_store.default_commission,
        description=the_store.description,
        shopify_api_key_object=ShopifyStoreCredentials(
            shop_name=os.getenv('SHOPIFY_TEST_SHOP_NAME'),
            shopify_api_key=os.getenv('SHOPIFY_TEST_ACCESS_TOKEN')
        ),
        address=Address(
            street_number=the_store.address.street_number,
            street_name=the_store.address.street_name,
            zip_code=the_store.address.zip_code,
            city=the_store.address.city,
            country=the_store.address.country
        )
    )

    try:
        status_code, result = cloud_function_tester.call_function(
            function_name="create_store",
            data=store_data.model_dump(),
            user_id=user_with_root_account.uid
        )
    except Exception as e:
        pytest.skip(f"Skipping test due to issue calling function: {e}")
    
    # Add a small delay to ensure any pending operations complete
    
    assert status_code == 200  # The HTTP call succeeds, but result contains error

    # Assert the error response
    assert result["success"] is False
    assert result["code"] == 409  # Bad request code

    # Cleanup - delete the store
    # auth_context = AuthContext(user_id=user_with_root_account.uid)
    # store_manager = StoreManager(db, auth_context)
    # delete_request = DeleteStoreRequest(store_id=test_store)
    # store_manager.delete_store(delete_request)


def test_hard_delete_store_function(
        test_store_NL, test_force_create_active_partnership, 
        n_sales_stagin_to_gold, db, user_with_root_account,
        cloud_function_tester
    ):
    """Test deleting a store through the deployed Cloud Function"""
    # Get the ID token for authentication
    delete_request = DeleteStoreRequest(
        store_id=test_store_NL, 
        hard_delete=True
    )
    sleep(10)

    try:
        status_code, result = cloud_function_tester.call_function(
            function_name="delete_store",
            data=delete_request.model_dump(),
            user_id=user_with_root_account.uid
        )
    except Exception as e:
        pytest.skip(f"Skipping test due to issue calling function: {e}")

    assert status_code == 200, f"Failed to call function: {result}"
    assert result["success"] is True, f"Function call failed: {result}"
    

def test_create_store_sets_claims_and_enforces_rules(
    db: firestore.Client,
    user_with_root_account: auth.UserRecord,
    cloud_function_tester,
    firestore_rest_client_helper,
):
    """
    Tests if create_store sets claims correctly and Firestore rules enforce them.
    """
    unique_suffix = int(time.time())
    store_access_right = AccessRight.ADMIN.value  # Define the access right to test with

    shopify_api_key = os.getenv('SHOPIFY_TEST_ACCESS_TOKEN', 'fake_token_for_claim_test')
    shop_name = os.getenv('SHOPIFY_TEST_SHOP_NAME', f'test-shop-{unique_suffix}')


    store_data = CreateStoreRequest(
        display_name=f"Claim Test Store {unique_suffix}",
        email=f"claim-test-{unique_suffix}@example.com",
        tax_a2="DE",
        access_right=store_access_right, # <<< Pass the access right
        shopify_api_key_object=ShopifyStoreCredentials(
            shop_name=shop_name,
            shopify_api_key=shopify_api_key
        )
        # Add other required fields if necessary (address, etc.)
    )

    # wait for the user root to be created
    sleep(10)

    # 1. Call create_store function
    try:
        status_code, result = cloud_function_tester.call_function(
            function_name="create_store",
            data=store_data.model_dump(),
            user_id=user_with_root_account.uid
        )
    except Exception as e:
        pytest.skip(f"Skipping test due to issue calling function: {e}")

    assert status_code == 200, f"Failed to call create_store function: {result}"
    if isinstance(result, str):
        result = json.loads(result)
    assert result["success"] is True, f"create_store function call failed: {result}"
    store_id = result["message"].split("id: ")[1]
    print(f"Store created with ID: {store_id}")

    # Extract store ID from the success message
    claim_store_id = result["message"].split("id: ")[1]
    print(f"Store created with ID: {claim_store_id}")

    # 2. Wait briefly for claims to potentially propagate (might need adjustment)
    print("Waiting for claims propagation...")
    sleep(15) # Firebase claim propagation can take a little time

    # 3. Get a FRESH ID token AFTER the function call
    print("Getting fresh ID token...")
    fresh_id_token = cloud_function_tester.get_id_token(user_with_root_account.uid)
    assert fresh_id_token is not None

    # 4. Verify the token claims (optional but highly recommended)
    print("Verifying token claims...")
    try:
        decoded_token = auth.verify_id_token(fresh_id_token, check_revoked=True)
        print(f"Decoded token: {decoded_token}")
        assert 'store_access_rights' in decoded_token
        assert store_id in decoded_token['store_access_rights']
        assert decoded_token['store_access_rights'][store_id] == store_access_right
        print("Token claims verified successfully.")
    except Exception as e:
        pytest.fail(f"Failed to verify token claims: {e}\nToken: {fresh_id_token}")

    # 5. Configure REST client with the fresh token
    firestore_rest_client_helper.set_id_token(fresh_id_token)

    # 6. Prepare Firestore test data using ADMIN SDK
    test_doc_id = f"claim-test-doc-{unique_suffix}"
    test_doc_path = f"{sales_gold_collection}/{test_doc_id}"
    print(f"Setting up test document at {test_doc_path} using admin SDK...")
    db.collection(sales_gold_collection).document(test_doc_id).set({
        'storeId': store_id,
        'data': 'accessible_data'
    })

    # 7. Test Firestore Read (Success) using REST client
    print(f"Attempting to read {test_doc_path} using REST client (expect success)...")
    try:
        read_status, read_result = firestore_rest_client_helper.get_document(test_doc_path)
        assert read_status is True
        # Ensure 'fields' exists before accessing it
        assert 'fields' in read_result, f"Response missing 'fields': {read_result}"
        assert 'storeId' in read_result['fields']
        # Firestore REST API wraps values, e.g., {'stringValue': 'actual_id'}
        assert read_result['fields']['storeId'].get('stringValue') == store_id
        print("Firestore read successful as expected.")
    except Exception as e:
        pytest.fail(f"Firestore read failed unexpectedly: {e}")

    # 10. Cleanup
    print("Cleaning up test resources...")
    # Delete Firestore test doc
    try:
        db.collection(sales_gold_collection).document(test_doc_id).delete()
    except Exception as e:
        print(f"Warning: Failed to cleanup Firestore test doc {test_doc_id}: {e}")

    # Delete the store
    store_manager = StoreManager(db=db)
    delete_request = DeleteStoreRequest(store_id=store_id, hard_delete=True) # Use hard_delete if needed
    try:
        store_manager.delete_store(delete_request, user_with_root_account.uid)
        store_manager.delete_store(
            DeleteStoreRequest(store_id=claim_store_id, hard_delete=False), 
            user_with_root_account.uid
        )
        print(f"Store {store_id} deleted.")
    except Exception as e:
        print(f"Warning: Failed to cleanup store {store_id}: {e}")

    