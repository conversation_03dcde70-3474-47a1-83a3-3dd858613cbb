import pytest
import time
import json
import uuid
import random
from datetime import datetime, timezone, timedelta
from models.sales import SalesSilver
from models.requests.sanitization_request import SanitizeSalesStagingWithAgreementRequest
from models.sales import SalesStaging
from services.store_manager import StoreManager
from models.requests.store_requests import DeleteStoreRequest
from models.producer import Producer
from models.partner import PartnershipStatus
from gaco_framework.auth import AuthContext
    
    
def test_sanitize_sales_staging_with_new_agreement_function(
        db, 
        test_store, 
        cloud_function_tester, 
        user_with_root_account
    ):
    """Test sanitizing sales staging with a new producer through the deployed Cloud Function"""
    # First, create a test sale-staging document in Firestore
    unique_suffix = int(time.time())
    
    # Generate a unique document ID for the sales staging entry
    sales_id = str(uuid.uuid4())
    
    # Create sales staging document
    store_id = test_store
    
    # Create a SalesStaging object
    sales_staging = SalesStaging(
        document_id=sales_id,
        store_id=store_id,
        order_id=f"order_{random.randint(10000, 99999)}",
        line_item_id=f"item_{random.randint(10000, 99999)}",
        product_id=f"prod_{random.randint(10000, 99999)}",
        title="Test Product",
        variant_title="Test Variant",
        variant_display_name="Test Product - Test Variant",
        quantity=1,
        unit_price=100,
        subtotal=100,
        total_price=100,
        currency="USD",
        discount=0,
        vendor="Unknown Vendor",  # This is why we need to link it to a producer
        updated_at=datetime.now(timezone.utc),
        store_display_name="Test Store"
    )
    
    # Add the sales staging document to Firestore
    db.collection('sales-staging').document(sales_id).set(sales_staging.model_dump())
    
    # Create sanitization request
    sanitization_request = SanitizeSalesStagingWithAgreementRequest(
        email=f"test-producer-{unique_suffix}@example.com",
        tax_a2="US",
        sale_id=sales_id,
        commission=10,
        effective_date=datetime.now(timezone.utc) - timedelta(days=1),
        display_name=f"Test Producer {unique_suffix}",
        store_id=store_id
    )

    data = {
        SanitizeSalesStagingWithAgreementRequest.EMAIL_FIELD: sanitization_request.email,
        SanitizeSalesStagingWithAgreementRequest.TAX_A2_FIELD: sanitization_request.tax_a2,
        SanitizeSalesStagingWithAgreementRequest.SALE_ID_FIELD: sales_id,
        SanitizeSalesStagingWithAgreementRequest.COMMISSION_FIELD: sanitization_request.commission,
        SanitizeSalesStagingWithAgreementRequest.EFFECTIVE_DATE_FIELD: sanitization_request.effective_date.isoformat(),
        SanitizeSalesStagingWithAgreementRequest.DISPLAY_NAME_FIELD: sanitization_request.display_name,
        SanitizeSalesStagingWithAgreementRequest.STORE_ID_FIELD: store_id,
        SanitizeSalesStagingWithAgreementRequest.SALE_ID_FIELD: sales_id
    }

    
    # Call the cloud function
    try:
        status_code, result = cloud_function_tester.call_function(
            function_name="sanitize_sales_staging_with_new_agreement",
            data=data,
            user_id=user_with_root_account.uid
        )
    except Exception as e:
        # Clean up the sales staging document even if the test fails
        pytest.skip(f"Skipping test due to issue calling function: {e}")
    
    # Assert response
    assert status_code == 200, f"Failed to call function: {result}"
    assert result["success"] is True, f"Function call failed: {result}"
    assert "Sales staging with no parentId producer sanitized successfully" in result["message"]
    assert result["code"] == 200
    
    # Verify the sales document has been moved from staging to silver collection
    silver_doc = db.collection('sales-silver').document(sales_id).get()
    assert silver_doc.exists, f"Sale document {sales_id} not found in sales-silver collection"
    
    # Verify the document has been removed from staging collection
    staging_doc = db.collection('sales-staging').document(sales_id).get()
    assert not staging_doc.exists, f"Sale document {sales_id} still exists in sales-staging collection"
    
    # Verify silver document has producer details
    silver_data = silver_doc.to_dict()
    assert "producerId" in silver_data, "producer_id field missing in sales-silver document"
    
    # Retrieve and verify the created producer
    silver_data = SalesSilver.model_validate(silver_data)
    producer_id = silver_data.producer_id
    producer_doc = db.collection('producersV2').document(producer_id).get()
    assert producer_doc.exists, f"Producer {producer_id} not found in Firestore"
    
    producer_data = producer_doc.to_dict()
    producer_data = Producer.model_validate(producer_data)
    assert producer_data.display_name == sanitization_request.display_name
    assert producer_data.email == sanitization_request.email
    assert producer_data.tax_a2 == sanitization_request.tax_a2
    assert producer_data.parent_id == ""  # Should be empty string for no parent
    
    # Verify partnership was created
    partnership_query = db.collection('partnerships').where(
        'producerId', '==', producer_id
    ).where(
        'storeId', '==', store_id
    ).get()

    
    assert len(partnership_query) > 0, "Partnership not created between store and producer"
    partnership = partnership_query[0].to_dict()
    assert partnership["commission"] == sanitization_request.commission
    assert partnership["status"] == PartnershipStatus.ACTIVE.value  # Should be automatically accepted
    
    # Cleanup - delete the created resources
    # 1. Delete the sales-silver document
    auth_context = AuthContext(user_id=user_with_root_account.uid)
    store_manager = StoreManager(db, auth_context)
    store_manager.delete_store(
        DeleteStoreRequest(
            store_id=store_id,
            hard_delete=True
        )
    )


@pytest.mark.skip(reason="This test is not working as expected")
def test_send_sanitization_request_to_cloud_function(db, cloud_function_tester, user_with_root_account):
    sanitization_request = SanitizeSalesStagingWithAgreementRequest(
        email=f"<EMAIL>",
        tax_a2="DE",
        sale_id="00479af7-bec2-46b4-b1d4-69555cee31dd",
        commission=10,
        effective_date=datetime.now(timezone.utc) - timedelta(days=1),
        expiration_date=datetime.now(timezone.utc) + timedelta(days=366),
        display_name="Mora, Lewis and Hurley"
    )

    
    # Call the cloud function
    try:
        status_code, result = cloud_function_tester.call_function(
            function_name="sanitize_sales_staging_with_new_agreement",
            data=sanitization_request.model_dump(),
            user_id=user_with_root_account.uid
        )
    except Exception as e:
        # Clean up the sales staging document even if the test fails
        pytest.skip(f"Skipping test due to issue calling function: {e}")
