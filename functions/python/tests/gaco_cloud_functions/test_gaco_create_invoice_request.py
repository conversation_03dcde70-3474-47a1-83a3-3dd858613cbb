import pytest
from models.requests.invoice_request import CreateInvoiceRequest


@pytest.fixture
def test_invoice_data(
    test_store_NL, test_start_date, test_end_date, 
    test_force_create_active_partnership, n_sales_stagin_to_gold
    ):
    return CreateInvoiceRequest(
        store_id=test_store_NL,
        producer_id=test_force_create_active_partnership["producer_id"],
        start_date=test_start_date,
        end_date=test_end_date
    )


@pytest.mark.skip(reason="Skipping the end point does not exist")
def test_gaco_create_invoice_request(
    db,
    test_invoice_data,
    cloud_function_tester,
    user_with_root_account
):

    data = test_invoice_data.model_dump()
    start_date = test_invoice_data.start_date.isoformat()
    end_date = test_invoice_data.end_date.isoformat()
    data = {
        CreateInvoiceRequest.STORE_ID_FIELD: test_invoice_data.store_id,
        CreateInvoiceRequest.PRODUCER_ID_FIELD: test_invoice_data.producer_id,
        CreateInvoiceRequest.START_DATE_FIELD: start_date,
        CreateInvoiceRequest.END_DATE_FIELD: end_date
    }

    response = cloud_function_tester.call_function(
        'create_invoices',
        data,
        user_id=user_with_root_account.uid
    )
    assert response.status_code == 200
    assert response.data is not None