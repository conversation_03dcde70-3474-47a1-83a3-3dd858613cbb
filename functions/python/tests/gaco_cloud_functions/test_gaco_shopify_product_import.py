import pytest
import os
import json
import uuid
from datetime import datetime, timezone

from firebase_admin import firestore, storage # For type hinting if fixtures are not typed
from firebase_functions import tasks_fn # For mocking CallableRequest

from gaco_secrets.secret_manager import SecretManager
from services.shopify_product_getter import ShopifyProductGetter
from models.shopify_product_get_operation import (
    ShopifyProductGetOperation,
    ShopifyGetOperationStatus,
    shopify_product_fetch_operations_collection
)
from models.response import ResponseData
from models.requests.shopify_product_import import InitializeShopifyProductImportRequest # For type-correct payloads
from models.secret import SecretLabels
from constants.gaco_values import project_id as gcp_project_id # project_id from conftest may be different

from models.store import StoreV2

# --- Test File Specific Configuration ---
# Use distinct names to avoid conflicts with other test files if they run in the same session
TEST_STORE_ID_FOR_PRODUCT_IMPORT = f"testStoreProdImport123"
# It's crucial these env vars are set for your test environment for these tests to run.
TEST_SHOP_NAME_FOR_PRODUCT_IMPORT = os.getenv("SHOPIFY_TEST_SHOP_NAME", "your-default-test-shop-for-import")
TEST_API_KEY_FOR_PRODUCT_IMPORT = os.getenv("SHOPIFY_TEST_ACCESS_TOKEN", "dummy_api_key_for_import_tests")

# Globals for module/function scoped cleanup fixtures in this file
_module_created_secret_id_for_import = None
_function_created_op_ids_for_import = []
_function_created_gcs_paths_for_import = []

@pytest.fixture(scope="module")
def import_test_secret_manager(firebase_app): # firebase_app ensures init
    # Uses the project_id from constants.gaco_values, assuming it's the correct one for SecretManager
    return SecretManager(project_id=gcp_project_id)


@pytest.fixture(scope="function")
def product_import_function_cleanup(db, storage_bucket): # storage_bucket from conftest
    """Cleanup function for product import tests.
    Deletes Firestore operations and GCS files created by the tests.
    But don't run it successively, cuz it will delete all the file processing traking doc.
    """
    global _function_created_op_ids_for_import, _function_created_gcs_paths_for_import
    _function_created_op_ids_for_import.clear()
    _function_created_gcs_paths_for_import.clear()
    yield
    for op_id in _function_created_op_ids_for_import:
        try:
            db.collection(shopify_product_fetch_operations_collection).document(op_id).delete()
        except Exception as e:
            print(f"PYTEST_WARNING: Cleanup failed for op {op_id}: {e}")
    for gcs_path in _function_created_gcs_paths_for_import:
        try:
            blob = storage_bucket.blob(gcs_path)
            if blob.exists():
                blob.delete()
        except Exception as e:
            print(f"PYTEST_WARNING: Cleanup failed for GCS {gcs_path}: {e}")

# --- Tests for initialize_shopify_product_import (HTTPS Callable) ---

def test_initialize_import_success(
    cloud_function_tester, user_with_root_account, db, storage_bucket,
    test_store # Ensure setups run
):
    """ Test successful initialization of product import. """
    # product_import_env_setup ensures TEST_STORE_ID_FOR_PRODUCT_IMPORT is configured
    payload = InitializeShopifyProductImportRequest(
        store_producer_id=test_store
    ).model_dump()

    # IMPORTANT: This assumes that initialize_shopify_product_import internally
    # correctly instantiates ShopifyProductGetter with a SecretManager instance.
    # If it's just ShopifyProductGetter(db), this test will fail at the getter's
    # get_access_token method if that method expects self.secret_manager to be set.

    _, response_json = cloud_function_tester.call_function(
        function_name="initialize_shopify_product_import",
        data=payload,
        user_id=user_with_root_account.uid
    )

    response = ResponseData.model_validate(response_json) # Cloud function returns JSON string
    assert response.success is True
    assert response.data is not None

    data = response.data['data']

    op_id = data.get(ShopifyProductGetOperation.PRODUCT_GETTER_ID_FIELD)
    gcs_path = data.get(ShopifyProductGetOperation.FILE_PATH_FIELD)
    if op_id: _function_created_op_ids_for_import.append(op_id)
    if gcs_path: _function_created_gcs_paths_for_import.append(gcs_path)

    assert op_id is not None
    assert gcs_path is not None

    assert data.get(ShopifyProductGetOperation.STORE_ID_FIELD) == test_store

    op_doc = db.collection(shopify_product_fetch_operations_collection).document(op_id).get()
    assert op_doc.exists
    op_data = ShopifyProductGetOperation.model_validate(op_doc.to_dict())
    assert op_data.store_id == test_store
    assert op_data.shop_name == TEST_SHOP_NAME_FOR_PRODUCT_IMPORT

    blob = storage_bucket.blob(gcs_path)
    assert blob.exists()
    # Content check for the GCS blob can be added here if needed
    # try:
    #     content = json.loads(blob.download_as_string())
    #     assert "products" in content and "edges" in content["products"]
    # except Exception as e:
    #     pytest.fail(f"Failed to download or parse GCS file {gcs_path}: {e}")


def test_initialize_import_missing_store_id(cloud_function_tester, user_with_root_account):
    """ Test request with missing store_id. """
    payload = {"store_producer_id": ""} # Send as dict to simulate bad client
    
    _, response_json = cloud_function_tester.call_function(
        "initialize_shopify_product_import", 
        payload, 
        user_id=user_with_root_account.uid
    )

    # The function directly returns a model_dump(), so parse it back for assertion
    response = ResponseData.model_validate(response_json)

    assert response.success is False
    assert response.code == 400
    assert "store_id is required" in response.message

def test_initialize_import_unauthenticated(cloud_function_tester):
    """ Test unauthenticated access to the callable function. """
    payload = InitializeShopifyProductImportRequest(
        store_producer_id=TEST_STORE_ID_FOR_PRODUCT_IMPORT
    ).model_dump()
    
    success, response_json = cloud_function_tester.call_function(
        "initialize_shopify_product_import", payload, user_id=None # Unauthenticated
    )
    response = ResponseData.model_validate(response_json)
    assert response.success is False
    assert response.code == 401
    assert response.message == "Unauthorized"


def test_initialize_import_store_not_in_customer_api_keys(cloud_function_tester, user_with_root_account):
    """ Test with a store_id not configured in customer_api_keys. """
    non_existent_store_id = f"non-existent-import-store-{uuid.uuid4().hex[:6]}"
    payload = InitializeShopifyProductImportRequest(store_producer_id=non_existent_store_id).model_dump()

    success, response_json = cloud_function_tester.call_function(
        "initialize_shopify_product_import", payload, user_id=user_with_root_account.uid
    )
    response = ResponseData.model_validate(response_json)
    assert response.success is False
    assert response.code == 500
    assert "No API key found for shop" in response.message
