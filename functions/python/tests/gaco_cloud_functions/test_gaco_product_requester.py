import pytest
import uuid
from datetime import datetime, timezone

from firebase_functions import logger
from firebase_admin import firestore, auth

from models.allocations import (
    StoreProductRequest, ProductAllocationItem, ProductAllocation,
    store_product_requests_collection, product_allocations_collection,
    ProductRequestStatus, AllocationStatus, DeliveryMethod
)
from models.products import Product, ProductVariant
from models.response import ResponseData
from models.requests.product_request_requests import (
    ApproveStoreProductRequest, 
    RejectStoreProductRequest, 
    CancelStoreProductRequest,
)

from gaco_framework.types import AuthContext

from services.product_manager import ProductManager, SHOPIFY_PRODUCT_GID_PREFIX, SHOPIFY_VARIANT_GID_PREFIX
from services.producer_manager import ProducerManager
from services.store_manager import StoreManager
from services.product_allocator import ProductAllocationManager
from services.product_requester import ProductRequester

# If ProductRequestStatus is actually RequestStatus in your allocations model, use that:
try:
    from models.allocations import RequestStatus as ProductRequestStatus
except ImportError:
    pass # If it's already ProductRequestStatus, this alias won't break anything

# --- Helper ID Generators ---
def new_req_integ_shopify_product_gid() -> str:
    return f"{SHOPIFY_PRODUCT_GID_PREFIX}{uuid.uuid4().hex}"

def new_req_integ_shopify_variant_gid() -> str:
    return f"{SHOPIFY_VARIANT_GID_PREFIX}{uuid.uuid4().hex}"

# --- Service Fixtures ---
@pytest.fixture
def product_manager_for_requester_integ(db: firestore.Client) -> ProductManager:
    return ProductManager(db=db)

@pytest.fixture
def producer_manager_for_requester_integ(db: firestore.Client) -> ProducerManager:
    return ProducerManager(db=db) # Assuming simple init

@pytest.fixture
def store_manager_for_requester_integ(db: firestore.Client) -> StoreManager:
    return StoreManager(db=db) # Assuming simple init

@pytest.fixture
def product_allocator_for_requester_integ(db: firestore.Client) -> ProductAllocationManager:
    return ProductAllocationManager(db=db)

@pytest.fixture
def product_requester_service_for_integ(
    db: firestore.Client,
    test_producer: str,
    test_store: str
) -> ProductRequester:
    """Provides a ProductRequester service instance for direct DB interaction/verification."""
    logger.info(f"test_producer: {test_producer}")
    logger.info(f"test_store: {test_store}")
    # return ProductRequester(
    #     db=db, 
    #     product_manager=product_manager_for_requester_integ,
    #     producer_manager=producer_manager_for_requester_integ,
    #     store_manager=store_manager_for_requester_integ,
    #     product_allocator=product_allocator_for_requester_integ
    # )
    return ProductRequester(db=db)

# --- Data Setup Fixtures ---
@pytest.fixture
def req_integ_test_product_with_variants(
    product_manager_for_requester_integ: ProductManager,
    test_producer # From conftest.py
):
    now = datetime.now(timezone.utc)
    prod_id = new_req_integ_shopify_product_gid()
    var1_id = new_req_integ_shopify_variant_gid()
    product_data = Product(
        product_id=prod_id, title="Req Integ Product", producer_id=test_producer,
        store_id=f"s_{uuid.uuid4().hex[:4]}", store_display_name="Integ Store", status="ACTIVE",
        created_at=now, updated_at=now,
        variants=[
            ProductVariant(product_variant_id=var1_id, product_id=prod_id, title="V1", sku="RIV1", price=10, inventory_quantity=100, created_at=now, updated_at=now)
        ]
    )
    created_product = product_manager_for_requester_integ.create_product(product_data)
    yield created_product
    try:
        product_manager_for_requester_integ.delete_product(created_product.product_id, delete_variants=True)
    except Exception as e:
        print(f"Cleanup failed for product {created_product.product_id} in req_integ_test_product: {e}")

@pytest.fixture
def sample_store_product_request_endpoint_payload_factory(
    test_store, test_producer, req_integ_test_product_with_variants: Product
):
    """Factory for creating raw dict payloads for create_store_product_request endpoint."""
    def _factory(items_override: list = None, producer_id_override: str = None, store_id_override: str = None) -> dict:
        product_model = req_integ_test_product_with_variants
        default_items = [
            ProductAllocationItem(product_id=product_model.product_id, product_variant_id=product_model.variants[0].product_variant_id, quantity=5).model_dump()
        ]
        return {
            "store_id": store_id_override if store_id_override else test_store,
            "producer_id": producer_id_override if producer_id_override else test_producer,
            "requested_items": items_override if items_override is not None else default_items,
            "desired_delivery_date": datetime.now(timezone.utc).isoformat(),
            "store_request_notes": "Integration test request"
        }
    return _factory

@pytest.fixture
def created_store_request_via_endpoint(
    cloud_function_tester, sample_store_product_request_endpoint_payload_factory,
    user_with_root_account, db: firestore.Client # user_with_root_account for auth
):
    """Calls create_store_product_request CF & yields created StoreProductRequest data. Cleans up."""
    payload = sample_store_product_request_endpoint_payload_factory()
    _, response_json = cloud_function_tester.call_function(
        "create_store_product_request", payload, user_id=user_with_root_account.uid
    )
    response = ResponseData.model_validate(response_json)
    assert response.success is True, f"Failed to create store request via endpoint: {response.message}"
    created_req_data = StoreProductRequest.model_validate(response.data)
    
    yield created_req_data

    # Cleanup
    if created_req_data and created_req_data.request_id:
        try:
            db.collection(store_product_requests_collection).document(created_req_data.request_id).delete()
            if created_req_data.related_allocation_id: # If approve was tested and created one
                db.collection(product_allocations_collection).document(created_req_data.related_allocation_id).delete()
        except Exception as e:
            print(f"Cleanup failed for request {created_req_data.request_id} in created_store_request_via_endpoint: {e}")


# --- Cloud Function Test Cases ---

def test_create_store_product_request_endpoint_ok(
    cloud_function_tester, sample_store_product_request_endpoint_payload_factory,
    user_with_root_account, product_requester_service_for_integ: ProductRequester, db: firestore.Client
):
    payload = sample_store_product_request_endpoint_payload_factory()
    request_id_from_cf = None
    try:
        _, response_json = cloud_function_tester.call_function(
            "create_store_product_request", payload, user_id=user_with_root_account.uid
        )
        response = ResponseData.model_validate(response_json)
        assert response.success is True
        assert response.code == 201
        created_data = StoreProductRequest.model_validate(response.data)
        request_id_from_cf = created_data.request_id
        assert request_id_from_cf is not None
        assert created_data.store_id == payload["store_id"]
        assert created_data.status == ProductRequestStatus.PENDING_PRODUCER_APPROVAL.value

        # Verify directly in DB
        db_request = product_requester_service_for_integ.get_request(request_id_from_cf)
        assert db_request is not None
        assert db_request.producer_id == payload["producer_id"]
    finally:
        if request_id_from_cf:
            db.collection(store_product_requests_collection).document(request_id_from_cf).delete()


def test_create_store_product_request_endpoint_unauthenticated(
    cloud_function_tester, sample_store_product_request_endpoint_payload_factory
):
    payload = sample_store_product_request_endpoint_payload_factory()
    _, response_json = cloud_function_tester.call_function("create_store_product_request", payload, user_id=None)
    response = ResponseData.model_validate(response_json)
    assert response.success is False
    assert response.code == 401


def test_approve_store_product_request_endpoint_ok(
    cloud_function_tester, created_store_request_via_endpoint: StoreProductRequest,
    user_with_root_account, test_producer, # test_producer is the one who owns product
    product_requester_service_for_integ: ProductRequester, db: firestore.Client
):
    initial_request = created_store_request_via_endpoint
    
    approve_payload = ApproveStoreProductRequest(
        request_id=initial_request.request_id,
        acting_producer_id=test_producer, # Correct producer
        producer_response_notes="Looks good, approving via CF.",
        delivery_method_for_allocation=DeliveryMethod.STORE_PICKUP.value
    ).model_dump()

    _, response_json = cloud_function_tester.call_function(
        "approve_store_product_request", 
        approve_payload, 
        user_id=user_with_root_account.uid # Assuming producer auth
    )
    response = ResponseData.model_validate(response_json)
    assert response.success is True
    assert response.code == 200
    approved_data = StoreProductRequest.model_validate(response.data)
    assert approved_data.status == ProductRequestStatus.APPROVED_BY_PRODUCER.value
    assert approved_data.related_allocation_id is not None

    # Verify allocation creation
    allocation = db.collection(product_allocations_collection).document(approved_data.related_allocation_id).get()
    assert allocation.exists
    assert allocation.to_dict()[ProductAllocation.DELIVERY_METHOD_FIELD] == DeliveryMethod.STORE_PICKUP.value
    assert allocation.to_dict()[ProductAllocation.STATUS_FIELD] == AllocationStatus.PENDING_CONFIRMATION.value


@pytest.mark.skip(reason="Skipping this test for now, verification logic must be implemented at the entry point")
def test_approve_store_product_request_endpoint_wrong_producer(
    cloud_function_tester, 
    created_store_request_via_endpoint: StoreProductRequest, 
    user_with_root_account
):
    initial_request = created_store_request_via_endpoint
    approve_payload = ApproveStoreProductRequest(
        request_id=initial_request.request_id,
        acting_producer_id="wrong-producer-id-123", # Incorrect producer
        producer_response_notes="Trying with wrong producer."
    ).model_dump()


    _, response_json = cloud_function_tester.call_function(
        "approve_store_product_request", approve_payload, user_id=user_with_root_account.uid
    )

    auth_context = AuthContext(user_id=user_with_root_account.uid)
    auth_context.custom_claims

    response = ResponseData.model_validate(response_json)
    assert response.success is False
    assert response.code == 400 # Should be ValueError from service
    assert "mismatch" in response.message.lower() or "denied" in response.message.lower()


def test_reject_store_product_request_endpoint_ok(
    cloud_function_tester, created_store_request_via_endpoint: StoreProductRequest,
    user_with_root_account, test_producer, product_requester_service_for_integ: ProductRequester
):
    initial_request = created_store_request_via_endpoint
    reject_payload = RejectStoreProductRequest(
        request_id=initial_request.request_id,
        acting_producer_id=test_producer,
        producer_response_notes="Sorry, cannot fulfill this request now via CF."
    ).model_dump()

    _, response_json = cloud_function_tester.call_function(
        "reject_store_product_request", 
        reject_payload, 
        user_id=user_with_root_account.uid
    )

    response = ResponseData.model_validate(response_json)
    assert response.success is True
    assert response.code == 200
    rejected_data = StoreProductRequest.model_validate(response.data)
    assert rejected_data.status == ProductRequestStatus.REJECTED_BY_PRODUCER.value
    assert rejected_data.producer_response_notes == "Sorry, cannot fulfill this request now via CF."

    db_request = product_requester_service_for_integ.get_request(initial_request.request_id)
    assert db_request.status == ProductRequestStatus.REJECTED_BY_PRODUCER.value


def test_cancel_store_product_request_endpoint_ok(
    cloud_function_tester, created_store_request_via_endpoint: StoreProductRequest,
    user_with_root_account, test_store, product_requester_service_for_integ: ProductRequester
):
    initial_request = created_store_request_via_endpoint
    assert initial_request.store_id == test_store # Ensure fixture alignment

    cancel_payload = CancelStoreProductRequest(
        request_id=initial_request.request_id,
        acting_store_id=test_store # Correct store
    ).model_dump()

    _, response_json = cloud_function_tester.call_function(
        "cancel_store_product_request", 
        cancel_payload, 
        user_id=user_with_root_account.uid # Assuming store user auth
    )
    response = ResponseData.model_validate(response_json)
    assert response.success is True
    assert response.code == 200
    cancelled_data = StoreProductRequest.model_validate(response.data)
    assert cancelled_data.status == ProductRequestStatus.CANCELLED_BY_STORE.value

    db_request = product_requester_service_for_integ.get_request(initial_request.request_id)
    assert db_request.status == ProductRequestStatus.CANCELLED_BY_STORE.value

@pytest.mark.skip(reason="Skipping this test for now, verification logic must be implemented at the entry point")
def test_cancel_store_product_request_endpoint_wrong_store(
    cloud_function_tester, created_store_request_via_endpoint: StoreProductRequest, user_with_root_account
):
    initial_request = created_store_request_via_endpoint
    cancel_payload = CancelStoreProductRequest(
        request_id=initial_request.request_id,
        acting_store_id="wrong-store-id-456" # Incorrect store
    ).model_dump()

    _, response_json = cloud_function_tester.call_function(
        "cancel_store_product_request", cancel_payload, user_id=user_with_root_account.uid
    )
    response = ResponseData.model_validate(response_json)
    assert response.success is False
    assert response.code == 400 # ValueError from service
    assert "mismatch" in response.message.lower()
