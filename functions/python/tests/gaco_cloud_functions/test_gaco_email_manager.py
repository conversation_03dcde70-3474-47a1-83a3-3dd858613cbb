import pytest
from models.requests.email_requests import (
    SendEmailRequest, 
    Email, 
    SendInviteEmailRequest, 
    SendSalesReportEmailRequest
)
from models.response import ResponseData

@pytest.fixture
def test_send_email_request():
    return SendEmailRequest(
        email=Email(
            from_alias="Gaco Automation",
            from_email="<EMAIL>",
            to_alias="taisei",
            to_email="<EMAIL>",
            subject="Automation Unit Test",
            text="This is a test email"
        ).model_dump(),
        sender_id="test_sender",
        sender_role="test_sender_role",
        recipient_id="test_recipient"
    )


@pytest.mark.skip(reason="Skipping test_enqueue_send_invitation_email_request")
def test_enqueue_send_invitation_email_request(
    db,
    test_send_email_request,
    cloud_function_tester,
    user_with_root_account,
    test_producer,
    test_store
):

    send_invite_email_request = SendInviteEmailRequest(
        store_id='1IWeWcoCv0KOLfbxfpyM',
        producer_id='qYfPIJeFfJHnQeHMJIKj'
    )

    try:
        response = cloud_function_tester.call_function(
            'enqueue_send_invite_email',
            send_invite_email_request.model_dump(),
            user_id=user_with_root_account.uid
        )

        response_data = response[1]

        assert response_data['code'] == 200
        assert response_data['success'] == True
        assert response_data['message'] == 'Email queued successfully'
    except Exception as e:
        print(e)
        raise e


def test_enqueue_send_sales_report_email_request(
    db,
    test_send_email_request,
    cloud_function_tester,
    user_with_root_account,
    test_producer,
    test_store
):
    send_sales_report_email_request = SendSalesReportEmailRequest(
        store_id='aEwJD5kT9CQJcI2EmFnz',
        sales_report_id='aEwJD5kT9CQJcI2EmFnz-2025-2'
    )

    try:
        response = cloud_function_tester.call_function(
            'enqueue_send_sales_report_email',
            send_sales_report_email_request.model_dump(),
            user_id=user_with_root_account.uid
        )

        response_data = response[1]

        assert response_data['code'] == 200
        assert response_data['success'] == True
        assert response_data['message'] == 'Email queued successfully'
    except Exception as e:
        print(e)
        raise e