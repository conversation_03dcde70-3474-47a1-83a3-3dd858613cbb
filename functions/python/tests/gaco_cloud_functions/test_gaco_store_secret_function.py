import pytest
from models.requests.secret_requests import StoreSecretRequest
from models.response import ResponseData
from services.secret_service import SecretService
from gaco_secrets.secret_manager import SecretManager
from constants.gaco_values import project_id
from time import sleep


def test_store_api_key(
        cloud_function_tester, 
        test_store, 
        user_with_root_account,
        db
    ):
    """Test the store_api_key function."""
    payload = StoreSecretRequest(
        store_id=test_store,
        api_key="test_api_key",
        shop_name="test_shop_name"
    )

    sleep(20)

    tmp_payload = StoreSecretRequest\
      .model_validate(payload.model_dump())

    tmp_payload.store_id
    tmp_payload.shop_name
    tmp_payload.api_key

    _, response_json = cloud_function_tester.call_function(
        "store_api_key",
        payload.model_dump(),
        user_id=user_with_root_account.uid
    )

    response = ResponseData.model_validate(response_json)

    assert response.success is True

    secret_service = SecretService(
        db=db,
        secret_manager=SecretManager(
            project_id=project_id
        )
    )
    secret_service.delete_secret(test_store)
