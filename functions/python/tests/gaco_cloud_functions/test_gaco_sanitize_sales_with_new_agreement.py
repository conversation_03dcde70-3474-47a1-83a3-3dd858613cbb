import pytest
from models.agreement import Role
from models.requests.agreements_requests import SanitizeSalesStagingWithActiveAgreementRequest
from datetime import datetime, timezone, timedelta


def test_sanitize_sales_staging_with_new_agreement_function(
        sales_staging_test_document,
        cloud_function_tester,
        user_with_root_account,
        test_producer,
        db
    ):
    """Test sanitizing sales staging with a new producer through the deployed Cloud Function"""
    # First, create a test sale-staging document in Firestore
    sales_staging_test_document

    effective_date = (datetime.now(timezone.utc) - timedelta(days=1))
    expiration_date = (effective_date + timedelta(days=2))

    iso_effective_date = effective_date.isoformat()
    iso_expiration_date = expiration_date.isoformat()

    sales_staging_test_document.get().to_dict()

    data = {
        "saleId": sales_staging_test_document.id,
        "storeId": sales_staging_test_document.get().to_dict()["storeId"],
        "producerId": test_producer,
        "title": "Test Agreement",
        "effectiveDate": iso_effective_date,
        "expirationDate": iso_expiration_date,
        "commission": 10,
        "documentUrl": "https://example.com/agreement.pdf",
        "createdByRole": Role.STORE.value
    }

    try:
        status_code, result = cloud_function_tester.call_function(
            function_name="sanitize_sales_staging_with_new_active_agreement",
            data=data,
            user_id=user_with_root_account.uid
        )
        assert status_code == 200
        assert result["success"] == True
        assert result["data"]["collection"] == "sales-silver"

        document_id = result["data"]["sale"]["documentId"]
        agreement_id = result["data"]["sale"]["agreementId"]

        # clean up
        agreement_ref = db.collection("agreements").document(agreement_id)
        agreement_ref.delete()

        sales_silver_ref = db.collection("sales-silver").document(document_id)
        sales_silver_ref.delete()

        sales_gold_ref = db.collection("sales-gold").document(document_id)
        sales_gold_ref.delete()
        
    except Exception as e:
        # Clean up the sales staging document even if the test fails
        pytest.skip(f"Skipping test due to issue calling function: {e}")


def test_sanitize_sales_staging_with_new_agreement_only_needed_data(
        sales_staging_test_document,
        cloud_function_tester,
        user_with_root_account,
        test_producer,
        db
    ):
    """Test sanitizing sales staging with a new producer through the deployed Cloud Function"""
    # First, create a test sale-staging document in Firestore
    sales_staging_test_document

    effective_date = (datetime.now(timezone.utc) - timedelta(days=1))

    iso_effective_date = effective_date.isoformat()

    sales_staging_test_document.get().to_dict()

    data = {
        "saleId": sales_staging_test_document.id,
        "storeId": sales_staging_test_document.get().to_dict()["storeId"],
        "producerId": test_producer,
        "effectiveDate": iso_effective_date,
        "commission": 10,
        "createdByRole": Role.STORE.value
    }

    try:
        status_code, result = cloud_function_tester.call_function(
            function_name="sanitize_sales_staging_with_new_active_agreement",
            data=data,
            user_id=user_with_root_account.uid
        )
        assert status_code == 200
        assert result["success"] == True
        assert result["data"]["collection"] == "sales-silver"

        document_id = result["data"]["sale"]["documentId"]
        agreement_id = result["data"]["sale"]["agreementId"]

        # clean up
        agreement_ref = db.collection("agreements").document(agreement_id)
        agreement_ref.delete()

        sales_silver_ref = db.collection("sales-silver").document(document_id)
        sales_silver_ref.delete()

        sales_gold_ref = db.collection("sales-gold").document(document_id)
        sales_gold_ref.delete()
        
    except Exception as e:
        # Clean up the sales staging document even if the test fails
        pytest.skip(f"Skipping test due to issue calling function: {e}")


def test_sanitize_sales_staging_with_new_agreement_non_existing_producer(
        sales_staging_test_document,
        cloud_function_tester,
        user_with_root_account,
        test_producer,
        db
    ):
    """Test sanitizing sales staging with a new producer through the deployed Cloud Function"""
    # First, create a test sale-staging document in Firestore
    sales_staging_test_document

    effective_date = (datetime.now(timezone.utc) - timedelta(days=1))

    iso_effective_date = effective_date.isoformat()

    sales_staging_test_document.get().to_dict()

    data = {
        "saleId": sales_staging_test_document.id,
        "storeId": sales_staging_test_document.get().to_dict()["storeId"],
        "producerId": 'non-existing-producer',
        "effectiveDate": iso_effective_date,
        "commission": 10,
        "createdByRole": Role.STORE.value
    }

    try:
        status_code, result = cloud_function_tester.call_function(
            function_name="sanitize_sales_staging_with_new_active_agreement",
            data=data,
            user_id=user_with_root_account.uid
        )

        assert status_code == 200
        assert result["success"] == False
        assert result["code"] == 404
        
    except Exception as e:
        # Clean up the sales staging document even if the test fails
        pytest.skip(f"Skipping test due to issue calling function: {e}")
