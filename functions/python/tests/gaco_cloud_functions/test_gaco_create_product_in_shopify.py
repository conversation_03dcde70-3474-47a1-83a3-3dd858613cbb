import pytest
import os
import datetime
import uuid
import json # For parsing the response
import requests # For Shopify product cleanup

# Adjust imports based on your project structure
from models.products import Product, ProductVariant
from models.requests.create_product_in_shopify import CreateShopifyProductRequest

# Fixtures like db, test_user, test_store are expected from conftest.py
# CloudFunctionTester should also be available via conftest.py or direct import

# List to store Shopify GIDs of products created by these endpoint tests for potential cleanup
shopify_products_created_by_endpoint_tests = []

@pytest.fixture(scope="module", autouse=True)
def cleanup_shopify_products_module(db, request): # Using db to potentially get SecretManager if needed
    """
    Pytest finalizer to clean up products created in Shopify by this test module.
    This is a best-effort cleanup.
    """
    yield # Tests run here

    if not shopify_products_created_by_endpoint_tests:
        return

    print(f"\nAttempting to cleanup {len(shopify_products_created_by_endpoint_tests)} Shopify product(s) from endpoint tests...")
    
    # For cleanup, we need Shopify credentials. We'll try to get them for one of the stores
    # used during testing. This assumes TEST_STORE_ID is available or we can get it from a test_store fixture.
    # This part might need refinement based on how easy it is to get a valid store_id and its creds here.
    # For simplicity, if a test_store fixture is used in tests, it should have set up these credentials.
    # We'll try to use credentials for os.getenv("TEST_STORE_ID") if available,
    # otherwise, this cleanup might be manual.
    
    store_id_for_cleanup = os.getenv("TEST_STORE_ID") # A store ID that has credentials configured
    api_version_for_cleanup = os.getenv("TEST_SHOPIFY_API_VERSION", "2024-04")

    if not store_id_for_cleanup:
        print("Warning: TEST_STORE_ID not found for cleanup. Shopify products may need manual deletion.")
        return

    from gaco_secrets.secret_manager import SecretManager # Local import for fixture
    from services.shopify_product_creator import ShopifyProductCreator # Local import

    try:
        secret_manager = SecretManager(project_id=os.getenv("PROJECT_ID", "your-gcp-project-id")) # Ensure project_id is correct
        # We need a db instance here. The 'db' fixture is session-scoped.
        # If this finalizer runs after the db session is closed, this could be an issue.
        # A more robust way might be to initialize a temporary client or ensure db is module/session scoped.
        # For now, assuming 'db' fixture is available.
        creator_for_cleanup = ShopifyProductCreator(db=db, secret_manager=secret_manager)
        api_key, shop_name = creator_for_cleanup.get_access_token(store_id_for_cleanup)
        shop_domain = f"{shop_name}.myshopify.com"
        graphql_url = f"https://{shop_domain}/admin/api/{api_version_for_cleanup}/graphql.json"
        headers = {
            'Content-Type': 'application/json',
            'X-Shopify-Access-Token': api_key
        }

        for product_gid in shopify_products_created_by_endpoint_tests:
            delete_mutation = f"""
            mutation productDelete {{
              productDelete(input: {{id: "{product_gid}"}}) {{
                deletedProductId
                userErrors {{ field message }}
              }}
            }}
            """
            try:
                response = requests.post(graphql_url, headers=headers, json={'query': delete_mutation}, timeout=15)
                response.raise_for_status()
                # Further checks on response.json() if needed
                print(f"Endpoint Test Cleanup: Attempted deletion of {product_gid}. Status: {response.status_code}")
            except Exception as e:
                print(f"Endpoint Test Cleanup: Error during cleanup of product {product_gid}: {e}")
    except Exception as e:
        print(f"Endpoint Test Cleanup: General error during teardown: {e}")


def _get_sample_product_data_for_request(status="DRAFT", with_variants=False, with_inventory=False) -> dict:
    """Helper to create product data dictionary for the request payload."""
    now_utc_iso = datetime.datetime.now(datetime.timezone.utc).isoformat()
    unique_suffix = uuid.uuid4().hex[:8]

    product_dict = {
        "title": f"Endpoint Test Product {unique_suffix} ({status})",
        "vendor": "Endpoint Test Vendor",
        "product_type": "Endpoint Goods",
        "status": status.upper(),
        "handle": f"endpoint-test-product-{unique_suffix}",
        "created_at": now_utc_iso,
        "updated_at": now_utc_iso,
        "variants": [],
        # Contextual fields needed by Product model validator, but not directly by Shopify
        "store_id": "context_store_id_placeholder",
        "store_display_name": "Context Store Name Placeholder"
    }
    if with_variants:
        variant1 = {
            "title": "Small Size", "price": 10.99, "sku": f"EP-S-{unique_suffix}",
            "created_at": now_utc_iso, "updated_at": now_utc_iso
        }
        if with_inventory:
            variant1["inventory_quantity"] = 5
        product_dict["variants"].append(variant1)
    return product_dict


def test_create_product_in_shopify_success_no_variants(
    cloud_function_tester, test_user, test_store
):
    """Test successful creation of a product in Shopify without variants via the endpoint."""
    id_token = cloud_function_tester.get_id_token(test_user.uid)
    
    product_data_payload = _get_sample_product_data_for_request(status="ACTIVE")
    
    request_payload = CreateShopifyProductRequest(
        store_id=test_store, # Uses the store_id from the test_store fixture
        product_data=Product(**product_data_payload), # Validate with Product model
        shopify_api_version=os.getenv("TEST_SHOPIFY_API_VERSION", "2024-04")
        # shopify_location_gid is omitted to test auto-fetch or no-inventory case
    ).model_dump(mode='json') # Ensure Pydantic models are dumped correctly for JSON

    response = cloud_function_tester.call_function(
        function_name='create_product_in_shopify',
        data=request_payload,
        user_id=test_user.uid
    )

    assert response[0] == 200 # Callable functions return 200 OK for success
    response_data = response[1] # Assuming ResponseData.model_dump() was used

    assert response_data.get("success") is True
    assert response_data.get("code") == 201
    assert "Product successfully created in Shopify." in response_data.get("message", "")
    assert "shopify_product_gid" in response_data.get("data", {})
    
    created_gid = response_data["data"]["shopify_product_gid"]
    assert created_gid.startswith("gid://shopify/Product/")
    if created_gid:
        shopify_products_created_by_endpoint_tests.append(created_gid)


def test_create_product_in_shopify_with_variants_and_inventory_auto_location(
    cloud_function_tester, test_user, test_store
):
    """Test creating a product with inventory, allowing auto-fetch of default location."""
    product_data_payload = _get_sample_product_data_for_request(
        status="ACTIVE", with_variants=True, with_inventory=True
    )
    
    request_payload = CreateShopifyProductRequest(
        store_id=test_store,
        product_data=Product(**product_data_payload),
        shopify_api_version=os.getenv("TEST_SHOPIFY_API_VERSION", "2024-04"),
        shopify_location_gid=None # Explicitly None to trigger auto-fetch
    ).model_dump(mode='json')

    response = cloud_function_tester.call_function(
        function_name='create_product_in_shopify',
        data=request_payload,
        user_id=test_user.uid
    )
    
    assert response[0] == 200
    response_data = response[1]

    assert response_data.get("success") is True
    assert response_data.get("code") == 201
    assert "shopify_product_gid" in response_data.get("data", {})
    created_gid = response_data["data"]["shopify_product_gid"]
    assert created_gid.startswith("gid://shopify/Product/")
    if created_gid:
        shopify_products_created_by_endpoint_tests.append(created_gid)


def test_create_product_in_shopify_invalid_request_missing_store_id(
    cloud_function_tester, test_user
):
    """Test endpoint with invalid request data (missing store_id)."""
    product_data_payload = _get_sample_product_data_for_request()
    
    # Create payload that will fail Pydantic validation in the Cloud Function
    invalid_payload = {
        # "store_id": "some_store_id", # Missing store_id
        "product_data": product_data_payload,
        "shopify_api_version": os.getenv("TEST_SHOPIFY_API_VERSION", "2024-04")
    }

    response = cloud_function_tester.call_function(
        function_name='create_product_in_shopify',
        data=invalid_payload, # Not using CreateShopifyProductRequest model here intentionally
        user_id=test_user.uid
    )
    
    assert response[0] == 200 # Callable functions return 200 even for "application errors"
    response_data = response[1]
    
    assert response_data.get("success") is False
    assert response_data.get("code") == 400
    assert "Invalid request data." in response_data.get("message", "")


def test_create_product_in_shopify_unauthenticated(cloud_function_tester):
    """Test endpoint access without authentication."""
    product_data_payload = _get_sample_product_data_for_request()
    request_payload = {
        "store_id": "any_store_id",
        "product_data": product_data_payload,
    }
    
    response = cloud_function_tester.call_function(
        function_name='create_product_in_shopify',
        data=request_payload,
        user_id=None # No token
    )
    
    assert response[0] == 200
    response_data = response[1]
    
    assert response_data.get("success") is False
    assert response_data.get("code") == 401
    assert "User must be authenticated" in response_data.get("message", "")
