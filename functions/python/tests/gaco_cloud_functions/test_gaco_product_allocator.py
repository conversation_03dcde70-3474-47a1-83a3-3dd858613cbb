import pytest
import uuid
from datetime import datetime, timezone

from firebase_admin import firestore

from models.allocations import ProductAllocation, ProductAllocationItem, AllocationStatus, DeliveryMethod, product_allocations_collection
from models.products import Product, ProductVariant
from models.response import ResponseData
from models.requests.allocation_requests import UpdateAllocationStatusRequest # Only need this if others are removed

from services.product_manager import ProductManager, SHOPIFY_PRODUCT_GID_PREFIX, SHOPIFY_VARIANT_GID_PREFIX
from services.product_allocator import ProductAllocationManager


# --- Helper ID Generators ---
def new_alloc_integ_shopify_product_gid() -> str:
    return f"{SHOPIFY_PRODUCT_GID_PREFIX}{uuid.uuid4().hex}"

def new_alloc_integ_shopify_variant_gid() -> str:
    return f"{SHOPIFY_VARIANT_GID_PREFIX}{uuid.uuid4().hex}"

# --- Fixtures ---

@pytest.fixture
def product_manager_for_alloc_integ_tests(db: firestore.Client) -> ProductManager:
    return ProductManager(db=db)

@pytest.fixture
def product_allocator_service_for_integ(db: firestore.Client, product_manager_for_alloc_integ_tests: ProductManager) -> ProductAllocationManager:
    """Provides a ProductAllocator service instance for direct interaction or cleanup if needed."""
    return ProductAllocationManager(db=db)

@pytest.fixture
def alloc_integ_test_product_with_variants(
    product_manager_for_alloc_integ_tests: ProductManager,
    test_producer # From conftest.py
):
    """Creates a product with variants owned by test_producer for integration tests."""
    now = datetime.now(timezone.utc).isoformat()
    prod_id = new_alloc_integ_shopify_product_gid()
    var1_id = new_alloc_integ_shopify_variant_gid()
    var2_id = new_alloc_integ_shopify_variant_gid()

    product_data = Product(
        product_id=prod_id,
        title="Alloc Integ Product",
        producer_id=test_producer,
        store_id=f"s_{uuid.uuid4().hex[:4]}", store_display_name="Integ Store", # Dummy
        status="ACTIVE", created_at=now, updated_at=now,
        variants=[
            ProductVariant(
                product_variant_id=var1_id, 
                product_id=prod_id, 
                title="V1", 
                sku="IV1", 
                price=10, 
                inventory_quantity=100, 
                created_at=now, 
                updated_at=now
            ),
            ProductVariant(
                product_variant_id=var2_id, 
                product_id=prod_id, 
                title="V2", 
                sku="IV2", 
                price=20, 
                inventory_quantity=50, 
                created_at=now, 
                updated_at=now
            )
        ]
    )
    created_product = product_manager_for_alloc_integ_tests.create_product(product_data)
    yield created_product # Full Product model with variants
    try:
        product_manager_for_alloc_integ_tests.delete_product(created_product.product_id, delete_variants=True)
    except Exception as e:
        print(f"Cleanup failed for product {created_product.product_id} in alloc_integ_test_product: {e}")


@pytest.fixture
def sample_allocation_endpoint_payload_factory(
    test_producer, # from conftest
    test_store,    # from conftest
    alloc_integ_test_product_with_variants: Product
):
    """Factory to create raw dict payloads for the create_allocation endpoint."""
    def _factory(items_override: list = None, delivery_method_override: str = None) -> dict:
        product_model = alloc_integ_test_product_with_variants
        
        default_items = [
            ProductAllocationItem(product_id=product_model.product_id, product_variant_id=product_model.variants[0].product_variant_id, quantity=2).model_dump(),
            ProductAllocationItem(product_id=product_model.product_id, product_variant_id=product_model.variants[1].product_variant_id, quantity=3).model_dump()
        ]

        return {
            "producer_id": test_producer,
            "store_id": test_store,
            "items": items_override if items_override is not None else default_items,
            "delivery_method": delivery_method_override if delivery_method_override is not None else DeliveryMethod.PRODUCER_SHIPMENT.value,
            "producer_notes": "Integration test allocation",
            # allocation_date and status will be set by model defaults/service
        }
    return _factory

@pytest.fixture
def created_allocation_via_endpoint(
    cloud_function_tester,
    sample_allocation_endpoint_payload_factory,
    user_with_root_account,
    product_allocator_service_for_integ: ProductAllocationManager, # For cleanup
    db: firestore.Client
):
    """Calls create_allocation endpoint and yields created allocation data. Handles DB cleanup."""
    payload = sample_allocation_endpoint_payload_factory()
    
    _, response_json = cloud_function_tester.call_function(
        function_name="create_allocation",
        data=payload,
        user_id=user_with_root_account.uid
    )
    response = ResponseData.model_validate(response_json)
    assert response.success is True
    assert response.data is not None
    created_allocation_data = ProductAllocation.model_validate(response.data) # Includes allocation_id
    
    yield created_allocation_data

    # Cleanup from DB
    if created_allocation_data and created_allocation_data.allocation_id:
        try:
            db.collection(product_allocations_collection).document(created_allocation_data.allocation_id).delete()
        except Exception as e:
            print(f"Cleanup failed for allocation {created_allocation_data.allocation_id} in created_allocation_via_endpoint: {e}")


# --- Endpoint Test Cases ---

def test_create_allocation_endpoint_ok(
    cloud_function_tester,
    sample_allocation_endpoint_payload_factory,
    user_with_root_account,
    product_allocator_service_for_integ: ProductAllocationManager, # for direct verification and cleanup
    db: firestore.Client
):
    payload = sample_allocation_endpoint_payload_factory()
    allocation_id_from_response = None
    try:
        _, response_json = cloud_function_tester.call_function(
            "create_allocation", payload, user_id=user_with_root_account.uid
        )
        response = ResponseData.model_validate(response_json)
        assert response.success is True
        assert response.code == 201
        assert response.data is not None

        allocation_data_from_response = ProductAllocation.model_validate(response.data)
        allocation_id_from_response = allocation_data_from_response.allocation_id
        assert allocation_id_from_response is not None
        assert allocation_data_from_response.producer_id == payload["producer_id"]
        assert allocation_data_from_response.status == AllocationStatus.PENDING_CONFIRMATION.value # Default

        # Verify in DB
        retrieved_from_db = product_allocator_service_for_integ.get_allocation(allocation_id_from_response)
        assert retrieved_from_db is not None
        assert retrieved_from_db.store_id == payload["store_id"]
        assert len(retrieved_from_db.items) == len(payload["items"])

    finally:
        if allocation_id_from_response:
             db.collection(product_allocations_collection).document(allocation_id_from_response).delete()


def test_create_allocation_endpoint_unauthenticated(
        cloud_function_tester, 
        sample_allocation_endpoint_payload_factory
    ):
    payload = sample_allocation_endpoint_payload_factory()
    _, response_json = cloud_function_tester.call_function("create_allocation", payload, user_id=None)
    response = ResponseData.model_validate(response_json)
    assert response.success is False
    assert response.code == 401

def test_create_allocation_endpoint_invalid_payload(cloud_function_tester, sample_allocation_endpoint_payload_factory, user_with_root_account):
    payload = sample_allocation_endpoint_payload_factory()
    del payload["producer_id"] # Make it invalid (missing required field)

    _, response_json = cloud_function_tester.call_function(
        "create_allocation", 
        payload, 
        user_id=user_with_root_account.uid
    )
    response = ResponseData.model_validate(response_json)

    assert response.success is False
    assert response.code == 400
    assert "Request validation failed" in response.message # Pydantic validation errors

def test_create_allocation_endpoint_service_validation_error(
    cloud_function_tester, sample_allocation_endpoint_payload_factory, user_with_root_account,
    alloc_integ_test_product_with_variants: Product
):
    """Test service-level validation like 'variant not found' through the endpoint."""
    product_model = alloc_integ_test_product_with_variants
    invalid_items = [
        ProductAllocationItem(
            product_id=product_model.product_id, 
            product_variant_id="non-existent-variant-123", 
            quantity=1
        ).model_dump()
    ]
    payload = sample_allocation_endpoint_payload_factory(items_override=invalid_items)

    _, response_json = cloud_function_tester.call_function("create_allocation", payload, user_id=user_with_root_account.uid)
    response = ResponseData.model_validate(response_json)
    assert response.success is False
    assert response.code == 404 # Service raises ValueError, caught by CF wrapper
    assert "not found" in response.message.lower()


def test_update_allocation_status_endpoint_ok(
    cloud_function_tester, 
    created_allocation_via_endpoint: ProductAllocation, 
    user_with_root_account,
    product_allocator_service_for_integ: ProductAllocationManager
):
    initial_allocation = created_allocation_via_endpoint # This is ProductAllocation model
    
    update_request_payload = UpdateAllocationStatusRequest(
        allocation_id=initial_allocation.allocation_id,
        new_status=AllocationStatus.IN_TRANSIT.value,
        details={ProductAllocation.TRACKING_NUMBER_FIELD: "TRACK123ENDPOINT"}
    ).model_dump()

    _, response_json = cloud_function_tester.call_function(
        "update_allocation_status", 
        update_request_payload, 
        user_id=user_with_root_account.uid
    )
    response = ResponseData.model_validate(response_json)
    assert response.success is True
    assert response.code == 200

    updated_data_from_response = ProductAllocation.model_validate(response.data)
    assert updated_data_from_response.status == AllocationStatus.IN_TRANSIT.value
    assert updated_data_from_response.tracking_number == "TRACK123ENDPOINT"
    assert updated_data_from_response.shipped_on is not None

    # Verify in DB
    retrieved_from_db = product_allocator_service_for_integ.get_allocation(initial_allocation.allocation_id)
    assert retrieved_from_db is not None
    assert retrieved_from_db.status == AllocationStatus.IN_TRANSIT.value
    assert retrieved_from_db.tracking_number == "TRACK123ENDPOINT"

def test_update_allocation_status_endpoint_not_found(cloud_function_tester, user_with_root_account):
    payload = UpdateAllocationStatusRequest(
        allocation_id="non-existent-allocation-id-123",
        new_status=AllocationStatus.DELIVERED.value
    ).model_dump()

    _, response_json = cloud_function_tester.call_function(
        "update_allocation_status", 
        payload, 
        user_id=user_with_root_account.uid
    )
    response = ResponseData.model_validate(response_json)
    assert response.success is False
    assert response.code == 404 # Service should return None, CF wraps it
    assert "not found" in response.message

def test_update_allocation_status_endpoint_unauthenticated(cloud_function_tester, created_allocation_via_endpoint: ProductAllocation):
    initial_allocation = created_allocation_via_endpoint
    payload = UpdateAllocationStatusRequest(
        allocation_id=initial_allocation.allocation_id,
        new_status=AllocationStatus.CANCELLED.value
    ).model_dump()
    _, response_json = cloud_function_tester.call_function(
        "update_allocation_status", 
        payload, 
        user_id=None
    ) # No auth
    response = ResponseData.model_validate(response_json)
    assert response.success is False
    assert response.code == 401

def test_update_allocation_status_endpoint_invalid_status_value(
    cloud_function_tester, created_allocation_via_endpoint: ProductAllocation, user_with_root_account
):
    initial_allocation = created_allocation_via_endpoint
    # Construct payload directly as dict to send an invalid status
    payload = {
        "allocation_id": initial_allocation.allocation_id,
        "new_status": "INVALID_STATUS_VALUE_XYZ", # This is not a valid AllocationStatus
        "details": {}
    }
    _, response_json = cloud_function_tester.call_function(
        "update_allocation_status", 
        payload, 
        user_id=user_with_root_account.uid
    )
    response = ResponseData.model_validate(response_json)
    assert response.success is False
    assert response.code == 400 # Pydantic validation error
    assert 'Invalid status value' in response.message