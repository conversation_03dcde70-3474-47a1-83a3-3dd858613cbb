import pytest
from time import sleep


def test_sanitize_sales_staging_with_existing_agreement(
    n_sales_stagin,
    cloud_function_tester,
    user_with_root_account,
    sample_agreement,
    db
):
    """Test sanitizing sales staging with an existing agreement"""

    data = {
        "saleIds": n_sales_stagin,
        "agreementId": sample_agreement.id
    }

    try:
        status_code, result = cloud_function_tester.call_function(
            function_name="sanitize_sales_staging_with_existing_agreement",
            data=data,
            user_id=user_with_root_account.uid
        )
        # needs lots of sleep to ensure the task is executed
        sleep(30)

        assert status_code == 200
        assert result["success"] == True

        # check if the document is created in the sales-gold collection
        for sale_id in n_sales_stagin:
            sales_gold_document = db.collection("sales-gold").document(sale_id).get()
            assert sales_gold_document.exists

    except Exception as e:
        pytest.skip(f"Skipping test due to issue calling function: {e}")

