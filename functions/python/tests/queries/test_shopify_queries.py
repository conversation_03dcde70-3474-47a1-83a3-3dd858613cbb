# functions/python/tests/queries/test_shopify_queries.py
import os
import requests
import json
import pytest
from datetime import datetime, timedelta, timezone
from queries.shopify_query_builder import ShopifyQueryBuilder

def test_shopify_api_query():
    """
    Test that a query to the Shopify API works correctly with a provided API key and shop name.
    This test only checks if the query is properly formed and the API responds successfully.
    """
    # Get credentials from environment variables
    api_key = os.environ.get('SHOPIFY_TEST_ACCESS_TOKEN')
    shop_name = os.environ.get('SHOPIFY_TEST_SHOP_NAME')
    
    # Skip test if credentials aren't available
    if not api_key or not shop_name:
        pytest.skip("Shopify API credentials not configured in environment")
    
    # Setup test parameters
    shop_domain = f"{shop_name}.myshopify.com"
    start_date = (datetime.now(timezone.utc) - timedelta(days=360)).strftime("%Y-%m-%d")
    end_date = datetime.now(timezone.utc).strftime("%Y-%m-%d")
    
    # Get the query from the query builder
    query = ShopifyQueryBuilder.get_orders_query(start_date, end_date)
    
    # Make the request to Shopify API
    response = requests.post(
        f"https://{shop_domain}/admin/api/2024-10/graphql.json",
        headers={
            'Content-Type': 'application/json',
            'X-Shopify-Access-Token': api_key
        },
        json={
            'query': query,
            'variables': {
                'startDate': start_date,
                'endDate': end_date
            }
        }
    )

    
    # Check that the request was successful
    assert response.status_code == 200, f"API request failed with status {response.status_code}: {response.text}"
    
    # Verify response contains expected structure
    data = response.json()
    
    # Save the fetched JSON data locally
    
    # Create a directory for test data if it doesn't exist
    test_data_dir = os.path.join(os.path.dirname(__file__), '..', 'test_data')
    os.makedirs(test_data_dir, exist_ok=True)
    
    # Save the response data to a file
    timestamp = datetime.now(timezone.utc).strftime("%Y%m%d_%H%M%S")
    file_path = os.path.join(test_data_dir, f"shopify_orders_{timestamp}.json")
    
    with open(file_path, 'w') as f:
        json.dump(data, f, indent=2)
    
    print(f"Saved Shopify orders data to: {file_path}")

    assert 'data' in data, "Response missing 'data' field"
    assert 'orders' in data['data'], "Response missing 'orders' field"
    assert 'edges' in data['data']['orders'], "Response missing 'edges' field"
    
    # Print basic info about retrieved orders
    order_count = len(data['data']['orders']['edges'])
    print(f"Successfully retrieved {order_count} orders from Shopify")

if __name__ == "__main__":
    # This allows running the test directly with python
    pytest.main(["-xvs", __file__])