# functions/python/tests/services/test_shopify_pagination.py
import os
import requests
import pytest
import json
from datetime import datetime, timedelta, timezone
from queries.shopify_query_builder import ShopifyQueryBuilder

def test_shopify_pagination():
    """
    Test that the pagination for Shopify orders query works correctly.
    """
    # Get credentials from environment variables
    api_key = os.environ.get('SHOPIFY_TEST_ACCESS_TOKEN')
    shop_name = os.environ.get('SHOPIFY_TEST_SHOP_NAME')
    
    # Skip test if credentials aren't available
    if not api_key or not shop_name:
        pytest.skip("Shopify API credentials not configured in environment")
    
    # Setup test parameters with a wide date range to ensure pagination
    shop_domain = f"{shop_name}.myshopify.com"
    
    # Use a wide date range to increase chances of pagination
    start_date = (datetime.now(timezone.utc) - timedelta(days=365)).strftime("%Y-%m-%d")
    end_date = datetime.now(timezone.utc).strftime("%Y-%m-%d")
    
    # Test pagination
    all_orders = []
    cursor = None
    has_next_page = True
    page_count = 0
    
    while has_next_page and page_count < 3:  # Limit to 3 pages for testing
        page_count += 1
        print(f"Fetching page {page_count} of orders...")
        
        # Get the query with pagination
        query = ShopifyQueryBuilder.get_orders_query(start_date, end_date, cursor)
        
        # Make the request to Shopify API
        response = requests.post(
            f"https://{shop_domain}/admin/api/2024-10/graphql.json",
            headers={
                'Content-Type': 'application/json',
                'X-Shopify-Access-Token': api_key
            },
            json={
                'query': query,
                'variables': {
                    'startDate': start_date,
                    'endDate': end_date
                }
            }
        )
        
        assert response.status_code == 200, f"API request failed with status {response.status_code}: {response.text}"
        
        data = response.json()
        assert 'data' in data, "Response missing 'data' field"
        assert 'orders' in data['data'], "Response missing 'orders' field"
        assert 'edges' in data['data']['orders'], "Response missing 'edges' field"
        
        # Extract pagination info
        page_info = data['data']['orders'].get('pageInfo', {})
        has_next_page = page_info.get('hasNextPage', False)
        cursor = page_info.get('endCursor') if has_next_page else None
        
        # Add orders from this page
        orders_on_page = data['data']['orders']['edges']
        all_orders.extend(orders_on_page)
        
        print(f"Page {page_count}: Retrieved {len(orders_on_page)} orders, hasNextPage: {has_next_page}")
        
        # Small pause to avoid rate limiting
        if has_next_page:
            import time
            time.sleep(0.5)
    
    print(f"Total orders retrieved across {page_count} pages: {len(all_orders)}")
    
    # Verify we've either got multiple pages or have proven pagination works
    if page_count > 1:
        assert len(all_orders) > 250, "Pagination should have retrieved more than one page of orders"
    
    # Write the results to a file for examination (optional)
    with open("test_pagination_results.json", "w") as f:
        json.dump({
            "total_orders": len(all_orders),
            "pages_retrieved": page_count,
            "has_more_pages": has_next_page
        }, f, indent=2)

if __name__ == "__main__":
    pytest.main(["-xvs", __file__])