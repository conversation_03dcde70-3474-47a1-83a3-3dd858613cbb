import pytest
from python.queries.sales_staging_query_builder import SalesStagingQueryBuilder
from datetime import datetime

@pytest.fixture
def test_data():
    return {
        'store_id': 'fxKyqX2xIStNJv4pPzQb',
        'start_date': datetime(2024, 11, 1),
        'end_date': datetime(2024, 12, 31)
    }

def test_sales_query_builder_initialization(db):
    builder = SalesStagingQueryBuilder(db)
    query = builder.build()
    assert query._path == ('sales-staging',)

def test_for_store_id_filter(db, test_data):
    query = (
        SalesStagingQueryBuilder(db)
        .for_store_id(test_data['store_id'])
        .between_dates(
            test_data['start_date'], 
            test_data['end_date']
        )
        .build()
    )
    results = list(query.stream())

    assert len(results) == 1  # Should find 1 doc with test_data['store_id']