import pytest
from queries.partners_query_builder import ApplicationsQueryBuilder
from models.application import Status, Application
from datetime import datetime, timezone

@pytest.fixture
def test_data(db, unique_id):
    """Fixture to create and clean up test documents"""
    docs_to_delete = []
    current_time = datetime.now(timezone.utc)
    recipient_id = f'{unique_id}-recipient'
    
    # Create documents
    doc1 = db.collection('applications').document()
    doc1.set(Application(
        senderId=unique_id,
        recipientId=recipient_id,
        status=Status.PENDING.value,
        commission=50,
        appliedAt=current_time
    ).model_dump())
    docs_to_delete.append(doc1)
    
    doc2 = db.collection('applications').document()
    doc2.set(Application(
        senderId='test',
        recipientId=recipient_id,
        status=Status.PENDING.value,
        commission=50,
        appliedAt=current_time
    ).model_dump())
    docs_to_delete.append(doc2)
    
    doc3 = db.collection('applications').document()
    doc3.set(Application(
        senderId=unique_id,
        recipientId=recipient_id,
        status=Status.ACCEPTED.value,
        commission=50,
        appliedAt=current_time
    ).model_dump())
    docs_to_delete.append(doc3)
    
    doc4 = db.collection('applications').document()
    doc4.set(Application(
        senderId=unique_id,
        recipientId=recipient_id,
        status=Status.PENDING.value,
        commission=50,
        appliedAt=current_time
    ).model_dump())
    docs_to_delete.append(doc4)
    
    yield
    
    # Cleanup
    for doc in docs_to_delete:
        doc.delete()

def test_query_builder_initialization(db):
    builder = ApplicationsQueryBuilder(db)
    query = builder.build()
    
    # Verify collection path
    assert query._path == ('applications',)

def test_for_sender_id_filter(db, unique_id):
    query = (
        ApplicationsQueryBuilder(db)
        .for_sender_id(unique_id)
        .build()
    )
    
    results = list(query.stream())
    assert len(results) == 3  # Should find 2 docs with unique_id sender

def test_with_status_filter(db, unique_id):
    query = (
        ApplicationsQueryBuilder(db)
        .with_status(unique_id)
        .build()
    )
    
    results = list(query.stream())
    assert len(results) == 1
    assert results[0].to_dict()['status'] == unique_id

def test_combined_filters(db, unique_id):
    query = (
        ApplicationsQueryBuilder(db)
        .for_sender_id(unique_id)
        .with_status('accepted')
        .build()
    )
    
    results = list(query.stream())
    assert len(results) == 1
    assert results[0].to_dict()['status'] == Status.ACCEPTED.value