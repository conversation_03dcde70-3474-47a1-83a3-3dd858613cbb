import os
import requests
import pytest
import json
from datetime import datetime, timedelta, timezone
from queries.shopify_query_builder import ShopifyQueryBuilder

def test_shopify_product_pagination():
    """
    Test that the pagination for Shopify products query works correctly.
    """
    # Get credentials from environment variables
    api_key = os.environ.get('SHOPIFY_TEST_ACCESS_TOKEN')
    shop_name = os.environ.get('SHOPIFY_TEST_SHOP_NAME')
    
    # Skip test if credentials aren't available
    if not api_key or not shop_name:
        pytest.skip("Shopify API credentials not configured in environment")
    
    # Setup test parameters
    shop_domain = f"{shop_name}.myshopify.com"
    
    # Test pagination
    all_products = []
    cursor = None
    has_next_page = True
    page_count = 0
    max_pages_to_test = 3 # Limit pages for testing to avoid long runs
    
    print(f"Starting Shopify product pagination test for shop: {shop_domain}")

    while has_next_page and page_count < max_pages_to_test:
        page_count += 1
        print(f"Fetching page {page_count} of products...")
        
        # Get the query with pagination for products
        # Using default num_products (100) and num_variants (50) from the builder
        query = ShopifyQueryBuilder.get_products_query(cursor=cursor)
        
        # Make the request to Shopify API
        response = requests.post(
            f"https://{shop_domain}/admin/api/2024-10/graphql.json", # Ensure API version is current or as needed
            headers={
                'Content-Type': 'application/json',
                'X-Shopify-Access-Token': api_key
            },
            json={
                'query': query
                # No variables needed for this product query unless filtering is added
            }
        )

        raw_query_result = response.json()
        with open("raw_products_query_result.json", "w") as f:
            json.dump(raw_query_result, f, indent=4)
        
        assert response.status_code == 200, f"API request failed with status {response.status_code}: {response.text}"
        
        data = response.json()
        assert 'data' in data, f"Response missing 'data' field. Response: {data}"
        assert 'products' in data['data'], f"Response missing 'products' field. Response: {data}"
        assert 'edges' in data['data']['products'], f"Response missing 'edges' field in products. Response: {data}"
        
        # Extract pagination info
        page_info = data['data']['products'].get('pageInfo', {})
        has_next_page = page_info.get('hasNextPage', False)
        cursor = page_info.get('endCursor') if has_next_page else None
        
        # Add products from this page
        products_on_page = data['data']['products']['edges']
        all_products.extend(products_on_page)
        
        print(f"Page {page_count}: Retrieved {len(products_on_page)} products, hasNextPage: {has_next_page}, new cursor: {cursor}")
        
        # Small pause to avoid rate limiting
        if has_next_page and page_count < max_pages_to_test:
            import time
            time.sleep(0.5) # Be mindful of Shopify API rate limits
    
    print(f"Total products retrieved across {page_count} pages: {len(all_products)}")
    
    # Verify we've either got multiple pages or have proven pagination works
    # Default items per page is 100 for products in the query builder.
    # If we fetched more than one page, we should have more than 100 items (if available)
    # This assertion might need adjustment based on the actual number of products in the test store.
    if page_count > 1 and len(all_products) > 0: # Ensure there were products to paginate
         # The default items_per_page for products is 100.
         # A more robust check would be (page_count -1) * items_per_page + len(last_page_items)
         # For simplicity, if we got to a second page, we assume pagination is working.
        pass # Test passes if it successfully paginates more than one page or exhausts products

    elif page_count == 1 and not has_next_page:
        print("Only one page of products found or less than one full page.")
        # This is also a valid scenario, pagination is "working" if there's no next page.
        pass
        
    elif page_count == max_pages_to_test and has_next_page:
        print(f"Reached max test pages ({max_pages_to_test}), more products might be available.")
        pass

    # Write the results to a file for examination
    output_file = "test_product_pagination_results.json"
    with open(output_file, "w") as f:
        json.dump({
            "test_description": "Shopify Product Pagination Test",
            "shop_name": shop_name,
            "total_products_retrieved": len(all_products),
            "pages_retrieved": page_count,
            "stopped_after_max_pages": page_count == max_pages_to_test and has_next_page,
            "has_more_pages_after_test": has_next_page if page_count == max_pages_to_test else False,
            "final_cursor": cursor
        }, f, indent=4)
    print(f"Test results saved to {output_file}")

if __name__ == "__main__":
    # This allows running the test directly:
    # python -m functions.python.tests.queries.test_shopify_products_query
    # Ensure PYTHONPATH includes the project root for `from queries...` to work.
    # Example: export PYTHONPATH=$PYTHONPATH:/path/to/your/gaco-platform
    pytest.main(["-xvs", __file__])
