"""
Tests for Sales Staging Tagger Cloud Functions
"""

import pytest
from unittest.mock import Mock, patch, MagicMock
from firebase_admin import firestore
from firebase_functions import https_fn, tasks_fn

from gaco_cloud_functions.gaco_sales_staging_tagger import (
    initializeSalesTagging,
    processSalesTagging,
    getSalesTaggingStatus
)
from tagger.sales_tagger import SalesStagingProducerTagRequest


@pytest.fixture
def mock_db():
    """Mock Firestore database client"""
    return Mock(spec=firestore.Client)


@pytest.fixture
def mock_auth():
    """Mock Firebase auth context"""
    auth = Mock()
    auth.uid = "test_user_123"
    auth.token = {"uid": "test_user_123"}
    return auth


@pytest.fixture
def sample_tag_request_data():
    """Sample tagging request data"""
    return {
        "storeId": "test_store_123",
        "fields": ["title"],
        "tagRule": {
            "producer_1": ["artisan", "handmade"],
            "producer_2": ["vintage", "antique"]
        },
        "overrideVendor": True,
        "updateVendorIfNull": False
    }


@pytest.fixture
def mock_sales_docs():
    """Mock sales documents"""
    docs = []
    for i in range(75):  # Create 75 docs to test batching
        doc = Mock()
        doc.id = f"sale_{i}"
        doc.to_dict.return_value = {
            "storeId": "test_store_123",
            "title": f"Test item {i}",
            "vendor": "test_vendor"
        }
        docs.append(doc)
    return docs


class TestInitializeSalesTagging:
    """Test cases for initializeSalesTagging function"""
    
    @patch('gaco_cloud_functions.gaco_sales_staging_tagger.SalesStagingProducerTagger')
    @patch('gaco_cloud_functions.gaco_sales_staging_tagger.functions.task_queue')
    @patch('gaco_cloud_functions.gaco_sales_staging_tagger.firestore.client')
    def test_successful_initialization(
        self, 
        mock_firestore_client, 
        mock_task_queue, 
        mock_tagger_class,
        mock_auth,
        sample_tag_request_data,
        mock_sales_docs
    ):
        """Test successful initialization of sales tagging"""
        # Setup mocks
        mock_db = Mock()
        mock_firestore_client.return_value = mock_db
        
        mock_tagger = Mock()
        mock_tagger._get_sales_staging_data.return_value = mock_sales_docs
        mock_tagger_class.return_value = mock_tagger
        
        mock_queue = Mock()
        mock_task_queue.return_value = mock_queue
        
        mock_collection = Mock()
        mock_document = Mock()
        mock_db.collection.return_value = mock_collection
        mock_collection.document.return_value = mock_document
        
        # Create request
        request = Mock()
        request.auth = mock_auth
        request.data = sample_tag_request_data
        
        # Execute
        result = initializeSalesTagging(request)
        
        # Verify
        assert result["success"] is True
        assert result["code"] == 200
        assert "operation_id" in result["data"]
        assert result["data"]["total_documents"] == 75
        assert result["data"]["total_batches"] == 2  # 75 docs / 50 batch_size = 2 batches
        
        # Verify task queue was called for each batch
        assert mock_queue.enqueue.call_count == 2
        
        # Verify operation was stored
        mock_document.set.assert_called_once()
    
    @patch('gaco_cloud_functions.gaco_sales_staging_tagger.SalesStagingProducerTagger')
    @patch('gaco_cloud_functions.gaco_sales_staging_tagger.firestore.client')
    def test_no_documents_to_process(
        self, 
        mock_firestore_client, 
        mock_tagger_class,
        mock_auth,
        sample_tag_request_data
    ):
        """Test initialization when no documents need processing"""
        # Setup mocks
        mock_db = Mock()
        mock_firestore_client.return_value = mock_db
        
        mock_tagger = Mock()
        mock_tagger._get_sales_staging_data.return_value = []  # No documents
        mock_tagger_class.return_value = mock_tagger
        
        # Create request
        request = Mock()
        request.auth = mock_auth
        request.data = sample_tag_request_data
        
        # Execute
        result = initializeSalesTagging(request)
        
        # Verify
        assert result["success"] is True
        assert result["code"] == 200
        assert result["data"]["total_documents"] == 0
        assert result["data"]["batches_created"] == 0
    
    def test_unauthenticated_request(self, sample_tag_request_data):
        """Test initialization with unauthenticated request"""
        request = Mock()
        request.auth = None
        request.data = sample_tag_request_data
        
        result = initializeSalesTagging(request)
        
        assert result["success"] is False
        assert result["code"] == 401
        assert "authenticated" in result["message"]
    
    def test_invalid_request_data(self, mock_auth):
        """Test initialization with invalid request data"""
        request = Mock()
        request.auth = mock_auth
        request.data = {"invalid": "data"}
        
        result = initializeSalesTagging(request)
        
        assert result["success"] is False
        assert result["code"] == 400
        assert "Invalid request data" in result["message"]


class TestProcessSalesTagging:
    """Test cases for processSalesTagging background task"""
    
    @patch('gaco_cloud_functions.gaco_sales_staging_tagger.SalesStagingProducerTagger')
    @patch('gaco_cloud_functions.gaco_sales_staging_tagger.firestore.client')
    def test_successful_batch_processing(
        self, 
        mock_firestore_client, 
        mock_tagger_class,
        mock_sales_docs
    ):
        """Test successful processing of a batch"""
        # Setup mocks
        mock_db = Mock()
        mock_firestore_client.return_value = mock_db
        
        # Mock operation document
        mock_operation_doc = Mock()
        mock_operation_doc.exists = True
        mock_operation_doc.to_dict.return_value = {
            "tag_request": {
                "storeId": "test_store_123",
                "fields": ["title"],
                "tagRule": {"producer_1": ["test"]},
                "overrideVendor": True,
                "updateVendorIfNull": False
            },
            "total_batches": 2,
            "batches_completed": 0,
            "documents_processed": 0,
            "documents_tagged": 0
        }
        
        mock_collection = Mock()
        mock_document = Mock()
        mock_db.collection.return_value = mock_collection
        mock_collection.document.return_value = mock_document
        mock_document.get.return_value = mock_operation_doc
        
        # Mock tagger
        mock_tagger = Mock()
        mock_tagger._get_sales_staging_data.return_value = mock_sales_docs
        mock_tagger._process_sales_documents.return_value = {
            "processed": 50,
            "tagged": 25,
            "tagged_documents": []
        }
        mock_tagger.sales_staging_collection = "sales-staging"
        mock_tagger_class.return_value = mock_tagger
        
        # Mock transaction
        mock_transaction = Mock()
        mock_db.transaction.return_value = mock_transaction
        
        # Create task request
        request = Mock()
        request.data = {
            "operation_id": "test_operation_123",
            "batch_index": 0,
            "batch_size": 50
        }
        
        # Execute
        processSalesTagging(request)
        
        # Verify tagger was called with correct batch
        mock_tagger._process_sales_documents.assert_called_once()
        
        # Verify operation document was retrieved
        mock_collection.document.assert_called_with("test_operation_123")
    
    def test_missing_operation_id(self):
        """Test task with missing operation_id"""
        request = Mock()
        request.data = {"batch_index": 0}
        
        # Should not raise exception, just log error and return
        processSalesTagging(request)
    
    def test_missing_batch_index(self):
        """Test task with missing batch_index"""
        request = Mock()
        request.data = {"operation_id": "test_operation_123"}
        
        # Should not raise exception, just log error and return
        processSalesTagging(request)
    
    @patch('gaco_cloud_functions.gaco_sales_staging_tagger.firestore.client')
    def test_operation_not_found(self, mock_firestore_client):
        """Test task when operation document doesn't exist"""
        # Setup mocks
        mock_db = Mock()
        mock_firestore_client.return_value = mock_db
        
        mock_operation_doc = Mock()
        mock_operation_doc.exists = False
        
        mock_collection = Mock()
        mock_document = Mock()
        mock_db.collection.return_value = mock_collection
        mock_collection.document.return_value = mock_document
        mock_document.get.return_value = mock_operation_doc
        
        # Create task request
        request = Mock()
        request.data = {
            "operation_id": "nonexistent_operation",
            "batch_index": 0,
            "batch_size": 50
        }
        
        # Should not raise exception, just log error and return
        processSalesTagging(request)


class TestGetSalesTaggingStatus:
    """Test cases for getSalesTaggingStatus function"""
    
    @patch('gaco_cloud_functions.gaco_sales_staging_tagger.firestore.client')
    def test_successful_status_retrieval(self, mock_firestore_client, mock_auth):
        """Test successful status retrieval"""
        # Setup mocks
        mock_db = Mock()
        mock_firestore_client.return_value = mock_db
        
        mock_operation_doc = Mock()
        mock_operation_doc.exists = True
        mock_operation_doc.to_dict.return_value = {
            "status": "processing",
            "total_documents": 100,
            "documents_processed": 75,
            "documents_tagged": 50,
            "total_batches": 2,
            "batches_completed": 1,
            "created_at": "2024-01-01T00:00:00Z"
        }
        
        mock_collection = Mock()
        mock_document = Mock()
        mock_db.collection.return_value = mock_collection
        mock_collection.document.return_value = mock_document
        mock_document.get.return_value = mock_operation_doc
        
        # Create request
        request = Mock()
        request.auth = mock_auth
        request.data = {"operation_id": "test_operation_123"}
        
        # Execute
        result = getSalesTaggingStatus(request)
        
        # Verify
        assert result["success"] is True
        assert result["code"] == 200
        assert result["data"]["status"] == "processing"
        assert result["data"]["progress_percentage"] == 50.0  # 1/2 batches completed
        assert result["data"]["total_documents"] == 100
        assert result["data"]["documents_processed"] == 75
        assert result["data"]["documents_tagged"] == 50
    
    def test_unauthenticated_status_request(self):
        """Test status request without authentication"""
        request = Mock()
        request.auth = None
        request.data = {"operation_id": "test_operation_123"}
        
        result = getSalesTaggingStatus(request)
        
        assert result["success"] is False
        assert result["code"] == 401
    
    def test_missing_operation_id(self, mock_auth):
        """Test status request without operation_id"""
        request = Mock()
        request.auth = mock_auth
        request.data = {}
        
        result = getSalesTaggingStatus(request)
        
        assert result["success"] is False
        assert result["code"] == 400
        assert "operation_id is required" in result["message"]
    
    @patch('gaco_cloud_functions.gaco_sales_staging_tagger.firestore.client')
    def test_operation_not_found_status(self, mock_firestore_client, mock_auth):
        """Test status request for non-existent operation"""
        # Setup mocks
        mock_db = Mock()
        mock_firestore_client.return_value = mock_db
        
        mock_operation_doc = Mock()
        mock_operation_doc.exists = False
        
        mock_collection = Mock()
        mock_document = Mock()
        mock_db.collection.return_value = mock_collection
        mock_collection.document.return_value = mock_document
        mock_document.get.return_value = mock_operation_doc
        
        # Create request
        request = Mock()
        request.auth = mock_auth
        request.data = {"operation_id": "nonexistent_operation"}
        
        # Execute
        result = getSalesTaggingStatus(request)
        
        # Verify
        assert result["success"] is False
        assert result["code"] == 404
        assert "Operation not found" in result["message"]
