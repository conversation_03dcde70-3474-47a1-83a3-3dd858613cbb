import pytest
from queries.sales_silver_query_builder import SalesSilverQueryBuilder
from models.sales import SalesSilver, SalesGold, sales_gold_collection
from data_generator.single_invoice_data_generator import (
    generate_single_invoice_data_handler_from_documentId
)


def test_migrate_sales_silver_to_gold(db):

    sales_silver_query_builder = SalesSilverQueryBuilder(db)
    store_id = "k8mqH8BBv8zpV7enshI0"
    
    
    sales_silver_query_result = sales_silver_query_builder\
      .for_store_id(store_id).build()

    gold_docs_to_set = []
    for sale in sales_silver_query_result.stream():
        print(sale)
        sales_silver = SalesSilver.model_validate(sale.to_dict())

        sales_gold = generate_single_invoice_data_handler_from_documentId(db, sale.id)
        gold_docs_to_set.append(sales_gold.model_dump())


    batch_size = 400
    current_batch = db.batch()
    docs_count = 0
    
    print(f"Found {len(gold_docs_to_set)} documents to migrate")
    
    # Add documents to batch
    for gold_data in gold_docs_to_set:
        # Add to current batch
        gold_ref = db.collection(sales_gold_collection) \
          .document(gold_data[SalesGold.DOCUMENT_ID_FIELD])

        current_batch.set(gold_ref, gold_data)
        docs_count += 1
        
        # If reached batch size, commit and create new batch
        if docs_count >= batch_size:
            print(f"Committing batch of {docs_count} documents")
            current_batch.commit()
            current_batch = db.batch()
            docs_count = 0
    
    # Commit any remaining documents
    if docs_count > 0:
        print(f"Committing final batch of {docs_count} documents")
        current_batch.commit()
    
    print(f"Successfully migrated {len(gold_docs_to_set)} documents to sales-gold collection")


