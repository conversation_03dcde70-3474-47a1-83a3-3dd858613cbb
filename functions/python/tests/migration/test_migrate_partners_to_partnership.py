import pytest
from datetime import datetime, timedelta, timezone
from models.partner import Partnership

# pytest tests/migration/test_migrate_partners_to_partnership.py -s -v
# -s is to print the print statements
# -v is to print the verbose output
def test_migrate_partners_to_partnership(db):
    # get all documents from partnerships collection and move it to partnerships collection
    partners_collection = db.collection('partnerships')
    partnerships_collection = db.collection('partnerships')
    
    # Count for logging
    total_documents = 0
    migrated_documents = 0
    
    try:
        # Get all documents from partnerships collection
        partners_docs = partners_collection.stream()
        
        # Count total documents
        partners_list = list(partners_docs)
        total_documents = len(partners_list)
        print(f"Found {total_documents} documents in partnerships collection")
        
        # Process each document
        for doc in partners_list:
            partner_data = doc.to_dict()
            partner_id = doc.id
            
            try:
                # Create document in partnerships collection with same ID
                partnerships_collection.document(partner_id).set(partner_data)
                print(f"Migrated partner with ID: {partner_id}")
                migrated_documents += 1
            except Exception as e:
                print(f"Error migrating partner with ID {partner_id}: {str(e)}")
                
        print(f"Migration completed. Migrated {migrated_documents} out of {total_documents} documents.")
        
    except Exception as e:
        print(f"Error during migration: {str(e)}")


def test_add_start_and_end_date_to_partnerships(db):
    # get all documents from partnerships collection and move it to partnerships collection
    partnerships_collection = db.collection('partnerships')
    
    start_date = datetime.now(timezone.utc)
    end_date = start_date + timedelta(days=365)

    for partner_doc in partnerships_collection.stream():
        partner_data = partner_doc.to_dict()
        partner_data[Partnership.START_DATE_FIELD] = start_date
        partner_data[Partnership.END_DATE_FIELD] = end_date
        a_partner = Partnership.model_validate(partner_data)


        partnerships_collection.document(partner_doc.id)\
            .set(a_partner.model_dump())
    