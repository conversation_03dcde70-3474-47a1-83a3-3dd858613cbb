# functions/python/tests/utils/storage_rest_client_helper.py
import requests
import google.auth
import google.auth.transport.requests
from urllib.parse import quote
from python.constants.gaco_values import project_id


class StorageRestClientHelper:
    """
    A helper class to interact with Google Cloud Storage via its JSON REST API,
    primarily for testing security rules with user ID tokens.
    """
    # OLD GCS JSON API URL:
    # BASE_URL = "https://storage.googleapis.com/storage/v1/b" 
    
    # NEW Firebase Storage API URL:
    BASE_URL = "https://firebasestorage.googleapis.com/v0/b"

    def __init__(self, project_id_or_bucket_name: str = project_id, id_token: str = None):
        # For firebasestorage.googleapis.com, you typically use the bucket name directly.
        # The project_id is part of the default bucket name (e.g., project-id.appspot.com)
        self.bucket_name_for_url = project_id_or_bucket_name # This should be your full bucket name
        self.id_token = id_token
        self.headers = {}
        if self.id_token:
            self.headers["Authorization"] = f"Bearer {self.id_token}"

    def set_id_token(self, id_token: str):
        self.id_token = id_token
        self.headers["Authorization"] = f"Bearer {self.id_token}"
        print(f"StorageRestClientHelper: Set Authorization header with token: Bearer {self.id_token[:20]}...")

    def _get_object_url(self, bucket_name: str, object_name: str) -> str:
        # For Firebase Storage API, the bucket name is part of the BASE_URL path construction
        encoded_object_name = quote(object_name, safe='')
        # The bucket_name here should be the one passed to the constructor, 
        # as it's part of the base path segment.
        return f"{self.BASE_URL}/{bucket_name}/o/{encoded_object_name}"

    def get_object_media(self, gcs_path: str) -> requests.Response:
        """
        Downloads the media (content) of a GCS object.
        Parses gs://bucket_name/object_path format.
        """
        if not gcs_path.startswith("gs://"):
            raise ValueError("GCS path must start with gs://")
        
        parts = gcs_path[5:].split("/", 1)
        if len(parts) < 2:
            raise ValueError("GCS path must be in the format gs://bucket_name/object_path")
            
        actual_bucket_name, object_name = parts[0], parts[1]
        
        # Ensure the bucket name used for URL construction is the one configured for the client,
        # or validate it matches. For Firebase Storage API, the bucket name is part of the URL path.
        # Here, we'll use actual_bucket_name from the gs:// path directly.
        url = self._get_object_url(actual_bucket_name, object_name)
        media_url = f"{url}?alt=media" 
        
        if not self.id_token:
            print("StorageRestClientHelper: ERROR - ID token is not set before making request!")
            raise ValueError("ID token not set in StorageRestClientHelper")

        print(f"StorageRestClientHelper: Making GET request to {media_url} with headers: {self.headers}")
        response = requests.get(media_url, headers=self.headers)
        return response

    def get_object_metadata(self, gcs_path: str) -> requests.Response:
        """
        Retrieves the metadata of a GCS object.
        Parses gs://bucket_name/object_path format.
        """
        if not gcs_path.startswith("gs://"):
            raise ValueError("GCS path must start with gs://")
        parts = gcs_path[5:].split("/", 1)
        if len(parts) < 2:
            raise ValueError("GCS path must be in the format gs://bucket_name/object_path")
        actual_bucket_name, object_name = parts[0], parts[1]
        
        url = self._get_object_url(actual_bucket_name, object_name)
        if not self.id_token:
            raise ValueError("ID token not set in StorageRestClientHelper")
        print(f"StorageRestClientHelper: Making GET request to {url} for metadata with headers: {self.headers}")
        response = requests.get(url, headers=self.headers)
        return response
