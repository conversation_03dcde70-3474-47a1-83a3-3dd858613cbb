from firebase_functions import logger # This should already be in your conftest.py
import requests # Add this if it's not already there
import json # Add this if it's not already there
from python.constants.gaco_values import project_id


class FirestoreRestClientHelper:
    """
    Helper class to interact with Firestore via its REST API.
    """
    def __init__(self, id_token: str = None, project_id: str = project_id):
        self.id_token = id_token
        self.project_id = project_id
        # For the default database, the path includes '/databases/(default)/documents'
        self.base_url = f"https://firestore.googleapis.com/v1/projects/{self.project_id}/databases/(default)/documents"
        self.headers = {
            "Authorization": f"Bearer {self.id_token}",
            "Content-Type": "application/json",
            "Accept": "application/json"
        }
        # Using the logger already imported in your conftest.py
        self.logger = logger 

    def set_id_token(self, id_token: str):
        self.id_token = id_token
        self.headers["Authorization"] = f"Bearer {self.id_token}"

    def get_document(self, document_path: str) -> tuple[bool, dict]:
        """
        Reads a Firestore document using the Firestore REST API.

        Args:
            document_path: The full path to the document (e.g., "collectionName/documentId").

        Returns:
            A tuple (success, data_or_error_info).
            If successful, success is True and data_or_error_info is the document data (dict).
            The document data will be in the Firestore REST API format (fields wrapped in type descriptors).
            If failed, success is False and data_or_error_info is a dict with error details.
        """
        if not self.id_token:
            self.logger.error("Firestore REST Client: ID token not provided.")
            return False, {"error": "ID token not provided to FirestoreRestClientHelper."}

        # Ensure document_path doesn't start with a slash if base_url already provides the root
        if document_path.startswith("/"):
            document_path = document_path[1:]
            
        url = f"{self.base_url}/{document_path}"
        self.logger.info(f"Firestore REST GET: {url}")
        self.logger.info(f"Using ID Token: {self.id_token[:30]}...{self.id_token[-10:] if len(self.id_token) > 40 else ''}")

        try:
            response = requests.get(url, headers=self.headers, timeout=10) # Added a 10-second timeout
            self.logger.info(f"Firestore REST Response Status: {response.status_code}")

            if response.ok:
                return True, response.json()
            else:
                error_text = response.text
                self.logger.error(f"Firestore REST Error: {response.status_code} - {error_text}")
                try:
                    # Try to parse error response if it's JSON
                    error_details = response.json()
                    return False, {"status_code": response.status_code, "details": error_details, "error": "API request failed"}
                except json.JSONDecodeError:
                    return False, {"status_code": response.status_code, "text": error_text, "error": "API request failed (non-JSON error response)"}
        except requests.exceptions.Timeout:
            self.logger.error(f"Firestore REST Request Timeout for URL: {url}")
            return False, {"error": f"Request timed out: {url}"}
        except requests.exceptions.RequestException as e:
            self.logger.error(f"Firestore REST RequestException: {e}")
            return False, {"error": f"Request failed: {str(e)}"}

    def _format_fields_for_rest(self, data: dict) -> dict:
        """Helper to format python dict to Firestore REST API fields map."""
        formatted_fields = {}
        for key, value in data.items():
            if isinstance(value, str):
                formatted_fields[key] = {"stringValue": value}
            elif isinstance(value, bool):
                formatted_fields[key] = {"booleanValue": value}
            elif isinstance(value, int) or isinstance(value, float):
                # Firestore REST API expects integerValue and doubleValue to be strings for numbers
                # but API itself handles Python numbers directly in JSON for POST/PATCH.
                # Let's stick to direct number for now, can adjust if API complains.
                # It's often safer to send numbers as strings if type is 'integerValue' or 'doubleValue'.
                # For simplicity, let's assume direct numbers work for now.
                # If not, convert to string and use "integerValue" or "doubleValue".
                if isinstance(value, int):
                    formatted_fields[key] = {"integerValue": str(value)}
                else:
                    formatted_fields[key] = {"doubleValue": value} # doubleValue can often take float directly
            # Add other type mappings as needed (e.g., timestampValue, mapValue, arrayValue)
            elif value is None:
                formatted_fields[key] = {"nullValue": None}
            else:
                # Default to stringValue if type is unknown, or raise error
                self.logger.warn(f"Unsupported type for field '{key}': {type(value)}. Defaulting to string.")
                formatted_fields[key] = {"stringValue": str(value)}
        return formatted_fields

    def create_or_update_document(self, document_path: str, data: dict, is_update: bool = False) -> tuple[bool, dict]:
        """
        Creates or updates (patches) a Firestore document using the Firestore REST API.
        For a 'set' operation that creates if not exists or overwrites, use is_update=False (POST to collection, or PUT to document path).
        For a 'set' that merges/updates, use is_update=True (PATCH to document path).
        Firestore's 'set' with no merge is like a 'create' or 'overwrite'.
        Let's use PATCH for set operations here, which is like set with merge=true or an update.
        If you want to strictly create (fail if exists) or fully overwrite, the REST methods differ.
        This implementation will use PATCH (update, or create if ?mask.fieldPaths ensures creation logic).
        A simple 'set' often maps to a PATCH with field masks or a PUT to fully replace.
        For simplicity, this will be a PATCH (update).

        Args:
            document_path: The full path to the document (e.g., "collectionName/documentId").
            data: Python dictionary of data to set.
            is_update: If True, uses PATCH (update). If False, could be POST (create) or PUT (overwrite).
                       We'll use PATCH for this example to simulate a 'set' or 'update'.

        Returns:
            A tuple (success, data_or_error_info).
        """
        if not self.id_token:
            self.logger.error("Firestore REST Client: ID token not provided.")
            return False, {"error": "ID token not provided."}

        url = f"{self.base_url}/{document_path}"
        # Firestore REST API expects fields to be wrapped in type descriptors.
        payload = {"fields": self._format_fields_for_rest(data)}

        http_method = "PATCH" # Use PATCH for set/update operations
        self.logger.info(f"Firestore REST {http_method}: {url} with payload: {json.dumps(payload, indent=2)}")

        try:
            if http_method == "PATCH":
                # For PATCH, you can specify updateMasks to only update certain fields.
                # To update all fields present in the payload:
                # Add ?updateMask.fieldPaths=field1&updateMask.fieldPaths=field2 to the URL
                # For a general 'set' (like Firestore SDK's set without merge), a PUT might be closer,
                # or a PATCH where the mask includes all keys from the data.
                # For simplicity, we'll patch all fields in `data`.
                update_paths = [f"updateMask.fieldPaths={key}" for key in data.keys()]
                query_params = "&".join(update_paths)
                request_url = f"{url}?{query_params}" if query_params else url

                response = requests.patch(request_url, headers=self.headers, json=payload, timeout=10)
            # else: # Handle PUT or POST if needed for stricter create/overwrite
            #     return False, {"error": "Unsupported write operation"}

            self.logger.info(f"Firestore REST Response Status: {response.status_code}")

            if response.ok:
                return True, response.json() # Returns the updated document
            else:
                # ... (same error handling as get_document) ...
                error_text = response.text
                self.logger.error(f"Firestore REST Error: {response.status_code} - {error_text}")
                try:
                    error_details = response.json()
                    return False, {"status_code": response.status_code, "details": error_details, "error": f"API {http_method} request failed"}
                except json.JSONDecodeError:
                    return False, {"status_code": response.status_code, "text": error_text, "error": f"API {http_method} request failed (non-JSON error)"}
        except requests.exceptions.Timeout:
            self.logger.error(f"Firestore REST Request Timeout for URL: {url}")
            return False, {"error": f"Request timed out: {url}"}
        except requests.exceptions.RequestException as e:
            self.logger.error(f"Firestore REST RequestException: {e}")
            return False, {"error": f"Request failed: {str(e)}"}
            
    def delete_document(self, document_path: str) -> tuple[bool, dict]:
        """Deletes a document."""
        if not self.id_token:
            self.logger.error("Firestore REST Client: ID token not provided.")
            return False, {"error": "ID token not provided."}
            
        url = f"{self.base_url}/{document_path}"
        self.logger.info(f"Firestore REST DELETE: {url}")
        
        try:
            response = requests.delete(url, headers=self.headers, timeout=10)
            self.logger.info(f"Firestore REST Response Status: {response.status_code}")
            if response.ok:
                return True, response.json() # Empty JSON {} on success
            else:
                # ... (same error handling as get_document) ...
                error_text = response.text
                self.logger.error(f"Firestore REST Error: {response.status_code} - {error_text}")
                try:
                    error_details = response.json()
                    return False, {"status_code": response.status_code, "details": error_details, "error": "API DELETE request failed"}
                except json.JSONDecodeError:
                    return False, {"status_code": response.status_code, "text": error_text, "error": "API DELETE request failed (non-JSON error)"}
        except requests.exceptions.Timeout:
            self.logger.error(f"Firestore REST Request Timeout for URL: {url}")
            return False, {"error": f"Request timed out: {url}"}
        except requests.exceptions.RequestException as e:
            self.logger.error(f"Firestore REST RequestException: {e}")
            return False, {"error": f"Request failed: {str(e)}"}
