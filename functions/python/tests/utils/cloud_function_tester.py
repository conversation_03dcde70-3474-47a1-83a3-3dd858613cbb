import json
import os
import requests
from firebase_admin import auth
from typing import Dict, Any, Tu<PERSON>, Optional

class CloudFunctionTester:
    """
    Utility class for testing Firebase Cloud Functions.
    Provides methods for authentication and calling Cloud Function endpoints.
    """
    
    def __init__(self, region: str = "europe-west3"):
        """
        Initialize the CloudFunctionTester with a default region.
        
        Args:
            region (str): GCP region where functions are deployed. Defaults to "europe-west3".
        """
        self.region = region
        self.project_id = os.getenv("PROJECT_ID")
    
    @staticmethod
    def get_id_token(user_id: str) -> str:
        """
        Get an ID token for a user by creating a custom token and exchanging it.
        
        Args:
            user_id (str): Firebase Auth user ID
            
        Returns:
            str: ID token for authentication
            
        Raises:
            Exception: If token exchange fails
        """
        try:
            # Create a custom token
            custom_token = auth.create_custom_token(user_id).decode('utf-8')
            
            # API key for testing from environment
            api_key = os.getenv("LOCAL_FIREBASE_API_KEY")
            if not api_key:
                raise ValueError("LOCAL_FIREBASE_API_KEY environment variable not set")
            
            # Exchange the custom token for an ID token
            response = requests.post(
                f"https://identitytoolkit.googleapis.com/v1/accounts:signInWithCustomToken?key={api_key}",
                json={"token": custom_token, "returnSecureToken": True}
            )

            if response.status_code != 200:
                print(f"Error response: {response.text}")
                raise Exception(f"Failed to exchange custom token: {response.status_code}")
            
            return response.json()['idToken']
        except Exception as e:
            print(f"Error getting ID token: {e}")
            raise
    
    def call_function(self, 
                     function_name: str, 
                     data: Dict[str, Any], 
                     user_id: Optional[str] = None) -> Tuple[int, Dict[str, Any]]:
        """
        Call a deployed Firebase Cloud Function.
        
        Args:
            function_name (str): Name of the function to call
            data (Dict[str, Any]): Data to send in the request
            user_id (Optional[str]): User ID to authenticate as (if None, call anonymously)
            
        Returns:
            Tuple[int, Dict[str, Any]]: Tuple of (status_code, parsed_response)
        """
        # Construct the URL
        url = f"https://{self.region}-{self.project_id}.cloudfunctions.net/{function_name}"
        
        # Set up headers
        headers = {"Content-Type": "application/json"}
        
        # Add authentication if user_id provided
        if user_id:
            token = self.get_id_token(user_id)
            headers["Authorization"] = f"Bearer {token}"
        
        # For callable functions, the request body needs this structure
        request_body = {"data": data}
        
        # Make the request
        response = requests.post(url, headers=headers, json=request_body)
        
        # Print debug info
        print(f"Function: {function_name}")
        print(f"Response Status: {response.status_code}")
        print(f"Response Body: {response.text}")
        
        # Parse the response
        result = {}
        if response.text:
            result = response.json().get("result", {})
            # If result is a JSON string, parse it
            if isinstance(result, str):
                try:
                    result = json.loads(result)
                except json.JSONDecodeError:
                    # If it's not valid JSON, keep it as is
                    pass
        
        return response.status_code, result
