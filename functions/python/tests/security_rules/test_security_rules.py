# functions/python/tests/test_security_rules.py

import pytest
from firebase_admin import auth, firestore
from firebase_functions import logger
from google.cloud import firestore as google_firestore
from google.auth.credentials import Credentials
from google.auth.transport.requests import Request
import grpc


def test_public_store_read_access(db, test_user):
    """Test that stores collection is publicly readable"""
    # Try to read stores collection without authentication
    stores_ref = db.collection('stores')
    stores = stores_ref.limit(1).get()
    assert len(stores) >= 0  # Should be able to read

def test_sales_gold_access_without_rights(firestore_rest_client, db, test_user, test_store):
    """
    Test that users without proper access rights cannot access sales-gold via REST API.
    The test_user (used by firestore_rest_client) by default has no custom claims.
    """
    # Ensure the document exists so the failure is due to permissions, not a missing doc.
    # The 'db' fixture here provides an admin client.
    # 'test_store' provides the store_id which should match the storeId in test-sale doc
    doc_path_parts = ["sales-gold", "test-sale-for-no-rights-test"] # Use a unique doc ID for clarity
    doc_ref = db.collection(doc_path_parts[0]).document(doc_path_parts[1])
    
    # Make sure the document exists for the test, otherwise a 404 might be confused with 403
    # test_store fixture gives the store_id
    doc_ref.set({
        "storeId": test_store, # Ensure this field exists and matches what rules might check
        "amount": 50,
        "description": "Document for testing access without rights"
    })

    document_path = f"{doc_path_parts[0]}/{doc_path_parts[1]}"
    
    print(f"Attempting to read document '{document_path}' with default user (no specific store_access_rights).")
    success, result = firestore_rest_client.get_document(document_path)

    assert success is False, "Should not be able to read document without proper access rights."
    
    # Check for permission denied (typically 403 Forbidden for REST API when rules deny)
    # The result dict contains error details as returned by FirestoreRestClientHelper
    assert "status_code" in result, "Error result should contain 'status_code'."
    assert result["status_code"] == 403, \
        f"Expected status code 403 (Forbidden), but got {result['status_code']}. Error: {result.get('details', result.get('text'))}"
    
    print(f"Correctly received permission denied (status {result['status_code']}) as expected.")

    # Clean up the test document
    doc_ref.delete()

def test_sales_gold_access_with_store_rights(
    db,
    test_user,
    test_store,
    firestore_rest_client_helper,
    cloud_function_tester
):
    auth.set_custom_user_claims(test_user.uid, {
        'store_access_rights': { test_store: 'admin' }
    })

    doc_path_parts = ["sales-gold", "test-sale-with-store-rights"]
    doc_ref = db.collection(doc_path_parts[0]).document(doc_path_parts[1])

    doc_ref.set({
        "storeId": test_store, # Ensure this field exists and matches what rules might check
        "amount": 50,
        "description": "Document for testing access without rights"
    })

    document_path = f"{doc_path_parts[0]}/{doc_path_parts[1]}"

    try:
        new_token = cloud_function_tester.get_id_token(test_user.uid)
    except Exception as e:
        assert False, f"Should be able to get ID token for user {test_user.uid}: {str(e)}"

    firestore_rest_client_helper.set_id_token(new_token)

    success, result = firestore_rest_client_helper.get_document(document_path)

    assert success is True, "Should be able to read document with proper access rights."
    assert result is not None, "Document should exist and be readable."
    assert result["fields"]["amount"]["integerValue"] == '50', "Document should have the correct amount."

    doc_ref.delete()

def test_sales_gold_access_with_producer_rights(
    db,
    test_user,
    test_producer, # Fixture that creates a producer and returns its ID
    firestore_rest_client_helper, # Use the fixture that provides the helper instance
    cloud_function_tester # To get a new token
):
    """
    Test that a user WITH correct 'producer_access_rights' CAN access a sales-gold document
    that is associated with that producer, via the REST API.
    """
    # 1. Set custom claims for the test_user
    claims = {
        'producer_access_rights': {
            test_producer: 'admin'  # Grant 'admin' rights for the specific test_producer
                                    # Adjust 'admin' to 'viewer' or 'editor' if your read rule allows those for producers
        }
    }
    auth.set_custom_user_claims(test_user.uid, claims)
    print(f"Set custom claims for user {test_user.uid}: {claims}")

    # 2. Ensure the target document exists and has the correct producerId
    doc_id = f"test-sale-prod-rights-{test_producer}"
    doc_path_parts = ["sales-gold", doc_id]
    doc_ref = db.collection(doc_path_parts[0]).document(doc_path_parts[1])
    document_data_to_set = {
        "producerId": test_producer, # This document is linked to the producer
        "amount": 75,
        "description": "Document for testing access with producer rights"
    }
    doc_ref.set(document_data_to_set)
    print(f"Set document {doc_ref.path} with data: {document_data_to_set}")

    document_path = doc_ref.path

    # 3. Get a NEW ID token that includes the recently set claims
    try:
        new_id_token = cloud_function_tester.get_id_token(test_user.uid)
        print(f"Decoded new ID token: {auth.verify_id_token(new_id_token)}")
        print(f"Obtained new ID token for user {test_user.uid} after setting producer claims.")
    except Exception as e:
        pytest.fail(f"Failed to get new ID token for user {test_user.uid}: {str(e)}")

    # 4. Update the token in the FirestoreRestClientHelper instance
    firestore_rest_client_helper.set_id_token(new_id_token)

    # 5. Attempt to read the document using the helper with the updated token
    print(f"Attempting to read document '{document_path}' with user having 'admin' rights for producer '{test_producer}'.")
    success, result = firestore_rest_client_helper.get_document(document_path)

    # 6. Assertions
    assert success is True, f"Should be able to read document with proper producer_access_rights. Error: {result.get('error', result)}"
    
    assert "fields" in result, "REST API response should contain 'fields'."
    retrieved_fields = result.get("fields", {})
    
    expected_amount_str = str(document_data_to_set["amount"])
    
    assert retrieved_fields.get("producerId", {}).get("stringValue") == test_producer, \
        f"Document producerId mismatch. Expected: {test_producer}, Got: {retrieved_fields.get('producerId')}"
    assert retrieved_fields.get("amount", {}).get("integerValue") == expected_amount_str, \
        f"Document amount mismatch. Expected: {expected_amount_str}, Got: {retrieved_fields.get('amount')}"
    assert retrieved_fields.get("description", {}).get("stringValue") == document_data_to_set["description"]

    print(f"Successfully read document {document_path} and verified content for producer rights.")

    # 7. Cleanup
    try:
        doc_ref.delete()
        print(f"Deleted test document: {document_path}")
        auth.set_custom_user_claims(test_user.uid, None) # Clear claims for the user
        print(f"Cleared custom claims for user {test_user.uid}")
    except Exception as e:
        print(f"Error during cleanup: {e}")

def test_sales_gold_write_access_levels(
    db, # Keep db for potential admin checks or direct data verification if needed, though not for rule-tested ops
    test_user,
    test_store,
    firestore_rest_client_helper, # Your fixture for the helper
    cloud_function_tester # To get new tokens
):
    """Test different access levels for sales-gold write operations using REST API."""
    
    # Document path for this test
    doc_id = f"test-write-levels-{test_store}"
    document_path = f"sales-gold/{doc_id}"

    # --- Test with 'viewer' access (should fail write) ---
    print("\n--- Testing 'viewer' access for write ---")
    auth.set_custom_user_claims(test_user.uid, {'store_access_rights': {test_store: 'viewer'}})
    new_id_token_viewer = cloud_function_tester.get_id_token(test_user.uid)
    firestore_rest_client_helper.set_id_token(new_id_token_viewer)
    
    data_to_set_viewer = {'storeId': test_store, 'amount': 100, 'testRun': 'viewer'}
    success_viewer_write, result_viewer_write = firestore_rest_client_helper.create_or_update_document(
        document_path, data_to_set_viewer
    )
    
    assert success_viewer_write is False, "Viewer should not be able to write."
    assert result_viewer_write.get("status_code") == 403, \
        f"Viewer write expected 403, got {result_viewer_write.get('status_code')}. Details: {result_viewer_write}"
    print("Viewer correctly denied write access.")

    # --- Test with 'editor' access (should succeed write) ---
    print("\n--- Testing 'editor' access for write ---")
    auth.set_custom_user_claims(test_user.uid, {'store_access_rights': {test_store: 'editor'}})
    new_id_token_editor = cloud_function_tester.get_id_token(test_user.uid)
    firestore_rest_client_helper.set_id_token(new_id_token_editor)

    data_to_set_editor = {'storeId': test_store, 'amount': 150, 'testRun': 'editor'}
    success_editor_write, result_editor_write = firestore_rest_client_helper.create_or_update_document(
        document_path, data_to_set_editor
    )
    assert success_editor_write is True, \
        f"Editor should be able to write. Error: {result_editor_write.get('error', result_editor_write)}"
    
    # Verify by reading back (still as editor)
    success_editor_read, doc_editor_read = firestore_rest_client_helper.get_document(document_path)
    assert success_editor_read is True, "Editor should be able to read after write."
    assert doc_editor_read.get("fields", {}).get("amount", {}).get("integerValue") == str(data_to_set_editor["amount"])
    assert doc_editor_read.get("fields", {}).get("testRun", {}).get("stringValue") == "editor"
    print(f"Editor successfully wrote and read document. Amount: {data_to_set_editor['amount']}")

    # --- Test with 'admin' access (should succeed update/write) ---
    print("\n--- Testing 'admin' access for write/update ---")
    auth.set_custom_user_claims(test_user.uid, {'store_access_rights': {test_store: 'admin'}})
    new_id_token_admin = cloud_function_tester.get_id_token(test_user.uid)
    firestore_rest_client_helper.set_id_token(new_id_token_admin)

    data_to_set_admin = {'storeId': test_store, 'amount': 200, 'testRun': 'admin'} # Update amount
    success_admin_write, result_admin_write = firestore_rest_client_helper.create_or_update_document(
        document_path, data_to_set_admin
    )
    assert success_admin_write is True, \
        f"Admin should be able to write/update. Error: {result_admin_write.get('error', result_admin_write)}"

    # Verify by reading back (still as admin)
    success_admin_read, doc_admin_read = firestore_rest_client_helper.get_document(document_path)
    assert success_admin_read is True, "Admin should be able to read after write."
    assert doc_admin_read.get("fields", {}).get("amount", {}).get("integerValue") == str(data_to_set_admin["amount"])
    assert doc_admin_read.get("fields", {}).get("testRun", {}).get("stringValue") == "admin"
    print(f"Admin successfully wrote/updated and read document. Amount: {data_to_set_admin['amount']}")
    
    # --- Cleanup ---
    # Delete the document using admin rights (or the last successful authed client)
    # For robust cleanup, using the admin 'db' client is safest if the doc might not have been created by 'admin'
    admin_doc_ref = db.collection("sales-gold").document(doc_id)
    if admin_doc_ref.get().exists: # Check if it exists before deleting
        admin_doc_ref.delete()
        print(f"Cleaned up document: {document_path}")
    else:
        print(f"Document {document_path} not found for cleanup (might have failed creation).")

    auth.set_custom_user_claims(test_user.uid, None) # Clear claims
    print(f"Cleared custom claims for user {test_user.uid}")

def test_sales_gold_delete_access(
    db, # Admin client for initial document setup if needed and final verification
    test_user,
    test_store,
    firestore_rest_client_helper, # Your fixture for the helper
    cloud_function_tester # To get new tokens
):
    """Test that only users with 'admin' rights can delete sales-gold documents via REST API."""

    # Document path for this test
    doc_id = f"test-delete-access-{test_store}"
    document_path = f"sales-gold/{doc_id}"
    admin_doc_ref = db.collection("sales-gold").document(doc_id) # For admin operations

    # --- Setup: Create a document to be deleted ---
    # For setup, we can use the admin client (db) or an appropriately permissioned REST client.
    # Using admin client for setup simplicity to ensure the document exists before delete attempts.
    initial_data = {'storeId': test_store, 'amount': 100, 'status': 'to_be_deleted'}
    admin_doc_ref.set(initial_data)
    assert admin_doc_ref.get().exists, "Test document should exist before delete tests."
    print(f"\n--- Test Setup: Created document {document_path} ---")

    # --- Test with 'editor' access (should fail delete) ---
    print("\n--- Testing 'editor' access for delete ---")
    auth.set_custom_user_claims(test_user.uid, {'store_access_rights': {test_store: 'editor'}})
    new_id_token_editor = cloud_function_tester.get_id_token(test_user.uid)
    firestore_rest_client_helper.set_id_token(new_id_token_editor)
    
    success_editor_delete, result_editor_delete = firestore_rest_client_helper.delete_document(document_path)
    
    assert success_editor_delete is False, "Editor should not be able to delete."
    assert result_editor_delete.get("status_code") == 403, \
        f"Editor delete expected 403, got {result_editor_delete.get('status_code')}. Details: {result_editor_delete}"
    
    # Verify document still exists using admin client
    assert admin_doc_ref.get().exists, "Document should still exist after failed delete attempt by editor."
    print("Editor correctly denied delete access.")

    # --- Test with 'admin' access (should succeed delete) ---
    print("\n--- Testing 'admin' access for delete ---")
    auth.set_custom_user_claims(test_user.uid, {'store_access_rights': {test_store: 'admin'}})
    new_id_token_admin = cloud_function_tester.get_id_token(test_user.uid)
    firestore_rest_client_helper.set_id_token(new_id_token_admin)

    success_admin_delete, result_admin_delete = firestore_rest_client_helper.delete_document(document_path)
    
    assert success_admin_delete is True, \
        f"Admin should be able to delete. Error: {result_admin_delete.get('error', result_admin_delete)}"
    # A successful DELETE via REST API usually returns an empty JSON object {}
    assert isinstance(result_admin_delete, dict) and not result_admin_delete, \
        f"Expected empty dict on successful delete, got: {result_admin_delete}"

    # Verify document no longer exists using admin client
    assert not admin_doc_ref.get().exists, "Document should NOT exist after successful delete by admin."
    print(f"Admin successfully deleted document: {document_path}")
    
    # --- Cleanup ---
    # Document is already deleted if admin test passed.
    # Just clear claims.
    auth.set_custom_user_claims(test_user.uid, None) # Clear claims
    print(f"Cleared custom claims for user {test_user.uid}")

def test_user_root_account_access(
    db, # Admin client for setup
    test_user, # The authenticated user for the test
    firestore_rest_client, # Fixture for REST client
    cloud_function_tester # To get ID tokens (though for this test, the initial token in helper might be enough)
):
    """
    Test that users can only read their own userRootAccount via REST API,
    and cannot read other users' accounts.
    """
    
    # --- Setup: Ensure test_user's userRootAccount document exists ---
    # We'll use the admin client 'db' to create it, as client-side creation is disallowed by rules.
    own_account_path = f"userRootAccounts/{test_user.uid}"
    own_account_admin_ref = db.collection("userRootAccounts").document(test_user.uid)
    own_account_data = {"displayName": test_user.display_name, "email": test_user.email, "someData": "my_data"}
    own_account_admin_ref.set(own_account_data)
    assert own_account_admin_ref.get().exists, f"Own userRootAccount for {test_user.uid} should exist for the test."
    print(f"\n--- Test Setup: Ensured {own_account_path} exists ---")

    # The firestore_rest_client_helper is already initialized with test_user's ID token.
    # No need to set claims or refresh token unless the user context changes mid-test for this rule.
    
    # --- 1. Try to read own userRootAccount (should succeed) ---
    print(f"\n--- Testing read access to own userRootAccount: {own_account_path} ---")
    success_own_read, result_own_read = firestore_rest_client.get_document(own_account_path)
    
    assert success_own_read is True, \
        f"Should be able to read own userRootAccount. Error: {result_own_read.get('error', result_own_read)}"
    assert "fields" in result_own_read, "Own account data should contain 'fields'."
    assert result_own_read["fields"].get("email", {}).get("stringValue") == test_user.email, \
        "Email in own account data mismatch."
    print(f"Successfully read own userRootAccount for UID: {test_user.uid}")

    # --- 2. Try to read another user's userRootAccount (should fail) ---
    other_user_uid = "some-other-uid-that-does-not-exist-or-is-not-test-user"
    other_account_path = f"userRootAccounts/{other_user_uid}"
    
    # For this test, it doesn't strictly matter if 'some-other-uid' document exists or not,
    # as the rule `request.auth.uid == userId` should fail first if the UIDs don't match.
    # If it doesn't exist, we'd get a 404 if rules allowed, but rules should give 403 first.
    # Let's ensure it exists using admin to make the test more robust about permissions.
    other_account_admin_ref = db.collection("userRootAccounts").document(other_user_uid)
    other_account_admin_ref.set({"displayName": "Other User", "email": "<EMAIL>"})
    print(f"--- Test Setup: Ensured other user account {other_account_path} exists for permission test ---")


    print(f"\n--- Testing read access to another user's account: {other_account_path} (expecting failure) ---")
    success_other_read, result_other_read = firestore_rest_client.get_document(other_account_path)
    
    assert success_other_read is False, \
        "Should NOT be able to read another user's userRootAccount."
    assert result_other_read.get("status_code") == 403, \
        f"Expected 403 (Forbidden) when reading other user's account, got {result_other_read.get('status_code')}. Details: {result_other_read}"
    print(f"Correctly denied read access to another user's account (UID: {other_user_uid}).")

    # --- Cleanup ---
    own_account_admin_ref.delete()
    other_account_admin_ref.delete()
    print(f"Cleaned up test userRootAccount documents.")
