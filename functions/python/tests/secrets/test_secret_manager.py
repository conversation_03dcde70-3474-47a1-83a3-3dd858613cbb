import pytest
from datetime import datetime, timezone
from gaco_secrets.secret_manager import Secret<PERSON><PERSON><PERSON>
from models.secret import Secret<PERSON><PERSON><PERSON>

@pytest.fixture
def secret_manager():
    project_id = "gast-art-platform-87104"
    return SecretManager(project_id=project_id)

@pytest.fixture
def test_secret_data():
    return {
        "secret_id": f"test-secret-{int(datetime.now(timezone.utc).timestamp())}",
        "secret_value": "test-secret-value"
    }

def test_create_and_get_secret(secret_manager, test_secret_data):
    # Create a new secret
    secret_version_path = secret_manager.create_secret(
        test_secret_data["secret_id"],
        test_secret_data["secret_value"],
        labels=SecretLabels(platform="shopify", shop_name="test-shop")
    )
    
    # Verify the secret was created and can be retrieved
    retrieved_value = secret_manager.get_secret(secret_version_path)
    assert test_secret_data["secret_value"] == retrieved_value

def test_create_existing_secret(secret_manager, test_secret_data):
    # Create initial secret
    first_version_path = secret_manager.create_secret(
        test_secret_data["secret_id"],
        test_secret_data["secret_value"],
        labels=SecretLabels(platform="shopify", shop_name="test-shop")
    )
    
    # Create new version of the same secret
    new_secret_value = "updated-secret-value"
    second_version_path = secret_manager.create_secret(
        test_secret_data["secret_id"],
        new_secret_value,
        labels=SecretLabels(platform="shopify", shop_name="test-shop")
    )
    
    # Verify both versions exist and have correct values
    assert first_version_path != second_version_path
    assert test_secret_data["secret_value"] == secret_manager.get_secret(first_version_path)
    assert new_secret_value == secret_manager.get_secret(second_version_path)

@pytest.fixture(autouse=True)
def cleanup(secret_manager, test_secret_data):
    yield
    # Clean up after each test
    try:
        secret_manager.delete_secret(test_secret_data["secret_id"])
    except Exception as e:
        print(f"Error cleaning up test secret: {str(e)}")
