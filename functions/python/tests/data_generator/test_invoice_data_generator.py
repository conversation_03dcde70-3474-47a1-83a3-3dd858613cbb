import pytest
import polars as pl
from datetime import datetime
from models.requests.invoice_request import CreateInvoiceRequest
from models.sales import SalesSilver, SalesGold
from data_generator.invoice_data_generator import generate_invoice_data_handler
from data_generator.single_invoice_data_generator import generate_single_invoice_data_handler
from flows.force_active_partnership import ForceActivePartnershipFlow
from models.requests.sanitization_request import ForceAgreementRequest
from models.requests.invoice_request import CreateInvoiceBySaleIdRequest
from datetime import datetime, timezone, timedelta
from firebase_functions import logger
from models.sales import SalesStaging
import random
from services.user_root_account_manager import UserRootAccountManager
from sanitizer.sales_sanitizer_factory import SalesSanitizerFactory
from time import sleep
from columns.sales_columns import SalesColumns, SalesInvoiceColumns
from data_generator.report_data_generator import ReportDataGenerator 
from data_source.sales_data_source import SalesDataSource
from calculators.pipelines.invoice_pipeline import create_invoice_pipeline
from data_generator.helper_invoice_data_gold_converter import HelperInvoiceDataGoldConverter


@pytest.fixture
def test_query_data(
    test_store_NL, test_start_date, test_end_date, 
    test_force_create_active_partnership, n_sales_stagin_to_gold
    ):
    """n_sales_stagin_to_gold is not used here, but it is used in the test_invoice_generator"""
    return CreateInvoiceRequest(
        store_id=test_store_NL,
        producer_id=test_force_create_active_partnership["producer_id"],
        start_date=test_start_date,
        end_date=test_end_date
    )


@pytest.fixture
def test_query_no_producer_id(
    test_store_NL, test_start_date, test_end_date, 
    test_force_create_active_partnership, n_sales_stagin_to_gold
    ):
    """n_sales_stagin_to_gold is not used here, but it is used in the test_invoice_generator"""
    return CreateInvoiceRequest(
        store_id=test_store_NL,
        start_date=test_start_date,
        end_date=test_end_date
    )


@pytest.fixture
def query_criteria_with_producer(test_store_NL, test_start_date, test_end_date, test_force_create_active_partnership):
    """Provides criteria dictionary including producer_id"""
    return CreateInvoiceRequest(
        store_id=test_store_NL,
        producer_id=test_force_create_active_partnership["producer_id"],
        start_date=test_start_date,
        end_date=test_end_date
    )

@pytest.fixture
def query_criteria_no_producer(test_store_NL, test_start_date, test_end_date):
    """Provides criteria dictionary without producer_id"""
    return CreateInvoiceRequest(
        store_id=test_store_NL,
        start_date=test_start_date,
        end_date=test_end_date
    )

@pytest.mark.parametrize(
    "query_criteria_fixture_name", # Parametrize only with the *name* of the fixture
    [
        "query_criteria_with_producer",
        "query_criteria_no_producer",
    ]
)
def test_invoice_generation_parametrized(
    db, 
    query_criteria_fixture_name, # Argument to receive the fixture name string
    request,                     # Inject the special 'request' fixture from pytest
    n_sales_stagin_to_gold   # Ensure data exists before running
    ):
    """
    Tests the invoice generation handler with different query criteria,
    using request.getfixturevalue instead of lazy_fixture.
    """
    # --- Retrieve the actual fixture value using its name ---
    query_criteria = request.getfixturevalue(query_criteria_fixture_name)
    # --- Now 'query_criteria' holds the dictionary from the correct fixture ---

    logger.info(f"Running invoice generation test for case: {query_criteria_fixture_name}")
    logger.info(f"Using query criteria: {query_criteria}")

    df = generate_invoice_data_handler(db, query_criteria)

    # --- Assertions ---
    # Define the expected DataFrame structure and data 
    expected_data = [
        # Note: Update these values to the actual expected results of your calculations
        {'sale_id': 'test-0', 'title': 'title_0', 'variant_title': 'variant_title_0', 'variant_display_name': 'variant_display_name_0', 'store_total_gross_payout': 100.0, 'producer_gross_payout': 0.0},
        {'sale_id': 'test-1', 'title': 'title_1', 'variant_title': 'variant_title_1', 'variant_display_name': 'variant_display_name_1', 'store_total_gross_payout': 100.0, 'producer_gross_payout': 0.0},
        {'sale_id': 'test-2', 'title': 'title_2', 'variant_title': 'variant_title_2', 'variant_display_name': 'variant_display_name_2', 'store_total_gross_payout': 100.0, 'producer_gross_payout': 0.0},
        {'sale_id': 'test-3', 'title': 'title_3', 'variant_title': 'variant_title_3', 'variant_display_name': 'variant_display_name_3', 'store_total_gross_payout': 100.0, 'producer_gross_payout': 0.0},
        {'sale_id': 'test-4', 'title': 'title_4', 'variant_title': 'variant_title_4', 'variant_display_name': 'variant_display_name_4', 'store_total_gross_payout': 100.0, 'producer_gross_payout': 0.0},
    ]

    # Define the expected schema using constants and Polars types
    expected_schema = {
        SalesColumns.SALE_ID.value: pl.Utf8,
        SalesColumns.TITLE.value: pl.Utf8,
        SalesColumns.VARIANT_TITLE.value: pl.Utf8,
        SalesColumns.VARIANT_DISPLAY_NAME.value: pl.Utf8,
        SalesInvoiceColumns.STORE_TOTAL_GROSS_PAYOUT.value: pl.Float64,
        SalesInvoiceColumns.PRODUCER_GROSS_PAYOUT.value: pl.Float64,
    }

    # Create the expected DataFrame
    expected_df = pl.DataFrame(data=expected_data, schema=expected_schema)

    # --- Comparison ---
    actual_df_for_comparison = df.select(expected_df.columns) 
    sort_column = SalesColumns.SALE_ID.value 
    actual_df_sorted = actual_df_for_comparison.sort(sort_column)
    expected_df_sorted = expected_df.sort(sort_column)

    assert actual_df_sorted.equals(expected_df_sorted), \
        f"Sorted DataFrames do not match for case '{query_criteria_fixture_name}'.\nExpected (sorted):\n{expected_df_sorted}\nActual (selected cols, sorted):\n{actual_df_sorted}"


def test_invoice_generation_by_sale_id(db, n_sales_stagin_to_gold):
    """
    Tests the invoice generation handler for a single sale ID request.
    """
    # Arrange: Pick a specific sale ID from the created data
    # Using n_sales_stagin_to_gold ensures the document exists in sales-silver
    target_sale_id = "test-2" # Example: Choose one of the IDs created by the fixture
    assert target_sale_id in n_sales_stagin_to_gold # Sanity check

    logger.info(f"Running single sale invoice generation test for sale_id: {target_sale_id}")

    # Create the request object for a single sale
    single_sale_request = CreateInvoiceBySaleIdRequest(sale_id=target_sale_id)

    # Act: Call the handler function
    result_df = generate_invoice_data_handler(db, single_sale_request)

    # --- Assertions ---
    # Check that the result is a DataFrame and has exactly one row
    assert isinstance(result_df, pl.DataFrame)
    assert len(result_df) == 1, f"Expected 1 row for single sale request, got {len(result_df)}"
    
    # Check if the sale_id in the result matches the requested one
    # Ensure the column name constant is correct
    assert result_df[SalesColumns.SALE_ID.value][0] == target_sale_id 

    # Define the expected DataFrame structure and data for ONLY test-2
    # Recalculate expected values for test-2 based on its input in the fixture
    # Input for test-2 (example): subtotal=100, commission=100, store_tax_a2='NL' -> assigned_vat_rate=21.0
    # vat_excluded_sale = 100 * (1 - 1/1.21) = 17.355... -> 17.36
    # net_sales = 100 - 17.36 = 82.64
    # store_net_payout = 82.64 * 100 / 100 = 82.64
    # vat_on_sales_service = 82.64 * 21 / 100 = 17.3544 -> 17.35
    # store_total_gross_payout = 82.64 + 17.35 = 99.99 
    # producer_gross_payout = 100 - 99.99 = 0.01 
    # NOTE: The fixture data might be different (e.g., subtotal=100, commission=100). 
    # Double-check the values in n_sales_stagin_to_gold for test-2
    # Assuming test-2 fixture data leads to the following results (ADJUST IF NEEDED):
    expected_data = [
        {
            SalesColumns.SALE_ID.value: 'test-2', 
            SalesColumns.TITLE.value: 'title_2', 
            SalesColumns.VARIANT_TITLE.value: 'variant_title_2', 
            SalesColumns.VARIANT_DISPLAY_NAME.value: 'variant_display_name_2', 
            SalesColumns.SUBTOTAL.value: 100.0, # Include input columns needed for verification
            SalesColumns.COMMISSION.value: 100.0,
            SalesColumns.STORE_TAX_A2.value: 'NL',
            SalesColumns.PRODUCER_TAX_A2.value: 'NL',
            SalesInvoiceColumns.ASSIGNED_VAT_RATE.value: 0.0,
            SalesInvoiceColumns.VAT_EXCLUDED_SALE.value: 0.0,
            SalesInvoiceColumns.NET_SALES.value: 100.0,
            SalesInvoiceColumns.STORE_NET_PAYOUT.value: 100.0, 
            SalesInvoiceColumns.VAT_ON_SALES_SERVICE.value: 0.0,
            SalesInvoiceColumns.STORE_TOTAL_GROSS_PAYOUT.value: 100.00, # Adjusted for rounding (99.99 -> 100.00)
            SalesInvoiceColumns.PRODUCER_GROSS_PAYOUT.value: 0.00, # Adjusted for rounding (0.01 -> 0.00)
        }
    ]

    # Define the schema for the expected output, including necessary input columns for context
    # Ensure all these constants exist in SalesColumns or SalesInvoiceColumns
    expected_schema = {
        SalesColumns.SALE_ID.value: pl.Utf8,
        SalesColumns.TITLE.value: pl.Utf8,
        SalesColumns.VARIANT_TITLE.value: pl.Utf8,
        SalesColumns.VARIANT_DISPLAY_NAME.value: pl.Utf8,
        SalesColumns.SUBTOTAL.value: pl.Float64,
        SalesColumns.COMMISSION.value: pl.Float64,
        SalesColumns.STORE_TAX_A2.value: pl.Utf8,
        SalesColumns.PRODUCER_TAX_A2.value: pl.Utf8,
        SalesInvoiceColumns.ASSIGNED_VAT_RATE.value: pl.Float64,
        SalesInvoiceColumns.VAT_EXCLUDED_SALE.value: pl.Float64,
        SalesInvoiceColumns.NET_SALES.value: pl.Float64,
        SalesInvoiceColumns.STORE_NET_PAYOUT.value: pl.Float64, 
        SalesInvoiceColumns.VAT_ON_SALES_SERVICE.value: pl.Float64,
        SalesInvoiceColumns.STORE_TOTAL_GROSS_PAYOUT.value: pl.Float64,
        SalesInvoiceColumns.PRODUCER_GROSS_PAYOUT.value: pl.Float64,
    }

    # Create the expected DataFrame
    expected_df = pl.DataFrame(data=expected_data, schema=expected_schema)


    assert len(result_df.to_dicts()) == 1

    sales_silver = db.collection("sales-silver").document(target_sale_id).get().to_dict()
    sales_silver = SalesSilver.model_validate(sales_silver)
    sales_gold = HelperInvoiceDataGoldConverter.convert_invoice_data_gold(result_df, sales_silver)

    assert sales_gold.store_gross_payout == 100.0
    assert sales_gold.producer_gross_payout == 0.0
    
    # test with the helper function

    sales_gold = generate_single_invoice_data_handler(db, single_sale_request, sales_silver)


    # --- Comparison ---
    # Select only the columns present in expected_df for comparison
    # Make sure the result_df actually contains all these columns after calculation
    actual_df_for_comparison = result_df.select(expected_df.columns) 

    # Round float columns before comparison (sorting isn't needed for 1 row)
    precision = 2 
    result_rounded = actual_df_for_comparison.with_columns(
        pl.col(pl.Float64).round(precision) 
    )
    expected_rounded = expected_df.with_columns(
        pl.col(pl.Float64).round(precision)
    )
    
    # Assert DataFrames are equal
    assert result_rounded.equals(expected_rounded), \
        f"Single sale DataFrame does not match.\nExpected (rounded to {precision}):\n{expected_rounded}\nActual (rounded to {precision}):\n{result_rounded}"
