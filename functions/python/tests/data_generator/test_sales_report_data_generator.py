import pytest
import polars as pl
from queries.sales_silver_query_builder import SalesSilverQueryBuilder
from models.requests.sales_report_request import CreateSalesReportRequest
from data_generator.sales_report_data_generator import generate_sales_report_data_handler, generate_sales_report_from_sales_silver_handler
from columns.sales_columns import SalesColumns, SalesCommonComputedColumns
from models.sanitized_sales import sales_silver_collection
from firebase_admin import firestore
from models.sales import SalesSilver, SalesGold
from data_source.helper_doc_to_df import HelperDocToDf
from calculators.pipelines.sales_report_pipeline import create_sales_report_pipeline

@pytest.fixture
def test_sales_report_request(
    test_store_NL, test_start_date, test_end_date, 
    test_force_create_active_partnership, n_sales_stagin_to_gold
):
    return CreateSalesReportRequest(
        store_id=test_store_NL,
        producer_id=test_force_create_active_partnership["producer_id"],
        start_date=test_start_date,
        end_date=test_end_date
    )


@pytest.fixture
def test_sales_report_request_no_producer_id(
    test_store_NL, test_start_date, test_end_date, 
    test_force_create_active_partnership, n_sales_stagin_to_gold
):
    return CreateSalesReportRequest(
        store_id=test_store_NL,
        start_date=test_start_date,
        end_date=test_end_date
    )


@pytest.mark.parametrize(
    "test_sales_report_request_name", 
    [
        "test_sales_report_request",
        "test_sales_report_request_no_producer_id"
    ]
)
def test_sales_report_data_generator(db, test_sales_report_request_name, request):

    test_sales_report_request = request.getfixturevalue(test_sales_report_request_name)

    sales_report_data = generate_sales_report_data_handler(db, test_sales_report_request)


    # --- Assertions ---
    # Define the expected DataFrame structure and data 
    expected_data = [
        # Note: Update these values to the actual expected results of your calculations
        {'sale_id': 'test-0', 'title': 'title_0', 'variant_title': 'variant_title_0', 'variant_display_name': 'variant_display_name_0', 'store_gross_payout': 100.0, 'producer_gross_payout': 0.0},
        {'sale_id': 'test-1', 'title': 'title_1', 'variant_title': 'variant_title_1', 'variant_display_name': 'variant_display_name_1', 'store_gross_payout': 100.0, 'producer_gross_payout': 0.0},
        {'sale_id': 'test-2', 'title': 'title_2', 'variant_title': 'variant_title_2', 'variant_display_name': 'variant_display_name_2', 'store_gross_payout': 100.0, 'producer_gross_payout': 0.0},
        {'sale_id': 'test-3', 'title': 'title_3', 'variant_title': 'variant_title_3', 'variant_display_name': 'variant_display_name_3', 'store_gross_payout': 100.0, 'producer_gross_payout': 0.0},
        {'sale_id': 'test-4', 'title': 'title_4', 'variant_title': 'variant_title_4', 'variant_display_name': 'variant_display_name_4', 'store_gross_payout': 100.0, 'producer_gross_payout': 0.0},
    ]

    # Define the expected schema using constants and Polars types
    expected_schema = {
        SalesColumns.SALE_ID.value: pl.Utf8,
        SalesColumns.TITLE.value: pl.Utf8,
        SalesColumns.VARIANT_TITLE.value: pl.Utf8,
        SalesColumns.VARIANT_DISPLAY_NAME.value: pl.Utf8,
        SalesCommonComputedColumns.STORE_GROSS_PAYOUT.value: pl.Float64,
        SalesCommonComputedColumns.PRODUCER_GROSS_PAYOUT.value: pl.Float64,
    }

    # Create the expected DataFrame
    expected_df = pl.DataFrame(data=expected_data, schema=expected_schema)

    # --- Comparison ---
    actual_df_for_comparison = sales_report_data.select(expected_df.columns) 
    sort_column = SalesColumns.SALE_ID.value 
    actual_df_sorted = actual_df_for_comparison.sort(sort_column)
    expected_df_sorted = expected_df.sort(sort_column)

    assert actual_df_sorted.equals(expected_df_sorted), \
        f"Sorted DataFrames do not match for case '{test_sales_report_request}'.\nExpected (sorted):\n{expected_df_sorted}\nActual (selected cols, sorted):\n{actual_df_sorted}"



    assert sales_report_data is not None
    assert len(sales_report_data) > 0


def test_generate_sales_report_from_sales_silver_handler(db, test_sales_report_request):
    sales_silver_query_builder = SalesSilverQueryBuilder(db)

    sales_silver_query = sales_silver_query_builder\
        .for_store_id(test_sales_report_request.store_id) \
        .between_dates(
            test_sales_report_request.start_date,
            test_sales_report_request.end_date
        ) \
        .build()

    for doc in sales_silver_query.stream():

        sales_silver_doc = SalesSilver.model_validate(doc.to_dict())
        sales_gold = generate_sales_report_from_sales_silver_handler(sales_silver_doc, doc.id)

        assert sales_gold is not None
        assert isinstance(sales_gold, SalesGold)
        assert sales_gold.store_gross_payout == 100.0
        assert sales_gold.producer_gross_payout == 0.0
