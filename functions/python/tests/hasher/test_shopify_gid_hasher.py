import pytest
from python.hasher.shopify_gid import shopify_gid_to_hash

def test_shopify_gid_to_hash_basic():
    """Test basic GID hashing functionality"""
    test_gid = "gid://shopify/LineItem/15952700801355"
    result = shopify_gid_to_hash(test_gid)
    
    # Check that result is a string
    assert isinstance(result, str)
    # Check length is exactly 30 characters
    assert len(result) == 30
    # Check that only valid base62 characters are used
    valid_chars = set("0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ")
    assert all(c in valid_chars for c in result)

def test_shopify_gid_to_hash_consistency():
    """Test that the same input always produces the same hash"""
    test_gid = "gid://shopify/LineItem/15952700801355"
    result1 = shopify_gid_to_hash(test_gid)
    result2 = shopify_gid_to_hash(test_gid)
    
    assert result1 == result2

def test_shopify_gid_to_hash_different_inputs():
    """Test that different inputs produce different hashes"""
    gid1 = "gid://shopify/LineItem/15952700801355"
    gid2 = "gid://shopify/LineItem/15952700801356"
    
    result1 = shopify_gid_to_hash(gid1)
    result2 = shopify_gid_to_hash(gid2)
    
    assert result1 != result2

def test_shopify_gid_to_hash_with_custom_salt():
    """Test that different salts produce different hashes for the same input"""
    test_gid = "gid://shopify/LineItem/15952700801355"
    result1 = shopify_gid_to_hash(test_gid, salt="salt1")
    result2 = shopify_gid_to_hash(test_gid, salt="salt2")
    
    assert result1 != result2

def test_shopify_gid_to_hash_empty_string():
    """Test handling of empty string input"""
    result = shopify_gid_to_hash("")
    
    assert isinstance(result, str)
    assert len(result) == 30
    
def test_shopify_gid_to_hash_special_characters():
    """Test handling of GIDs with special characters"""
    test_gid = "gid://shopify/Product/Test!@#$%^&*()"
    result = shopify_gid_to_hash(test_gid)
    
    assert isinstance(result, str)
    assert len(result) == 30
    valid_chars = set("0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ")
    assert all(c in valid_chars for c in result)

def test_shopify_gid_to_hash_different_types():
    """Test that different types of GIDs produce unique hashes"""
    gids = [
        "gid://shopify/Order/123456789",
        "gid://shopify/Product/123456789",
        "gid://shopify/LineItem/123456789",
        "gid://shopify/Variant/123456789"
    ]
    
    # Convert all GIDs to hashes
    hashes = [shopify_gid_to_hash(gid) for gid in gids]
    
    # Check that all hashes are unique
    assert len(set(hashes)) == len(hashes)

def test_shopify_gid_to_hash_non_string_input():
    """Test handling of non-string input"""
    with pytest.raises(AttributeError):
        shopify_gid_to_hash(123456789) 