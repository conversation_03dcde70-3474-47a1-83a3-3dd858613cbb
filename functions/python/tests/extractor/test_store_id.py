import pytest
from python.extractor.store_id import extract_store_id

def test_extract_store_id_from_valid_path():
    """Test extracting store ID from a valid file path"""
    test_path = "stores/test-store-123/orders/2024/03/20/shopify_raw_orders.json"
    result = extract_store_id(test_path)
    assert result == "test-store-123"

def test_extract_store_id_from_path_with_numbers():
    """Test extracting store ID that contains numbers"""
    test_path = "stores/store-42/orders/2024/03/20/shopify_raw_orders.json"
    result = extract_store_id(test_path)
    assert result == "store-42"

def test_extract_store_id_from_path_with_special_chars():
    """Test extracting store ID that contains special characters"""
    test_path = "stores/test-store_123-dev/orders/2024/03/20/shopify_raw_orders.json"
    result = extract_store_id(test_path)
    assert result == "test-store_123-dev"

def test_extract_store_id_with_multiple_stores_in_path():
    """Test that only the correct store ID segment is extracted"""
    test_path = "stores/real-store/backup/old-store/orders/data.json"
    result = extract_store_id(test_path)
    assert result == "real-store"


def test_extract_store_id_from_shopify_products_path():
    """Test extracting store ID from a Shopify products file path"""
    test_path = "stores/test-store-123/products/2024/03/20/shopify_products_test-store-123_20240320_123456/page_1.json"
    result = extract_store_id(test_path)
    assert result == "test-store-123"

