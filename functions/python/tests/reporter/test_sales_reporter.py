from reporter.sales_reporter import SalesReporter
from models.requests.sales_report_request import CreateSalesReportRequest
from data_generator.sales_report_data_generator import generate_sales_report_data_handler
from models.sales_report import sales_report_collection
import pytest
import polars as pl
import json
import datetime
from unittest.mock import patch
from firebase_admin import auth # For setting claims and generating tokens
from google.cloud.exceptions import Forbidden # To catch permission errors


@pytest.fixture
def test_sales_report_request(
    test_store_NL, test_start_date, test_end_date, 
    test_force_create_active_partnership, n_sales_stagin_to_gold
):
    return CreateSalesReportRequest(
        store_id=test_store_NL,
        producer_id=test_force_create_active_partnership["producer_id"],
        start_date=test_start_date,
        end_date=test_end_date
    )


def test_sales_reporter(db, test_sales_report_request):
    sales_report_data = generate_sales_report_data_handler(db, test_sales_report_request)

    reporter = SalesReporter(
        db=db,
        sales_report_data_frame=sales_report_data,
        template_file_name="sales-report-template.html", # Ensure this exists locally or in template_dir
        # If you have a specific template for testing:
        template_dir=".", # Or wherever test templates are
        use_cloud_storage=False,
        # Specify local paths if different from defaults
        local_output_dir='./test_output',
        local_default_template_dir='./templates/sales_report'
    )
    report = reporter.generate_report()

    assert report[0]['sales_report_id'] == '2025-1'


def test_sales_reporter_with_cloud_storage(db, test_sales_report_request):
    sales_report_data = generate_sales_report_data_handler(db, test_sales_report_request)

    reporter = SalesReporter(
        db=db,
        sales_report_data_frame=sales_report_data,
        template_file_name="sales-report-template.html", # Ensure this exists locally or in template_dir
    )

    report = reporter.generate_report()

    sales_report_doc_id = f"{report[0]['store_id']}-{report[0]['sales_report_id']}"
    sales_report_doc = db.collection(sales_report_collection)\
        .document(sales_report_doc_id).get()

    sales_report_data = sales_report_doc.to_dict()

    assert report[0]['sales_report_id'] == '2025-1'


def test_sales_reporter_with_cloud_storage_security_rules(
    db, 
    test_sales_report_request, 
    test_user, # Your fixture for a test Firebase user
    storage_rest_client_helper, # Our new fixture
    firestore_rest_client, # For refreshing token if needed, or use a dedicated refresh function
    cloud_function_tester
):
    # --- Arrange ---
    # 1. Generate sales report data and upload to GCS (using admin privileges, which is fine for upload)
    #    The SalesReporter will run with its usual (likely admin) privileges to create the file.
    
    # Ensure the storeId from the report request will match claims
    store_id_for_report = test_sales_report_request.store_id 
    
    # We might need to mock parts of SalesReporter if it interacts with external services
    # or if we want to control the exact bucket/path for easier testing.
    # For now, let's assume it works as is and we get a gcs_path.

    reporter = SalesReporter(
        db=db,
        sales_report_data_frame=generate_sales_report_data_handler(db, test_sales_report_request), # Assuming this helper exists
        template_file_name="sales-report-template.html", 
    )
    
    # This part runs as admin/backend to generate and upload the report
    generated_report_info_list = reporter.generate_report() 
    assert generated_report_info_list
    generated_report_info = generated_report_info_list[0] # Assuming one report
    
    gcs_path = generated_report_info.get('gcs_path')
    assert gcs_path, "gcs_path not found in generated report info"

    # 2. Set custom claims for the test_user to grant access to this store's reports
    #    As per your storage.rules: `hasStoreStorageAccess(storeId, ['viewer', 'editor', 'admin'])`
    claims_to_set = {
        "store_access_rights": {
            store_id_for_report: "viewer" # Or 'editor'/'admin'
        }
    }
    auth.set_custom_user_claims(test_user.uid, claims_to_set)
    
    # 3. Get a fresh ID token for test_user that includes these new claims
    #    You might have a helper/fixture for this, e.g., get_fresh_token(user_uid)
    #    For demonstration, let's assume a simple way to get a token.
    #    In a real setup, the client SDK would handle token refresh. For testing, we force it.
    #    This often involves creating a custom token and then "signing in" with it via REST API
    #    or using a Firebase Admin SDK method if available to mint an ID token directly for testing.
    #    Let's use a placeholder for how you get the token.
    #    If your `firestore_rest_client` fixture or `get_fresh_client` handles token refresh internally, adapt this.
    
    # Simplified token refresh for testing (you might have a more robust fixture)
    # This step is CRUCIAL and often tricky in tests.
    # One common pattern:
    auth.create_custom_token(test_user.uid, claims_to_set)
    id_token = cloud_function_tester.get_id_token(test_user.uid)

    auth.verify_id_token(id_token)
    storage_rest_client_helper.set_id_token(id_token)


    # --- Act & Assert: User with correct claims CAN read ---
    print(f"Attempting to download GCS object: {gcs_path} with token for user {test_user.uid}")
    response_viewer = storage_rest_client_helper.get_object_media(gcs_path)
    
    if response_viewer.status_code != 200:
        print(f"Error fetching object with viewer rights: {response_viewer.status_code} - {response_viewer.text}")
    assert response_viewer.status_code == 200, "User with viewer rights should be able to download."
    assert len(response_viewer.content) > 0, "Downloaded content should not be empty."

    # --- Act & Assert: User with INSUFFICIENT/NO claims CANNOT read ---
    # 1. Clear or set insufficient claims
    auth.set_custom_user_claims(test_user.uid, {"store_access_rights": {"some_other_store": "viewer"}})
    
    # 2. Get a new token with these insufficient claims
    # id_token_no_access = firestore_rest_client.get_id_token_for_user(test_user.uid, {"store_access_rights": {"some_other_store": "viewer"}}) # Adapt this
    custom_token_no_access = cloud_function_tester.get_id_token(test_user.uid)
    storage_rest_client_helper.set_id_token(custom_token_no_access)

    print(f"Attempting to download GCS object: {gcs_path} with token for user {test_user.uid} (no access expected)")
    response_no_access = storage_rest_client_helper.get_object_media(gcs_path)

    if response_no_access.status_code != 403:
         print(f"Unexpected status for no access: {response_no_access.status_code} - {response_no_access.text}")
    assert response_no_access.status_code == 403, "User without correct store access rights should be denied (403)."
