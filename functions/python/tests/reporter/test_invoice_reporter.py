from reporter.invoice_reporter import InvoiceReporter
import polars as pl
import pytest
from models.requests.invoice_request import CreateInvoiceRequest
from datetime import datetime, timezone
from data_generator.invoice_data_generator import generate_invoice_data_handler

@pytest.fixture
def test_invoice_data(
    test_store_NL, test_start_date, test_end_date, 
    test_force_create_active_partnership, n_sales_stagin_to_gold
    ):
    return CreateInvoiceRequest(
        store_id=test_store_NL,
        producer_id=test_force_create_active_partnership["producer_id"],
        start_date=test_start_date,
        end_date=test_end_date
    )



# @pytest.mark.skip(reason="done testing for now")
def test_invoice_reporter(db, test_invoice_data):

    df = generate_invoice_data_handler(db, test_invoice_data)

    # template_dir = '/Users/<USER>/Documents/.projects/gaco-platform/functions/python/tests/'
    template_file_name = 'invoice-template-v2.html'

    reporter = InvoiceReporter(
        invoice_data_frame=df,
        template_file_name=template_file_name,
        db=db
      )

    tmp = reporter.generate_report()

    year = datetime.now(timezone.utc).year

    assert tmp[0]['invoice_id'] == f"{year}-1"