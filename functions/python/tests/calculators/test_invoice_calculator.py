import pytest
import polars as pl
from typing import Dict, List

# --- Import Components to Test ---
from calculators.steps.invoice_steps import (
    CalculateVatRatesStep,
    CalculateVatExcludedSalesStep,
    CalculateNetSalesStep,
    CalculateCommissionPayoutsStep,
    CalculateFinalPayoutsStep,
)
from calculators.pipelines.pipeline_runner import CalculationPipelineRunner
from data_source.auxiliary.vat_rates import VatRatesAuxiliaryDataSource 
from calculators.pipelines.invoice_pipeline import create_invoice_pipeline # To test the assembly

# --- Import Constants ---
# Assuming SalesColumns includes all necessary input/output column names
from columns.sales_columns import SalesColumns, SalesInvoiceColumns 

# --- Mock Dependencies ---
class MockVatDataSource:
    """Mock for VatRateAuxiliaryDataSource for testing steps in isolation."""
    def __init__(self, rates_dict: Dict[str, Dict]):
        self.rates = rates_dict
        # Simulate the structure expected from the real source
        self._internal_rates = {cc: data.get('default_rate', 0.0) 
                                for cc, data in rates_dict.items()}

    def get_rates(self, country_codes: List[str]) -> Dict[str, Dict]:
        # Return the full dict format expected by the step logic using map_elements
        # Although the step only uses 'default_rate', return the structure
        return {cc: {'default_rate': self._internal_rates.get(cc, 0.0)} 
                for cc in country_codes if cc in self._internal_rates}

# --- Fixtures ---
@pytest.fixture
def sample_vat_rates_dict() -> Dict[str, Dict]:
    """Provides a sample VAT rates dictionary for mocking."""
    return {
        'DE': {'default_rate': 19.0},
        'FR': {'default_rate': 20.0},
        'US': {'default_rate': 0.0},
        'NL': {'default_rate': 21.0} # Added NL for completeness
    }

@pytest.fixture
def mock_vat_data_source(sample_vat_rates_dict):
    """Provides a mocked VatRateAuxiliaryDataSource."""
    return MockVatDataSource(sample_vat_rates_dict)

# --- Test Class for Individual Steps ---
class TestInvoiceCalculationSteps:

    def test_calculate_vat_rates_step(self, mock_vat_data_source):
        # Arrange
        step = CalculateVatRatesStep(mock_vat_data_source)
        input_df_mismatch = pl.DataFrame({
            SalesColumns.PRODUCER_TAX_A2.value: ['DE', 'FR', 'DE', 'US', 'NL', 'DE'],
            SalesColumns.STORE_TAX_A2.value:   ['DE', 'DE', 'FR', 'US', 'NL', 'NL'], # Note mismatches DE-FR, DE-NL
        })
        # Validation should raise error due to mismatch
        with pytest.raises(ValueError, match="Producer and Store Tax Country Codes .* must match"):
             step.execute(input_df_mismatch)

        # Arrange - Valid Input
        input_df_valid = pl.DataFrame({
            SalesColumns.PRODUCER_TAX_A2.value: ['DE', 'FR', 'US', 'NL', 'XX'], # XX tests missing rate handling
            SalesColumns.STORE_TAX_A2.value:   ['DE', 'FR', 'US', 'NL', 'XX'],
        })
        expected_df = input_df_valid.with_columns(
             pl.Series(SalesInvoiceColumns.ASSIGNED_VAT_RATE.value, [19.0, 20.0, 0.0, 21.0, 0.0]) # 0.0 for unknown 'XX'
        )
        
        # Act
        result_df = step.execute(input_df_valid)

        # Assert using .equals()
        assert result_df.equals(expected_df), \
               f"DataFrame mismatch in test_calculate_vat_rates_step.\nExpected:\n{expected_df}\nActual:\n{result_df}"

    def test_calculate_vat_excluded_sales_step(self):
        # Arrange
        step = CalculateVatExcludedSalesStep()
        input_df = pl.DataFrame({
            SalesColumns.SUBTOTAL.value:           [119.0, 100.0, 242.0, 150.0],
            SalesInvoiceColumns.ASSIGNED_VAT_RATE.value: [19.0,   0.0,  21.0,  0.0 ] # Using NL rate 21%
        })
        # Expected VAT amounts: 19.0, 0.0, 42.0, 0.0
        expected_df = input_df.with_columns(
             pl.Series(SalesInvoiceColumns.VAT_EXCLUDED_SALE.value, [19.0, 0.0, 42.0, 0.0])
        )

        # Act
        result_df = step.execute(input_df)
        
        # Assert using .equals() - Use rounding before comparison for floats if needed
        # Polars .equals() is strict with floats, consider tolerance or rounding
        # Option 1: Round both before comparing
        precision = 6 # Define desired precision
        result_rounded = result_df.with_columns(pl.all().round(precision))
        expected_rounded = expected_df.with_columns(pl.all().round(precision))
        assert result_rounded.equals(expected_rounded), \
               f"DataFrame mismatch in test_calculate_vat_excluded_sales_step.\nExpected:\n{expected_rounded}\nActual:\n{result_rounded}"
        
        # Option 2: Check approximate equality (less strict than .equals)
        # assert (result_df - expected_df).abs().max().max() < 1e-6 # Check max absolute difference

    def test_calculate_net_sales_step(self):
        # Arrange
        step = CalculateNetSalesStep()
        input_df = pl.DataFrame({
            SalesColumns.SUBTOTAL.value:            [119.0, 100.0, 242.0],
            SalesInvoiceColumns.VAT_EXCLUDED_SALE.value:  [19.0,   0.0,  42.0]
        })
        # Expected Net Sales: 100.0, 100.0, 200.0
        expected_df = input_df.with_columns(
             pl.Series(SalesInvoiceColumns.NET_SALES.value, [100.0, 100.0, 200.0])
        )

        # Act
        result_df = step.execute(input_df)

        # Assert using .equals()
        assert result_df.equals(expected_df), \
               f"DataFrame mismatch in test_calculate_net_sales_step.\nExpected:\n{expected_df}\nActual:\n{result_df}"

    def test_calculate_commission_payouts_step(self):
        # Arrange
        step = CalculateCommissionPayoutsStep()
        input_df = pl.DataFrame({
            SalesInvoiceColumns.NET_SALES.value:         [100.0, 100.0, 200.0],
            SalesColumns.COMMISSION.value:        [20.0,   15.0,  10.0],
            SalesInvoiceColumns.ASSIGNED_VAT_RATE.value: [19.0,    0.0,  21.0] # DE, US, NL rates
        })
        # Expected Store Net Payout: 20.0, 15.0, 20.0
        # Expected VAT on Service: 3.8 (20*0.19), 0.0 (15*0.0), 4.2 (20*0.21)
        expected_df = input_df.with_columns([
             pl.Series(SalesInvoiceColumns.STORE_NET_PAYOUT.value, [20.0, 15.0, 20.0]),
             pl.Series(SalesInvoiceColumns.VAT_ON_SALES_SERVICE.value, [3.8, 0.0, 4.2])
        ])

        # Act
        result_df = step.execute(input_df)

        # Assert using .equals() - round floats
        precision = 6 
        result_rounded = result_df.with_columns(pl.all().round(precision))
        expected_rounded = expected_df.with_columns(pl.all().round(precision))
        assert result_rounded.equals(expected_rounded), \
               f"DataFrame mismatch in test_calculate_commission_payouts_step.\nExpected:\n{expected_rounded}\nActual:\n{result_rounded}"

    def test_calculate_final_payouts_step(self):
        # Arrange
        step = CalculateFinalPayoutsStep()
        input_df = pl.DataFrame({
            SalesColumns.SUBTOTAL.value:               [119.0, 100.0, 242.0],
            SalesInvoiceColumns.STORE_NET_PAYOUT.value:       [20.0,  15.0,  20.0],
            SalesInvoiceColumns.VAT_ON_SALES_SERVICE.value:  [3.8,   0.0,   4.2]
        })
        # Expected Store Total Gross: 23.8, 15.0, 24.2
        # Expected Producer Gross: 95.2 (119-23.8), 85.0 (100-15), 217.8 (242-24.2)
        expected_df = input_df.with_columns([
             pl.Series(SalesInvoiceColumns.STORE_TOTAL_GROSS_PAYOUT.value, [23.8, 15.0, 24.2]),
             pl.Series(SalesInvoiceColumns.PRODUCER_GROSS_PAYOUT.value, [95.2, 85.0, 217.8])
        ])
        
        # Act
        result_df = step.execute(input_df)

        # Assert using .equals() - round floats
        precision = 6
        result_rounded = result_df.with_columns(pl.all().round(precision))
        expected_rounded = expected_df.with_columns(pl.all().round(precision))
        assert result_rounded.equals(expected_rounded), \
               f"DataFrame mismatch in test_calculate_final_payouts_step.\nExpected:\n{expected_rounded}\nActual:\n{result_rounded}"


# --- Test Class for the Full Pipeline ---
class TestInvoicePipeline:

    @pytest.fixture
    def pipeline_input_df(self):
         # Base input data covering different scenarios
         return pl.DataFrame({
            # Ensure constants map to these strings correctly
            SalesColumns.SALE_ID.value:           ['s1','s2','s3','s4','s5'], 
            SalesColumns.PRODUCER_TAX_A2.value: ['DE', 'DE', 'US', 'NL', 'DE'],
            SalesColumns.STORE_TAX_A2.value:   ['DE', 'DE', 'US', 'NL', 'DE'], 
            SalesColumns.SUBTOTAL.value:         [119.0, 240.0, 150.0, 121.0, 59.5],
            SalesColumns.COMMISSION.value:        [20.0,  10.0,  0.0,   15.0,  50.0]
         })

    @pytest.fixture
    def expected_pipeline_output_df(self, pipeline_input_df):
        # Expected final state after all steps - RECALCULATED and ROUNDED to 2 decimal places
        # Ensure constants map to these strings correctly
        return pipeline_input_df.with_columns([
             #                                                           s1      s2       s3       s4       s5
             pl.Series(SalesInvoiceColumns.ASSIGNED_VAT_RATE.value,       [19.0,  19.0,   0.0,  21.0,  19.0 ]), # s2 uses DE rate (19%)
             pl.Series(SalesInvoiceColumns.VAT_EXCLUDED_SALE.value,       [19.00, 38.32,  0.00, 21.00,  9.50 ]), # Corrected s2
             pl.Series(SalesInvoiceColumns.NET_SALES.value,               [100.00,201.68,150.00,100.00, 50.00]), # Corrected s2
             pl.Series(SalesInvoiceColumns.STORE_NET_PAYOUT.value,        [20.00, 20.17,  0.00, 15.00,  25.00]), # Corrected s2
             pl.Series(SalesInvoiceColumns.VAT_ON_SALES_SERVICE.value,    [3.80,  3.83,   0.00,  3.15,  4.75 ]), # Corrected s2
             pl.Series(SalesInvoiceColumns.STORE_TOTAL_GROSS_PAYOUT.value,[23.80, 24.00,  0.00, 18.15,  29.75]), # Corrected s2
             pl.Series(SalesInvoiceColumns.PRODUCER_GROSS_PAYOUT.value,   [95.20, 216.00,150.00,102.85, 29.75]), # Corrected s2
        ])


    def test_full_invoice_pipeline(self, mock_vat_data_source, pipeline_input_df, expected_pipeline_output_df):
        # Arrange: Create the pipeline using the actual assembly function
        # This tests the integration of steps and dependencies like the VAT source
        # It uses the *real* VatRateAuxiliaryDataSource which might hit the DB 
        # if not using the emulator or if test data isn't populated.
        # Consider mocking 'db' if true unit testing of the pipeline runner is desired.
        pipeline = create_invoice_pipeline(mock_vat_data_source) 

        # Act
        result_df = pipeline.compute(pipeline_input_df)

        # Assert: Sort both before comparing to handle potential order changes
        sort_col = SalesColumns.SALE_ID.value
        # Ensure the sort column actually exists in both dataframes before sorting
        if sort_col not in result_df.columns:
             pytest.fail(f"Sort column '{sort_col}' not found in result DataFrame.")
        if sort_col not in expected_pipeline_output_df.columns:
             pytest.fail(f"Sort column '{sort_col}' not found in expected DataFrame.")
             
        result_sorted = result_df.sort(sort_col)
        expected_sorted = expected_pipeline_output_df.sort(sort_col)
        
        # Use .equals() for comparison - round float columns first for stability
        precision = 2
        
        # Select only float columns and apply rounding
        result_rounded = result_sorted.with_columns(
            pl.col(pl.Float64).round(precision) 
        )
        expected_rounded = expected_sorted.with_columns(
            pl.col(pl.Float64).round(precision)
        )
        
        assert result_rounded.equals(expected_rounded), \
            f"Sorted DataFrames do not match.\nExpected (sorted & rounded to {precision}):\n{expected_rounded}\nActual (sorted & rounded to {precision}):\n{result_rounded}"