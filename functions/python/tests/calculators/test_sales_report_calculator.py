import pytest
import polars as pl
from polars.testing import assert_frame_equal

# --- Import Pipeline Creator ---
from calculators.pipelines.sales_report_pipeline import create_sales_report_pipeline

# --- Import Constants (adjust if your sales report uses different column names) ---
# Using SalesInvoiceColumns for output as Sales Report steps likely calculate these.
# Using SalesColumns for input. Adapt if necessary.
from columns.sales_columns import SalesColumns, SalesCommonComputedColumns 

# --- Test Class for the Sales Report Pipeline ---
class TestSalesReportPipeline:

    @pytest.fixture
    def sales_report_input_df(self) -> pl.DataFrame:
         """Provides a basic input DataFrame for the sales report pipeline."""
         return pl.DataFrame({
            # Using names from SalesColumns Enum
            SalesColumns.SALE_ID.value:    ['sr1', 'sr2', 'sr3'],
            SalesColumns.SUBTOTAL.value:  [100.0, 250.0, 50.0],
            SalesColumns.COMMISSION.value: [10.0,  20.0,  15.0],
            # Add any other columns potentially required by the steps
         })

    @pytest.fixture
    def expected_sales_report_output_df(self, sales_report_input_df: pl.DataFrame) -> pl.DataFrame:
        """Provides the expected output after running the sales report pipeline."""
        # Based on assumed simple calculations:
        # Store Gross = Subtotal * Commission / 100
        # Producer Gross = Subtotal - Store Gross
        df = sales_report_input_df.with_columns([
            (pl.col(SalesColumns.SUBTOTAL.value) * pl.col(SalesColumns.COMMISSION.value) / 100.0)
            .alias(SalesCommonComputedColumns.STORE_GROSS_PAYOUT.value),
        ])
        df = df.with_columns([
             (pl.col(SalesColumns.SUBTOTAL.value) - pl.col(SalesCommonComputedColumns.STORE_GROSS_PAYOUT.value))
             .alias(SalesCommonComputedColumns.PRODUCER_GROSS_PAYOUT.value),
        ])
        return df


    def test_full_sales_report_pipeline(
        self, 
        sales_report_input_df: pl.DataFrame, 
        expected_sales_report_output_df: pl.DataFrame
    ):
        """Tests the complete sales report pipeline execution."""
        # Arrange: Create the pipeline using the assembly function
        pipeline = create_sales_report_pipeline()

        # Act: Compute the result
        result_df = pipeline.compute(sales_report_input_df)

        # Assert: Check if the result matches the expected output
        # Use polars.testing.assert_frame_equal for robust comparison
        # It handles float tolerance better than direct .equals()
        try:
             assert_frame_equal(result_df, expected_sales_report_output_df, check_dtype=True, check_column_order=False, rtol=1e-2)
        except AssertionError as e:
             print(f"AssertionError: {e}")
             print("\nResult DataFrame:\n", result_df)
             print("\nExpected DataFrame:\n", expected_sales_report_output_df)
             pytest.fail(f"DataFrame mismatch in test_full_sales_report_pipeline: {e}")
