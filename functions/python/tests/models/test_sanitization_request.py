import pytest
from models.requests.sanitization_request import SanitizeSalesStagingWithAgreementRequest
from pydantic import ValidationError

def test_sanitize_sales_staging_with_producer_request():
    request = SanitizeSalesStagingWithAgreementRequest(
        producerName="Test Producer",
        email="<EMAIL>",
        country="US",
        saleId="123",
        commission=10,
    )
    assert request.producerName == "Test Producer"
    assert request.email == "<EMAIL>"
    assert request.country == "US"
    assert request.saleId == "123"
    assert request.commission == 10

def test_invalid_commission():
    with pytest.raises(ValidationError) as excinfo:
        SanitizeSalesStagingWithAgreementRequest(
            producerName="Test Producer",
            email="<EMAIL>",
            country="US",
            saleId="123",
            commission=150,
        )
    
    # safer but fazzier way to check the error message
    assert "Commission must be between 0 and 100" in str(excinfo.value)
