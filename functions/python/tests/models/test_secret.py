import pytest
from pydantic import ValidationError
from models.secret import SecretLabels, Platform

def test_secret_labels_validation():
    # Valid cases
    assert SecretLabels(platform="shopify", shop_name="test").platform == "shopify"
    assert SecretLabels(platform=Platform.SHOPIFY, shop_name="test").platform == "shopify"

    # Invalid case
    with pytest.raises(ValidationError) as exc_info:
        SecretLabels(platform="invalid", shop_name="test")
    assert f"Platform must be one of: {[p.value for p in Platform]}" in str(exc_info.value)