import pytest
from models.requests.store_requests import CreateStoreRequest
from pydantic import ValidationError
from models.payout_account import Address

def test_create_store_request():
    # Test valid values
    request = CreateStoreRequest(
        display_name="Test Store",
        email="<EMAIL>",
        parent_id="123",
        tax_a2="123",
        shopify_api_key="123",
        default_commission=50,  # Valid value
    )
    assert request.display_name == "Test Store"
    assert request.email == "<EMAIL>"
    assert request.default_commission == 50

def test_invalid_commission():
    # Test that invalid commission raises ValidationError
    with pytest.raises(ValidationError) as excinfo:
        CreateStoreRequest(
            display_name="Test Store",
            email="<EMAIL>",
            parent_id="123",
            tax_a2="123",
            shopify_api_key="123",
            default_commission=150,  # Invalid value
        )
    
    # Check that the error message mentions the commission validation
    error_msg = str(excinfo.value)
    assert "Default commission must be between 0 and 100" in error_msg

def test_missing_optional_fields():
    request = CreateStoreRequest(
        display_name="Test Store",
        email="<EMAIL>",
        parent_id="123",
        tax_a2="123",
        shopify_api_key="123",
    )
    assert request.default_commission is None
    
def test_address_validation_with_the_entire_request():
    # Test valid address
    request = CreateStoreRequest(
        display_name="Test Store",
        email="<EMAIL>",
        parent_id="123",
        tax_a2="123",
        shopify_api_key="123",
        address=Address(
            street_number="123",
            street_name="Main St",
            city="Anytown",
            zip_code="12345",
            country="USA"
        )
    )
    assert request.address.street_number == "123"
    assert request.address.street_name == "Main St"
    assert request.address.city == "Anytown"
    assert request.address.zip_code == "12345"
    assert request.address.country == "USA"
    assert request.address.house_number is None
    assert request.address.additional_info is None
    assert request.address.state is None
    
    