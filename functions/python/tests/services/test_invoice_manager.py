from pytest import fixture
from services.invoice_manager import InvoiceManager
from models.requests.invoice_request import CreateInvoiceRequest, DeleteInvoiceRequest
from datetime import datetime
import pytest


@pytest.mark.skip(reason="skipping for now")
def test_create_invoice(db):
    invoice_request = CreateInvoiceRequest(
        store_id='M0mMi2bx7jYSMtT7rIh7',
        start_date=datetime(2024, 11, 1),
        end_date=datetime(2024, 12, 1)
    )
    invoice_manager = InvoiceManager(db)
    result = invoice_manager.create_invoices(invoice_request)
    import pdb; pdb.set_trace()

    assert result is not None

@pytest.fixture
def get_delete_invoice_request():
    return DeleteInvoiceRequest(
        invoice_doc_id='M0mMi2bx7jYSMtT7rIh7-2025-15'
    )

def test_delete_invoice(db, get_delete_invoice_request):
    invoice_manager = InvoiceManager(db)
    invoice_doc_id = get_delete_invoice_request.invoice_doc_id
    result = invoice_manager.delete_invoice(invoice_doc_id)

    assert result is not None
