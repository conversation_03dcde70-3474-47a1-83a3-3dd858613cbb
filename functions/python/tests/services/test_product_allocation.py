import pytest
import uuid
from datetime import datetime, timezone
from typing import List

from firebase_admin import firestore

from services.product_allocator import ProductAllocationManager
from services.product_manager import ProductManager, SHOPIFY_PRODUCT_GID_PREFIX, SHOPIFY_VARIANT_GID_PREFIX
from models.allocations import ProductAllocation, ProductAllocationItem, AllocationStatus, product_allocations_collection, DeliveryMethod
from models.products import Product, ProductVariant
from gaco_framework.exceptions import NotFoundError, PermissionError, ValidationError

# --- Helper ID Generators (can be shared or simplified if already in conftest or another util) ---
def new_alloc_test_shopify_product_gid() -> str:
    return f"{SHOPIFY_PRODUCT_GID_PREFIX}{uuid.uuid4().hex}"

def new_alloc_test_shopify_variant_gid() -> str:
    return f"{SHOPIFY_VARIANT_GID_PREFIX}{uuid.uuid4().hex}"

# --- Fixtures ---

@pytest.fixture
def product_manager_for_alloc_tests(db: firestore.Client) -> ProductManager:
    """Independent ProductManager for allocation tests setup."""
    return ProductManager(db=db)

@pytest.fixture
def product_allocator_service(db: firestore.Client) -> ProductAllocationManager:
    return ProductAllocationManager(db=db)

@pytest.fixture
def alloc_test_product_with_variants(
    product_manager_for_alloc_tests: ProductManager,
    test_producer # From conftest.py, provides producer_id
):
    """
    Creates a product with variants owned by the test_producer.
    Yields the created Product model and a list of its ProductVariant models.
    Handles cleanup of the product and its variants.
    """
    now = datetime.now(timezone.utc).isoformat()
    prod_logical_id = new_alloc_test_shopify_product_gid()

    variant1_logical_id = new_alloc_test_shopify_variant_gid()
    variant2_logical_id = new_alloc_test_shopify_variant_gid()

    product_data = Product(
        product_id=prod_logical_id,
        title="Alloc Test Product",
        vendor="Alloc Test Vendor", # Not strictly producer_id, but can be
        producer_id=test_producer, # Crucial for ownership validation
        store_id=f"store_{uuid.uuid4().hex[:4]}", # Dummy store context
        store_display_name="Dummy Store",
        handle="alloc-test-product",
        status="ACTIVE",
        created_at=now,
        updated_at=now,
        variants=[ # Variants are defined here but created via product_manager for consistency
            ProductVariant(
                product_variant_id=variant1_logical_id,
                product_id=prod_logical_id,
                title="Alloc Variant 1",
                sku="ALLOC-V1",
                price=10.00,
                inventory_quantity=100,
                created_at=now,
                updated_at=now
            ),
            ProductVariant(
                product_variant_id=variant2_logical_id,
                product_id=prod_logical_id,
                title="Alloc Variant 2",
                sku="ALLOC-V2",
                price=20.00,
                inventory_quantity=50,
                created_at=now,
                updated_at=now
            )
        ]
    )
    
    created_product = product_manager_for_alloc_tests.create_product(product_data)
    # Variants are created as part of product creation if included in the Product model

    yield created_product # Product model now contains the variants as created by manager

    # Cleanup
    try:
        product_manager_for_alloc_tests.delete_product(created_product.product_id, delete_variants=True)
    except Exception as e:
        print(f"Cleanup failed for product {created_product.product_id} in alloc_test_product_with_variants: {e}")


@pytest.fixture
def sample_allocation_payload(
    test_producer, # producer_id from conftest
    test_store,    # store_id from conftest
    alloc_test_product_with_variants: Product # Contains list of ProductVariant models
) -> ProductAllocation:
    """Creates a valid ProductAllocation model instance for testing."""
    product_model = alloc_test_product_with_variants
    
    if not product_model.variants or len(product_model.variants) < 2:
        pytest.fail("Test setup error: alloc_test_product_with_variants did not yield enough variants.")

    item1 = ProductAllocationItem(
        product_id=product_model.product_id, # Added product_id
        product_variant_id=product_model.variants[0].product_variant_id,
        quantity=5  # 'quantity' as per ProductAllocationItem model
    )
    item2 = ProductAllocationItem(
        product_id=product_model.product_id, # Added product_id
        product_variant_id=product_model.variants[1].product_variant_id,
        quantity=10 # 'quantity' as per ProductAllocationItem model
    )
    
    return ProductAllocation(
        producer_id=test_producer,
        store_id=test_store,
        items=[item1, item2],
        delivery_method=DeliveryMethod.PRODUCER_SHIPMENT.value, # Added required field
        # allocation_date, status, etc., will use model defaults or be set by service
    )

@pytest.fixture
def created_allocation(
    product_allocator_service: ProductAllocationManager,
    sample_allocation_payload: ProductAllocation,
    db: firestore.Client
):
    """Creates an allocation using the service and yields the created model (with ID). Cleans up."""
    created_alloc = product_allocator_service.create_allocation(sample_allocation_payload)
    assert created_alloc.allocation_id is not None
    yield created_alloc

    # Cleanup
    try:
        db.collection(product_allocations_collection).document(created_alloc.allocation_id).delete()
    except Exception as e:
        print(f"Cleanup failed for allocation {created_alloc.allocation_id}: {e}")


@pytest.fixture
def alloc_test_one_of_a_kind_product(
    product_manager_for_alloc_tests: ProductManager,
    test_producer # From conftest.py, provides producer_id
):
    """Creates a product with NO variants, owned by the test_producer."""
    now = datetime.now(timezone.utc)
    prod_logical_id = new_alloc_test_shopify_product_gid() # Can be any unique ID

    product_data = Product(
        product_id=prod_logical_id,
        title="One of a Kind Art Piece",
        producer_id=test_producer,
        store_id=f"store_{uuid.uuid4().hex[:4]}", # Dummy context
        store_display_name="Dummy Store",
        handle="one-of-a-kind-art",
        status="ACTIVE",
        created_at=now,
        updated_at=now,
        variants=[] # Explicitly no variants
    )
    created_product = product_manager_for_alloc_tests.create_product(product_data)
    yield created_product
    try:
        product_manager_for_alloc_tests.delete_product(created_product.product_id, delete_variants=True)
    except Exception as e:
        print(f"Cleanup failed for one_of_a_kind_product {created_product.product_id}: {e}")


# --- Test Cases ---

def test_create_allocation_ok(
    product_allocator_service: ProductAllocationManager,
    sample_allocation_payload: ProductAllocation,
    db: firestore.Client
):
    created_allocation_model = None
    try:
        created_allocation_model = product_allocator_service.create_allocation(sample_allocation_payload)
        assert created_allocation_model.allocation_id is not None
        assert created_allocation_model.producer_id == sample_allocation_payload.producer_id
        assert created_allocation_model.store_id == sample_allocation_payload.store_id
        assert len(created_allocation_model.items) == len(sample_allocation_payload.items)
        assert created_allocation_model.status == AllocationStatus.PENDING_CONFIRMATION.value # Default from model
        assert created_allocation_model.delivery_method == sample_allocation_payload.delivery_method

        # Verify in DB
        doc = db.collection(product_allocations_collection).document(created_allocation_model.allocation_id).get()
        assert doc.exists
        db_data = doc.to_dict()
        assert db_data[ProductAllocation.PRODUCER_ID_FIELD] == sample_allocation_payload.producer_id
    finally:
        if created_allocation_model and created_allocation_model.allocation_id:
            db.collection(product_allocations_collection).document(created_allocation_model.allocation_id).delete()


def test_create_allocation_variant_not_found(
    product_allocator_service: ProductAllocationManager,
    sample_allocation_payload: ProductAllocation
):
    sample_allocation_payload.items[0].product_variant_id = "non-existent-variant-id"
    with pytest.raises(NotFoundError, match="not found"):
        product_allocator_service.create_allocation(sample_allocation_payload)

def test_create_allocation_producer_mismatch(
    product_allocator_service: ProductAllocationManager,
    sample_allocation_payload: ProductAllocation,
):
    sample_allocation_payload.producer_id = "mismatched-producer-id" # This ID does not own the products
    with pytest.raises(PermissionError, match="does not own product"): # Error from _validate_product_ownership_and_variants
        product_allocator_service.create_allocation(sample_allocation_payload)

def test_create_allocation_insufficient_stock(
    product_allocator_service: ProductAllocationManager,
    sample_allocation_payload: ProductAllocation,
    alloc_test_product_with_variants: Product # To access original inventory quantity
):
    # Assuming the first variant (index 0) from alloc_test_product_with_variants
    # was used for the first item in sample_allocation_payload.
    # And its inventory_quantity is known (e.g., 100 from fixture setup).
    original_inventory_quantity = alloc_test_product_with_variants.variants[0].inventory_quantity
    
    # Modify the quantity in the payload to be greater than available stock
    sample_allocation_payload.items[0].quantity = original_inventory_quantity + 10 
    
    with pytest.raises(ValidationError, match="Insufficient stock"):
        product_allocator_service.create_allocation(sample_allocation_payload)


def test_get_allocation_ok(product_allocator_service: ProductAllocationManager, created_allocation: ProductAllocation):
    retrieved_alloc = product_allocator_service.get_allocation(created_allocation.allocation_id)
    assert retrieved_alloc is not None
    assert retrieved_alloc.allocation_id == created_allocation.allocation_id
    assert retrieved_alloc.producer_id == created_allocation.producer_id

def test_get_allocation_not_found(product_allocator_service: ProductAllocationManager):
    with pytest.raises(NotFoundError):
        product_allocator_service.get_allocation("non-existent-alloc-id")

def test_update_allocation_status_ok(product_allocator_service: ProductAllocationManager, created_allocation: ProductAllocation):
    updated_alloc = product_allocator_service.update_allocation_status(
        created_allocation.allocation_id,
        AllocationStatus.IN_TRANSIT.value, # Changed from SHIPPED to match AllocationStatus Literal
        details={ProductAllocation.TRACKING_NUMBER_FIELD: "TRK123"}
    )
    assert updated_alloc is not None
    assert updated_alloc.status == AllocationStatus.IN_TRANSIT.value
    assert updated_alloc.shipped_on is not None # This is set when status becomes IN_TRANSIT
    assert updated_alloc.tracking_number == "TRK123"

    retrieved_again = product_allocator_service.get_allocation(created_allocation.allocation_id)
    assert retrieved_again.status == AllocationStatus.IN_TRANSIT.value
    assert retrieved_again.tracking_number == "TRK123"


def test_list_allocations_by_producer_ok(
    product_allocator_service: ProductAllocationManager,
    created_allocation: ProductAllocation # Ensures at least one allocation for the producer
):
    producer_id = created_allocation.producer_id
    allocations = product_allocator_service.list_allocations_by_producer(producer_id)
    assert len(allocations) >= 1
    assert all(alloc.producer_id == producer_id for alloc in allocations)
    # Check if our created_allocation is in the list
    assert any(alloc.allocation_id == created_allocation.allocation_id for alloc in allocations)

def test_list_allocations_by_producer_with_status_filter(
    product_allocator_service: ProductAllocationManager,
    created_allocation: ProductAllocation, # PENDING_CONFIRMATION by default
    test_producer # producer_id
):
    # Update status of the created_allocation to IN_TRANSIT
    product_allocator_service.update_allocation_status(
        created_allocation.allocation_id, 
        AllocationStatus.IN_TRANSIT.value
    )

    # Query for PENDING_CONFIRMATION allocations
    pending_confirmation_allocs = product_allocator_service\
      .list_allocations_by_producer(test_producer, status=AllocationStatus.PENDING_CONFIRMATION)
    # Query for IN_TRANSIT allocations
    in_transit_allocs = product_allocator_service\
      .list_allocations_by_producer(test_producer, status=AllocationStatus.IN_TRANSIT)

    # The original created_allocation (now IN_TRANSIT) should not be in the PENDING_CONFIRMATION list
    assert not any(alloc.allocation_id == created_allocation.allocation_id for alloc in pending_confirmation_allocs)
    # It should be in the IN_TRANSIT list
    assert any(alloc.allocation_id == created_allocation.allocation_id for alloc in in_transit_allocs)

    for alloc in pending_confirmation_allocs:
        assert alloc.status == AllocationStatus.PENDING_CONFIRMATION.value
    for alloc in in_transit_allocs:
        assert alloc.status == AllocationStatus.IN_TRANSIT.value

def test_create_allocation_product_level_one_of_a_kind_ok(
    product_allocator_service: ProductAllocationManager,
    alloc_test_one_of_a_kind_product: Product,
    test_producer,
    test_store,
    db: firestore.Client
):
    """Tests allocating a product directly (no variants specified) when it's a '1 of a kind' item."""
    product_model = alloc_test_one_of_a_kind_product
    
    item = ProductAllocationItem(
        product_id=product_model.product_id,
        product_variant_id=None, # Explicitly product-level
        quantity=1 # For "1 of a kind"
    )
    
    allocation_payload = ProductAllocation(
        producer_id=test_producer,
        store_id=test_store,
        items=[item],
        delivery_method=DeliveryMethod.STORE_PICKUP.value
    )
    
    created_allocation_model = None
    try:
        created_allocation_model = product_allocator_service.create_allocation(allocation_payload)
        assert created_allocation_model.allocation_id is not None
        assert created_allocation_model.items[0].product_id == product_model.product_id
        assert created_allocation_model.items[0].product_variant_id is None
        assert created_allocation_model.items[0].quantity == 1

        doc = db.collection(product_allocations_collection).document(created_allocation_model.allocation_id).get()
        assert doc.exists
    finally:
        if created_allocation_model and created_allocation_model.allocation_id:
            db.collection(product_allocations_collection).document(created_allocation_model.allocation_id).delete()

def test_create_allocation_product_level_fails_if_product_has_variants(
    product_allocator_service: ProductAllocationManager,
    alloc_test_product_with_variants: Product, # This product HAS variants
    test_producer,
    test_store
):
    """Tests that product-level allocation fails if the product actually has variants."""
    product_model = alloc_test_product_with_variants # Has variants
    
    item = ProductAllocationItem(
        product_id=product_model.product_id, # Targeting the parent product
        product_variant_id=None,             # But not specifying which variant
        quantity=1
    )
    
    allocation_payload = ProductAllocation(
        producer_id=test_producer,
        store_id=test_store,
        items=[item],
        delivery_method=DeliveryMethod.PRODUCER_DELIVERY
    )
    
    with pytest.raises(ValidationError, match="has variants. A product_variant_id must be specified"):
        product_allocator_service.create_allocation(allocation_payload)

def test_create_allocation_product_level_one_of_a_kind_fails_if_quantity_too_high(
    product_allocator_service: ProductAllocationManager,
    alloc_test_one_of_a_kind_product: Product,
    test_producer,
    test_store
):
    """Tests product-level allocation for '1 of a kind' fails if quantity > 1."""
    product_model = alloc_test_one_of_a_kind_product
    
    item = ProductAllocationItem(
        product_id=product_model.product_id,
        product_variant_id=None,
        quantity=2 # More than 1 for a "1 of a kind"
    )
    
    allocation_payload = ProductAllocation(
        producer_id=test_producer,
        store_id=test_store,
        items=[item],
        delivery_method=DeliveryMethod.STORE_PICKUP
    )
    
    with pytest.raises(ValidationError, match="quantity must be 1"):
        product_allocator_service.create_allocation(allocation_payload)