import pytest
from firebase_admin import auth
from services.user_root_account_manager import UserRootAccountManager
from models.user_root_account import UserRootAccount
from queries.user_root_account_query_builder import UserRootAccountQueryBuilder
import time


@pytest.fixture
def test_user(firebase_app):
    unique_timestamp = int(time.time() * 1000)
    email = f'test-integration-{unique_timestamp}@example.com'
    user = None
    try:
        user = auth.create_user(
            email=email,
            password='testpassword123',
            display_name='Test Integration User'
        )
        print(f"Created test auth user: {user.uid} with email: {email}")
        yield user
    finally:
        if user:
            try:
                auth.delete_user(user.uid)
                print(f"Deleted test auth user: {user.uid}")
            except auth.UserNotFoundError:
                print(f"Auth user {user.uid} already deleted or not found during cleanup.")
            except Exception as e:
                print(f"Error deleting auth user {user.uid} during cleanup: {e}")

@pytest.fixture
def user_root_account_manager(db):
    return UserRootAccountManager(db)

def create_test_user_root_account_doc(db, user_id, email, role="user", stores=None, producers=None):
    if stores is None:
        stores = {}
    if producers is None:
        producers = {}
    
    account_data = {
        UserRootAccount.EMAIL_FIELD: email,
        UserRootAccount.ROLE_FIELD: role,
        UserRootAccount.STORES_FIELD: stores,
        UserRootAccount.PRODUCERS_FIELD: producers,
        UserRootAccount.CREATED_AT_FIELD: time.time(), 
        UserRootAccount.UPDATED_AT_FIELD: time.time(),
    }
    db.collection('userRootAccounts').document(user_id).set(account_data)
    print(f"Created test userRootAccount for UID: {user_id}")
    return account_data

@pytest.fixture
def managed_test_user(db, test_user, user_root_account_manager):
    create_test_user_root_account_doc(db, test_user.uid, test_user.email)
    yield test_user
    try:
        user_root_account_manager.delete_user_root_account(test_user.uid)
        print(f"Cleaned up userRootAccount for UID: {test_user.uid}")
    except Exception:
        pass

def test_delete_all_test_users(db, user_root_account_manager):
    unique_timestamp = int(time.time() * 1000)
    temp_email = f'test-to-delete-{unique_timestamp}@example.com'
    temp_auth_user = None
    temp_user_uid = None
    try:
        temp_auth_user = auth.create_user(email=temp_email, password="password")
        temp_user_uid = temp_auth_user.uid
        create_test_user_root_account_doc(db, temp_user_uid, temp_email)
        print(f"Created temporary user for deletion test: {temp_user_uid}")

        user_root_account_qb = UserRootAccountQueryBuilder(db)
        user_root_accounts = user_root_account_qb.build().get()
        
        deleted_count = 0
        for doc in user_root_accounts:
            user_data = doc.to_dict()
            if 'email' in user_data and (user_data['email'].startswith('test-') or user_data['email'] == temp_email) :
                print(f"Deleting test user root account: {doc.id} with email: {user_data['email']}")
                db.collection('userRootAccounts').document(doc.id).delete()
                deleted_count +=1
        
        assert deleted_count > 0, "Expected to delete at least one test user root account"

    finally:
        if temp_auth_user:
            auth.delete_user(temp_auth_user.uid)

def test_create_user_root_account_implicitly_by_managed_fixture(db, managed_test_user):
    firebase_user = auth.get_user(managed_test_user.uid)
    
    doc_snapshot = db.collection('userRootAccounts').document(managed_test_user.uid).get()
    assert doc_snapshot.exists, f"User root account document not found for UID: {managed_test_user.uid}"
    
    data = doc_snapshot.to_dict()
    assert data.get('email') == firebase_user.email
    assert data.get(UserRootAccount.ROLE_FIELD) == 'user'
    assert data.get(UserRootAccount.STORES_FIELD) == {}
    assert data.get(UserRootAccount.PRODUCERS_FIELD) == {}

def test_check_if_user_root_account_exists(user_root_account_manager, managed_test_user):
    assert user_root_account_manager.check_if_user_root_account_exists(managed_test_user.uid)

def test_add_producer_to_user_root_account(db, user_root_account_manager, managed_test_user):
    producer_id = "producerid123"

    user_root_account_manager.add_producer_with_access_right(managed_test_user.uid, producer_id, "viewer")
    
    doc_snapshot = db.collection('userRootAccounts').document(managed_test_user.uid).get()
    data = doc_snapshot.to_dict()
    
    assert producer_id in data[UserRootAccount.PRODUCERS_FIELD]
    assert data[UserRootAccount.PRODUCERS_FIELD][producer_id] == "viewer"

def test_add_producer_to_nonexistent_user_account(user_root_account_manager):
    producer_id = "test-producer-id"
    nonexistent_user_id = "nonexistent-user-id-123"
    
    with pytest.raises(Exception, match="Error setting producer access_right in user root account"):
         user_root_account_manager.add_producer_with_access_right(nonexistent_user_id, producer_id, "viewer")

def test_remove_producer_from_user_root_account(db, user_root_account_manager, managed_test_user):
    producer_id = "testProducerIdToRemove"
    user_root_account_manager.add_producer_with_access_right(managed_test_user.uid, producer_id, "editor")
    
    doc_snapshot = db.collection('userRootAccounts').document(managed_test_user.uid).get()
    data = doc_snapshot.to_dict()
    assert producer_id in data[UserRootAccount.PRODUCERS_FIELD]
    
    user_root_account_manager.remove_producer_association(managed_test_user.uid, producer_id)
    
    doc_snapshot_after_remove = db.collection('userRootAccounts').document(managed_test_user.uid).get()
    data_after_remove = doc_snapshot_after_remove.to_dict()
    assert producer_id not in data_after_remove[UserRootAccount.PRODUCERS_FIELD]

def test_remove_producer_from_nonexistent_user_account(user_root_account_manager):
    producer_id = "testProducerIdToRemove"
    nonexistent_user_id = "nonexistentUserId456"
    
    with pytest.raises(Exception, match="Error removing producer from user root account"):
        user_root_account_manager.remove_producer_association(nonexistent_user_id, producer_id)

