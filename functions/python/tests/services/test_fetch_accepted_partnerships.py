import pytest
from sanitizer.sales_sanitizer_factory import SalesSanitizerFactory
from datetime import datetime, timedelta, timezone
from models.producer import Producer
from models.partner import Partnership, PartnershipStatus


@pytest.fixture
def test_fetch_active_partnerships(db):
    """Test the fetch_active_partnerships method
    Create a test store and 2 test producers
    Create a partnership between the store and the producers
    Fetch the accepted partnerships
    one of the partnership should be expired
    when fetching the accepted partnerships, the expired partnership should not be returned
    """
    store_id = 'test-store'
    producer_id = 'test-producer'

    # Create a producer to attach the display name
    db.collection('producersV2').document(producer_id).set(
        Producer(
          created_at=datetime.now(timezone.utc),
          display_name='Test Producer',
          email='<EMAIL>',
          tax_a2='US',
          parent_id=store_id,
          created_by='test-user'
        ).model_dump()
      )

    # Timestamps for expired vs. active
    now = datetime.now(timezone.utc)
    past = now - timedelta(days=1)
    future = now + timedelta(days=1)

    # 1) An expired partnership (endDate < now)
    expired_ref = db.collection('partnerships').document()
    expired_partnership = Partnership(
      sender_id=store_id,
      recipient_id=producer_id,
      status=PartnershipStatus.ACCEPTED.value,
      applied_at=now,
      commission=5,
      contract_link=None,
      created_by='test-user',
      start_date=now - timedelta(days=10),
      end_date=past
    )
    expired_ref.set(expired_partnership.model_dump())

    # 2) An active partnership (endDate > now)
    active_ref = db.collection('partnerships').document()
    active_partnership = Partnership(
      sender_id=store_id,
      recipient_id=producer_id,
      status=PartnershipStatus.ACCEPTED.value,
      applied_at=now,
      commission=7,
      contract_link=None,
      created_by='test-user',
      start_date=now - timedelta(days=10),
      end_date=future
    )
    active_ref.set(active_partnership.model_dump())

    yield expired_ref, active_ref, store_id, producer_id

    # Cleanup
    db.collection('partnerships').document(expired_ref.id).delete()
    db.collection('partnerships').document(active_ref.id).delete()
    db.collection('producersV2').document(producer_id).delete()



def test_fetch_active_partnerships_filters_expired_partnerships(db, testfetch_active_partnerships):
    expired_ref, active_ref, store_id, producer_id = testfetch_active_partnerships


    # Act
    sales_sanitizer = SalesSanitizerFactory.create(db)
    results = sales_sanitizer.fetch_active_partnerships(store_id)

    # Only the expired partnership should be returned
    assert len(results) == 1
    partner = results[0]
    assert partner['recipientId'] == producer_id
    assert partner['commission'] == 5
    assert partner['producerDisplayName'] == 'Test Producer'

    # And Firestore now marks it as expired
    expired_doc = db.collection('partnerships').document(expired_ref.id).get().to_dict()
    assert expired_doc['status'] == PartnershipStatus.EXPIRED.value
