import pytest
from datetime import datetime, timezone, timedelta
from firebase_admin import firestore
from firebase_functions import logger
from enum import Enum

from services.agreement_manager import AgreementManager
from services.partner_manager import PartnershipManager
from models.agreement import Agreement, AgreementStatus, Role, ApprovalStatus, NegotiationChange, ApprovalStep
from models.partner import Partnership, PartnershipStatus
from models.requests.agreements_requests import CreateAgreementRequest
from gaco_framework.auth import AuthContext
from gaco_framework.exceptions import ConflictError

# -------------------- Fixtures --------------------

@pytest.fixture
def draft_agreement_data_with_no_expiration(test_store, test_producer):
    """Provides data for creating a draft agreement."""
    return CreateAgreementRequest(
        store_id=test_store,
        producer_id=test_producer,
        effective_date=datetime.now(timezone.utc) + timedelta(days=1),
        commission=20,
        document_url="http://example.com/doc.pdf",
        created_by_role=Role.STORE.value
    )

@pytest.fixture
def created_draft_agreement_data_with_no_expiration(db, draft_agreement_data_with_no_expiration, test_user):
    """Provides data for creating a draft agreement."""
    auth_context = AuthContext(user_id=test_user.uid)
    agreement_manager = AgreementManager(db, auth_context)
    agreement_id = agreement_manager.create_draft_agreement(draft_agreement_data_with_no_expiration)
    agreement_manager.submit_for_approval(agreement_id, Role.STORE.value)
    return agreement_id
    

@pytest.fixture
def draft_agreement_data(test_store, test_producer):
    """Provides data for creating a draft agreement."""
    return CreateAgreementRequest(
        store_id=test_store,
        producer_id=test_producer,
        title="Test Draft Agreement",
        effective_date=datetime.now(timezone.utc) - timedelta(days=1),
        expiration_date=datetime.now(timezone.utc) + timedelta(days=366),
        commission=20,
        document_url="http://example.com/doc.pdf",
        created_by_role=Role.STORE.value
    )

@pytest.fixture
def draft_agreement_data_producer_first(test_store, test_producer):
    """Provides data for creating a draft agreement."""
    return CreateAgreementRequest(
        store_id=test_store,
        producer_id=test_producer,
        title="Test Draft Agreement",
        effective_date=datetime.now(timezone.utc) - timedelta(days=1),
        expiration_date=datetime.now(timezone.utc) + timedelta(days=366),
        commission=20,
        document_url="http://example.com/doc.pdf",
        created_by_role=Role.PRODUCER.value
    )


@pytest.fixture
def created_draft_agreement(db, draft_agreement_data, test_user):
    """Creates a draft agreement and returns its ID."""
    auth_context = AuthContext(user_id=test_user.uid)
    agreement_manager = AgreementManager(db, auth_context)
    agreement_id = agreement_manager.create_draft_agreement(draft_agreement_data)
    yield agreement_id
    # Cleanup
    try:
        agreement_manager.agreements_collection.document(agreement_id).delete()
    except Exception as e:
         logger.warn(f"Cleanup failed for agreement {agreement_id}: {e}")

@pytest.fixture
def created_draft_agreement_producer_first(db, draft_agreement_data_producer_first, test_user):
    """Creates a draft agreement and returns its ID."""
    auth_context = AuthContext(user_id=test_user.uid)
    agreement_manager = AgreementManager(db, auth_context)
    agreement_id = agreement_manager.create_draft_agreement(draft_agreement_data_producer_first)
    yield agreement_id
    # Cleanup
    try:
        agreement_manager.agreements_collection.document(agreement_id).delete()
    except Exception as e:
         logger.warn(f"Cleanup failed for agreement {agreement_id}: {e}")


@pytest.fixture
def created_negotiation_agreement(db, draft_agreement_data, user_with_root_account):
    """Creates an agreement in NEGOTIATION state."""
    auth_context = AuthContext(user_id=user_with_root_account.uid)
    agreement_manager = AgreementManager(db, auth_context)
    agreement_id = agreement_manager.create_draft_agreement(draft_agreement_data)
    
    # Move to negotiation state
    agreement_ref = agreement_manager.agreements_collection.document(agreement_id)
    agreement_ref.update({
        Agreement.STATUS_FIELD: AgreementStatus.NEGOTIATION.value,
        Agreement.UPDATED_BY_FIELD: user_with_root_account.uid,
        Agreement.UPDATED_AT_FIELD: datetime.now(timezone.utc)
    })
    
    yield agreement_id
    # Cleanup
    try:
        agreement_manager.agreements_collection.document(agreement_id).delete()
    except Exception as e:
        logger.warn(f"Cleanup failed for agreement {agreement_id}: {e}")


@pytest.fixture
def created_pending_approval_agreement(db, draft_agreement_data, user_with_root_account):
    """Creates an agreement in PENDING_APPROVAL state."""
    auth_context = AuthContext(user_id=user_with_root_account.uid)
    agreement_manager = AgreementManager(db, auth_context)
    agreement_id = agreement_manager.create_draft_agreement(draft_agreement_data)
    agreement_manager.submit_for_approval(agreement_id, Role.STORE.value)
    
    yield agreement_id
    # Cleanup
    try:
        agreement_manager.agreements_collection.document(agreement_id).delete()
    except Exception as e:
        logger.warn(f"Cleanup failed for agreement {agreement_id}: {e}")


@pytest.fixture
def created_approved_agreement(db, draft_agreement_data_producer_first, user_with_root_account):
    """Creates an agreement in APPROVED state."""
    auth_context = AuthContext(user_id=user_with_root_account.uid)
    agreement_manager = AgreementManager(db, auth_context)
    agreement_id = agreement_manager.create_draft_agreement(draft_agreement_data_producer_first)
    agreement_manager.submit_for_approval(agreement_id, Role.PRODUCER.value)
    agreement_manager.approve_agreement(agreement_id, Role.STORE, user_with_root_account.uid)
    
    yield agreement_id
    # Cleanup
    try:
        agreement_manager.agreements_collection.document(agreement_id).delete()
        # delete the partnership
        db.collection('partnerships').where('agreementId', '==', agreement_id)\
            .get()[0].reference.delete()
    except Exception as e:
        logger.warn(f"Cleanup failed for agreement {agreement_id}: {e}")


@pytest.fixture
def created_active_agreement(db, draft_agreement_data, user_with_root_account):
    """Creates an agreement in ACTIVE state with a linked partnership."""
    auth_context = AuthContext(user_id=user_with_root_account.uid)
    agreement_manager = AgreementManager(db, auth_context)
    agreement_id = agreement_manager.create_draft_agreement(
        draft_agreement_data
    )
    agreement_manager.submit_for_approval(agreement_id, Role.STORE.value)
    agreement_manager.approve_agreement(
        agreement_id, 
        Role.PRODUCER.value, 
        user_with_root_account.uid
    )
    
    yield agreement_id
    # Cleanup
    try:
        # Get partnership ID for cleanup
        agreement_doc = agreement_manager.agreements_collection.document(agreement_id).get()
        if agreement_doc.exists:
            agreement_data = agreement_doc.to_dict()
            partnership_id = agreement_data.get('partnershipId')
            
            # Delete partnership if exists
            if partnership_id:
                db.collection('partnerships').document(partnership_id).delete()
        
        # Delete agreement
        agreement_manager.agreements_collection.document(agreement_id).delete()

        # delete the partnership
        db.collection('partnerships').where('agreementId', '==', agreement_id)\
        .get()[0].reference.delete()
    except Exception as e:
        logger.warn(f"Cleanup failed for agreement {agreement_id}: {e}")


@pytest.fixture
def created_expired_agreement(db, draft_agreement_data, user_with_root_account):
    """Creates an agreement in EXPIRED state."""
    # Modify draft data to have past dates
    past_start = datetime.now(timezone.utc) - timedelta(days=366)
    past_end = datetime.now(timezone.utc) - timedelta(days=1)
    
    draft_agreement_data.effective_date = past_start
    draft_agreement_data.expiration_date = past_end
    
    auth_context = AuthContext(user_id=user_with_root_account.uid)
    agreement_manager = AgreementManager(db, auth_context)
    agreement_id = agreement_manager.create_draft_agreement(draft_agreement_data)
    
    # Move through states to ACTIVE
    agreement_manager.submit_for_approval(agreement_id, Role.STORE.value)
    agreement_manager.approve_agreement(agreement_id, Role.PRODUCER, user_with_root_account.uid)
    
    # Set to EXPIRED
    agreement_ref = agreement_manager.agreements_collection.document(agreement_id)
    agreement_ref.update({
        Agreement.STATUS_FIELD: AgreementStatus.EXPIRED.value,
        Agreement.UPDATED_BY_FIELD: Role.SYSTEM.value,
        Agreement.UPDATED_AT_FIELD: datetime.now(timezone.utc)
    })
    
    yield agreement_id
    # Cleanup
    try:
        # Get partnership ID for cleanup
        agreement_doc = agreement_manager.agreements_collection.document(agreement_id).get()
        if agreement_doc.exists:
            agreement_data = agreement_doc.to_dict()
            partnership_id = agreement_data.get('partnershipId')
            
            # Delete partnership if exists
            partner_manager = PartnershipManager(db)
            partner_manager.delete_partnership_and_agreement(partnership_id)
        
        # Delete agreement
        agreement_manager.agreements_collection.document(agreement_id).delete()
    except Exception as e:
        logger.warn(f"Cleanup failed for agreement {agreement_id}: {e}")


@pytest.fixture
def created_terminated_agreement(db, draft_agreement_data, user_with_root_account):
    """Creates an agreement in TERMINATED state."""
    auth_context = AuthContext(user_id=user_with_root_account.uid)
    agreement_manager = AgreementManager(db, auth_context)
    agreement_id = agreement_manager.create_draft_agreement(draft_agreement_data)
    
    # Move through states to ACTIVE
    agreement_manager.submit_for_approval(agreement_id, Role.STORE.value)
    agreement_manager.approve_agreement(agreement_id, Role.PRODUCER)
    
    # Terminate it
    agreement_manager.terminate_agreement(agreement_id)
    
    yield agreement_id
    # Cleanup
    try:
        # Get partnership ID for cleanup
        agreement_doc = agreement_manager.agreements_collection.document(agreement_id).get()
        if agreement_doc.exists:
            agreement_data = agreement_doc.to_dict()
            partnership_id = agreement_data.get('partnershipId')
            
            # Delete partnership if exists
            partner_manager = PartnershipManager(db)
            partner_manager.delete_partnership_and_agreement(partnership_id)
        
        # Delete agreement
        agreement_manager.agreements_collection.document(agreement_id).delete()
    except Exception as e:
        logger.warn(f"Cleanup failed for agreement {agreement_id}: {e}")


# -------------------- Test Cases: Draft State --------------------

def test_create_draft_agreement(db, draft_agreement_data, user_with_root_account):
    """Test creating a valid draft agreement."""
    auth_context = AuthContext(user_id=user_with_root_account.uid)
    agreement_manager = AgreementManager(db, auth_context)
    agreement_id = agreement_manager.create_draft_agreement(draft_agreement_data)
    assert agreement_id is not None

    # Verify in Firestore
    doc_ref = db.collection(agreement_manager.agreements_collection.id).document(agreement_id)
    doc = doc_ref.get()
    assert doc.exists
    data = Agreement.model_validate(doc.to_dict())

    assert data.store_id == draft_agreement_data.store_id
    assert data.producer_id == draft_agreement_data.producer_id
    assert data.title == draft_agreement_data.title
    assert data.commission == draft_agreement_data.commission
    assert data.created_by == user_with_root_account.uid
    assert data.status == AgreementStatus.DRAFT.value
    assert len(data.approval_workflow) == 2 # Store and Producer steps
    assert any(step.status == ApprovalStatus.PENDING.value for step in data.approval_workflow)

    # Cleanup
    doc_ref.delete()


def test_create_draft_agreement_with_no_expiration(
        db, 
        draft_agreement_data_with_no_expiration, 
        user_with_root_account
    ):
    """Test creating a draft agreement with no expiration date."""
    auth_context = AuthContext(user_id=user_with_root_account.uid)
    agreement_manager = AgreementManager(db, auth_context)
    agreement_id = agreement_manager.create_draft_agreement(
        draft_agreement_data_with_no_expiration
    )
    assert agreement_id is not None

    # Verify in Firestore
    doc_ref = db.collection(agreement_manager.agreements_collection.id).document(agreement_id)
    doc = doc_ref.get()
    assert doc.exists
    data = Agreement.model_validate(doc.to_dict())
    
    assert data.expiration_date is None


def test_update_draft_agreement(db, created_draft_agreement, user_with_root_account):
    """Test updating fields of a draft agreement."""
    update_payload = {
        Agreement.TITLE_FIELD: "Updated Test Title",
        Agreement.COMMISSION_FIELD: 25,
        Agreement.DOCUMENT_URL_FIELD: "http://example.com/new_doc.pdf"
    }
    auth_context = AuthContext(user_id=user_with_root_account.uid)
    agreement_manager = AgreementManager(db, auth_context)
    result = agreement_manager.update_agreement(
      created_draft_agreement, 
      update_payload
    )
    assert result["success"] is True

    # Verify in Firestore
    doc = db.collection(agreement_manager.agreements_collection.id).document(created_draft_agreement).get()
    data = Agreement.model_validate(doc.to_dict())
    assert data.title == update_payload[Agreement.TITLE_FIELD]
    assert data.commission == update_payload[Agreement.COMMISSION_FIELD]
    assert data.document_url == update_payload[Agreement.DOCUMENT_URL_FIELD]
    assert data.updated_by == user_with_root_account.uid
    assert data.updated_at is not None


# -------------------- Test Cases: Negotiation State --------------------

@pytest.mark.skip(reason="Not implemented")
def test_submit_for_negotiation(db, created_draft_agreement, user_with_root_account):
    """Test submitting a draft agreement for negotiation."""
    auth_context = AuthContext(user_id=user_with_root_account.uid)
    agreement_manager = AgreementManager(db, auth_context)
    
    # Update implementation to add this function if it doesn't exist
    agreement_ref = agreement_manager.agreements_collection.document(created_draft_agreement)
    agreement_ref.update({
        Agreement.STATUS_FIELD: AgreementStatus.NEGOTIATION.value,
        Agreement.UPDATED_BY_FIELD: user_with_root_account.uid,
        Agreement.UPDATED_AT_FIELD: datetime.now(timezone.utc)
    })
    
    # Verify status change in Firestore
    doc = agreement_ref.get()
    data = Agreement.model_validate(doc.to_dict())
    assert data.status == AgreementStatus.NEGOTIATION.value
    assert data.updated_by == user_with_root_account.uid

@pytest.mark.skip(reason="Not implemented")
def test_propose_changes_in_negotiation(db, created_negotiation_agreement, user_with_root_account):
    """Test proposing changes to an agreement in negotiation."""
    auth_context = AuthContext(user_id=user_with_root_account.uid)
    agreement_manager = AgreementManager(db, auth_context)
    agreement_ref = agreement_manager.agreements_collection.document(created_negotiation_agreement)
    
    # Simulate propose_changes function
    changes = {
        Agreement.TITLE_FIELD: "Negotiated Title",
        Agreement.COMMISSION_FIELD: 22
    }
    comments = "Proposing a change to commission rate"
    
    # First, add negotiation_history field if it doesn't exist
    agreement_doc = agreement_ref.get()
    agreement_data = agreement_doc.to_dict()
    negotiation_history = agreement_data.get(Agreement.NEGOTIATION_HISTORY_FIELD, [])
    
    # Add new negotiation entry
    negotiation_change = {
        NegotiationChange.USER_ID_FIELD: user_with_root_account.uid,
        NegotiationChange.ROLE_FIELD: Role.STORE.value,
        NegotiationChange.TIMESTAMP_FIELD: datetime.now(timezone.utc),
        NegotiationChange.CHANGES_FIELD: changes,
        NegotiationChange.COMMENTS_FIELD: comments
    }
    negotiation_history.append(negotiation_change)
    
    # Update the agreement with changes
    update_data = {
        Agreement.NEGOTIATION_HISTORY_FIELD: negotiation_history,
        Agreement.UPDATED_BY_FIELD: user_with_root_account.uid,
        Agreement.UPDATED_AT_FIELD: datetime.now(timezone.utc)
    }
    
    # Add the actual changes to fields
    for field, value in changes.items():
        update_data[field] = value
    
    agreement_ref.update(update_data)
    
    # Verify changes in Firestore
    doc = agreement_ref.get()
    data = doc.to_dict()

    assert data[Agreement.TITLE_FIELD] == changes[Agreement.TITLE_FIELD]
    assert data[Agreement.COMMISSION_FIELD] == changes[Agreement.COMMISSION_FIELD]
    assert len(data[Agreement.NEGOTIATION_HISTORY_FIELD]) == 1
    assert data[Agreement.NEGOTIATION_HISTORY_FIELD][0][NegotiationChange.COMMENTS_FIELD] == comments

@pytest.mark.skip(reason="Not implemented")
def test_accept_negotiation_changes(db, created_negotiation_agreement, user_with_root_account):
    """Test accepting negotiated changes and moving to approval phase."""
    auth_context = AuthContext(user_id=user_with_root_account.uid)
    agreement_manager = AgreementManager(db, auth_context)
    agreement_ref = agreement_manager.agreements_collection.document(created_negotiation_agreement)
    
    # Simulate accept_changes function by adding final negotiation entry and changing status
    agreement_doc = agreement_ref.get()
    agreement_data = agreement_doc.to_dict()
    negotiation_history = agreement_data.get(Agreement.NEGOTIATION_HISTORY_FIELD, [])
    
    negotiation_change = {
        NegotiationChange.USER_ID_FIELD: user_with_root_account.uid,
        NegotiationChange.ROLE_FIELD: Role.PRODUCER.value,
        NegotiationChange.TIMESTAMP_FIELD: datetime.now(timezone.utc),
        NegotiationChange.CHANGES_FIELD: {},
        NegotiationChange.COMMENTS_FIELD: "Accepting current agreement terms"
    }
    negotiation_history.append(negotiation_change)
    
    # Update to move to pending approval
    agreement_ref.update({
        Agreement.NEGOTIATION_HISTORY_FIELD: negotiation_history,
        Agreement.STATUS_FIELD: AgreementStatus.PENDING_APPROVAL.value,
        Agreement.UPDATED_BY_FIELD: user_with_root_account.uid,
        Agreement.UPDATED_AT_FIELD: datetime.now(timezone.utc)
    })
    
    # Verify status change in Firestore
    doc = agreement_ref.get()
    data = doc.to_dict()
    assert data[Agreement.STATUS_FIELD] == AgreementStatus.PENDING_APPROVAL.value
    assert len(data[Agreement.NEGOTIATION_HISTORY_FIELD]) > 0
    assert data[Agreement.NEGOTIATION_HISTORY_FIELD][-1][NegotiationChange.COMMENTS_FIELD] == "Accepting current agreement terms"


# -------------------- Test Cases: Approval State --------------------

def test_store_submit_for_approval(db, created_draft_agreement, user_with_root_account):
    """Test submitting a draft agreement for approval."""
    auth_context = AuthContext(user_id=user_with_root_account.uid)
    agreement_manager = AgreementManager(db, auth_context)
    result = agreement_manager.submit_for_approval(
        created_draft_agreement, 
        Role.STORE.value
    )
    assert result["success"] is True

    # Verify status change in Firestore
    doc = db.collection(agreement_manager.agreements_collection.id)\
        .document(created_draft_agreement).get()
    data = Agreement.model_validate(doc.to_dict())
    assert data.status == AgreementStatus.PENDING_APPROVAL.value
    assert data.updated_by == user_with_root_account.uid


def test_producer_submit_for_approval(db, created_draft_agreement_producer_first, user_with_root_account):
    """Test submitting a draft agreement for approval."""
    auth_context = AuthContext(user_id=user_with_root_account.uid)
    agreement_manager = AgreementManager(db, auth_context)
    result = agreement_manager.submit_for_approval(
        created_draft_agreement_producer_first, 
        Role.PRODUCER.value
    )
    assert result["success"] is True

    # Verify status change in Firestore
    doc = db.collection(agreement_manager.agreements_collection.id)\
        .document(created_draft_agreement_producer_first).get()
    data = Agreement.model_validate(doc.to_dict())
    assert data.status == AgreementStatus.PENDING_APPROVAL.value
    assert data.updated_by == user_with_root_account.uid


def test_approve_agreement_store_first(db, created_pending_approval_agreement, user_with_root_account):
    """Test store approving a pending agreement."""
    auth_context = AuthContext(user_id=user_with_root_account.uid)
    agreement_manager = AgreementManager(db, auth_context)

    with pytest.raises(ConflictError):
        agreement_manager.approve_agreement(
            agreement_id = created_pending_approval_agreement, 
            role = Role.STORE.value, 
            comments = "Store approves"
        )


    # Verify workflow step and overall status in Firestore
    doc = db.collection(agreement_manager.agreements_collection.id)\
        .document(created_pending_approval_agreement).get()
    data = Agreement.model_validate(doc.to_dict())
    assert data.status == AgreementStatus.PENDING_APPROVAL.value # Still pending
    store_step = next(s for s in data.approval_workflow if s.role == Role.STORE)
    assert store_step.status == ApprovalStatus.APPROVED.value
    assert store_step.approver_id == user_with_root_account.uid
    producer_step = next(s for s in data.approval_workflow if s.role == Role.PRODUCER)
    assert producer_step.status == ApprovalStatus.PENDING.value


def test_approve_agreement_fully(db, created_pending_approval_agreement, user_with_root_account):
    """Test full approval cycle."""
    auth_context = AuthContext(user_id=user_with_root_account.uid)
    agreement_manager = AgreementManager(db, auth_context)

    # Producer approves
    result = agreement_manager.approve_agreement(
        agreement_id = created_pending_approval_agreement, 
        role = Role.PRODUCER, 
        comments = "Producer OK"
    )
    assert result["success"] is True
    assert result["status"] == "approved"

    # Check that partnership was created
    partnership_doc = db.collection('partnerships')\
        .where('agreementId', '==', created_pending_approval_agreement).get()

    assert len(partnership_doc) == 1
    partnership_data = partnership_doc[0].to_dict()
    assert partnership_data['status'] == PartnershipStatus.ACTIVE.value

    # Verify final status and both steps in Firestore
    doc = db.collection(agreement_manager.agreements_collection.id)\
        .document(created_pending_approval_agreement).get()

    data = Agreement.model_validate(doc.to_dict())

    assert data.status == AgreementStatus.ACTIVE.value
    assert all(s.status == ApprovalStatus.APPROVED.value for s in data.approval_workflow)
    assert data.updated_by == user_with_root_account.uid # Last updater

    # Delete the partnership
    db.collection('partnerships').document(partnership_doc[0].id).delete()



# -------------------- Test Cases: Activation --------------------

def test_activate_agreement(db, created_approved_agreement, user_with_root_account):
    """Test activating an approved agreement."""
    auth_context = AuthContext(user_id=user_with_root_account.uid)
    agreement_manager = AgreementManager(db, auth_context)


    # Verify status change in Firestore
    doc = db.collection(agreement_manager.agreements_collection.id)\
        .document(created_approved_agreement).get()
    data = Agreement.model_validate(doc.to_dict())
    assert data.status == AgreementStatus.ACTIVE.value
    assert data.updated_by == user_with_root_account.uid
    
    # Check that partnership was created
    assert data.partnership_id is not None

    partnership_doc = db.collection('partnerships').document(data.partnership_id).get()
    assert partnership_doc.exists
    partnership_data = partnership_doc.to_dict()
    assert partnership_data['status'] == PartnershipStatus.ACTIVE.value


def test_activate_agreement_with_pending_approval(db, created_pending_approval_agreement, user_with_root_account):
    """Test activating an agreement with pending approval."""
    auth_context = AuthContext(user_id=user_with_root_account.uid)
    agreement_manager = AgreementManager(db, auth_context)
    with pytest.raises(ConflictError):
        agreement_manager.activate_agreement(
            created_pending_approval_agreement
        )


def test_activate_agreement_with_draft(db, created_draft_agreement, user_with_root_account):
    """Test activating an agreement with draft status."""
    auth_context = AuthContext(user_id=user_with_root_account.uid)
    agreement_manager = AgreementManager(db, auth_context)
    with pytest.raises(ConflictError):
        agreement_manager.activate_agreement(
            created_draft_agreement
        )

# -------------------- Test Cases: Termination --------------------

def test_terminate_agreement(db, created_active_agreement, user_with_root_account):
    """Test terminating an active agreement."""

    partnership_doc = db.collection('partnerships')\
        .where('agreementId', '==', created_active_agreement).get()
    assert len(partnership_doc) == 1 # make sure that the partnership exists

    auth_context = AuthContext(user_id=user_with_root_account.uid)
    agreement_manager = AgreementManager(db, auth_context)
    result = agreement_manager.terminate_agreement(created_active_agreement)
    assert result["success"] is True

    # Verify status change in Firestore
    doc = db.collection(agreement_manager.agreements_collection.id)\
        .document(created_active_agreement).get()
    data = Agreement.model_validate(doc.to_dict())
    assert data.status == AgreementStatus.TERMINATED.value
    assert data.updated_by == user_with_root_account.uid
    
    # Check that partnership was terminated
    partnership_id = data.partnership_id
    assert partnership_id is not None
    partnership_doc = db.collection('partnerships').document(partnership_id).get()

    assert partnership_doc.exists == False


# -------------------- Test Cases: Renewal --------------------

def test_create_renewal_from_expired_agreement(db, created_expired_agreement, user_with_root_account):
    """Test creating a renewal from an expired agreement."""
    auth_context = AuthContext(user_id=user_with_root_account.uid)
    agreement_manager = AgreementManager(db, auth_context)
    
    # New dates for renewal
    new_effective_date = datetime.now(timezone.utc) + timedelta(days=1)
    new_expiration_date = datetime.now(timezone.utc) + timedelta(days=366)

    # Create renewal
    renewal_id = agreement_manager.create_renewal_draft(
        original_agreement_id = created_expired_agreement,
        new_effective_date = new_effective_date.isoformat(),
        new_expiration_date = new_expiration_date.isoformat()
    )
    
    assert renewal_id is not None
    
    # Verify renewal in Firestore
    renewal_doc = db.collection(agreement_manager.agreements_collection.id).document(renewal_id).get()
    assert renewal_doc.exists

    renewal_data = Agreement.model_validate(renewal_doc.to_dict())
    
    assert renewal_data.status == AgreementStatus.DRAFT.value
    assert renewal_data.title.startswith("Renewal:")
    assert renewal_data.renewed_by == created_expired_agreement
    assert renewal_data.version != "1.0"  # Should be incremented
    assert renewal_data.effective_date >= datetime.now(timezone.utc)
    assert renewal_data.created_by == user_with_root_account.uid
    
    # Verify original agreement has renewal reference
    original_doc = db.collection(agreement_manager.agreements_collection.id)\
        .document(created_expired_agreement).get()
    original_data = original_doc.to_dict()
    assert original_data.get(Agreement.RENEWED_BY_FIELD) == renewal_id
    
    # Cleanup
    db.collection(agreement_manager.agreements_collection.id).document(renewal_id).delete()


def test_create_renewal_from_terminated_agreement(db, created_terminated_agreement, user_with_root_account):
    """Test creating a renewal from a terminated agreement."""
    auth_context = AuthContext(user_id=user_with_root_account.uid)
    agreement_manager = AgreementManager(db, auth_context)
    
    # New dates for renewal
    new_effective_date = datetime.now(timezone.utc) + timedelta(days=1)
    new_expiration_date = datetime.now(timezone.utc) + timedelta(days=366)
    
    # Create renewal with custom changes
    custom_changes = {
        Agreement.TITLE_FIELD: "New Custom Renewal Title",
        Agreement.COMMISSION_FIELD: 30  # Different commission rate
    }
    
    renewal_id = agreement_manager.create_renewal_draft(
        original_agreement_id = created_terminated_agreement,
        new_effective_date = new_effective_date.isoformat(),
        new_expiration_date = new_expiration_date.isoformat(),
        custom_changes = custom_changes
    )
    
    assert renewal_id is not None
    
    # Verify renewal in Firestore with custom changes
    renewal_doc = db.collection(agreement_manager.agreements_collection.id).document(renewal_id).get()
    assert renewal_doc.exists
    renewal_data = Agreement.model_validate(renewal_doc.to_dict())
    
    assert renewal_data.status == AgreementStatus.DRAFT.value
    assert renewal_data.title == custom_changes[Agreement.TITLE_FIELD]
    assert renewal_data.commission == custom_changes[Agreement.COMMISSION_FIELD]
    assert renewal_data.renewed_by == created_terminated_agreement
    
    # Cleanup
    db.collection(agreement_manager.agreements_collection.id).document(renewal_id).delete()


def test_complete_renewal_lifecycle(db, created_expired_agreement, user_with_root_account):
    """Test the complete lifecycle of renewing an agreement to an active state."""
    auth_context = AuthContext(user_id=user_with_root_account.uid)
    agreement_manager = AgreementManager(db, auth_context)
    
    # 1. Create renewal
    new_effective_date = datetime.now(timezone.utc) + timedelta(days=1)
    new_expiration_date = datetime.now(timezone.utc) + timedelta(days=366)
    
    renewal_id = agreement_manager.create_renewal_draft(
        original_agreement_id = created_expired_agreement,
        new_effective_date = new_effective_date.isoformat(),
        new_expiration_date = new_expiration_date.isoformat()
    )
    
    # 2. Submit for approval
    agreement_manager.submit_for_approval(renewal_id, Role.STORE.value)
    
    # 3. Approve by both parties
    agreement_manager.approve_agreement(
        renewal_id, 
        Role.PRODUCER.value, 
        "Store approves renewal"
    )
    # Verify it's pending approval
    renewal_doc = db.collection(agreement_manager.agreements_collection.id).document(renewal_id).get()
    renewal_data = Agreement.model_validate(renewal_doc.to_dict())
    assert renewal_data.status == AgreementStatus.ACTIVE.value

    # 5. Check that partnership exists
    partnership_doc = db.collection('partnerships').document(renewal_data.partnership_id).get()
    assert partnership_doc.exists
    partnership_data = partnership_doc.to_dict()
    assert partnership_data['status'] == PartnershipStatus.ACTIVE.value
    
    # Cleanup
    db.collection('partnerships').document(renewal_data.partnership_id).delete()
    db.collection(agreement_manager.agreements_collection.id).document(renewal_id).delete()


def test_activate_agreement_with_old_active_agreement(
        db, 
        created_active_agreement, 
        user_with_root_account, 
        created_draft_agreement_data_with_no_expiration
    ):
    """Test activating an agreement with an old active agreement."""
    auth_context = AuthContext(user_id=user_with_root_account.uid)
    agreement_manager = AgreementManager(db, auth_context)

    agreement_doc = db.collection("agreements").document(created_active_agreement).get()
    agreement_data = Agreement.model_validate(agreement_doc.to_dict())
    assert agreement_data.status == AgreementStatus.ACTIVE.value

    draft_agreement_doc = db.collection("agreements")\
        .document(created_draft_agreement_data_with_no_expiration).get()
    draft_agreement_data = Agreement.model_validate(draft_agreement_doc.to_dict())
    assert draft_agreement_data.status == AgreementStatus.PENDING_APPROVAL.value


    agreement_manager.approve_agreement(
        agreement_id = created_draft_agreement_data_with_no_expiration, 
        role = Role.PRODUCER, 
        comments = "Producer approves renewal"
    )

    # check if the old active agreement is terminated
    old_active_agreement = db.collection(agreement_manager.agreements_collection.id)\
        .document(created_active_agreement).get()
    old_active_agreement.to_dict()
    old_active_agreement_data = Agreement.model_validate(old_active_agreement.to_dict())
    assert old_active_agreement_data.status == AgreementStatus.TERMINATED.value


def test_reject_agreement(db, created_pending_approval_agreement, user_with_root_account):
    """Test rejecting an agreement."""
    auth_context = AuthContext(user_id=user_with_root_account.uid)
    agreement_manager = AgreementManager(db, auth_context)

    agreement_manager.reject_agreement(
        agreement_id = created_pending_approval_agreement, 
        role = Role.PRODUCER.value, 
        comments = "Producer rejects"
    )

    # Verify status change in Firestore
    doc = db.collection(agreement_manager.agreements_collection.id)\
        .document(created_pending_approval_agreement).get()
    data = Agreement.model_validate(doc.to_dict())
    assert data.status == AgreementStatus.REJECTED.value



# -------------------- Test Cases: _update_agreement_status --------------------

def test_update_agreement_status_all_approved(db, user_with_root_account):
    """Test _update_agreement_status when all approval steps are approved."""
    auth_context = AuthContext(user_id=user_with_root_account.uid)
    agreement_manager = AgreementManager(db, auth_context)

    # Create approval workflow with all steps approved
    approval_workflow = [
        ApprovalStep(
            role=Role.STORE,
            approver_id=user_with_root_account.uid,
            status=ApprovalStatus.APPROVED.value,
            comments="Store approved",
            timestamp=datetime.now(timezone.utc)
        ),
        ApprovalStep(
            role=Role.PRODUCER,
            approver_id=user_with_root_account.uid,
            status=ApprovalStatus.APPROVED.value,
            comments="Producer approved",
            timestamp=datetime.now(timezone.utc)
        )
    ]

    # Create agreement with pending approval status
    agreement = Agreement(
        store_id="store123",
        producer_id="producer123",
        effective_date=datetime.now(timezone.utc),
        commission=15,
        status=AgreementStatus.PENDING_APPROVAL.value,
        approval_workflow=approval_workflow,
        created_by=user_with_root_account.uid,
        created_by_role=Role.STORE,
        created_at=datetime.now(timezone.utc)
    )

    # Test the function
    updated_agreement = agreement_manager._update_agreement_status(agreement)

    # Verify status is updated to APPROVED
    assert updated_agreement.status == AgreementStatus.APPROVED.value


def test_update_agreement_status_partial_approval(db, user_with_root_account):
    """Test _update_agreement_status when only some approval steps are approved."""
    auth_context = AuthContext(user_id=user_with_root_account.uid)
    agreement_manager = AgreementManager(db, auth_context)

    # Create approval workflow with one approved, one pending
    approval_workflow = [
        ApprovalStep(
            role=Role.STORE,
            approver_id=user_with_root_account.uid,
            status=ApprovalStatus.APPROVED.value,
            comments="Store approved",
            timestamp=datetime.now(timezone.utc)
        ),
        ApprovalStep(
            role=Role.PRODUCER,
            status=ApprovalStatus.PENDING.value
        )
    ]

    # Create agreement
    agreement = Agreement(
        store_id="store123",
        producer_id="producer123",
        effective_date=datetime.now(timezone.utc),
        commission=15,
        status=AgreementStatus.DRAFT.value,
        approval_workflow=approval_workflow,
        created_by=user_with_root_account.uid,
        created_by_role=Role.STORE,
        created_at=datetime.now(timezone.utc)
    )

    # Test the function
    updated_agreement = agreement_manager._update_agreement_status(agreement)

    # Verify status is updated to PENDING_APPROVAL
    assert updated_agreement.status == AgreementStatus.PENDING_APPROVAL.value


def test_update_agreement_status_rejected(db, user_with_root_account):
    """Test _update_agreement_status when any approval step is rejected."""
    auth_context = AuthContext(user_id=user_with_root_account.uid)
    agreement_manager = AgreementManager(db, auth_context)

    # Create approval workflow with one approved, one rejected
    approval_workflow = [
        ApprovalStep(
            role=Role.STORE,
            approver_id=user_with_root_account.uid,
            status=ApprovalStatus.APPROVED.value,
            comments="Store approved",
            timestamp=datetime.now(timezone.utc)
        ),
        ApprovalStep(
            role=Role.PRODUCER,
            approver_id=user_with_root_account.uid,
            status=ApprovalStatus.REJECTED.value,
            comments="Producer rejected",
            timestamp=datetime.now(timezone.utc)
        )
    ]

    # Create agreement
    agreement = Agreement(
        store_id="store123",
        producer_id="producer123",
        effective_date=datetime.now(timezone.utc),
        commission=15,
        status=AgreementStatus.PENDING_APPROVAL.value,
        approval_workflow=approval_workflow,
        created_by=user_with_root_account.uid,
        created_by_role=Role.STORE,
        created_at=datetime.now(timezone.utc)
    )

    # Test the function
    updated_agreement = agreement_manager._update_agreement_status(agreement)

    # Verify status is updated to REJECTED
    assert updated_agreement.status == AgreementStatus.REJECTED.value


def test_update_agreement_status_all_pending(db, user_with_root_account):
    """Test _update_agreement_status when all approval steps are pending."""
    auth_context = AuthContext(user_id=user_with_root_account.uid)
    agreement_manager = AgreementManager(db, auth_context)

    # Create approval workflow with all steps pending
    approval_workflow = [
        ApprovalStep(
            role=Role.STORE,
            status=ApprovalStatus.PENDING.value
        ),
        ApprovalStep(
            role=Role.PRODUCER,
            status=ApprovalStatus.PENDING.value
        )
    ]

    # Create agreement
    agreement = Agreement(
        store_id="store123",
        producer_id="producer123",
        effective_date=datetime.now(timezone.utc),
        commission=15,
        status=AgreementStatus.DRAFT.value,
        approval_workflow=approval_workflow,
        created_by=user_with_root_account.uid,
        created_by_role=Role.STORE,
        created_at=datetime.now(timezone.utc)
    )

    # Test the function
    updated_agreement = agreement_manager._update_agreement_status(agreement)

    # Verify status is updated to PENDING_APPROVAL
    assert updated_agreement.status == AgreementStatus.DRAFT.value


    """Test _update_agreement_status with empty approval workflow."""
    auth_context = AuthContext(user_id=user_with_root_account.uid)
    agreement_manager = AgreementManager(db, auth_context)

    # Create agreement with empty approval workflow
    agreement = Agreement(
        store_id="store123",
        producer_id="producer123",
        effective_date=datetime.now(timezone.utc),
        commission=15,
        status=AgreementStatus.DRAFT.value,
        approval_workflow=[],  # Empty workflow
        created_by=user_with_root_account.uid,
        created_by_role=Role.STORE,
        created_at=datetime.now(timezone.utc)
    )

    # Test the function
    updated_agreement = agreement_manager._update_agreement_status(agreement)

    # Verify status remains unchanged when workflow is empty
    assert updated_agreement.status == AgreementStatus.DRAFT.value


def test_update_agreement_status_single_step_approved(db, user_with_root_account):
    """Test _update_agreement_status with single approval step that is approved."""
    auth_context = AuthContext(user_id=user_with_root_account.uid)
    agreement_manager = AgreementManager(db, auth_context)

    # Create approval workflow with single approved step
    approval_workflow = [
        ApprovalStep(
            role=Role.STORE,
            approver_id=user_with_root_account.uid,
            status=ApprovalStatus.APPROVED.value,
            comments="Store approved",
            timestamp=datetime.now(timezone.utc)
        )
    ]

    # Create agreement
    agreement = Agreement(
        store_id="store123",
        producer_id="producer123",
        effective_date=datetime.now(timezone.utc),
        commission=15,
        status=AgreementStatus.PENDING_APPROVAL.value,
        approval_workflow=approval_workflow,
        created_by=user_with_root_account.uid,
        created_by_role=Role.STORE,
        created_at=datetime.now(timezone.utc)
    )

    # Test the function
    with pytest.raises(ConflictError):
        agreement_manager._update_agreement_status(agreement)


def test_update_agreement_status_mixed_statuses_with_rejection(db, user_with_root_account):
    """Test _update_agreement_status with mixed statuses including rejection (rejection takes priority)."""
    auth_context = AuthContext(user_id=user_with_root_account.uid)
    agreement_manager = AgreementManager(db, auth_context)

    # Create approval workflow with mixed statuses including rejection
    approval_workflow = [
        ApprovalStep(
            role=Role.STORE,
            approver_id=user_with_root_account.uid,
            status=ApprovalStatus.APPROVED.value,
            comments="Store approved",
            timestamp=datetime.now(timezone.utc)
        ),
        ApprovalStep(
            role=Role.PRODUCER,
            approver_id=user_with_root_account.uid,
            status=ApprovalStatus.REJECTED.value,
            comments="Producer rejected",
            timestamp=datetime.now(timezone.utc)
        )
    ]

    # Create agreement
    agreement = Agreement(
        store_id="store123",
        producer_id="producer123",
        effective_date=datetime.now(timezone.utc),
        commission=15,
        status=AgreementStatus.PENDING_APPROVAL.value,
        approval_workflow=approval_workflow,
        created_by=user_with_root_account.uid,
        created_by_role=Role.STORE,
        created_at=datetime.now(timezone.utc)
    )

    # Test the function
    updated_agreement = agreement_manager._update_agreement_status(agreement)

    # Verify status is updated to REJECTED (rejection takes priority over approval)
    assert updated_agreement.status == AgreementStatus.REJECTED.value


def test_update_agreement_status_preserves_other_fields(db, user_with_root_account):
    """Test that _update_agreement_status only modifies the status field and preserves other fields."""
    auth_context = AuthContext(user_id=user_with_root_account.uid)
    agreement_manager = AgreementManager(db, auth_context)

    # Create approval workflow with all steps approved
    approval_workflow = [
        ApprovalStep(
            role=Role.STORE,
            approver_id=user_with_root_account.uid,
            status=ApprovalStatus.APPROVED.value,
            comments="Store approved",
            timestamp=datetime.now(timezone.utc)
        ),
        ApprovalStep(
            role=Role.PRODUCER,
            approver_id=user_with_root_account.uid,
            status=ApprovalStatus.APPROVED.value,
            comments="Producer approved",
            timestamp=datetime.now(timezone.utc)
        )
    ]

    original_title = "Original Agreement Title"
    original_commission = 20
    original_document_url = "https://example.com/doc.pdf"
    original_created_at = datetime.now(timezone.utc) - timedelta(days=1)

    # Create agreement with specific field values
    agreement = Agreement(
        store_id="store123",
        producer_id="producer123",
        effective_date=datetime.now(timezone.utc),
        commission=original_commission,
        status=AgreementStatus.PENDING_APPROVAL.value,
        title=original_title,
        document_url=original_document_url,
        approval_workflow=approval_workflow,
        created_by=user_with_root_account.uid,
        created_by_role=Role.STORE,
        created_at=original_created_at
    )

    # Test the function
    updated_agreement = agreement_manager._update_agreement_status(agreement)

    # Verify status is updated
    assert updated_agreement.status == AgreementStatus.APPROVED.value

    # Verify other fields are preserved
    assert updated_agreement.title == original_title
    assert updated_agreement.commission == original_commission
    assert updated_agreement.document_url == original_document_url
    assert updated_agreement.created_at == original_created_at
    assert updated_agreement.store_id == "store123"
    assert updated_agreement.producer_id == "producer123"
    assert updated_agreement.approval_workflow == approval_workflow


def test_update_agreement_status_priority_order(db, user_with_root_account):
    """Test the priority order: all approved > any approved > any rejected > any pending."""
    auth_context = AuthContext(user_id=user_with_root_account.uid)
    agreement_manager = AgreementManager(db, auth_context)

    # Test case 1: Any rejected takes priority over any approved
    approval_workflow_rejected = [
        ApprovalStep(role=Role.STORE, status=ApprovalStatus.APPROVED.value),
        ApprovalStep(role=Role.PRODUCER, status=ApprovalStatus.REJECTED.value)
    ]

    agreement_rejected = Agreement(
        store_id="store123",
        producer_id="producer123",
        effective_date=datetime.now(timezone.utc),
        commission=15,
        status=AgreementStatus.PENDING_APPROVAL.value,
        approval_workflow=approval_workflow_rejected,
        created_by=user_with_root_account.uid,
        created_by_role=Role.STORE,
        created_at=datetime.now(timezone.utc)
    )

    updated_rejected = agreement_manager._update_agreement_status(agreement_rejected)
    assert updated_rejected.status == AgreementStatus.REJECTED.value

    # Test case 2: Any approved takes priority over any pending
    approval_workflow_partial = [
        ApprovalStep(role=Role.STORE, status=ApprovalStatus.APPROVED.value),
        ApprovalStep(role=Role.PRODUCER, status=ApprovalStatus.PENDING.value)
    ]

    agreement_partial = Agreement(
        store_id="store123",
        producer_id="producer123",
        effective_date=datetime.now(timezone.utc),
        commission=15,
        status=AgreementStatus.DRAFT.value,
        approval_workflow=approval_workflow_partial,
        created_by=user_with_root_account.uid,
        created_by_role=Role.STORE,
        created_at=datetime.now(timezone.utc)
    )

    updated_partial = agreement_manager._update_agreement_status(agreement_partial)
    assert updated_partial.status == AgreementStatus.PENDING_APPROVAL.value


def test_update_agreement_status_multiple_calls_idempotent(db, user_with_root_account):
    """Test that calling _update_agreement_status multiple times with same data is idempotent."""
    auth_context = AuthContext(user_id=user_with_root_account.uid)
    agreement_manager = AgreementManager(db, auth_context)

    # Create approval workflow with all steps approved
    approval_workflow = [
        ApprovalStep(
            role=Role.STORE,
            approver_id=user_with_root_account.uid,
            status=ApprovalStatus.APPROVED.value,
            comments="Store approved",
            timestamp=datetime.now(timezone.utc)
        ),
        ApprovalStep(
            role=Role.PRODUCER,
            approver_id=user_with_root_account.uid,
            status=ApprovalStatus.APPROVED.value,
            comments="Producer approved",
            timestamp=datetime.now(timezone.utc)
        )
    ]

    # Create agreement
    agreement = Agreement(
        store_id="store123",
        producer_id="producer123",
        effective_date=datetime.now(timezone.utc),
        commission=15,
        status=AgreementStatus.PENDING_APPROVAL.value,
        approval_workflow=approval_workflow,
        created_by=user_with_root_account.uid,
        created_by_role=Role.STORE,
        created_at=datetime.now(timezone.utc)
    )

    # Call the function multiple times
    updated_agreement_1 = agreement_manager._update_agreement_status(agreement)
    updated_agreement_2 = agreement_manager._update_agreement_status(updated_agreement_1)
    updated_agreement_3 = agreement_manager._update_agreement_status(updated_agreement_2)

    # Verify all calls produce the same result
    assert updated_agreement_1.status == AgreementStatus.APPROVED.value
    assert updated_agreement_2.status == AgreementStatus.APPROVED.value
    assert updated_agreement_3.status == AgreementStatus.APPROVED.value

    # Verify the agreement objects are equivalent
    assert updated_agreement_1.model_dump() == updated_agreement_2.model_dump()
    assert updated_agreement_2.model_dump() == updated_agreement_3.model_dump()


def test_update_agreement_status_with_different_roles(db, user_with_root_account):
    """Test _update_agreement_status with different role combinations in approval workflow."""
    auth_context = AuthContext(user_id=user_with_root_account.uid)
    agreement_manager = AgreementManager(db, auth_context)

    # Test with only STORE role approved
    approval_workflow_store_only = [
        ApprovalStep(
            role=Role.STORE,
            approver_id=user_with_root_account.uid,
            status=ApprovalStatus.APPROVED.value,
            comments="Store approved",
            timestamp=datetime.now(timezone.utc)
        )
    ]

    agreement_store_only = Agreement(
        store_id="store123",
        producer_id="producer123",
        effective_date=datetime.now(timezone.utc),
        commission=15,
        status=AgreementStatus.DRAFT.value,
        approval_workflow=approval_workflow_store_only,
        created_by=user_with_root_account.uid,
        created_by_role=Role.STORE,
        created_at=datetime.now(timezone.utc)
    )

    with pytest.raises(ConflictError):
        agreement_manager._update_agreement_status(agreement_store_only)

    # Test with only PRODUCER role approved
    approval_workflow_producer_only = [
        ApprovalStep(
            role=Role.PRODUCER,
            approver_id=user_with_root_account.uid,
            status=ApprovalStatus.APPROVED.value,
            comments="Producer approved",
            timestamp=datetime.now(timezone.utc)
        )
    ]

    agreement_producer_only = Agreement(
        store_id="store123",
        producer_id="producer123",
        effective_date=datetime.now(timezone.utc),
        commission=15,
        status=AgreementStatus.DRAFT.value,
        approval_workflow=approval_workflow_producer_only,
        created_by=user_with_root_account.uid,
        created_by_role=Role.PRODUCER,
        created_at=datetime.now(timezone.utc)
    )

    with pytest.raises(ConflictError):
        agreement_manager._update_agreement_status(agreement_producer_only)
