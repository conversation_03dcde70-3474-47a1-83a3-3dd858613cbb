import pytest
import time
from time import sleep
from firebase_admin import auth
from services.producer_manager import ProducerManager
from services.user_root_account_manager import UserRootAccountManager
from models.requests.producer_requests import CreateProducerRequest, DeleteProducerRequest
from models.producer import Producer
from gaco_framework.auth import AuthContext
from gaco_framework.exceptions import ConflictError, NotFoundError

@pytest.fixture
def test_user(firebase_app):
    # Create a test user in Firebase Auth
    try:
        # Generate a unique email for each test run to avoid conflicts
        unique_timestamp = int(time.time())
        email = f'test-integration-{unique_timestamp}@example.com'
        
        # Create the user in Firebase Auth
        user = auth.create_user(
            email=email,
            password='testpassword123',
            display_name='Test Integration User'
        )
        
        # Verify the user was created by fetching it
        fetched_user = auth.get_user(user.uid)
        print(f"Created test user: {fetched_user.uid} with email: {fetched_user.email}")

        yield user
        
        # Cleanup: Delete the test user after tests
        try:
            auth.delete_user(user.uid)
            print(f"Deleted test user: {user.uid}")
        except auth.UserNotFoundError:
            print(f"User {user.uid} already deleted or not found")
        
    except Exception as e:
        print(f"Error in test_user fixture: {e}")
        raise


@pytest.fixture
def other_test_user(firebase_app):
    # Create a test user in Firebase Auth
    try:
        # Generate a unique email for each test run to avoid conflicts
        unique_timestamp = int(time.time())
        email = f'test-integration-other-user-{unique_timestamp}@example.com'
        
        # Create the user in Firebase Auth
        user = auth.create_user(
            email=email,
            password='testpassword123',
            display_name='Test Integration Other User'
        )
        
        # Verify the user was created by fetching it
        fetched_user = auth.get_user(user.uid)
        print(f"Created test user: {fetched_user.uid} with email: {fetched_user.email}")

        yield user
        
        # Cleanup: Delete the test user after tests
        try:
            auth.delete_user(user.uid)
            print(f"Deleted test user: {user.uid}")
        except auth.UserNotFoundError:
            print(f"User {user.uid} already deleted or not found")
        
    except Exception as e:
        print(f"Error in test_user fixture: {e}")
        raise


@pytest.fixture
def user_root_account_manager(db):
    return UserRootAccountManager(db)

@pytest.fixture
def producer_manager(db, user_root_account_manager, test_user):
    auth_context = AuthContext(user_id=test_user.uid)
    manager = ProducerManager(db, auth_context)
    # Set user_root_account_manager since it's used in the ProducerManager
    manager.user_root_account_manager = user_root_account_manager
    return manager

@pytest.fixture
def user_with_root_account(test_user, user_root_account_manager):
    # Create user root account for the test user
    pass
    yield test_user
    # Cleanup after test
    user_root_account_manager.delete_user_root_account(test_user.uid)

def test_create_producer(db, producer_manager, user_with_root_account):
    # Arrange
    unique_suffix = int(time.time())
    
    request = CreateProducerRequest(
        display_name=f"Test Producer {unique_suffix}",
        email=f"test-producer-{unique_suffix}@example.com",
        tax_a2="US"
    )

    sleep(15)
    
    # Act
    producer_id = producer_manager.create_producer(request)
    
    # Assert
    doc_snapshot = db.collection('producersV2').document(producer_id).get()
    assert doc_snapshot.exists, f"Producer document not found with ID: {producer_id}"
    
    data = doc_snapshot.to_dict()
    producer = Producer(**data)

    assert producer.display_name == request.display_name
    assert producer.email == request.email
    assert producer.tax_a2 == request.tax_a2
    
    # Check if producer was added to user's root account
    user_doc = db.collection('userRootAccounts').document(user_with_root_account.uid).get()
    user_data = user_doc.to_dict()
    assert producer_id in user_data['producers'], f"Producer {producer_id} not found in user's producers list"
    
    # Cleanup
    producer_manager.producer_collection.document(producer_id).delete()

def test_create_duplicate_producer(producer_manager, user_with_root_account):
    # Arrange - Create first producer
    # wait till root user account is created
    sleep(10)
    unique_suffix = int(time.time())
    
    request = CreateProducerRequest(
        display_name=f"Test Producer {unique_suffix}",
        email=f"test-producer-{unique_suffix}@example.com",
        tax_a2="US"
    )
    
    producer_id = producer_manager.create_producer(request)
    
    # Act & Assert - Attempt to create duplicate
    with pytest.raises(ConflictError) as exc_info:
        producer_manager.create_producer(request)
    assert "Producer with this email already exists" in str(exc_info.value)
    
    # Cleanup
    producer_manager.producer_collection.document(producer_id).delete()

def test_delete_producer(db, producer_manager, user_with_root_account):
    # Arrange - Create a producer first

    # wait till root user account is created
    sleep(10)
    unique_suffix = int(time.time())
    
    create_request = CreateProducerRequest(
        display_name=f"Test Producer {unique_suffix}",
        email=f"test-producer-{unique_suffix}@example.com",
        tax_a2="US"
    )
    
    producer_id = producer_manager.create_producer(create_request)
    
    # Verify the producer exists
    doc_snapshot = db.collection('producersV2').document(producer_id).get()
    assert doc_snapshot.exists, "Setup failed: Producer not created"
    
    # Act - Delete the producer
    delete_request = DeleteProducerRequest(producer_id=producer_id)
    producer_manager.delete_producer(delete_request)
    
    # Assert - Producer should be deleted
    doc_snapshot = db.collection('producersV2').document(producer_id).get()
    assert not doc_snapshot.exists, "Producer was not deleted successfully"
    
    # Check if producer was removed from user's root account
    user_doc = db.collection('userRootAccounts').document(user_with_root_account.uid).get()
    user_data = user_doc.to_dict()
    assert producer_id not in user_data['producers'], "Producer was not removed from user's root account"

@pytest.mark.skip(reason="This auth logic does not exist yet")
def test_delete_producer_with_unauthorized_user(producer_manager, user_with_root_account, other_test_user):
    # Arrange - Create a producer first
    unique_suffix = int(time.time())
    producer_id = f"non-existing-producer-id-{unique_suffix}"

    # Act & Assert - Attempt to delete with wrong user
    delete_request = DeleteProducerRequest(
        producer_id=producer_id
    )
    
    with pytest.raises(NotFoundError) as exc_info:
        producer_manager.delete_producer(delete_request)
    assert "Cannot delete producer, producer not found:" in str(exc_info.value)
    
    # Cleanup
    producer_manager.producer_collection.document(producer_id).delete()

def test_delete_nonexistent_producer(producer_manager, user_with_root_account):
    # Arrange
    nonexistent_producer_id = "nonexistent-producer-id"
    
    # Act & Assert
    delete_request = DeleteProducerRequest(
        producer_id=nonexistent_producer_id
    )
    
    with pytest.raises(Exception):
        producer_manager.delete_producer(delete_request, user_with_root_account.uid)

@pytest.mark.skip(reason="Invoice function is temporairy removed")
def test_delete_producer_with_invoices(mocker, producer_manager, user_with_root_account):
    # Arrange - Create a producer first
    sleep(10)
    unique_suffix = int(time.time())
    
    create_request = CreateProducerRequest(
        display_name=f"Test Producer {unique_suffix}",
        email=f"test-producer-{unique_suffix}@example.com",
        tax_a2="US"
    )
    
    producer_id = producer_manager.create_producer(create_request)
    
    # Mock the invoice manager to return that this producer has invoices
    mocker.patch.object(
        producer_manager.invoice_manager,
        'find_invoice_by_producer_id',
        return_value=(True, {"mock": "data"}, "")
    )
    
    # Act & Assert - Should not allow deletion of producer with invoices
    delete_request = DeleteProducerRequest(
        producer_id=producer_id
    )
    
    with pytest.raises(ValueError) as exc_info:
        producer_manager.delete_producer(delete_request)
    assert "Cannot delete producer, producer_id is linked to an invoice" in str(exc_info.value)
    
    # Cleanup
    # Reset the mock and directly delete the producer
    mocker.patch.object(
        producer_manager.invoice_manager,
        'find_invoice_by_producer_id',
        return_value=(False, {}, "")
    )
    producer_manager.producer_collection.document(producer_id).delete()


def test_delete_all_test_producers(db):
    # Arrange - Create a producer first
    producers = db.collection('producersV2').get()
    for producer in producers:
        producer_data = producer.to_dict()
        if 'email' in producer_data and producer_data['email'].startswith('test-'):
            db.collection('producersV2').document(producer.id).delete()
