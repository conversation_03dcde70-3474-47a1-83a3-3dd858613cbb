import pytest
import time
from firebase_admin import auth
from gaco_framework.exceptions import NotFoundError, ConflictError
from services.store_manager import StoreManager
from services.user_root_account_manager import UserRootAccountManager
from models.requests.store_requests import CreateStoreRequest, DeleteStoreRequest, ShopifyStoreCredentials
from models.store import StoreV2
from models.payout_account import Address
from gaco_secrets.secret_manager import SecretManager
from constants.gaco_values import project_id
from time import sleep
from gaco_framework.auth import AuthContext

@pytest.fixture
def test_user(firebase_app):
    # Create a test user in Firebase Auth
    try:
        # Generate a unique email for each test run to avoid conflicts
        unique_timestamp = int(time.time())
        email = f'test-integration-{unique_timestamp}@example.com'
        
        # Create the user in Firebase Auth
        user = auth.create_user(
            email=email,
            password='testpassword123',
            display_name='Test Integration User'
        )
        
        # Verify the user was created by fetching it
        fetched_user = auth.get_user(user.uid)
        print(f"Created test user: {fetched_user.uid} with email: {fetched_user.email}")

        yield user
        
        # Cleanup: Delete the test user after tests
        try:
            auth.delete_user(user.uid)
            print(f"Deleted test user: {user.uid}")
        except auth.UserNotFoundError:
            print(f"User {user.uid} already deleted or not found")
        
    except Exception as e:
        print(f"Error in test_user fixture: {e}")
        raise

@pytest.fixture
def user_root_account_manager(db):
    return UserRootAccountManager(db)

@pytest.fixture
def store_manager(db, auth_context):
    """Fixture to provide a StoreManager with an authenticated context."""
    return StoreManager(db, auth_context=auth_context)

@pytest.fixture
def user_with_root_account(test_user, user_root_account_manager):
    # Create user root account for the test user
    pass
    yield test_user
    # Cleanup after test
    user_root_account_manager.delete_user_root_account(test_user.uid)

def test_create_store(db, store_manager, user_with_root_account):
    # Arrange
    unique_suffix = int(time.time())
    
    shopify_credentials = ShopifyStoreCredentials(
        shop_name=f"test-shop-{unique_suffix}",
        shopify_api_key="test_api_key_12345"
    )
    
    request = CreateStoreRequest(
        display_name=f"Test Store {unique_suffix}",
        email=f"test-store-{unique_suffix}@example.com",
        tax_a2="US",
        default_commission=10,
        description="Test store description",
        shopify_api_key_object=shopify_credentials,
        address=Address(
            street_number="123",
            street_name="Test Street",
            zip_code="12345",
            city="Test City",
            country="US"
        )
    )
    
    # Act
    store_id = store_manager.create_store(request)

    # Assert
    store_doc_ref = db.collection('storesV2').document(store_id)
    store_doc = store_doc_ref.get()
    assert store_doc.exists, f"Store document {store_id} was not created."
    store_data = store_doc.to_dict()
    assert store_data['parentId'] == user_with_root_account.uid

    user_doc = db.collection('userRootAccounts').document(user_with_root_account.uid).get()
    assert store_id in user_doc.to_dict()['stores']
    
    # Cleanup
    delete_request = DeleteStoreRequest(store_id=store_id)
    store_manager.delete_store(delete_request)

def test_create_duplicate_store(store_manager, user_with_root_account):
    # Arrange - Create first store
    unique_suffix = int(time.time())
    
    shopify_credentials = ShopifyStoreCredentials(
        shop_name=f"test-shop-{unique_suffix}",
        shopify_api_key="test_api_key_12345"
    )
    
    request = CreateStoreRequest(
        display_name=f"Test Store {unique_suffix}",
        email=f"test-store-{unique_suffix}@example.com",
        tax_a2="US",
        default_commission=10,
        description="Test store description",
        shopify_api_key_object=shopify_credentials
    )
    
    store_id = store_manager.create_store(request)
    
    # Act & Assert - Attempt to create duplicate
    with pytest.raises(ConflictError, match="already exists"):
        store_manager.create_store(request)
    
    # Cleanup
    delete_request = DeleteStoreRequest(store_id=store_id)
    store_manager.delete_store(delete_request)

def test_delete_store(db, store_manager, user_with_root_account):
    # Arrange
    unique_suffix = int(time.time())
    
    create_request = CreateStoreRequest(
        display_name=f"Test Store {unique_suffix}",
        email=f"test-store-{unique_suffix}@example.com",
        tax_a2="US",
        default_commission=10,
        description="Test store description",
        shopify_api_key_object=ShopifyStoreCredentials(
            shop_name=f"test-shop-{unique_suffix}",
            shopify_api_key="test_api_key_12345"
        )
    )
    
    store_id = store_manager.create_store(create_request)
        
    # Act
    delete_request = DeleteStoreRequest(store_id=store_id)
    store_manager.delete_store(delete_request)
    
    # Assert
    doc_snapshot = db.collection('storesV2').document(store_id).get()
    assert not doc_snapshot.exists

    user_doc = db.collection('userRootAccounts').document(user_with_root_account.uid).get()
    assert store_id not in user_doc.to_dict().get('stores', {})

def test_delete_nonexistent_store(store_manager):
    # Arrange
    delete_request = DeleteStoreRequest(store_id="nonexistent-store-id")
    
    # Act & Assert
    with pytest.raises(NotFoundError, match="not found"):
        store_manager.delete_store(delete_request)

def test_hard_delete_store(
        db, test_store_NL, user_with_root_account, auth_context
    ):
    # Arrange
    store_manager = StoreManager(db, auth_context=auth_context)
    delete_request = DeleteStoreRequest(
        store_id=test_store_NL, 
        hard_delete=True
    )

    # Act
    store_manager.delete_store(delete_request)

    # Assert
    store_doc = db.collection('storesV2').document(test_store_NL).get()
    assert not store_doc.exists
