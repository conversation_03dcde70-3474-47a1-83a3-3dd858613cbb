import pytest
import uuid
from datetime import datetime, timezone
from time import sleep

from firebase_admin import firestore
from firebase_functions import logger

from models.user_root_account import UserRootAccount
from services.product_requester import ProductRequester
from services.product_manager import ProductManager, SHOPIFY_PRODUCT_GID_PREFIX, SHOPIFY_VARIANT_GID_PREFIX
from services.producer_manager import ProducerManager
from services.store_manager import StoreManager
from services.product_allocator import ProductAllocationManager
from gaco_framework.types import AuthContext
from gaco_framework.exceptions import AuthorizationError, NotFoundError, ValidationError

from models.allocations import (
    StoreProductRequest,
    ProductAllocationItem,
    ProductAllocation,
    store_product_requests_collection,
    ProductRequestStatus,
    AllocationStatus,
    DeliveryMethod,
    product_allocations_collection
)
from models.products import Product, ProductVariant

# If ProductRequestStatus is actually RequestStatus in your allocations model, use that:
try:
    from models.allocations import RequestStatus as ProductRequestStatus
except ImportError:
    # If it's already ProductRequestStatus, this alias won't break anything
    pass


# --- Helper ID Generators ---
def new_req_test_shopify_product_gid() -> str:
    return f"{SHOPIFY_PRODUCT_GID_PREFIX}{uuid.uuid4().hex}"

def new_req_test_shopify_variant_gid() -> str:
    return f"{SHOPIFY_VARIANT_GID_PREFIX}{uuid.uuid4().hex}"

# --- Fixtures ---
@pytest.fixture
def product_manager_for_req_tests(db: firestore.Client) -> ProductManager:
    return ProductManager(db=db)

@pytest.fixture
def producer_manager_for_req_tests(db: firestore.Client) -> ProducerManager:
    return ProducerManager(db=db)

@pytest.fixture
def store_manager_for_req_tests(db: firestore.Client) -> StoreManager:
    return StoreManager(db=db)

@pytest.fixture
def product_requester(
    db: firestore.Client,
    user_with_root_account: UserRootAccount,
    test_store: str, # need this to configure the service with the right custom claims
    test_producer: str # need this to configure the service with the right custom claims
) -> ProductRequester:
    """Provides a ProductRequester service initialized with a store user context."""
    logger.info(f"user_with_root_account: {user_with_root_account}")
    logger.info(f"test_store: {test_store}")
    logger.info(f"test_producer: {test_producer}")

    sleep(20)

    auth_context = AuthContext(user_id=user_with_root_account.uid)
    service = ProductRequester(db=db, auth_context=auth_context)
    return service

@pytest.fixture
def req_test_product_with_variants(
    product_manager_for_req_tests: ProductManager,
    test_producer # From conftest.py, provides producer_id
):
    """Creates a product with variants owned by test_producer for requester tests."""
    now = datetime.now(timezone.utc)
    prod_logical_id = new_req_test_shopify_product_gid()
    variant1_id = new_req_test_shopify_variant_gid()
    variant2_id = new_req_test_shopify_variant_gid()

    product_data = Product(
        product_id=prod_logical_id,
        title="Requester Test Product",
        producer_id=test_producer,
        store_id=f"store_{uuid.uuid4().hex[:4]}", # Dummy context
        store_display_name="Dummy Store Context",
        handle="requester-test-product", status="ACTIVE", created_at=now, updated_at=now,
        variants=[
            ProductVariant(product_variant_id=variant1_id, product_id=prod_logical_id, title="Req Var 1", sku="RTV1", price=15.00, inventory_quantity=70, created_at=now, updated_at=now),
            ProductVariant(product_variant_id=variant2_id, product_id=prod_logical_id, title="Req Var 2", sku="RTV2", price=25.00, inventory_quantity=30, created_at=now, updated_at=now)
        ]
    )
    created_product = product_manager_for_req_tests.create_product(product_data)
    yield created_product
    try:
        product_manager_for_req_tests.delete_product(created_product.product_id, delete_variants=True)
    except Exception as e:
        print(f"Cleanup failed for product {created_product.product_id} in req_test_product_with_variants: {e}")


@pytest.fixture
def sample_store_product_request_payload_factory(
    test_store, # from conftest
    test_producer, # from conftest
    req_test_product_with_variants: Product
):
    """Factory to create StoreProductRequest model instances for testing."""
    def _factory(items_override: list = None, producer_id_override: str = None) -> StoreProductRequest:
        product_model = req_test_product_with_variants
        
        default_items = [
            ProductAllocationItem(product_id=product_model.product_id, product_variant_id=product_model.variants[0].product_variant_id, quantity=3),
            ProductAllocationItem(product_id=product_model.product_id, product_variant_id=product_model.variants[1].product_variant_id, quantity=7)
        ]
        return StoreProductRequest(
            store_id=test_store,
            producer_id=producer_id_override if producer_id_override else test_producer,
            requested_items=items_override if items_override is not None else default_items,
            desired_delivery_date=datetime.now(timezone.utc) # Example
        )
    return _factory

@pytest.fixture
def created_store_request(
    product_requester: ProductRequester,
    sample_store_product_request_payload_factory,
    db: firestore.Client
):
    """Creates a StoreProductRequest via the service and yields it. Handles cleanup."""
    payload = sample_store_product_request_payload_factory()
    created_req = product_requester.create_request(payload)
    assert created_req.request_id is not None
    yield created_req

    # Cleanup
    try:
        db.collection(store_product_requests_collection).document(created_req.request_id).delete()
        if created_req.related_allocation_id:
            db.collection(product_allocations_collection).document(created_req.related_allocation_id).delete()
    except Exception as e:
        print(f"Cleanup failed for request {created_req.request_id}: {e}")

# --- Test Cases ---

def test_create_request_ok(
    product_requester: ProductRequester,
    sample_store_product_request_payload_factory,
    db: firestore.Client
):
    payload = sample_store_product_request_payload_factory()
    created_req_model = None
    try:
        created_req_model = product_requester.create_request(payload)
        assert created_req_model.request_id is not None
        assert created_req_model.store_id == payload.store_id
        assert created_req_model.producer_id == payload.producer_id
        assert len(created_req_model.requested_items) == len(payload.requested_items)
        assert created_req_model.status == ProductRequestStatus.PENDING_PRODUCER_APPROVAL.value

        doc = db.collection(store_product_requests_collection).document(created_req_model.request_id).get()
        assert doc.exists
        db_data = doc.to_dict()
        assert db_data[StoreProductRequest.STORE_ID_FIELD] == payload.store_id
    finally:
        if created_req_model and created_req_model.request_id:
            db.collection(store_product_requests_collection).document(created_req_model.request_id).delete()

def test_create_request_variant_not_found(
    product_requester: ProductRequester,
    sample_store_product_request_payload_factory,
    req_test_product_with_variants: Product
):
    product_model = req_test_product_with_variants
    invalid_items = [
        ProductAllocationItem(
            product_id=product_model.product_id, 
            product_variant_id="non-existent-variant-id", 
            quantity=1
        )
    ]
    payload = sample_store_product_request_payload_factory(items_override=invalid_items)
    with pytest.raises(NotFoundError):
        product_requester.create_request(payload)

def test_create_request_producer_mismatch_on_item(
    product_requester: ProductRequester,
    sample_store_product_request_payload_factory,
    req_test_product_with_variants: Product
):
    payload = sample_store_product_request_payload_factory(producer_id_override="different-producer-id")
    with pytest.raises(AuthorizationError):
        product_requester.create_request(payload)

def test_get_request_ok(
        product_requester: ProductRequester, 
        created_store_request: StoreProductRequest
    ):
    retrieved = product_requester.get_request(created_store_request.request_id)
    assert retrieved is not None
    assert retrieved.request_id == created_store_request.request_id
    assert retrieved.store_id == created_store_request.store_id

def test_get_request_not_found(
    product_requester: ProductRequester
):
    with pytest.raises(NotFoundError):
        product_requester.get_request("non-existent-req-id")

def test_approve_request_ok(
    product_requester: ProductRequester,
    created_store_request: StoreProductRequest,
    db: firestore.Client
):
    request_id = created_store_request.request_id
    
    updated_request = product_requester.approve_request(
        request_id=request_id,
        producer_response_notes="Approved, will ship soon.",
        delivery_method_for_allocation=DeliveryMethod.PRODUCER_DELIVERY,
        allocation_producer_notes="Created from test"
    )

    assert updated_request is not None
    assert updated_request.status == ProductRequestStatus.APPROVED_BY_PRODUCER.value
    assert updated_request.related_allocation_id is not None
    assert updated_request.producer_response_notes == "Approved, will ship soon."

    allocation_doc = db.collection(product_allocations_collection).document(updated_request.related_allocation_id).get()
    assert allocation_doc.exists
    allocation_data = allocation_doc.to_dict()

    assert allocation_data[ProductAllocation.PRODUCER_ID_FIELD] == created_store_request.producer_id
    assert allocation_data[ProductAllocation.STORE_ID_FIELD] == created_store_request.store_id
    assert len(allocation_data[ProductAllocation.ITEMS_FIELD]) == len(created_store_request.requested_items)
    assert allocation_data[ProductAllocation.DELIVERY_METHOD_FIELD] == DeliveryMethod.PRODUCER_DELIVERY.value
    assert allocation_data[ProductAllocation.STATUS_FIELD] == AllocationStatus.PENDING_CONFIRMATION.value

def test_approve_request_wrong_producer(
    db: firestore.Client, 
    created_store_request: StoreProductRequest,
    test_user_b
):
    wrong_producer_context = AuthContext(test_user_b)
    service_with_wrong_producer = ProductRequester(
        db=db, 
        auth_context=wrong_producer_context
    )

    with pytest.raises(AuthorizationError):
        service_with_wrong_producer.approve_request(
            request_id=created_store_request.request_id,
            producer_response_notes=None,
            delivery_method_for_allocation=DeliveryMethod.PRODUCER_SHIPMENT,
            allocation_producer_notes=None
        )

def test_approve_request_wrong_status(
    product_requester: ProductRequester, 
    created_store_request: StoreProductRequest
):
    product_requester.approve_request(
        created_store_request.request_id, 
        "Initial", 
        DeliveryMethod.PRODUCER_DELIVERY,
        "notes"
    )
    
    with pytest.raises(ValidationError, match="Request is not pending approval"):
        product_requester.approve_request(
            request_id=created_store_request.request_id,
            producer_response_notes="Second attempt",
            delivery_method_for_allocation=DeliveryMethod.PRODUCER_SHIPMENT,
            allocation_producer_notes=None
        )

def test_reject_request_ok(
    product_requester: ProductRequester, 
    created_store_request: StoreProductRequest
):
    updated_request = product_requester.reject_request(
        created_store_request.request_id,
        "Items currently out of stock."
    )
    assert updated_request is not None
    assert updated_request.status == ProductRequestStatus.REJECTED_BY_PRODUCER.value
    assert updated_request.producer_response_notes == "Items currently out of stock."
    assert updated_request.related_allocation_id is None

def test_cancel_request_ok(
    product_requester: ProductRequester, 
    created_store_request: StoreProductRequest
):
    updated_request = product_requester.cancel_request(
        created_store_request.request_id
    )
    assert updated_request is not None
    assert updated_request.status == ProductRequestStatus.CANCELLED_BY_STORE.value
    assert updated_request.related_allocation_id is None

def test_list_requests_for_producer(
    product_requester: ProductRequester, 
    created_store_request: StoreProductRequest, 
    test_producer
):
    requests = product_requester.list_requests_for_producer(test_producer)
    assert len(requests) >= 1
    assert any(req.request_id == created_store_request.request_id for req in requests)

    product_requester.approve_request(
        created_store_request.request_id, 
        "notes", 
        DeliveryMethod.PRODUCER_DELIVERY,
        "alloc notes"
    )
    
    pending_reqs = product_requester.list_requests_for_producer(
        test_producer, status=ProductRequestStatus.PENDING_PRODUCER_APPROVAL.value
    )
    approved_reqs = product_requester.list_requests_for_producer(
        test_producer, status=ProductRequestStatus.APPROVED_BY_PRODUCER.value
    )
    
    assert not any(req.request_id == created_store_request.request_id for req in pending_reqs)
    assert any(req.request_id == created_store_request.request_id for req in approved_reqs)

def test_list_requests_by_store(
    product_requester: ProductRequester, 
    created_store_request: StoreProductRequest, 
    test_store
):
    requests = product_requester.list_requests_by_store(test_store)
    assert len(requests) >= 1
    assert any(req.request_id == created_store_request.request_id for req in requests)

    product_requester.cancel_request(created_store_request.request_id)

    pending_reqs = product_requester.list_requests_by_store(
        test_store, 
        status=ProductRequestStatus.PENDING_PRODUCER_APPROVAL.value
    )
    cancelled_reqs = product_requester.list_requests_by_store(
        test_store, 
        status=ProductRequestStatus.CANCELLED_BY_STORE.value
    )
    assert not any(req.request_id == created_store_request.request_id for req in pending_reqs)
    assert any(req.request_id == created_store_request.request_id for req in cancelled_reqs)
