from python.services.shopify_order_processor import ShopifyOrderProcessor

def test_process_file(db):

    # test_path = "tests/tmp_data/shopify_order.json"
    local_file_path = 'tests/tmp_data/shopify_order.json'
    with open(local_file_path, 'r') as file:
        raw_order_json = file.read()

    shopify_order_processor = ShopifyOrderProcessor(db)

    processed_sales = shopify_order_processor.process_file(
        file_content=raw_order_json,
        file_path=local_file_path
    ) 

    import pdb; pdb.set_trace()

    # order document id that has been hashed.
    # vB9oM3kgkpcjQ6wtbv0MEf8uJoj0GZ
    # is working ! manual check tho ...

    assert True
