from pytest import fixture
from services.sales_report_manager import SalesReportManager
from services.agreement_manager import AgreementManager
from models.requests.sales_report_request import CreateSalesReportRequest, DeleteSalesReportRequest
from models.sales import sales_gold_collection, SalesGold
from models.sales_report import sales_report_collection
from models.agreement import Agreement, agreement_collection
from datetime import datetime
import pytest
from time import sleep


@pytest.fixture
def test_sales_report_request(
    test_store_NL, test_start_date, test_end_date, 
    test_force_create_active_partnership, n_sales_stagin_to_gold
):
    return CreateSalesReportRequest(
        store_id=test_store_NL,
        producer_id=test_force_create_active_partnership["producer_id"],
        start_date=test_start_date,
        end_date=test_end_date
    )


def test_sales_report_manager(db, test_sales_report_request):

    sales_report_manager = SalesReportManager(db)
    # The refactored manager now returns a list of reports directly
    created_reports = sales_report_manager.create_sales_reports(test_sales_report_request)

    # There should be 1 report created based on the test data structure
    assert len(created_reports) == 1
    
    # Get the single report from the list
    first_report = created_reports[0]
    sales_report_doc_id = first_report['sales_report_id']

    # Small delay to ensure all background updates can propagate
    sleep(2)

    # Check that all affected sales documents are updated with the report ID
    for sales_id in first_report['affected_sales']:
        sales_doc = db.collection(sales_gold_collection).document(sales_id).get()
        assert sales_doc.exists
        assert sales_doc.to_dict().get(SalesGold.SALES_REPORT_ID_FIELD) == sales_report_doc_id

    # Test the deletion logic
    sales_report_manager.delete_sales_report(sales_report_doc_id)

    # Verify the report document is deleted
    sales_report_doc = db.collection(sales_report_collection).document(sales_report_doc_id).get()
    assert not sales_report_doc.exists

    # Verify the agreement is still present
    agreement_docs = db.collection(agreement_collection).where(
        Agreement.PRODUCER_ID_FIELD, "==", test_sales_report_request.producer_id
    ).where(
        Agreement.STORE_ID_FIELD, "==", test_sales_report_request.store_id
    ).get()

    assert len(agreement_docs) == 1
    agreement_id = agreement_docs[0].id
    
    # Verify the sales documents are cleaned up (report ID removed)
    for sales_id in first_report['affected_sales']:
        sales_doc = db.collection(sales_gold_collection).document(sales_id).get()
        sale_data = sales_doc.to_dict()
        assert sale_data.get(SalesGold.AGREEMENT_ID_FIELD) == agreement_id
        assert SalesGold.SALES_REPORT_ID_FIELD not in sale_data

