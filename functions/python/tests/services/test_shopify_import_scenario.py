# with our latest update to the shopify data getting, lets imagine
# a scenario where we have a new store that we need to import data from.
# functions/python/tests/services/test_shopify_import_scenario.py

import os
import pytest
import json
import time
from datetime import datetime, timedelta, timezone
from unittest.mock import MagicMock, patch

from firebase_admin import firestore, initialize_app, auth
from gaco_secrets.secret_manager import SecretManager
from services.shopify_order_getter import ShopifyOrderGetter
from services.store_manager import StoreManager
from queries.shopify_query_builder import ShopifyQueryBuilder
from models.requests.store_requests import CreateStoreRequest, DeleteStoreRequest, ShopifyStoreCredentials, Address
from models.shopify_get_operation import ShopifyGetOperation, ShopifyGetOperationStatus
from models.store import StoreV2


class TestShopifyImportScenario:
    """
    Tests the complete scenario of importing historical Shopify data.
    
    This test simulates the process of:
    1. Creating a Firebase Auth user
    2. Setting up a store with Shopify credentials using StoreManager
    3. Initiating an import of 1 year of historical data
    4. Processing pagination in the background
    5. Checking the status of the import process
    """
    
    @pytest.fixture(scope="function")
    def test_user(self):
        """Create a test user in Firebase Auth"""
        unique_timestamp = int(time.time())
        email = f'test-shopify-import-{unique_timestamp}@example.com'
        password = 'testpassword123'
        
        try:
            # Create the user in Firebase Auth
            user = auth.create_user(
                email=email,
                password=password,
                display_name=f'Shopify Import Test User {unique_timestamp}'
            )
            
            print(f"Created test user with ID: {user.uid}")
            
            yield user
            
            # Cleanup: Delete the test user after tests
            try:
                auth.delete_user(user.uid)
                print(f"Deleted test user: {user.uid}")
            except auth.UserNotFoundError:
                print(f"User {user.uid} already deleted or not found")
            
        except Exception as e:
            print(f"Error in test_user fixture: {e}")
            raise
    
    @pytest.fixture
    def test_env(self):
        """Set up test environment variables"""
        # You should set these in your test environment or pass as parameters
        api_key = os.environ.get('SHOPIFY_TEST_ACCESS_TOKEN')
        shop_name = os.environ.get('SHOPIFY_TEST_SHOP_NAME')
        
        if not api_key or not shop_name:
            pytest.skip("Required environment variables for Shopify API testing not set")
        
        return {
            'api_key': api_key,
            'shop_name': shop_name,
            'one_year_ago': (datetime.now(timezone.utc) - timedelta(days=365)).strftime('%Y-%m-%d'),
            'today': datetime.now(timezone.utc).strftime('%Y-%m-%d')
        }
    
    @pytest.fixture
    def setup_store(self, db, test_user, test_env):
        """Set up a test store with Shopify credentials using StoreManager"""
        # Create a test store using the StoreManager
        store_manager = StoreManager(db=db)
        
        # Create a ShopifyAPIKey object (renamed to ShopifyStoreCredentials in test)
        shopify_credentials = ShopifyStoreCredentials(
            shop_name=test_env['shop_name'],
            shopify_api_key=test_env['api_key']
        )
        
        # Create an Address object
        address = Address(
            street_number="123",
            street_name="Test Street",
            zip_code="12345",
            city="Test City",
            country="US"
        )
        
        # Create a CreateStoreRequest
        unique_suffix = int(time.time())
        store_request = CreateStoreRequest(
            display_name=f"Test Shopify Store {unique_suffix}",
            email=f"test-shopify-{unique_suffix}@example.com",
            tax_a2="US",
            default_commission=10.0,
            description="Test store for Shopify import scenario",
            address=address,
            shopify_api_key_object=shopify_credentials
        )
        
        # Create the store
        print(f"Creating test store for user {test_user.uid}...")
        try:
            store_id = store_manager.create_store(
                request=store_request,
                user_uuid=test_user.uid
            )
            print(f"Created test store with ID: {store_id}")
            
            # Wait a moment for Firestore operations to complete
            time.sleep(1)
            
            # Get store details
            store_doc = db.collection('storesV2').document(store_id).get()
            store_data = store_doc.to_dict()
            

            # Return the store info
            # Create a StoreV2 object from the store data dictionary
            # Create a StoreV2 object directly from the dictionary using unpacking
            store_info = StoreV2(**store_data)
            
            
            yield store_info, store_id
            
            # Cleanup
            try:
                delete_request = DeleteStoreRequest(store_id=store_id)
                store_manager.delete_store(delete_request, test_user.uid)
                print(f"Cleaned up test store: {store_id}")
            except Exception as e:
                print(f"Error cleaning up test store: {str(e)}")
            
        except Exception as e:
            pytest.fail(f"Failed to create test store: {str(e)}")
    
    @pytest.fixture
    def order_fetcher(self, db):
        """Create a ShopifyOrderGetter instance"""
        secret_manager = SecretManager(project_id=os.environ.get('PROJECT_ID', 'test-project'))
        return ShopifyOrderGetter(db, secret_manager)
    
    def test_fetch_initial_orders(self, db, test_user, setup_store, order_fetcher, test_env):
        """Test getting the initial page of orders"""
        # Mock storage operations since we don't want to actually write to GCS
        with patch('services.shopify_order_fetcher.storage') as mock_storage:
            # Setup storage mocks
            mock_blob = MagicMock()
            mock_blob.generate_signed_url.return_value = "https://storage.googleapis.com/test-url"
            mock_bucket = MagicMock()
            mock_bucket.blob.return_value = mock_blob
            mock_storage.bucket.return_value = mock_bucket

            
            # Call the method to fetch the initial page
            print(f"Fetching initial orders for store: {setup_store[1]}")
            result = order_fetcher.fetch_initial_orders(
                store_id=setup_store[1],
                start_date=test_env['one_year_ago'],
                end_date=test_env['today']
            )

            # Verify the result
            assert 'fetchId' in result
            assert 'downloadUrl' in result
            assert 'ordersInFirstPage' in result
            assert 'hasMorePages' in result
            assert 'status' in result
            
            order_getter_id = result['fetchId']
            print(f"Fetch ID: {order_getter_id}")
            print(f"Orders in first page: {result['ordersInFirstPage']}")
            print(f"Has more pages: {result['hasMorePages']}")
            
            # Verify that the initial fetch was recorded in Firestore
            fetch_doc = db.collection('shopify_fetch_operations').document(order_getter_id).get()
            assert fetch_doc.exists
            
            fetch_data = fetch_doc.to_dict()
            assert fetch_data[ShopifyGetOperation.STORE_ID_FIELD] == setup_store[1]
            assert fetch_data[ShopifyGetOperation.STATUS_FIELD] in [
                ShopifyGetOperationStatus.IN_PROGRESS.value, 
                ShopifyGetOperationStatus.COMPLETED.value
            ]
            
            # Return the fetch ID for the next test
            return order_getter_id
    
    def test_continue_getting_orders(self, db, test_user, setup_store, order_fetcher, test_env):
        """Test continuing to fetch orders in the background"""
        # First get the initial page and fetch ID
        order_getter_id = self.test_fetch_initial_orders(db, test_user, setup_store, order_fetcher, test_env)
        
        # Mock storage operations
        with patch('services.shopify_order_fetcher.storage') as mock_storage:
            # Setup storage mocks
            mock_blob = MagicMock()
            mock_bucket = MagicMock()
            mock_bucket.blob.return_value = mock_blob
            mock_storage.bucket.return_value = mock_bucket
            
            # Call the method to continue getting
            print(f"Continuing fetch operation: {order_getter_id}")
            result = order_fetcher.continue_getting_orders(order_getter_id)
            
            # Verify the result
            assert ShopifyGetOperation.FETCH_ID_FIELD in result
            assert result[ShopifyGetOperation.FETCH_ID_FIELD] == order_getter_id
            assert ShopifyGetOperation.STATUS_FIELD in result
            
            # If there were multiple pages, verify we fetched them all
            if result[ShopifyGetOperation.STATUS_FIELD] == ShopifyGetOperationStatus.COMPLETED.value:
                assert ShopifyGetOperation.TOTAL_ORDERS_FIELD in result
                assert ShopifyGetOperation.PAGES_FETCHED_FIELD in result
                
                print(f"Total orders fetched: {result[ShopifyGetOperation.TOTAL_ORDERS_FIELD]}")
                print(f"Pages fetched: {result[ShopifyGetOperation.PAGES_FETCHED_FIELD]}")
                
                # Verify Firestore state was updated
                fetch_doc = db.collection('shopify_fetch_operations').document(order_getter_id).get()
                fetch_data = fetch_doc.to_dict()
                
                assert fetch_data[ShopifyGetOperation.STATUS_FIELD] == ShopifyGetOperationStatus.COMPLETED.value
                assert ShopifyGetOperation.COMPLETED_AT_FIELD in fetch_data
                assert fetch_data[ShopifyGetOperation.TOTAL_ORDERS_FIELD] == result[ShopifyGetOperation.TOTAL_ORDERS_FIELD]
                assert fetch_data[ShopifyGetOperation.PAGES_FETCHED_FIELD] == result[ShopifyGetOperation.PAGES_FETCHED_FIELD]
            
            return result
    
    def test_full_import_scenario(self, db, test_user, setup_store, order_fetcher, test_env):
        """Test the complete import scenario from start to finish"""
        # Mock storage operations
        with patch('services.shopify_order_fetcher.storage') as mock_storage:
            # Setup storage mocks
            mock_blob = MagicMock()
            mock_blob.generate_signed_url.return_value = "https://storage.googleapis.com/test-url"
            mock_bucket = MagicMock()
            mock_bucket.blob.return_value = mock_blob
            mock_storage.bucket.return_value = mock_bucket
            
            # Step 1: Fetch initial page
            print("\n--- STEP 1: Fetching Initial Page ---")
            initial_result = order_fetcher.fetch_initial_orders(
                store_id=setup_store[1],
                start_date=test_env['one_year_ago'],
                end_date=test_env['today']
            )
            
            order_getter_id = initial_result['fetchId']
            print(f"Fetch ID: {order_getter_id}")
            print(f"Orders in first page: {initial_result['ordersInFirstPage']}")
            print(f"Has more pages: {initial_result['hasMorePages']}")
            
            # Step 2: Check initial status
            print("\n--- STEP 2: Checking Initial Status ---")
            fetch_doc = db.collection('shopify_fetch_operations').document(order_getter_id).get()
            fetch_data = fetch_doc.to_dict()
            print(f"Initial status: {fetch_data[ShopifyGetOperation.STATUS_FIELD]}")
            print(f"Pages fetched so far: {fetch_data[ShopifyGetOperation.PAGES_FETCHED_FIELD]}")
            
            # Step 3: Continue getting in the background
            print("\n--- STEP 3: Continuing Fetch in Background ---")
            background_result = order_fetcher.continue_getting_orders(order_getter_id)
            
            print(f"Background process status: {background_result['status']}")
            if 'totalOrders' in background_result:
                print(f"Total orders fetched: {background_result['totalOrders']}")
                print(f"Pages fetched: {background_result['pagesFetched']}")
            
            # Step 4: Check final status
            print("\n--- STEP 4: Checking Final Status ---")
            final_doc = db.collection('shopify_fetch_operations').document(order_getter_id).get()
            final_data = final_doc.to_dict()
            
            print(f"Final status: {final_data['status']}")
            print(f"Total orders: {final_data.get('total_orders', 0)}")
            print(f"Total pages: {final_data.get('pages_fetched', 0)}")
            
            # Assertions to verify the process worked
            assert final_data['status'] in ['completed', 'error']
            
            if final_data['status'] == 'completed':
                assert ShopifyGetOperation.TOTAL_ORDERS_FIELD in final_data
                assert ShopifyGetOperation.PAGES_FETCHED_FIELD in final_data
                assert ShopifyGetOperation.COMPLETED_AT_FIELD in final_data
            else:
                assert ShopifyGetOperation.ERROR_MESSAGE_FIELD in final_data
                assert ShopifyGetOperation.ERROR_TIME_FIELD in final_data
                print(f"Error: {final_data['error_message']}")
            
            # Write test result to a file
            with open(f"shopify_import_test_result_{order_getter_id}.json", "w") as f:
                json.dump({
                    'order_getter_id': order_getter_id,
                    'user_id': test_user.uid,
                    'store_id': setup_store[1],
                    'store_name': setup_store[0].display_name,
                    'start_date': test_env['one_year_ago'],
                    'end_date': test_env['today'],
                    'initial_result': {k: v for k, v in initial_result.items() if k != 'downloadUrl'},
                    'final_status': final_data['status'],
                    'total_orders': final_data.get('total_orders', 0),
                    'total_pages': final_data.get('pages_fetched', 0),
                    'test_timestamp': datetime.now(timezone.utc).isoformat()
                }, f, indent=2, default=str)
            
            return order_getter_id
    
    def test_direct_api_with_store_credentials(self, db, test_user, setup_store):
        """
        Test the Shopify API directly using credentials from the created store.
        
        This test leverages the actual store we created and retrieves its credentials.
        """
        # Get store details from Firestore
        store_doc = db.collection('storesV2').document(setup_store[1]).get()
        assert store_doc.exists, f"Store {setup_store[1]} not found in Firestore"
        
        store_data = store_doc.to_dict()
        print(f"Retrieved store: {store_data['displayName']}")
        
        # Get secret reference from store
        secret_reference = store_data.get(StoreV2.SHOPIFY_API_KEY_REFERENCE_FIELD)
        assert secret_reference, "Store has no secret reference"
        
        # Get API key using SecretManager
        secret_manager = SecretManager(project_id=os.environ.get('PROJECT_ID', 'test-project'))
        api_key = secret_manager.get_secret(secret_reference)
        assert api_key, "Failed to retrieve API key from Secret Manager"
        
        # Get shop name from customer_api_keys collection
        api_key_doc = db.collection('customer_api_keys').document(setup_store[1]).get()
        assert api_key_doc.exists, f"API key document for store {setup_store[1]} not found"
        
        api_key_data = api_key_doc.to_dict()
        shop_name = api_key_data.get('shop_name')
        assert shop_name, "Shop name not found in API key document"
        
        shop_domain = f"{shop_name}.myshopify.com"
        print(f"Using shop domain: {shop_domain}")
        
        # Set a small page size to force pagination
        items_per_page = 5
        
        # Modify the query builder temporarily for testing
        original_method = ShopifyQueryBuilder.get_orders_query
        
        # Patch the method to use a smaller page size
        def test_query_method(start_date, end_date, cursor=None):
            # Add cursor-based pagination
            after_clause = f'after: "{cursor}"' if cursor else ''
            
            return f"""
                query {{
                    orders(first: {items_per_page}, {after_clause} query: "created_at:>='{start_date}' AND created_at:<='{end_date}'") {{
                        pageInfo {{
                            hasNextPage
                            endCursor
                        }}
                        edges {{
                            node {{
                                id
                                updatedAt
                                closed
                                fullyPaid
                                currentSubtotalPriceSet {{
                                    shopMoney {{
                                        amount
                                        currencyCode
                                    }}
                                }}
                                cartDiscountAmountSet {{
                                    shopMoney {{
                                        amount
                                        currencyCode
                                    }}
                                }}
                                lineItems(first: {items_per_page}) {{
                                    pageInfo {{
                                        hasNextPage
                                        endCursor
                                    }}
                                    edges {{
                                        node {{
                                            id
                                            title
                                            quantity
                                            vendor
                                        }}
                                    }}
                                }}
                            }}
                        }}
                    }}
                }}
            """
        
        # Apply the patch
        ShopifyQueryBuilder.get_orders_query = test_query_method
        
        try:
            # Get date range for one year
            one_year_ago = (datetime.now(timezone.utc) - timedelta(days=365)).strftime('%Y-%m-%d')
            today = datetime.now(timezone.utc).strftime('%Y-%m-%d')
            
            # Fetch orders directly, skipping our services
            all_orders = []
            cursor = None
            has_next_page = True
            page_count = 0
            
            # Only fetch up to 3 pages for testing
            while has_next_page and page_count < 3:
                page_count += 1
                print(f"Fetching page {page_count} directly (cursor: {cursor})")
                
                # Get the query
                query = ShopifyQueryBuilder.get_orders_query(one_year_ago, today, cursor)
                
                # Make the request
                import requests
                response = requests.post(
                    f"https://{shop_domain}/admin/api/2024-10/graphql.json",
                    headers={
                        'Content-Type': 'application/json',
                        'X-Shopify-Access-Token': api_key
                    },
                    json={
                        'query': query
                    }
                )
                
                # Check for successful response
                response.raise_for_status()
                result = response.json()
                
                if 'errors' in result:
                    print(f"GraphQL errors: {json.dumps(result['errors'], indent=2)}")
                    raise Exception(f"GraphQL error: {result['errors'][0]['message']}")
                
                # Extract data
                data = result.get('data', {})
                page_orders = data.get('orders', {}).get('edges', [])
                all_orders.extend(page_orders)
                
                # Extract pagination info
                page_info = data.get('orders', {}).get('pageInfo', {})
                has_next_page = page_info.get('hasNextPage', False)
                cursor = page_info.get('endCursor') if has_next_page else None
                
                print(f"Page {page_count}: Retrieved {len(page_orders)} orders")
                print(f"Has next page: {has_next_page}")
                print(f"Next cursor: {cursor}")
                
                # Add a small delay to avoid rate limiting
                if has_next_page:
                    time.sleep(0.5)
            
            # Print final results
            print(f"\nDirect API test completed. Total orders: {len(all_orders)}")
            print(f"Pages fetched: {page_count}")
            
            # Sample the first order
            if all_orders:
                first_order = all_orders[0]['node']
                print("\nSample order data:")
                print(f"Order ID: {first_order['id']}")
                print(f"Updated at: {first_order['updatedAt']}")
                print(f"Fully paid: {first_order['fullyPaid']}")
                print(f"Line items: {len(first_order['lineItems']['edges'])}")
            
            # Write results to a file
            with open(f"direct_api_test_store_{setup_store[1]}.json", "w") as f:
                # Only include a sample of orders to keep the file size reasonable
                sample_orders = all_orders[:5] if len(all_orders) > 5 else all_orders
                json.dump({
                    'user_id': test_user.uid,
                    'store_id': setup_store[1],
                    'shop_domain': shop_domain,
                    'total_orders': len(all_orders),
                    'pages_fetched': page_count,
                    'has_more_pages': has_next_page,
                    'sample_orders': sample_orders,
                    'test_timestamp': datetime.now(timezone.utc).isoformat()
                }, f, indent=2, default=str)
                
            # Assert we were able to fetch orders
            assert len(all_orders) >= 0  # Allow for stores with no orders
            assert page_count > 0
            
        finally:
            # Restore original method
            ShopifyQueryBuilder.get_orders_query = original_method

    @patch('services.shopify_order_fetcher.SecretManager')
    def test_something(self, mock_secret_manager):
        # Setup the mock to return a test API key when requested
        mock_secret_manager.get_secret.return_value = "test_api_key"
        
        # Your test code here


if __name__ == "__main__":
    pytest.main(["-xvs", __file__])