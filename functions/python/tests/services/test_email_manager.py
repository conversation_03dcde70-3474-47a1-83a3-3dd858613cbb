import os
import requests
from services.email_manager import EmailManager
from services.email_templater import EmailTemplater
from models.requests.email_requests import (
    Email, 
    SendSalesReportEmailRequest,
    SendInviteEmailRequest
)


def test_send_simple_message(db):
    email_manager = EmailManager(db)

    email = Email(
        from_alias="Gaco Automation Email",
        from_email="<EMAIL>",
        to_alias="dreamunit",
        to_email="<EMAIL>",
        subject="Hello dreamunit",
        text="email automation is active !"
    )

    response = email_manager.send_email(email)

    assert response.status_code == 200
    assert "Queued. Thank you." in response.message


def test_send_sales_report_email(db):
    request = SendSalesReportEmailRequest(
        store_id="CxS5oJuvojy6uA6XTj5W",
        sales_report_id="0NlGd86VseHdBmoqbvzJ-2025-1"
    )

    email_templater = EmailTemplater(db)
    email = email_templater.populate_sales_report_template(
        request=request
    )

    import pdb; pdb.set_trace()


def test_send_invite_email(db, test_store, test_producer):
    request = SendInviteEmailRequest(
        store_id='aEwJD5kT9CQJcI2EmFnz',
        producer_id='qYfPIJeFfJHnQeHMJIKj'
    )

    email_templater = EmailTemplater(db)
    email = email_templater.populate_invite_email_template(
        request=request
    )

    import pdb; pdb.set_trace()
