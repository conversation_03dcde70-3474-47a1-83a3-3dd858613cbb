import pytest
from datetime import datetime, timezone
from firebase_admin import firestore, initialize_app
from python.services.secret_service import SecretService
from python.gaco_secrets.secret_manager import SecretManager

@pytest.fixture(scope="module")
def firebase_app():
    try:
        return initialize_app()
    except ValueError:
        # App already initialized
        pass

@pytest.fixture
def db(firebase_app):
    return firestore.Client()

@pytest.fixture
def secret_service(db, project_id):
    secret_manager = SecretManager(project_id=project_id)
    return SecretService(db, secret_manager)

@pytest.fixture
def test_secret_data(unique_id):
    return {
        "store_id": f"test-store-{unique_id}",
        "secret_name": f"test-secret-{unique_id}",
        "secret_value": "test-secret-value",
        "description": "Test secret description"
    }

def test_create_and_get_secret(secret_service, test_secret_data):
    # Create a new secret
    secret = secret_service.create_secret(
        test_secret_data["store_id"],
        test_secret_data["secret_name"],
        test_secret_data["secret_value"],
        test_secret_data["description"]
    )
    
    # Verify the secret was created with correct attributes
    assert secret.store_id == test_secret_data["store_id"]
    assert secret.secret_name == test_secret_data["secret_name"]
    assert secret.description is None
    
    # Verify we can retrieve the secret value
    retrieved_value = secret_service.get_secret_value(secret.secret_version_path)
    assert retrieved_value == test_secret_data["secret_value"]

def test_list_customer_secrets(secret_service, test_secret_data):
    # First create a secret
    secret_service.create_secret(
        test_secret_data["store_id"],
        test_secret_data["secret_name"],
        test_secret_data["secret_value"],
        test_secret_data["description"]
    )
    
    # List secrets for the store
    secrets = secret_service.list_customer_secrets(test_secret_data["store_id"])
    
    # Verify the secret is in the list
    assert len(secrets) >= 1
    found_secret = next(
        (s for s in secrets if s.secret_name == test_secret_data["secret_name"]), 
        None
    )
    assert found_secret is not None
    assert found_secret.store_id == test_secret_data["store_id"]

def test_delete_secret(secret_service, test_secret_data):
    # First create a secret
    secret = secret_service.create_secret(
        test_secret_data["store_id"],
        test_secret_data["secret_name"],
        test_secret_data["secret_value"],
        test_secret_data["description"]
    )
    
    # Delete the secret
    secret_service.delete_secret(test_secret_data["store_id"], test_secret_data["secret_name"])
    
    # Verify the secret is deleted by checking the list
    secrets = secret_service.list_customer_secrets(test_secret_data["store_id"])
    found_secret = next(
        (s for s in secrets if s.secret_name == test_secret_data["secret_name"]), 
        None
    )
    assert found_secret is None

@pytest.fixture(autouse=True)
def cleanup(secret_service, test_secret_data):
    yield
    # Clean up after each test
    try:
        secret_service.delete_secret(
            test_secret_data["store_id"], 
            test_secret_data["secret_name"]
        )
    except Exception as e:
        print(f"Error cleaning up test secret: {str(e)}")