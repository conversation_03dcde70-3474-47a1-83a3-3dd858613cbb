import pytest
import uuid
from datetime import datetime, timezone
from typing import List, Optional, Literal

from firebase_admin import firestore
from hasher.shopify_gid import shopify_gid_to_hash
from services.product_manager import ProductManager, SHOPIFY_PRODUCT_GID_PREFIX, SHOPIFY_VARIANT_GID_PREFIX
from models.products import Product, ProductVariant
from gaco_framework.exceptions import ConflictError, ValidationError, NotFoundError, PermissionError

# --- Fixtures ---

@pytest.fixture
def product_manager(db: firestore.Client) -> ProductManager:
    return ProductManager(db=db)

# --- ID Generation Helpers ---
def new_shopify_product_gid() -> str:
    return f"{SHOPIFY_PRODUCT_GID_PREFIX}{uuid.uuid4().hex}"

def new_shopify_variant_gid() -> str:
    return f"{SHOPIFY_VARIANT_GID_PREFIX}{uuid.uuid4().hex}"

def new_custom_product_id(prefix: str = "custom_prod") -> str:
    return f"{prefix}_{uuid.uuid4().hex}"

def new_custom_variant_id(prefix: str = "custom_var") -> str:
    return f"{prefix}_{uuid.uuid4().hex}"


@pytest.fixture
def sample_product_variant_data_factory():
    def _factory(
        parent_product_logical_id: str, 
        variant_id_type: Literal["gid", "custom"] = "gid", # Variants must have an ID
        variant_logical_id_override: Optional[str] = None
    ) -> ProductVariant:
        now = datetime.now(timezone.utc)
        
        variant_logical_id: str
        if variant_logical_id_override:
            variant_logical_id = variant_logical_id_override
        elif variant_id_type == "gid":
            variant_logical_id = new_shopify_variant_gid()
        else: # custom
            variant_logical_id = new_custom_variant_id()

        return ProductVariant(
            product_variant_id=variant_logical_id,
            product_id=parent_product_logical_id,
            title="Test Variant Title",
            sku=f"TEST-VAR-{uuid.uuid4().hex[:6]}",
            price=19.99,
            compare_at_price=29.99,
            inventory_quantity=100,
            barcode=uuid.uuid4().hex,
            created_at=now,
            updated_at=now
        )
    return _factory

@pytest.fixture
def sample_product_data_factory(sample_product_variant_data_factory):
    def _factory(
        product_id_type: Literal["gid", "custom", "generate"] = "gid",
        product_id_override: Optional[str] = None, # Used if type is 'gid' or 'custom'
        with_store_context: bool = True,
        with_producer_context: bool = False,
        num_variants: int = 0,
        variant_id_type: Literal["gid", "custom"] = "gid"
    ) -> Product:
        now = datetime.now(timezone.utc)
        
        product_logical_id_input: Optional[str]
        if product_id_type == "generate":
            product_logical_id_input = None # Manager will generate it
        elif product_id_override:
            product_logical_id_input = product_id_override
        elif product_id_type == "gid":
            product_logical_id_input = new_shopify_product_gid()
        else: # custom
            product_logical_id_input = new_custom_product_id()

        # The Pydantic model Product.product_id should be Optional[str] = None for this to work
        # when product_logical_id_input is None.
        # If it's just `str`, Pydantic will raise error before manager sees `None`.
        # Forcing product_id to be a string for now, as per current model.
        # Manager logic for `if not product_data.product_id:` will only be hit
        # if the input `Product` object can have `product_id=None`.
        # This factory will always provide a string if product_id_type is not "generate".
        # If "generate", it provides None. We assume the Model supports this.

        temp_product_id_for_variants = product_logical_id_input or f"temp_for_variants_{uuid.uuid4().hex}"

        variants = []
        if num_variants > 0:
            for _ in range(num_variants):
                variants.append(sample_product_variant_data_factory(
                    parent_product_logical_id=temp_product_id_for_variants, # Will be overwritten by manager if needed
                    variant_id_type=variant_id_type
                ))

        product_data_dict = {
            "product_id": product_logical_id_input,
            "title": f"Test Product {uuid.uuid4().hex[:6]}",
            "vendor": "Test Vendor",
            "handle": f"test-product-{uuid.uuid4().hex[:6]}",
            "product_type": "Test Type",
            "status": "ACTIVE",
            "created_at": now,
            "updated_at": now,
            "variants": variants,
            "store_id": None,
            "store_display_name": None,
            "producer_id": None,
            "producer_display_name": None,
        }

        if with_store_context:
            product_data_dict["store_id"] = f"store-{uuid.uuid4().hex[:6]}"
            product_data_dict["store_display_name"] = "Test Store Name"
        
        if with_producer_context:
            product_data_dict["producer_id"] = f"producer-{uuid.uuid4().hex[:6]}"
            product_data_dict["producer_display_name"] = "Test Producer Name"
            
        return Product.model_validate(product_data_dict)
    return _factory

def _find_and_delete_doc(db: firestore.Client, collection_name: str, id_field_name: str, logical_id: str):
    """Helper to find a document by logical ID and delete it."""
    query = db.collection(collection_name).where(id_field_name, "==", logical_id).limit(1)
    docs = list(query.stream())
    for doc in docs:
        doc.reference.delete()
        print(f"Cleaned up document with logical ID '{logical_id}' from '{collection_name}' (Firestore ID: {doc.id})")


def _cleanup_product_and_variants(db: firestore.Client, logical_product_id: str):
    # Delete product
    _find_and_delete_doc(db, "products", Product.PRODUCT_ID_FIELD, logical_product_id)
    
    # Delete associated variants
    variant_query = db.collection("product_variants").where(ProductVariant.PRODUCT_ID_FIELD, "==", logical_product_id)
    variant_docs_to_delete = list(variant_query.stream())
    for var_doc in variant_docs_to_delete:
        var_doc.reference.delete()
        print(f"Cleaned up variant (Firestore ID: {var_doc.id}, Logical Parent ID: {logical_product_id})")


def _cleanup_variant(db: firestore.Client, logical_variant_id: str):
    _find_and_delete_doc(db, "product_variants", ProductVariant.PRODUCT_VARIANT_ID_FIELD, logical_variant_id)


# --- Product Tests ---

def test_create_product_with_shopify_gid(product_manager: ProductManager, sample_product_data_factory, db: firestore.Client):
    """Tests creating a product with a Shopify GID; Firestore doc ID should be the hash."""
    provided_gid = new_shopify_product_gid()
    product_data = sample_product_data_factory(product_id_type="gid", product_id_override=provided_gid, num_variants=1, variant_id_type="gid")
    
    try:
        created_product = product_manager.create_product(product_data)
        assert created_product is not None
        assert created_product.product_id == provided_gid
        assert created_product.store_id == product_data.store_id # Assuming with_store_context=True default
        assert len(created_product.variants) == 1
        assert created_product.variants[0].product_variant_id.startswith(SHOPIFY_VARIANT_GID_PREFIX)

        # Verify Firestore document ID is the hash of the GID
        doc_ref = product_manager._find_doc_ref_by_logical_id(
            product_manager.products_ref, Product.PRODUCT_ID_FIELD, provided_gid
        )
        assert doc_ref is not None
        expected_doc_id = shopify_gid_to_hash(provided_gid, product_manager.hash_salt)
        assert doc_ref.id == expected_doc_id

        retrieved_product = product_manager.get_product(provided_gid)
        assert retrieved_product is not None
        assert retrieved_product.title == product_data.title
        assert len(retrieved_product.variants) == 1
        assert retrieved_product.variants[0].sku == product_data.variants[0].sku

        # Verify variant doc ID is also hashed
        variant_logical_id = created_product.variants[0].product_variant_id


        variant_doc_ref = product_manager._find_doc_ref_by_logical_id(
            product_manager.variants_ref, 
            ProductVariant.PRODUCT_VARIANT_ID_FIELD, 
            variant_logical_id
        )
        assert variant_doc_ref is not None
        expected_variant_doc_id = shopify_gid_to_hash(variant_logical_id, product_manager.hash_salt)
        assert variant_doc_ref.id == expected_variant_doc_id

    finally:
        _cleanup_product_and_variants(db, provided_gid)
        # Variants are cleaned by _cleanup_product_and_variants if associated, but if one was somehow orphaned:
        if product_data.variants:
             _cleanup_variant(db, product_data.variants[0].product_variant_id)


def test_create_product_with_custom_id(product_manager: ProductManager, sample_product_data_factory, db: firestore.Client):
    """Tests creating a product with a custom ID; Firestore doc ID should be auto-generated."""
    custom_id = new_custom_product_id()
    product_data = sample_product_data_factory(product_id_type="custom", product_id_override=custom_id, num_variants=1, variant_id_type="custom")
    
    try:
        created_product = product_manager.create_product(product_data)
        assert created_product is not None
        assert created_product.product_id == custom_id
        assert len(created_product.variants) == 1
        assert not created_product.variants[0].product_variant_id.startswith(SHOPIFY_VARIANT_GID_PREFIX)


        doc_ref = product_manager._find_doc_ref_by_logical_id(
            product_manager.products_ref, Product.PRODUCT_ID_FIELD, custom_id
        )
        assert doc_ref is not None
        assert doc_ref.id != custom_id # Firestore auto-generated ID
        assert doc_ref.id != shopify_gid_to_hash(custom_id, product_manager.hash_salt) # Should not be a hash

        retrieved_product = product_manager.get_product(custom_id)
        assert retrieved_product is not None
        assert retrieved_product.title == product_data.title

        # Verify variant doc ID is also auto-generated
        variant_logical_id = created_product.variants[0].product_variant_id
        variant_doc_ref = product_manager._find_doc_ref_by_logical_id(
            product_manager.variants_ref, ProductVariant.PRODUCT_VARIANT_ID_FIELD, variant_logical_id
        )
        assert variant_doc_ref is not None
        assert variant_doc_ref.id != variant_logical_id
        assert variant_doc_ref.id != shopify_gid_to_hash(variant_logical_id, product_manager.hash_salt)


    finally:
        _cleanup_product_and_variants(db, custom_id)
        if product_data.variants:
            _cleanup_variant(db, product_data.variants[0].product_variant_id)


def test_create_product_with_generated_id(product_manager: ProductManager, sample_product_data_factory, db: firestore.Client):
    """Tests creating a product when no ID is provided; manager should generate one."""
    # Assumes Product model's product_id is Optional and defaults to None,
    # or factory sets it to None when product_id_type="generate".
    product_data = sample_product_data_factory(product_id_type="generate", num_variants=0)
    
    # product_data.product_id is None at this point because of product_id_type="generate"
    assert product_data.product_id is None 

    logical_id_after_creation = None
    try:
        created_product = product_manager.create_product(product_data)
        assert created_product is not None
        assert created_product.product_id is not None
        assert created_product.product_id.startswith("prod_") # Manager's generation format
        logical_id_after_creation = created_product.product_id

        doc_ref = product_manager._find_doc_ref_by_logical_id(
            product_manager.products_ref, Product.PRODUCT_ID_FIELD, logical_id_after_creation
        )
        assert doc_ref is not None
        assert doc_ref.id != logical_id_after_creation # Firestore auto-generated ID

        retrieved_product = product_manager.get_product(logical_id_after_creation)
        assert retrieved_product is not None
        assert retrieved_product.title == product_data.title
    finally:
        if logical_id_after_creation:
            _cleanup_product_and_variants(db, logical_id_after_creation)


def test_create_product_with_producer_context_gid(product_manager: ProductManager, sample_product_data_factory, db: firestore.Client):
    provided_gid = new_shopify_product_gid()
    product_data = sample_product_data_factory(
        product_id_type="gid", 
        product_id_override=provided_gid, 
        with_store_context=False, 
        with_producer_context=True, 
        num_variants=0
    )
    try:
        created_product = product_manager.create_product(product_data)
        assert created_product.product_id == provided_gid
        assert created_product.producer_id == product_data.producer_id
        assert created_product.store_id is None
        
        doc_ref = product_manager._find_doc_ref_by_logical_id(
            product_manager.products_ref, Product.PRODUCT_ID_FIELD, provided_gid
        )
        assert doc_ref is not None
        assert doc_ref.id == shopify_gid_to_hash(provided_gid, product_manager.hash_salt)

        retrieved_product = product_manager.get_product(provided_gid)
        assert retrieved_product is not None
        assert retrieved_product.producer_display_name == product_data.producer_display_name
    finally:
        _cleanup_product_and_variants(db, provided_gid)


def test_create_product_missing_context(product_manager: ProductManager, sample_product_data_factory):
    product_data = sample_product_data_factory(with_store_context=False, with_producer_context=False, product_id_type="custom")
    with pytest.raises(ValidationError, match="must have either"):
        product_manager.create_product(product_data)


def test_create_product_duplicate_logical_id(product_manager: ProductManager, sample_product_data_factory, db: firestore.Client):
    # Test with GID
    gid = new_shopify_product_gid()
    product_data_gid = sample_product_data_factory(product_id_type="gid", product_id_override=gid)
    try:
        product_manager.create_product(product_data_gid) 
        product_data_gid_dup = sample_product_data_factory(product_id_type="gid", product_id_override=gid) 
        with pytest.raises(ConflictError, match=f"Product with logical ID '{gid}' already exists."):
            product_manager.create_product(product_data_gid_dup)
    finally:
        _cleanup_product_and_variants(db, gid)

    # Test with Custom ID
    custom_id = new_custom_product_id()
    product_data_custom = sample_product_data_factory(product_id_type="custom", product_id_override=custom_id)
    try:
        product_manager.create_product(product_data_custom)
        product_data_custom_dup = sample_product_data_factory(product_id_type="custom", product_id_override=custom_id)
        with pytest.raises(ValueError, match=f"Product with logical ID '{custom_id}' already exists."):
            product_manager.create_product(product_data_custom_dup)
    finally:
        _cleanup_product_and_variants(db, custom_id)


def test_get_product_not_found(product_manager: ProductManager):
    assert product_manager.get_product("non-existent-logical-id") is None

def test_update_product_ok(product_manager: ProductManager, sample_product_data_factory, db: firestore.Client):
    # Using a custom ID for this test, GID/Generated ID behavior for update is the same via logical ID
    product_data = sample_product_data_factory(product_id_type="custom", num_variants=1)
    logical_id = product_data.product_id
    try:
        product_manager.create_product(product_data)
        
        update_payload = {"title": "Updated Title", "vendor": "Updated Vendor"}
        updated_product = product_manager.update_product(logical_id, update_payload)
        
        assert updated_product is not None
        assert updated_product.title == "Updated Title"
        assert updated_product.vendor == "Updated Vendor"
        assert len(updated_product.variants) == 1 
        assert updated_product.variants[0].product_variant_id == product_data.variants[0].product_variant_id
    finally:
        _cleanup_product_and_variants(db, logical_id)

def test_delete_product_with_variants(product_manager: ProductManager, sample_product_data_factory, db: firestore.Client):
    # Using a GID for this test
    product_data = sample_product_data_factory(product_id_type="gid", num_variants=2)
    logical_id = product_data.product_id
    variant_logical_ids = [v.product_variant_id for v in product_data.variants] # These will be GIDs by default from factory
    try:
        product_manager.create_product(product_data)
        assert product_manager.get_product(logical_id) is not None 

        product_manager.delete_product(logical_id, delete_variants=True)
        
        assert product_manager.get_product(logical_id) is None
        for var_id in variant_logical_ids:
            assert product_manager.get_variant(var_id) is None
    finally:
        _cleanup_product_and_variants(db, logical_id) # Should be gone, but for safety
        for var_id in variant_logical_ids: # Should be gone, but for safety
             _cleanup_variant(db, var_id)


# --- Variant Tests ---

@pytest.mark.parametrize("variant_id_type_to_test", ["gid", "custom"])
def test_create_variant_ok(
    variant_id_type_to_test: Literal["gid", "custom"],
    product_manager: ProductManager, 
    sample_product_data_factory, 
    sample_product_variant_data_factory, 
    db: firestore.Client
):
    # Parent product uses a GID
    parent_product_data = sample_product_data_factory(product_id_type="gid")
    parent_logical_id = parent_product_data.product_id
    
    variant_logical_id_override = None
    if variant_id_type_to_test == "gid":
        variant_logical_id_override = new_shopify_variant_gid()
    else: # custom
        variant_logical_id_override = new_custom_variant_id()

    variant_data = sample_product_variant_data_factory(
        parent_logical_id, 
        variant_id_type=variant_id_type_to_test, # This will be used if override is None
        variant_logical_id_override=variant_logical_id_override
    )
    variant_logical_id = variant_data.product_variant_id

    try:
        product_manager.create_product(parent_product_data) # Create parent first
        
        created_variant = product_manager.create_variant(variant_data)
        assert created_variant is not None
        assert created_variant.product_variant_id == variant_logical_id
        assert created_variant.product_id == parent_logical_id

        # Verify Firestore document ID for the variant
        variant_doc_ref = product_manager._find_doc_ref_by_logical_id(
            product_manager.variants_ref, ProductVariant.PRODUCT_VARIANT_ID_FIELD, variant_logical_id
        )
        assert variant_doc_ref is not None
        if variant_id_type_to_test == "gid":
            expected_variant_doc_id = shopify_gid_to_hash(variant_logical_id, product_manager.hash_salt)
            assert variant_doc_ref.id == expected_variant_doc_id
        else: # custom
            assert variant_doc_ref.id != variant_logical_id
            assert variant_doc_ref.id != shopify_gid_to_hash(variant_logical_id, product_manager.hash_salt)


        retrieved_variant = product_manager.get_variant(variant_logical_id)
        assert retrieved_variant is not None
        assert retrieved_variant.sku == variant_data.sku
    finally:
        _cleanup_product_and_variants(db, parent_logical_id)
        _cleanup_variant(db, variant_logical_id)


def test_get_variants_for_product_ok(
    product_manager: ProductManager, 
    sample_product_data_factory, 
    sample_product_variant_data_factory, 
    db: firestore.Client
):
    # Parent product with GID, variants with mixed GID/custom IDs
    product_data = sample_product_data_factory(product_id_type="gid", num_variants=0) # Start with no variants from factory

    
    # Manually create variants with different ID types
    variant1_data = sample_product_variant_data_factory(product_data.product_id, variant_id_type="gid")
    variant2_data = sample_product_variant_data_factory(product_data.product_id, variant_id_type="custom")
    variant3_data = sample_product_variant_data_factory(product_data.product_id, variant_id_type="gid")
    
    product_data.variants = [variant1_data, variant2_data, variant3_data]
    logical_id = product_data.product_id
    expected_variant_ids = sorted([v.product_variant_id for v in product_data.variants])
    try:
        product_manager.create_product(product_data)
        
        retrieved_variants = product_manager.get_variants_for_product_by_logical_id(logical_id)
        assert len(retrieved_variants) == 3
        
        retrieved_variant_ids = sorted([v.product_variant_id for v in retrieved_variants])
        assert retrieved_variant_ids == expected_variant_ids
    finally:
        _cleanup_product_and_variants(db, logical_id)

@pytest.mark.parametrize("variant_id_type_to_test", ["gid", "custom"])
def test_update_variant_ok(
    variant_id_type_to_test: Literal["gid", "custom"],
    product_manager: ProductManager, 
    sample_product_data_factory, 
    sample_product_variant_data_factory, 
    db: firestore.Client
):
    parent_product_data = sample_product_data_factory(product_id_type="gid") # Parent with GID
    parent_logical_id = parent_product_data.product_id
    
    variant_data = sample_product_variant_data_factory(parent_logical_id, variant_id_type=variant_id_type_to_test)
    variant_logical_id = variant_data.product_variant_id

    try:
        product_manager.create_product(parent_product_data)
        product_manager.create_variant(variant_data)
        
        update_payload = {"price": 99.99, "sku": "NEW-SKU-123"}
        updated_variant = product_manager.update_variant(variant_logical_id, update_payload)
        
        assert updated_variant is not None
        assert updated_variant.price == 99.99
        assert updated_variant.sku == "NEW-SKU-123"
    finally:
        _cleanup_product_and_variants(db, parent_logical_id)
        _cleanup_variant(db, variant_logical_id)

@pytest.mark.parametrize("variant_id_type_to_test", ["gid", "custom"])
def test_delete_variant_ok(
    variant_id_type_to_test: Literal["gid", "custom"],
    product_manager: ProductManager, 
    sample_product_data_factory, 
    sample_product_variant_data_factory, 
    db: firestore.Client
):
    parent_product_data = sample_product_data_factory(product_id_type="gid") # Parent with GID
    parent_logical_id = parent_product_data.product_id
    
    variant_data = sample_product_variant_data_factory(parent_logical_id, variant_id_type=variant_id_type_to_test)
    variant_logical_id = variant_data.product_variant_id

    try:
        product_manager.create_product(parent_product_data)
        product_manager.create_variant(variant_data)
        
        assert product_manager.get_variant(variant_logical_id) is not None
        product_manager.delete_variant(variant_logical_id)
        assert product_manager.get_variant(variant_logical_id) is None
    finally:
        _cleanup_product_and_variants(db, parent_logical_id)
        _cleanup_variant(db, variant_logical_id) # Should be gone
