# new data cleaning function
from time import sleep
from datetime import timedelta
import pytest
from flows.force_active_partnership import ForceActivePartnershipFlow
from models.requests.sanitization_request import ForceAgreementRequest
from tagger.sales_tagger import SalesStagingProducerTagger, SalesStagingProducerTagRequest
from gaco_framework.auth import AuthContext
from constants.collections import (
    producer_collection,
    agreement_collection
)
from firebase_admin import firestore


def test_sales_data_tagger(
        n_sales_stagin, 
        old_start_date, 
        old_end_date, 
        db, 
        user_with_root_account,
        test_store
    ):
    # missing objects
    sampel_0 = n_sales_stagin[0]
    
    sample_0_doc = db.collection("sales-staging").document(sampel_0).get()

    sample_0_data = sample_0_doc.to_dict()

    # old_start_date
    # old_end_date - timedelta(days=700)


    store_id = sample_0_data['storeId']

    # 1. make an agreement + producer

    sleep(15)
    # store_doc = db.collection('storesV2').document(test_store).get().to_dict()

    auth_context = AuthContext(user_id=user_with_root_account.uid)

    # 1. make an agreement + producer
    result = ForceActivePartnershipFlow(db, auth_context)\
        .force_create_active_partnership(
            ForceAgreementRequest(
                store_id=test_store,
                title="test-title",
                effective_date=old_start_date,
                expiration_date=old_end_date - timedelta(days=700),
                commission=60,
                document_url="document_url",
                display_name="producer one",
                email="<EMAIL>",
                tax_a2="NL"
            )
        )


    # Create the tagging request using our new model
    sales_data_tag_request = SalesStagingProducerTagRequest(
        store_id=store_id,
        fields=["title", "variantTitle", "variantDisplayName", "vendor"],
        tag_rule={
            result['producer_id']: ['producer_1', 'producer 1'],
        },
        override_vendor=True,
        update_vendor_if_null=True,
    )


    sleep(15)
    # Initialize the sales tagger and execute tagging
    sales_tagger = SalesStagingProducerTagger(db)
    tagging_result_one = sales_tagger.tag_sales_data(sales_data_tag_request)

    import pdb; pdb.set_trace()

    # manual check for the sales staging document,
    # there must be 15 sales staging that have been processed
    n_sales_stagin
    db.collection("sales-staging").document('test-2').get().to_dict()

    db.collection(producer_collection).document(result['producer_id']).get().to_dict()



    tagging_result_one

    # Verify the results
    assert tagging_result_one["success"] is True
    assert tagging_result_one["store_id"] == store_id
    # there are 15 sales staging documents
    assert tagging_result_one["total_processed"] == 15
    # there must be 5 sales that was tagged with the producer
    assert tagging_result_one["total_tagged"] == 5

    print(f"Tagging completed: {tagging_result_one}")
    
    import pdb; pdb.set_trace()

    # clean up
    db.collection(producer_collection).document(result['producer_id']).delete()
    db.collection(agreement_collection).document(result['agreement_id']).delete()



