import pytest
import os
import datetime
import uuid
import requests

# Adjust these import paths based on your project structure if necessary.
# Assumes 'gaco_secrets', 'services', 'models' are importable (e.g., via sys.path modification in conftest.py)
from gaco_secrets.secret_manager import SecretManager
from services.shopify_product_creator import ShopifyProductCreator
from models.products import Product, ProductVariant
from constants.gaco_values import project_id
# Assuming DateTime is either 'from datetime import datetime as DateTime' in models.base
# or a Pydantic-compatible custom type.
# from models.base import DateTime


# Environment variables expected for these integration tests:
# - GOOGLE_APPLICATION_CREDENTIALS: Path to Firebase service account key (for db fixture in conftest).
# - TEST_STORE_ID: Firestore document ID for the test Shopify store in 'customer_api_keys'.
# - TEST_SHOPIFY_API_VERSION: Shopify API version (e.g., "2024-07").
# - TEST_DEFAULT_LOCATION_ID (Optional): Shopify Location GID for inventory tests.
# - PROJECT_ID (implicitly used by SecretManager or from conftest fixture)

def _create_sample_product_data(with_variants=False, with_inventory=False) -> Product:
    """Helper function to create sample Product data for testing."""
    now_utc = datetime.datetime.now(datetime.timezone.utc)
    unique_suffix = uuid.uuid4().hex[:8]
    
    product_fields = {
        "title": f"Pytest Integration Test Product {unique_suffix}",
        "vendor": "Pytest Vendor Co.",
        "product_type": "Pytest Goods",
        "status": "DRAFT",  # Use DRAFT to avoid affecting a live store's visibility
        "handle": f"pytest-integration-product-{unique_suffix}", # Shopify uses handle for URL
        "created_at": now_utc, # Pydantic should handle datetime objects for DateTime fields
        "updated_at": now_utc,
        "variants": []
        # Optional fields like store_id, producer_id from your Product model are not sent to Shopify.
    }

    if with_variants:
        variant1_data = {
            "title": "Small Pytest Size",
            "price": 19.99,
            "sku": f"PYTEST-S-{unique_suffix}",
            "created_at": now_utc,
            "updated_at": now_utc
        }
        if with_inventory:
            variant1_data["inventory_quantity"] = 10

        variant2_data = {
            "title": "Medium Pytest Size",
            "price": 22.99,
            "sku": f"PYTEST-M-{unique_suffix}",
            "created_at": now_utc,
            "updated_at": now_utc,
        }
        product_fields["variants"] = [ProductVariant(**variant1_data), ProductVariant(**variant2_data)]
    
    return Product(**product_fields)


@pytest.fixture(scope="function")
def shopify_creator_instance(db, test_store): # db and project_id fixtures from conftest.py
    """
    Sets up the ShopifyProductCreator instance for the test module.
    Handles cleanup of any Shopify products created during the tests.
    Yields:
        tuple: (creator_instance, store_id, api_version, products_to_cleanup_list)
    """

    
    try:
        # Initialize SecretManager using project_id from conftest
        secret_manager = SecretManager(project_id=project_id)
        creator = ShopifyProductCreator(db=db, secret_manager=secret_manager)
    except Exception as e:
        pytest.skip(f"ShopifyProductCreator or SecretManager initialization failed: {e}. Skipping tests.")

    products_to_cleanup = [] # List to store GIDs of products created

    yield creator, test_store,  products_to_cleanup

    # --- Teardown ---
    if not products_to_cleanup:
        return

    print(f"\nCleaning up {len(products_to_cleanup)} Shopify product(s) created by Pytest...")
    try:
        api_key, shop_name = creator.get_access_token(test_store) # Use instance method
        shop_domain = f"{shop_name}.myshopify.com"
        graphql_url = f"https://{shop_domain}/admin/api/2024-04/graphql.json"
        headers = {
            'Content-Type': 'application/json',
            'X-Shopify-Access-Token': api_key
        }

        for product_gid in products_to_cleanup:
            delete_mutation = f"""
            mutation productDelete {{
              productDelete(input: {{id: "{product_gid}"}}) {{
                deletedProductId
                userErrors {{ field message }}
              }}
            }}
            """
            try:
                response = requests.post(graphql_url, headers=headers, json={'query': delete_mutation}, timeout=15)
                response.raise_for_status()
                result = response.json()
                deleted_payload = result.get('data', {}).get('productDelete', {})
                if deleted_payload.get('userErrors'):
                    print(f"Warning: UserErrors during Pytest cleanup of {product_gid}: {deleted_payload['userErrors']}")
                elif deleted_payload.get('deletedProductId'):
                    print(f"Pytest: Successfully cleaned up product {deleted_payload['deletedProductId']}")
                else:
                    print(f"Pytest cleanup response for {product_gid} did not confirm deletion: {result}")
            except Exception as e:
                print(f"Pytest: Error during cleanup of product {product_gid}: {e}")
    except Exception as e:
        print(f"Pytest: Error during ShopifyProductCreator teardown (e.g., getting access token for cleanup): {e}")


def test_add_product_to_shopify_no_variants(shopify_creator_instance):
    """Test creating a simple product without variants in Shopify."""
    creator, store_id, products_to_cleanup = shopify_creator_instance
    product_to_create = _create_sample_product_data(with_variants=False)
    
    created_product_gid = None
    try:
        created_product_gid = creator.add_product_to_shopify(
            store_id=store_id,
            product_to_add=product_to_create
        )
        assert created_product_gid is not None, "Shopify GID should not be None after creation."
        assert isinstance(created_product_gid, str), "Shopify GID should be a string."
        assert created_product_gid.startswith("gid://shopify/Product/"), \
            f"Returned GID '{created_product_gid}' does not conform to Product GID format."
        if created_product_gid:
            products_to_cleanup.append(created_product_gid)
    except Exception as e:
        pytest.fail(f"add_product_to_shopify (no variants) raised an exception: {e}")


def test_add_product_to_shopify_with_variants(shopify_creator_instance):
    """Test creating a product with multiple variants in Shopify."""
    creator, store_id, products_to_cleanup = shopify_creator_instance
    product_to_create = _create_sample_product_data(with_variants=True)
    
    created_product_gid = None
    try:
        created_product_gid = creator.add_product_to_shopify(
            store_id=store_id,
            product_to_add=product_to_create
        )
        assert created_product_gid is not None, "Shopify GID should not be None after creation."
        assert isinstance(created_product_gid, str), "Shopify GID should be a string."
        assert created_product_gid.startswith("gid://shopify/Product/"), \
            f"Returned GID '{created_product_gid}' does not conform to Product GID format."
        if created_product_gid:
            products_to_cleanup.append(created_product_gid)
    except Exception as e:
        pytest.fail(f"add_product_to_shopify (with variants) raised an exception: {e}")


def test_add_product_to_shopify_with_inventory(shopify_creator_instance):
    """Test creating a product with variants and inventory. Requires TEST_DEFAULT_LOCATION_ID."""
    creator, store_id, products_to_cleanup = shopify_creator_instance
    default_location_id = os.getenv("TEST_DEFAULT_LOCATION_ID", 'gid://shopify/Location/67203825914')
    
    if not default_location_id:
        pytest.skip("TEST_DEFAULT_LOCATION_ID environment variable not set. Skipping inventory test.")

    product_to_create = _create_sample_product_data(with_variants=True, with_inventory=True)
    created_product_gid = None
    try:
        created_product_gid = creator.add_product_to_shopify(
            store_id=store_id,
            product_to_add=product_to_create,
            default_location_id=default_location_id
        )
        assert created_product_gid is not None, "Shopify GID should not be None after creation."
        assert isinstance(created_product_gid, str), "Shopify GID should be a string."
        assert created_product_gid.startswith("gid://shopify/Product/"), \
            f"Returned GID '{created_product_gid}' does not conform to Product GID format."
        if created_product_gid:
            products_to_cleanup.append(created_product_gid)
    except Exception as e:
        pytest.fail(f"add_product_to_shopify (with inventory) raised an exception: {e}")


def test_add_product_to_shopify_with_inventory_and_no_location(shopify_creator_instance):
    """Test creating a product with variants and inventory. Requires TEST_DEFAULT_LOCATION_ID."""
    creator, store_id, products_to_cleanup = shopify_creator_instance

    product_to_create = _create_sample_product_data(with_variants=True, with_inventory=True)
    created_product_gid = None
    try:
        created_product_gid = creator.add_product_to_shopify(
            store_id=store_id,
            product_to_add=product_to_create
        )
        assert created_product_gid is not None, "Shopify GID should not be None after creation."
        assert isinstance(created_product_gid, str), "Shopify GID should be a string."
        assert created_product_gid.startswith("gid://shopify/Product/"), \
            f"Returned GID '{created_product_gid}' does not conform to Product GID format."
        if created_product_gid:
            products_to_cleanup.append(created_product_gid)
    except Exception as e:
        pytest.fail(f"add_product_to_shopify (with inventory) raised an exception: {e}")


def test_add_product_to_shopify_invalid_store_id(shopify_creator_instance):
    """Test behavior when an invalid store_id is provided (should fail to get credentials)."""
    creator, _,  _ = shopify_creator_instance # products_to_cleanup not used here
    
    product_to_create = _create_sample_product_data()
    invalid_store_id = f"non-existent-store-id-{uuid.uuid4().hex[:8]}"

    with pytest.raises(ValueError, match=f"Failed to fetch access token for {invalid_store_id}"):
        creator.add_product_to_shopify(
            store_id=invalid_store_id,
            product_to_add=product_to_create
        )
