import pytest
from datetime import datetime, timezone
from models.sales import SalesStaging
from models.application import Application
from matchers.application_matchers import Vendor<PERSON>d<PERSON>atch<PERSON>, TitleMatcher, CompositeApplicationMatcher
from matchers.factories import ApplicationMatcherFactory, MatchingStrategy
from matchers.application_matchers import DisplayNameMatcher

@pytest.fixture
def sample_sale():
    return {
        'vendor': 'vendor123',
        'title': 'Handmade Bracelet by ArtisanCrafts'
    }

@pytest.fixture
def sample_applications():
    return [
        {
            'recipientId': 'vendor123',
            'producer_display_name': 'ArtisanCrafts',
            'status': 'ACCEPTED'
        },
        {
            'recipientId': 'vendor456',
            'producer_display_name': 'OtherCrafts',
            'status': 'ACCEPTED'
        }
    ]

@pytest.fixture
def sample_sales_staging_display_name_matcher():
    return SalesStaging(
        document_id='123',
        store_id='123',
        order_id='123',
        line_item_id='123',
        product_id='123',
        title='Ring',
        variant_title='Silver',
        variant_display_name='Silver',
        quantity=1,
        unit_price=100,
        subtotal=100,
        total_price=100,
        currency='USD',
        discount=0,
        vendor='Laurie',
        updated_at=datetime.now(timezone.utc),
        store_display_name='Gast Art!'
    )

@pytest.fixture
def sample_sales_staging_display_name_matcher_applications():
    return [
        {
            'recipientId': 'l4ur13',
            'producer_display_name': 'Laurie'
        }
    ]

def test_display_name_matcher(
        sample_sales_staging_display_name_matcher, 
        sample_sales_staging_display_name_matcher_applications
    ):
    matcher = DisplayNameMatcher()
    result = matcher.match(
        sample_sales_staging_display_name_matcher, 
        sample_sales_staging_display_name_matcher_applications
    )

    assert result is not None
    assert result[Application.RECIPIENT_ID_FIELD] == 'l4ur13'

def test_vendor_id_matcher(sample_sale, sample_applications):
    matcher = VendorIdMatcher()
    result = matcher.match(sample_sale, sample_applications)
    
    assert result is not None
    assert result['recipientId'] == 'vendor123'

def test_vendor_id_matcher_no_match(sample_sale, sample_applications):
    matcher = VendorIdMatcher()
    modified_sale = {**sample_sale, 'vendor': 'nonexistent'}
    result = matcher.match(modified_sale, sample_applications)
    
    assert result is None

def test_vendor_id_matcher_no_vendor(sample_applications):
    matcher = VendorIdMatcher()
    sale_without_vendor = {'title': 'Test Product'}
    result = matcher.match(sale_without_vendor, sample_applications)
    
    assert result is None

def test_title_matcher(sample_sale, sample_applications):
    matcher = TitleMatcher()
    result = matcher.match(sample_sale, sample_applications)
    
    assert result is not None
    assert result['producer_display_name'] == 'ArtisanCrafts'

def test_title_matcher_no_match(sample_sale, sample_applications):
    matcher = TitleMatcher()
    modified_sale = {**sample_sale, 'title': 'Product without producer name'}
    result = matcher.match(modified_sale, sample_applications)
    
    assert result is None

def test_composite_matcher_vendor_first():
    sale = {
        'vendor': 'vendor123',
        'title': 'Generic Product'
    }
    applications = [
        {
            'recipientId': 'vendor123',
            'producer_display_name': 'Producer1'
        }
    ]
    
    matcher = CompositeApplicationMatcher([
        VendorIdMatcher(),
        TitleMatcher()
    ])
    
    result = matcher.match(sale, applications)
    assert result is not None
    assert result['recipientId'] == 'vendor123'

def test_factory_create_default():
    matcher = ApplicationMatcherFactory.create()

    isinstance(matcher, CompositeApplicationMatcher)

    print(f"Matcher type: {type(matcher)}")
    print(f"CompositeApplicationMatcher type: {CompositeApplicationMatcher}")
    print(f"Matcher module: {type(matcher).__module__}")
    print(f"CompositeApplicationMatcher module: {CompositeApplicationMatcher.__module__}")

    assert isinstance(matcher, CompositeApplicationMatcher)
    assert len(matcher.strategies) == 2
    assert isinstance(matcher.strategies[0], VendorIdMatcher)
    assert isinstance(matcher.strategies[1], TitleMatcher)

@pytest.mark.parametrize("strategy,expected_types", [
    (MatchingStrategy.VENDOR_ID_ONLY, [VendorIdMatcher]),
    (MatchingStrategy.TITLE_ONLY, [TitleMatcher]),
    (MatchingStrategy.VENDOR_ID_FIRST, [VendorIdMatcher, TitleMatcher]),
    (MatchingStrategy.TITLE_FIRST, [TitleMatcher, VendorIdMatcher]),
])
def test_factory_create_strategies(strategy, expected_types):
    matcher = ApplicationMatcherFactory.create(strategy)
    assert isinstance(matcher, CompositeApplicationMatcher)
    assert len(matcher.strategies) == len(expected_types)
    for strategy, expected_type in zip(matcher.strategies, expected_types):
        assert isinstance(strategy, expected_type)

def test_factory_create_custom():
    custom_matchers = [TitleMatcher(), VendorIdMatcher()]
    matcher = ApplicationMatcherFactory.create_custom(custom_matchers)
    
    assert isinstance(matcher, CompositeApplicationMatcher)
    assert len(matcher.strategies) == 2
    assert isinstance(matcher.strategies[0], TitleMatcher)
    assert isinstance(matcher.strategies[1], VendorIdMatcher)

def test_factory_from_config():
    config = {
        "strategy": "title_only",
        "options": {
            "title_match_threshold": 0.9,
            "case_sensitive": True
        }
    }
    
    matcher = ApplicationMatcherFactory.from_config(config)
    assert isinstance(matcher, CompositeApplicationMatcher)
    assert len(matcher.strategies) == 1
    assert isinstance(matcher.strategies[0], TitleMatcher)