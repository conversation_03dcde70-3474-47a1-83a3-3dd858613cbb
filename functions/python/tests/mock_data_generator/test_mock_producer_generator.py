import pytest
import pandas as pd
from models.producer import Producer
from datetime import datetime, timezone
from queries.producers_query_builder_v2 import ProducersQueryBuilderV2
from queries.partners_query_builder import ApplicationsQueryBuilder
from models.application import Application

@pytest.fixture
def dream_unit_producers():
  # df = pd.read_csv('tests/tmp_data/dream_unit_producers.csv')
  df = pd.read_csv('tests/tmp_data/dream_unit_producers - dream_unit_producers.csv')
  # df = pd.read_csv('tests/tmp_data/test_gaco_producers.csv')
  # Convert NaN values to empty strings
  df = df.fillna('')
  return df

@pytest.fixture
def dream_unit_applications():
  df = pd.read_csv('tests/tmp_data/dream_unit_applications - dream_unit_applications.csv')
  # df = pd.read_csv('tests/tmp_data/test_gaco_applications.csv')
  df = df.fillna('')
  return df


@pytest.mark.skip(reason="Skipping test for now")
def test_generate_dream_unit_producers(dream_unit_producers, db):
  # Convert DataFrame to numpy arrays for better performance
  display_names = dream_unit_producers['displayName'].to_numpy()
  emails = dream_unit_producers['email'].to_numpy()
  tax_a2s = dream_unit_producers['tax_A2'].to_numpy()
  parent_ids = dream_unit_producers['parentId'].to_numpy()

  for i in range(len(display_names)):
    # check if the producer already exists
    query = ProducersQueryBuilderV2(db)\
      .for_email(emails[i])\
      .for_display_name(display_names[i])\
      .build()

    # Get query results
    query_results = query.get()
    
    # Check the raw format of the query results
    print(f"Query results type: {type(query_results)}")
    print(f"Query results content: {query_results}")
    
    if len(query_results) > 0:
      for result in query_results:
          producer_id = result.id

          db.collection('producers').document(producer_id).update({
            'parentId': parent_ids[i],
            'tax_A2': tax_a2s[i],
          })
          continue
    else: 
      producer = Producer(
        displayName=display_names[i],
        email=emails[i],
        tax_A2=tax_a2s[i],
        parentId=parent_ids[i],
        created_at=datetime.now(timezone.utc)
      )

      producer_ref = db.collection('producers').document()
      producer_ref.set(producer.model_dump())

  pass

# @pytest.mark.skip(reason="Skipping test for now")
def test_generate_dream_unit_applications(dream_unit_applications, db):
  # 2. create applications with accepted status and other relevant info.
  dream_unit_applications

  sender_ids = dream_unit_applications['senderId'].to_numpy()
  producer_display_names = dream_unit_applications['producer_displayName'].to_numpy()
  producer_emails = dream_unit_applications['producer_email'].to_numpy()
  statuses = dream_unit_applications['status'].to_numpy()
  commissions = dream_unit_applications['commission'].to_numpy()

  for i in range(len(sender_ids)):
    sender_id = sender_ids[i]
    producer_display_name = producer_display_names[i]
    producer_email = producer_emails[i]
    status = statuses[i]
    commission = commissions[i]

    # Check if the producer already exists
    query = ProducersQueryBuilderV2(db)\
      .for_email(producer_email)\
      .for_display_name(producer_display_name)\
      .build()


    query_results = query.get()

    if len(query_results) > 0:
      for result in query_results:
        result.to_dict()
        producer_id = result.id

        application_ref = db.collection('applications')\
          .document()
        # check if the application already exists
        application_query = ApplicationsQueryBuilder(db)\
          .for_recipient_id(producer_id)\
          .for_sender_id(sender_id)\
          .build()

        application_query_results = application_query.get()

        if len(application_query_results) > 0:
          for application_result in application_query_results:
            application_result.to_dict()
            # Update the application with new commission and status
            application_id = application_result.id

            db.collection('applications').document(application_id).update({
                'commission': int(commission),
                'status': status
            })
            # do nothing as the application already exists
            # or update the application
            continue
          continue
        else:

          application_ref.set(
            Application(
              senderId=sender_id,
              recipientId=producer_id,
              status=status,
              commission=commission,
              appliedAt=datetime.now(timezone.utc)
            ).model_dump()
          )

          continue
    else: 
      raise Exception(f"Producer {producer_display_name} not found")
  pass


# 1. create producers
# -. get producers IDs that have been created
# 2. create applications with accepted status and other relevant info.
