import pytest
from datetime import datetime, timedelta, timezone
from models.sales import SalesStaging
import random
import uuid
from typing import Dict, List
import string
from faker import Faker


@pytest.fixture
def sales_staging_config():
  # max batch is not 500 but 10Mb
  return {
    'store_id': 'k8mqH8BBv8zpV7enshI0',
    'store_display_name': 'ga-test',
    'sales_count': 500,
    'sales_start_date': '2024-01-01',
    'sales_end_date': datetime.now(timezone.utc).strftime('%Y-%m-%d'),
    'percent_empty_vendor': 0.15,
    'percent_unlinked_vendor': 0.85
  }


def generate_mock_sales_staging(config: Dict) -> List[SalesStaging]:
    """
    Generate mock SalesStaging objects based on configuration.
    
    Args:
        config: Dictionary containing configuration parameters:
            - store_id: ID of the store
            - sales_count: Number of sales records to generate
            - sales_start_date: Start date for sales (YYYY-MM-DD)
            - sales_end_date: End date for sales (YYYY-MM-DD)
            - percent_empty_vendor: Percentage of sales with empty vendor (0.0-1.0)
            - percent_unlinked_vendor: Percentage of sales with random vendor names
    
    Returns:
        List of SalesStaging objects
    """
    # Validate config
    if not all(k in config for k in ['store_id', 'sales_count', 'sales_start_date', 
                                     'sales_end_date', 'percent_empty_vendor', 'percent_unlinked_vendor']):
        raise ValueError("Missing required configuration parameters")
    
    # Parse dates
    start_date = datetime.strptime(config['sales_start_date'], '%Y-%m-%d')
    
    if isinstance(config['sales_end_date'], str):
        end_date = datetime.strptime(config['sales_end_date'], '%Y-%m-%d')
    else:
        end_date = config['sales_end_date']
    
    # Validate percentages
    if config['percent_empty_vendor'] + config['percent_unlinked_vendor'] > 1.0:
        raise ValueError("Sum of vendor percentages exceeds 100%")
    
    store_id = config['store_id']
    store_display_name = config['store_display_name']
    sales_count = config['sales_count']
    
    
    # Generate mock sales
    mock_sales = []
    date_range = (end_date - start_date).days
    
    currencies = ["USD", "EUR", "GBP", "CAD"]
    product_types = ["Ring", "Necklace", "Bracelet", "Earrings", "Pendant", "Watch", "Brooch"]
    materials = ["Gold", "Silver", "Platinum", "Wood", "Ceramic", "Glass", "Leather"]

    faker = Faker()
    vendor_count = sales_count / 20
    # Generate a list of vendor names
    vendors = [faker.company() for _ in range(int(vendor_count))]
    vendors = vendors[:5]
# ['Mora, Lewis and Hurley', 'Smith-Leonard', 'Howell, Walker and Winters', 'Rogers-Ramsey', 'Long Inc']
    import pdb; pdb.set_trace()
    print(vendors)



    for _ in range(sales_count):
        # Generate random date within range
        random_days = random.randint(0, date_range)
        sale_date = start_date + timedelta(days=random_days)
        
        # Random time component
        sale_date = sale_date.replace(
            hour=random.randint(8, 23),
            minute=random.randint(0, 59),
            second=random.randint(0, 59)
        )
        
        # Determine vendor based on configuration percentages
        vendor_rand = random.random()
        if vendor_rand < config['percent_empty_vendor']:
            vendor = None
        else:
            # Random unlinked vendor name
            # vendor = f"Vendor {''.join(random.choices(string.ascii_uppercase, k=3))}"
            vendor = random.choice(vendors)
        
        # Generate random product details
        product_type = random.choice(product_types)
        material = random.choice(materials)
        title = f"{material} {product_type}"
        
        # Price and quantity
        unit_price = round(random.uniform(10.0, 500.0), 2)
        quantity = random.randint(1, 5)
        subtotal = unit_price * quantity
        discount = round(random.uniform(0, subtotal * 0.2), 2)  # Up to 20% discount
        total_price = subtotal - discount
        
        # Generate variant information (some items might not have variants)
        has_variant = random.random() < 0.7  # 70% chance of having a variant
        variant_title = f"{random.choice(['Small', 'Medium', 'Large', 'XL'])}" if has_variant else None
        variant_display_name = f"{title} - {variant_title}" if variant_title else None
        
        # Create SalesStaging object
        sale = SalesStaging(
            document_id=str(uuid.uuid4()),
            store_id=store_id,
            order_id=f"order_{random.randint(10000, 99999)}",
            line_item_id=f"item_{random.randint(10000, 99999)}",
            product_id=f"prod_{random.randint(10000, 99999)}",
            title=title,
            variant_title=variant_title,
            variant_display_name=variant_display_name,
            quantity=quantity,
            unit_price=unit_price,
            subtotal=subtotal,
            total_price=total_price,
            currency=random.choice(currencies),
            discount=discount,
            vendor=vendor,
            updated_at=sale_date,
            store_display_name=store_display_name
        )
        
        mock_sales.append(sale)
    
    return mock_sales


def test_generate_sales_staging_data(sales_staging_config, db):
  mock_sales = generate_mock_sales_staging(sales_staging_config)

  import pdb; pdb.set_trace()

  batch = db.batch()
  for sale in mock_sales:
    doc_ref = db.collection('sales-staging').document(sale.document_id)
    batch.set(doc_ref, sale.model_dump())
  batch.commit()

  assert False