import pytest
import random
from pydantic import ValidationError
from models.agreement import Agreement, Role
from constants.collections import agreement_collection


def test_agreement_data_migration_with_created_by_role(db):
    # 1. get all agreements
    agreements = db.collection(agreement_collection).stream()
    roles = [Role.STORE.value, Role.PRODUCER.value]
    for agreement in agreements:
        new_role = random.choice(roles)
        agreement_doc = agreement.to_dict()
        agreement_doc['createdByRole'] = new_role

        try:
            Agreement.model_validate(agreement_doc)
            print(f"Updating agreement {agreement.id} with role {new_role}")
            agreement.reference.update({'createdByRole': new_role})
        except ValidationError as e:
            if "Effective date must be before expiration date" in str(e):
                print(f"Fixing date for agreement {agreement.id}")
                expiration_date = agreement_doc.get('expirationDate')
                if expiration_date:
                    new_expiration_date = expiration_date.replace(year=expiration_date.year + 10)
                    agreement_doc['expirationDate'] = new_expiration_date
                    
                    Agreement.model_validate(agreement_doc)

                    print(f"Updating agreement {agreement.id} with fixed date and new role.")
                    agreement.reference.update({
                        'createdByRole': new_role,
                        'expirationDate': new_expiration_date
                    })
            else:
                print(f"Unhandled validation Error in agreement {agreement.id}: {e}")
                import pdb; pdb.set_trace()

            agreement_doc.get('expirationDate')
