import pytest
from services.store_manager import StoreManager
from services.producer_manager import <PERSON><PERSON>anager
from models.requests.store_requests import DeleteStoreRequest
from models.requests.producer_requests import DeleteProducerRequest
from firebase_admin import storage
from services.partner_manager import PartnershipManager
from models.user_root_account import UserRootAccount
from models.access_right import AccessRight
from models.store import StoreV2
from models.payout_account import Address


def test_clean_mock_store_data(db):
    # 1. delete all documents in the stores collection
    store_manager = StoreManager(db)

    # Query for stores where the email starts with "test-"
    query = db.collection('storesV2').where('email', '>=', 'test').where('email', '<', 'test\uf8ff')
    docs = query.stream() # Use stream() for an iterator or get() to fetch all at once

    # You can then iterate over the documents
    for doc in docs:
        print(f'{doc.id} => {doc.to_dict()}')
        delete_request = DeleteStoreRequest(
          store_id=doc.id,
          hard_delete=True
        )
        try:
          store_manager.delete_store(delete_request, 'test-user')
        except Exception as e:
          print(f'Error deleting store {doc.id}: {e}')

    pass

STORAGE_BUCKET_NAME = 'gast-art-platform-87104.appspot.com'
def test_stores_storage(db):
    """
    Cleans up orphaned objects in Firebase Storage under 'stores/' prefix
    using firebase_admin.storage. It deletes objects whose corresponding
    store ID (parsed from path) does not exist in either the 'stores'
    or 'storesV2' Firestore collections.
    """
    if not STORAGE_BUCKET_NAME or STORAGE_BUCKET_NAME == "your-project-id.appspot.com":
        pytest.skip("Firebase Storage bucket name is not configured in the test.")


    # 1. Get all valid store IDs from Firestore into a set for efficient lookup
    valid_store_ids = set()
    print("Fetching valid store IDs from Firestore 'storesV2'...")
    stores_v2 = db.collection('storesV2').stream()
    for store in stores_v2:
        valid_store_ids.add(store.id)
    print(f"Found {len(valid_store_ids)} IDs in 'storesV2'.")

    print("Fetching valid store IDs from Firestore 'stores'...")
    count_v1 = 0
    stores_v1 = db.collection('stores').stream()
    for store in stores_v1:
        valid_store_ids.add(store.id)
        count_v1 += 1
    print(f"Found {count_v1} IDs in 'stores'. Total unique valid IDs: {len(valid_store_ids)}")


    # 2. Get the storage bucket reference
    try:
        bucket = storage.bucket(STORAGE_BUCKET_NAME)
        if bucket is None: # Check if bucket retrieval failed silently
             pytest.fail(f"Failed to get bucket reference for '{STORAGE_BUCKET_NAME}'. Is firebase_admin initialized correctly?")
    except Exception as e:
        pytest.fail(f"Failed to get storage bucket '{STORAGE_BUCKET_NAME}': {e}. Check initialization and bucket name.")

    # 3. List objects under 'stores/' prefix and identify orphans
    prefix_to_check = "stores/"
    blobs_to_delete = []
    print(f"\nChecking objects in gs://{bucket.name}/{prefix_to_check} for orphans...")
    try:
        # Note: list_blobs in firebase_admin SDK doesn't need the bucket passed again
        blobs_iterator = bucket.list_blobs(prefix=prefix_to_check)
        processed_blob_count = 0
        for blob in blobs_iterator:
            processed_blob_count += 1
            # Expected path format: stores/STORE_ID/path/to/file.ext
            parts = blob.name.split('/')
            if len(parts) > 1 and parts[0] == 'stores':
                storage_store_id = parts[1]
                # Ensure the extracted ID is not empty and not in the valid set
                if storage_store_id and storage_store_id not in valid_store_ids:
                    print(f"  [ORPHAN FOUND] Object: {blob.name} (Store ID: {storage_store_id} not in Firestore)")
                    blobs_to_delete.append(blob) # Add the blob object itself
            # else:
                # Optional: Log blobs that don't match the expected format if needed
                # print(f"  [INFO] Skipping blob with unexpected path format: {blob.name}")

        print(f"Checked {processed_blob_count} blobs. Found {len(blobs_to_delete)} potential orphans.")

    except Exception as e:
        pytest.fail(f"Failed to list blobs with prefix '{prefix_to_check}' in bucket '{bucket.name}': {e}")

    # 4. Delete the identified orphaned objects
    if not blobs_to_delete:
        print("No orphaned storage objects found to delete.")
        return # Test passes if no orphans found

    print(f"\nAttempting to delete {len(blobs_to_delete)} orphaned objects...")
    deleted_count = 0
    errors_count = 0
    for blob_to_delete in blobs_to_delete:
        try:
            # print(f"  Deleting {blob_to_delete.name}...") # Can be verbose
            blob_to_delete.delete() # Call delete directly on the blob object
            deleted_count += 1
        except Exception as e:
            print(f"  [ERROR] Failed to delete {blob_to_delete.name}: {e}")
            errors_count += 1

    print(f"\nDeletion complete. Successfully deleted: {deleted_count}, Errors: {errors_count}")

    if errors_count > 0:
        pytest.fail(f"{errors_count} errors occurred during orphaned storage object deletion.")
    else:
        print("Orphaned storage objects cleaned up successfully.")


def test_delete_producers(db):
    producer_manager = ProducerManager(db)

    query = db.collection('producersV2').where('email', '>=', 'temp').where('email', '<', 'temp\uf8ff')
    docs = query.stream() # Use stream() for an iterator or get() to fetch all at once
    for doc in docs:
        print(f'{doc.id} => {doc.to_dict()}')
        # import pdb; pdb.set_trace()
        delete_request = DeleteProducerRequest(
          producer_id=doc.id,
          hard_delete=True
        )
        try:
          producer_manager.delete_producer(delete_request, 'test-user')
        except Exception as e:
          print(f'Error deleting producer {doc.id}: {e}')


def test_clean_partnerships(db):
    for doc in db.collection('partnerships').stream():
        print(f'{doc.id} => {doc.to_dict()}')
        if not 'agreementId' in doc.to_dict().keys():
          doc.reference.delete()
        store_snapshot = db.collection('storesV2').document(doc.to_dict()['storeId']).get()
        if not store_snapshot.exists:
          doc.reference.delete()
        pass

    pass


@pytest.mark.skip(reason="This test is not needed anymore")
def test_migrate_user_root_account_from_v1_to_v2(db):
    user_root_accounts_collection = db.collection('userRootAccounts')


    user = user_root_accounts_collection.document('oizhNyyivdM2vCLOa68VpIliW6A3').get()
    
    user_dict = user.to_dict()
    
    stores_dict = {store_id: AccessRight.ADMIN.value for store_id in user_dict.get('stores')}
    producers_dict = {producer_id: AccessRight.ADMIN.value for producer_id in user_dict.get('producers')}
    
    user_root_account = UserRootAccount(
        email=user_dict['email'],
        stores=stores_dict,
        producers=producers_dict,
        created_at=user_dict['created_at'],
    )

    user_root_account = db.collection('userRootAccounts').document(user.id).set(user_root_account.model_dump())

    pass

@pytest.mark.skip(reason="This test is not needed anymore")
def test_migrate_stores_from_v1_to_v2(db):
    id = 'OZYPNqC7om7nIiObrHkj'
    store_docs = db.collection('stores').document(id).get()
    store_dict = store_docs.to_dict()

    secret_doc = db.collection('customer_api_keys').document(id).get()
    secret_dict = secret_doc.to_dict()

    address = Address(
        street_number="",
        street_name="Gasthuismolensteeg",
        house_number="3",
        zip_code="1016AM",
        city="Amsterdam",
        country="NL",
    )

    store_v2 = StoreV2(
      created_at=store_dict['createdAt'],
      display_name=store_dict['displayName'],
      email=store_dict['email'],
      parent_id=store_dict['parentId'],
      tax_a2=store_dict['tax_A2'],
      default_commission=store_dict['defaultCommission'],
      shopify_api_key_reference=secret_dict['secret_version_path'],
      description=store_dict['description'],
      address=address,
    )

    db.collection('storesV2').document(id).set(store_v2.model_dump())
    
    
    pass
