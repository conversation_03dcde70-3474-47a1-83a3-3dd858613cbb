import polars as pl
from models.sales import SalesGold, SalesSilver

class HelperInvoiceDataGoldConverter:

    @staticmethod
    def convert_invoice_data_gold(
        invoice_data: pl.DataFrame, 
        sales_silver: SalesSilver
    ) -> pl.DataFrame:
        """
        Convert single row of the invoice data to a SalesGold object.
        """
        if len(invoice_data) != 1:
            raise ValueError("Invoice data must have exactly one row")

        invoice_data_dict = invoice_data.to_dicts()[0]

        sales_gold_dict = {
          **sales_silver.model_dump(),
          'sale_id': invoice_data_dict['sale_id'],
          'assigned_vat_rate': invoice_data_dict['assigned_vat_rate'],
          'vat_excluded_sale': invoice_data_dict['vat_excluded_sale'],
          'net_sales': invoice_data_dict['net_sales'],
          'store_net_payout': invoice_data_dict['store_net_payout'],
          'vat_on_sales_service': invoice_data_dict['vat_on_sales_service'],
          'store_total_gross_payout': invoice_data_dict['store_total_gross_payout'],
          'store_gross_payout': invoice_data_dict['store_total_gross_payout'],
          'producer_gross_payout': invoice_data_dict['producer_gross_payout']
        }

        sales_gold = SalesGold.model_validate(sales_gold_dict)
        return sales_gold
