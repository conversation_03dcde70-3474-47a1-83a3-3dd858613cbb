# Somewhere in your service layer or cloud function handler...
from firebase_admin import firestore
from models.sales import SalesSilver
from data_source.sales_data_source import SalesDataSource
from calculators.pipelines.sales_report_pipeline import create_sales_report_pipeline
from data_generator.report_data_generator import ReportDataGenerator
from models.requests.sales_report_request import CreateSalesReportRequest # Assuming request format is similar
from firebase_functions import logger
from data_source.helper_doc_to_df import HelperDocToDf
from models.sales import SalesGold
from columns.sales_columns import SalesColumns, SalesCommonComputedColumns


def generate_sales_report_data_handler(
        db: firestore.client, 
        request_data: CreateSalesReportRequest
    ): # Example function

    # 1. Instantiate Dependencies
    sales_data_source = SalesDataSource(db)
    sales_report_pipeline = create_sales_report_pipeline()

    # 2. Instantiate Generic Generator
    report_generator = ReportDataGenerator(
        data_source=sales_data_source, 
        pipeline=sales_report_pipeline
    )

    # 3. Generate Report
    try:
        final_report_df = report_generator \
            .generate_data(criteria=request_data.model_dump())

        # ... process/save/return final_report_df ...
        logger.info("Sales report generated successfully.")
        return final_report_df # Or however you return results
        
    except ValueError as ve:
        message = f"Value Error generating report data: {ve}"
        logger.error(message)
        # Handle specific errors (e.g., no data, missing columns)
        raise ValueError(message)
    except Exception as e:
        message = f"Unexpected Error generating report data: {e}"
        logger.error(message)
        # Handle unexpected errors
        raise Exception(message)


def generate_sales_report_from_sales_silver_handler(
        sales_silver: SalesSilver, 
        doc_id: str
    ) -> SalesGold:
    df = HelperDocToDf().sales_silver_doc_to_df(sales_silver, doc_id)
    df = create_sales_report_pipeline().compute(df)
    df_dict = df.to_dicts()[0]

    sales_gold = {
        SalesGold.SALE_ID_FIELD: df_dict[SalesColumns.SALE_ID.value],
        SalesGold.STORE_GROSS_PAYOUT_FIELD: df_dict[SalesCommonComputedColumns.STORE_GROSS_PAYOUT.value],
        SalesGold.PRODUCER_GROSS_PAYOUT_FIELD: df_dict[SalesCommonComputedColumns.PRODUCER_GROSS_PAYOUT.value],
        **sales_silver.model_dump()
    }

    sales_gold = SalesGold.model_validate(sales_gold)
    return sales_gold
