
# from models.sales import SalesSilver, SalesGold
# from firebase_admin import firestore
# from models.requests.invoice_request import CreateInvoiceBySaleIdRequest
# from data_generator.invoice_data_generator import generate_invoice_data_handler
# from data_generator.helper_invoice_data_gold_converter import HelperInvoiceDataGoldConverter
# from models.sanitized_sales import sales_silver_collection


# def generate_single_invoice_data_handler(
#     db: firestore.Client,
#     request: CreateInvoiceBySaleIdRequest,
#     sales_silver: SalesSilver
# ) -> SalesGold:
#     """
#     Generate a single invoice data for a sales-silver document.
#     there is sales_silver object since this will be called from the event function
#     on sales-silver udpated or created.
#     passing sales_silver will reduce query to the db
#     """

#     df = generate_invoice_data_handler(db, request)

#     sales_gold = HelperInvoiceDataGoldConverter\
#       .convert_invoice_data_gold(df, sales_silver)

#     return sales_gold

    

# def generate_single_invoice_data_handler_from_documentId(
#     db: firestore.Client,
#     document_id: str
# ) -> SalesGold:
#     sales_silver = db.collection(sales_silver_collection).document(document_id).get().to_dict()
#     if not sales_silver:
#         raise ValueError(f"Sales silver document not found for document_id: {document_id}")

#     sales_silver = SalesSilver.model_validate(sales_silver)
#     request = CreateInvoiceBySaleIdRequest(sale_id=document_id)
#     return generate_single_invoice_data_handler(db, request, sales_silver)
