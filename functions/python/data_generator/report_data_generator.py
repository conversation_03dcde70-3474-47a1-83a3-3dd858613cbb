# data_generators/report_data_generator.py
import polars as pl
from typing import Dict, Any
from calculators.calculator_protocols import CalculatorPipeline # Import protocols
from data_source.data_source_protocol import DataSource


class ReportDataGenerator:
    def __init__(self, data_source: DataSource, pipeline: CalculatorPipeline):
        self.data_source = data_source
        self.pipeline = pipeline

    def generate_data(self, criteria: Dict[str, Any], context: Dict[str, Any] = None) -> pl.DataFrame:
        print(f"--- Generating Report Data ---")
        print(f"Criteria: {criteria}")
        
        # 1. Fetch Data
        df = self.data_source.fetch_data(criteria)

        if df.is_empty():
             print("No data found matching criteria. Report generation skipped.")
             return df # Return the empty DF with schema

        print(f"Data fetched successfully. Shape: {df.shape}")
        
        # 2. Run Calculation Pipeline
        print(f"Running pipeline: {self.pipeline.__class__.__name__}")
        result_df = self.pipeline.compute(df, context) # Pass context if needed by steps
        
        print(f"Pipeline computation finished. Result Shape: {result_df.shape}")
        print(f"--- Report Generation Complete ---")
        return result_df