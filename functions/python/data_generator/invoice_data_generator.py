# Somewhere in your service layer or cloud function handler...
from firebase_admin import firestore
from data_source.sales_data_source import SalesDataSource
from calculators.pipelines.invoice_pipeline import create_invoice_pipeline
from data_generator.report_data_generator import ReportDataGenerator
from models.requests.invoice_request import CreateInvoiceRequest, CreateInvoiceBySaleIdRequest
from data_source.auxiliary.vat_rates import VatRatesAuxiliaryDataSource
from typing import Union
import polars as pl
from firebase_functions import logger
from models.sales import SalesSilver
from columns.sales_columns import SalesColumns
from models.sanitized_sales import sales_silver_collection

# Define the collection name as a constant if not already available
SALES_SILVER_COLLECTION = sales_silver_collection

def generate_invoice_data_handler(
        db: firestore.Client, 
        # Use Union for type hinting the request parameter
        request: Union[CreateInvoiceRequest, CreateInvoiceBySaleIdRequest]
    ) -> pl.DataFrame:
    """Generates a Polars DataFrame with calculated invoice data.

    This function fetches sales data based on the provided request, which can
    specify either a date range and other criteria for multiple sales, or a
    single sale ID. The fetched data is then processed through an invoice
    calculation pipeline to enrich it with invoice-specific details like
    taxes and commissions.

    Args:
        db (firestore.Client): An initialized Firebase Firestore client instance
            used for database interactions.
        request (Union[CreateInvoiceRequest, CreateInvoiceBySaleIdRequest]):
            The request object defining the sales data to be processed.
            - If `CreateInvoiceRequest`: Fetches sales based on criteria like
              `store_id`, `producer_id`, `start_date`, and `end_date`.
            - If `CreateInvoiceBySaleIdRequest`: Fetches a single sale by its
              `sale_id`.

    Returns:
        pl.DataFrame: A Polars DataFrame containing the calculated invoice data.
        Each row corresponds to a sale item with its associated invoice
        calculations. Returns an empty DataFrame with the correct schema if no
        sales data matches the request.

    Raises:
        ValueError: If a `sale_id` (from `CreateInvoiceBySaleIdRequest`) is not
            found, if the sale document data is invalid, or if there's an issue
            during the calculation pipeline (e.g., missing VAT configuration,
            required data columns).
        TypeError: If the `request` object is not an instance of
            `CreateInvoiceRequest` or `CreateInvoiceBySaleIdRequest`.
        Exception: Catches and re-raises other unexpected errors that may occur
            during data fetching or the calculation pipeline.

    Example:
        >>> from firebase_admin import initialize_app, firestore
        >>> from models.requests.invoice_request import CreateInvoiceRequest, CreateInvoiceBySaleIdRequest
        >>> # Assume Firebase app is initialized: initialize_app()
        >>> db_client = firestore.client()
        >>>
        >>> # Example 1: Using CreateInvoiceRequest for a batch of invoices
        >>> batch_req = CreateInvoiceRequest(
        ...     store_id="S001",
        ...     producer_id="P001",
        ...     start_date="2024-01-01",
        ...     end_date="2024-01-31"
        ... )
        >>> try:
        ...     invoice_df_batch = generate_invoice_data_handler(db_client, batch_req)
        ...     if not invoice_df_batch.is_empty():
        ...         print(f"Generated {invoice_df_batch.height} invoice lines for batch request.")
        ...     else:
        ...         print("No invoices generated for batch request.")
        ... except Exception as e:
        ...     print(f"Error during batch invoice generation: {e}")
        >>>
        >>> # Example 2: Using CreateInvoiceBySaleIdRequest for a single invoice
        >>> single_req = CreateInvoiceBySaleIdRequest(sale_id="some_sale_id_123")
        >>> try:
        ...     invoice_df_single = generate_invoice_data_handler(db_client, single_req)
        ...     if not invoice_df_single.is_empty():
        ...         print(f"Generated invoice data for sale ID: {single_req.sale_id}")
        ...     else:
        ...         print(f"No invoice data found for sale ID: {single_req.sale_id}")
        ... except ValueError as ve:
        ...     print(f"ValueError for single sale invoice: {ve}")
        ... except Exception as e:
        ...     print(f"Error during single sale invoice generation: {e}")
    """
    logger.info(f"Starting invoice generation for request type: {type(request).__name__}")

    sales_df: pl.DataFrame = None
    
    # --- Step 1: Fetch or Create the Input DataFrame ---
    if isinstance(request, CreateInvoiceRequest):
        logger.info(f"Handling batch request for store: {request.store_id}, producer: {request.producer_id}, dates: {request.start_date} to {request.end_date}")
        # Use the DataSource for batch fetching
        sales_data_source = SalesDataSource(db)
        criteria = request.model_dump() # Pass necessary criteria
        sales_df = sales_data_source.fetch_data(criteria)
        
    elif isinstance(request, CreateInvoiceBySaleIdRequest):
        logger.info(f"Handling single sale request for sale_id: {request.sale_id}")
        # Fetch the single document directly
        sale_ref = db.collection(SALES_SILVER_COLLECTION).document(request.sale_id)
        sale_doc = sale_ref.get()

        if not sale_doc.exists:
            logger.error(f"Sale document not found for sale_id: {request.sale_id}")
            raise ValueError(f"Sale document not found: {request.sale_id}")

        # Validate and convert to SalesSilver model (optional but good practice)
        try:
            sales_silver_obj = SalesSilver.model_validate(sale_doc.to_dict())
        except Exception as e:
             logger.error(f"Failed to validate sale document {request.sale_id}: {e}")
             raise ValueError(f"Invalid sale document data for {request.sale_id}")

        # Convert the single model instance to a 1-row DataFrame
        # Use the same constants/structure as SalesDataSource for consistency
        sale_row = {
            SalesColumns.SALE_ID.value: sale_doc.id, # Use doc id
            SalesColumns.TITLE.value: sales_silver_obj.title,
            SalesColumns.VARIANT_TITLE.value: sales_silver_obj.variant_title,
            SalesColumns.VARIANT_DISPLAY_NAME.value: sales_silver_obj.variant_display_name,
            SalesColumns.DOCUMENT_ID.value: sales_silver_obj.document_id,
            SalesColumns.PRODUCER_ID.value: sales_silver_obj.producer_id,
            SalesColumns.PRODUCER_DISPLAY_NAME.value: sales_silver_obj.producer_display_name,
            SalesColumns.COMMISSION.value: sales_silver_obj.commission,
            SalesColumns.PRODUCER_TAX_A2.value: sales_silver_obj.producer_tax_a2,
            SalesColumns.STORE_TAX_A2.value: sales_silver_obj.store_tax_a2,
            SalesColumns.DISCOUNT.value: sales_silver_obj.discount,
            SalesColumns.SUBTOTAL.value: sales_silver_obj.subtotal,
            SalesColumns.STORE_ID.value: sales_silver_obj.store_id
        }
        # Get the schema from SalesDataSource to ensure consistency
        # (Requires instantiating it, but avoids schema duplication)
        schema = SalesDataSource(db).sales_schema 
        sales_df = pl.DataFrame([sale_row], schema=schema) # Create 1-row DF

    else:
        # Handle unexpected request type
        logger.error(f"Unsupported request type: {type(request).__name__}")
        raise TypeError(f"Unsupported request type provided to generate_invoice_data_handler: {type(request).__name__}")

    # --- Step 2: Run Calculation Pipeline ---
    if sales_df is None or sales_df.is_empty():
        logger.warn("No sales data found or generated, skipping calculation pipeline.")
        # Return the empty DataFrame (it will have the correct schema from SalesDataSource)
        return sales_df if sales_df is not None else pl.DataFrame(schema=SalesDataSource(db).sales_schema)

    logger.info(f"Input DataFrame shape for pipeline: {sales_df.shape}")
    
    # Instantiate dependencies for the pipeline
    real_vat_provider = VatRatesAuxiliaryDataSource(db)
    invoice_pipeline = create_invoice_pipeline(real_vat_provider) 
    
    # Compute using the pipeline
    try:
        logger.info("Running invoice calculation pipeline...")
        final_invoice_df = invoice_pipeline.compute(sales_df)
        logger.info(f"Invoice calculation pipeline finished. Output shape: {final_invoice_df.shape}")
        return final_invoice_df
        
    except ValueError as ve:
        logger.error(f"Value Error during pipeline computation: {ve}")
        raise # Re-raise specific errors like missing columns or VAT config
    except Exception as e:
        logger.error(f"Unexpected Error during pipeline computation: {e}", exc_info=True) # Log traceback
        raise # Re-raise unexpected errors
