[tool.poetry]
name = "python-functions"
version = "0.1.0"
description = "Python Cloud Functions"
authors = ["Your Name <<EMAIL>>"]

[tool.poetry.dependencies]
python = "^3.11"
firebase-functions = ">=0.4.2"
firebase-admin = ">=6.6.0"
pytest = "^8.3.5"
pyarrow = ">=18.0.0"
pandas = ">=2.2.0"
hash-forge = ">=1.2.3"
google-cloud-secret-manager = "^2.22.0"
google-cloud-firestore = "^2.19.0"
pytest-cov = "^6.0.0"
pydantic = "^2.10.4"
flask = "^3.1.0"
pytest-asyncio = "^0.25.3"
polars-lts-cpu = "^1.22.0"
jinja2 = "^3.1.5"
faker = "^37.1.0"
pydantic-to-typescript = "^2.0.0"
requests = "^2.32.3"
mypy = "^1.15.0"

[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"