from firebase_admin import firestore
from typing import Optional

stores_collection = 'stores'

class StoresQueryBuilder:
    def __init__(self, db: Optional[firestore.Client] = None):
        self.db = db or firestore.client()
        self._query = self.db.collection(stores_collection)
      
    def for_id(self, store_id):
        self._query = self.db.collection(stores_collection)
        self._query = self._query.document(store_id)
        return self
    
    def build(self):
        return self._query
        