from firebase_admin import firestore
from typing import Optional

invoices_collection = 'invoices'

class InvoicesQueryBuilder:
    def __init__(self, db: Optional[firestore.Client] = None):
        self.db = db or firestore.client()
        self._query = self.db.collection(invoices_collection)
      
    def for_store_id(self, store_id):
        """Query a specific invoice document by ID.
        this is needed because .dcoument() returns 
        DocumentReference while other query methods return Query
        object"""
        # Reset query to collection before getting document
        self._query = self.db.collection(invoices_collection)
        self._query = self._query.where('store_id', '==', store_id)
        return self
    
    def get_latest(self):
        """
        Orders the query by 'created_at' in descending order and limits to 1 result
        to get the latest invoice.
        
        Returns:
            The query builder instance for method chaining
        """
        self._query = self._query.order_by('created_at', direction='DESCENDING').limit(1)
        return self
    
    def build(self):
        return self._query

        