
from firebase_admin import firestore
from typing import Optional
from models.store import StoreV2

stores_collection = 'storesV2'

class StoresQueryBuilderV2:
    def __init__(self, db: Optional[firestore.Client] = None):
        self.db = db or firestore.client()
        self._query = self.db.collection(stores_collection)
      
    def for_id(self, store_id):
        self._query = self.db.collection(stores_collection)
        self._query = self._query.document(store_id)
        return self
      
    def for_display_name(self, display_name):
        self._query = self._query.where(
            StoreV2.DISPLAY_NAME_FIELD, '==', display_name
        )
        return self

    def for_email(self, email):
        self._query = self._query.where(
            StoreV2.EMAIL_FIELD, '==', email
        )
        return self

    def for_parent_id(self, parent_id):
        self._query = self._query.where(
            StoreV2.PARENT_ID_FIELD, '==', parent_id
        )
        return self
    
    def build(self):
        return self._query
        