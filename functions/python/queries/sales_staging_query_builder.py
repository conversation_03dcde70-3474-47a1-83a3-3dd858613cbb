from typing import Optional
from firebase_admin import firestore
from models.sanitized_sales import SanitizedSales, sales_staging_collection
from models.sales import SalesStaging


class SalesStagingQueryBuilder:
    def __init__(self, db: Optional[firestore.Client] = None):
        self.db = db or firestore.client()
        self._query = self.db.collection(sales_staging_collection)

    def for_store_id(self, store_id):
        self._query = self._query.where(SalesStaging.STORE_ID_FIELD, '==', store_id)
        return self
        
    def between_dates(self, start_date=None, end_date=None):
        if start_date:
            self._query = self._query.where(SalesStaging.UPDATED_AT_FIELD, '>=', start_date)
        if end_date:
            self._query = self._query.where(SalesStaging.UPDATED_AT_FIELD, '<=', end_date)
        return self
    
    def get_document(self, sale_id):
        return self._query.document(sale_id).get()

    def build(self):
        return self._query

