from typing import Dict, Any, Optional, List
import json

# Assuming 'models' is a top-level directory accessible in the python path,
# or adjust path if it's relative e.g. from ..models.products
from models.products import Product, ProductVariant

class ShopifyQueryBuilder:
    @staticmethod
    def get_orders_query(start_date: str, end_date: str, cursor: Optional[str] = None) -> str:
        """
        Returns a GraphQL query for getting orders within a date range with pagination support.
        
        Args:
            start_date: ISO format date string for query start date
            end_date: ISO format date string for query end date
            cursor: Optional cursor for pagination (null for first page)
            
        Returns:
            GraphQL query string with formatted date parameters and pagination
        """
        # Add cursor-based pagination
        after_clause = f'after: "{cursor}"' if cursor else ''
        items_per_page = 100 # 100 with back off min of 60 will prevent us from hitting the limit
        
        return f"""
            query {{
                orders(first: {items_per_page}, {after_clause} query: "created_at:>='{start_date}' AND created_at:<='{end_date}'") {{
                    pageInfo {{
                        hasNextPage
                        endCursor
                    }}
                    edges {{
                        node {{
                            id
                            updatedAt
                            closed
                            fullyPaid
                            currentSubtotalPriceSet {{
                                shopMoney {{
                                    amount
                                    currencyCode
                                }}
                            }}
                            cartDiscountAmountSet {{
                                shopMoney {{
                                    amount
                                    currencyCode
                                }}
                            }}
                            lineItems(first: {items_per_page}) {{
                                pageInfo {{
                                    hasNextPage
                                    endCursor
                                }}
                                edges {{
                                    node {{
                                        id
                                        title
                                        quantity
                                        vendor
                                        originalTotalSet {{
                                            presentmentMoney {{
                                                amount
                                                currencyCode
                                            }}
                                        }}
                                        originalUnitPriceSet {{
                                            shopMoney {{
                                                amount
                                                currencyCode
                                            }}
                                        }}
                                        product {{
                                            id
                                        }}
                                        variantTitle
                                        variant {{
                                            displayName
                                            price
                                        }}
                                    }}
                                }}
                            }}
                        }}
                    }}
                }}
            }}
        """

    @staticmethod
    def get_products_query(cursor: Optional[str] = None, num_products: int = 100, num_variants: int = 50, status: str = 'ACTIVE') -> str:
        """
        Returns a GraphQL query for getting products with pagination support.
        
        Args:
            cursor: Optional cursor for pagination (null for first page)
            num_products: Number of products to fetch per page
            num_variants: Number of variants to fetch per product per page
            status: Optional product status to filter by (e.g., "ACTIVE", "ARCHIVED", "DRAFT")
            
        Returns:
            GraphQL query string with pagination and optional status filter
        """
        after_clause = f'after: "{cursor}"' if cursor else ''
        
        # Build the query filter string for product status
        query_filter_string = f'query: "status:{status.upper()}"'

        # Construct the arguments for the products query
        # Ensure correct comma placement depending on whether after_clause or query_filter_string are present
        products_args = f"first: {num_products}"
        if after_clause:
            products_args += f", {after_clause}"
        if query_filter_string:
            products_args += f", {query_filter_string}"
        
        return f"""
            query {{
                products({products_args}) {{
                    pageInfo {{
                        hasNextPage
                        endCursor
                    }}
                    edges {{
                        node {{
                            id
                            title
                            vendor # This usually represents the producer
                            handle
                            productType
                            status # This will show the status, useful for verification
                            createdAt
                            updatedAt
                            variants(first: {num_variants}) {{
                                pageInfo {{
                                    hasNextPage
                                    endCursor
                                }}
                                edges {{
                                    node {{
                                        id
                                        title
                                        sku
                                        price
                                        compareAtPrice
                                        inventoryQuantity
                                        barcode
                                        createdAt
                                        updatedAt
                                    }}
                                }}
                            }}
                        }}
                    }}
                }}
            }}
        """

    @staticmethod
    def get_default_location_query() -> str:
        """
        Returns a GraphQL query to get the shop's primary location.
        If no ID is provided to the 'location' query, Shopify returns the primary location.
        """
        return """
        query GetPrimaryShopLocation { # Renamed query for clarity
          location { # Querying 'location' without an ID
            id
            name
            isActive
            address {
              address1
              address2
              city
              countryCode
              zip
              provinceCode
            }
          }
        }
        """

    @staticmethod
    def get_all_locations_query(first: int = 10, after: Optional[str] = None) -> str:
        # ... (existing get_all_locations_query code) ...
        pass

    @staticmethod
    def create_product_mutation(product: Product, default_location_id: Optional[str] = None) -> str:
        """
        Returns a GraphQL mutation for creating a product in Shopify.

        Args:
            product: A Product object containing the product details and variants.
            default_location_id: Optional Shopify GID for the location to set inventory for variants.
                                 Example: "gid://shopify/Location/1234567890".

        Returns:
            GraphQL mutation string.
        """
        variants_list_str = []
        product_option_name = "Style" # Default option name if multiple variants

        if product.variants:
            for variant_model in product.variants:
                variant_fields = []
                
                # If there are multiple variants, each variant needs to specify its option value(s).
                # The variant_model.title (e.g., "Small Size") will become the value for the product_option_name.
                if len(product.variants) > 1:
                    # Pass the variant's original title as the value for the first product option
                    variant_fields.append(f'options: [{json.dumps(variant_model.title)}]')
                else:
                    # For a single variant, its title is used directly as the variant's title.
                    # No product-level options are needed, and no variant-level options are needed.
                    variant_fields.append(f'title: {json.dumps(variant_model.title)}')

                variant_fields.append(f'price: {json.dumps(str(variant_model.price))}')

                if variant_model.sku:
                    variant_fields.append(f'sku: {json.dumps(variant_model.sku)}')
                if variant_model.compare_at_price is not None:
                    variant_fields.append(f'compareAtPrice: {json.dumps(str(variant_model.compare_at_price))}')
                if variant_model.barcode:
                    variant_fields.append(f'barcode: {json.dumps(variant_model.barcode)}')

                if default_location_id and variant_model.inventory_quantity is not None:
                    variant_fields.append('inventoryItem: { tracked: true }')
                    inventory_quantity_str = f'{{ availableQuantity: {variant_model.inventory_quantity}, locationId: {json.dumps(default_location_id)} }}'
                    variant_fields.append(f'inventoryQuantities: [{inventory_quantity_str}]')
                
                variants_list_str.append(f"{{ {', '.join(variant_fields)} }}")
        
        variants_input_str = f"[{', '.join(variants_list_str)}]"

        product_input_fields = []
        product_input_fields.append(f'title: {json.dumps(product.title)}')

        if product.vendor:
            product_input_fields.append(f'vendor: {json.dumps(product.vendor)}')
        if product.handle:
            product_input_fields.append(f'handle: {json.dumps(product.handle)}')
        if product.product_type:
            product_input_fields.append(f'productType: {json.dumps(product.product_type)}')
        
        if product.status:
            product_input_fields.append(f'status: {product.status.upper()}')
        
        # If multiple variants are present, explicitly define the product-level option name.
        if product.variants and len(product.variants) > 1:
            product_input_fields.append(f'options: [{json.dumps(product_option_name)}]')
        
        # Always include variants, even if empty, to let Shopify handle default variant creation
        # or no variants based on its logic. If product.variants is empty, variants_input_str is "[]".
        product_input_fields.append(f'variants: {variants_input_str}')
        
        product_input_str = f"{{ {', '.join(product_input_fields)} }}"

        return f"""
            mutation productCreate {{
                productCreate(input: {product_input_str}) {{
                    product {{
                        id
                        title
                        handle
                        status
                        vendor
                        productType
                        options {{
                            name
                            values
                        }}
                        createdAt
                        updatedAt
                        variants(first: 10) {{
                            edges {{
                                node {{
                                    id
                                    title # This will be auto-generated by Shopify based on option values
                                    selectedOptions {{ # See what Shopify selected
                                        name
                                        value
                                    }}
                                    sku
                                    price
                                    compareAtPrice
                                    inventoryQuantity
                                    barcode
                                    createdAt
                                    updatedAt
                                }}
                            }}
                            pageInfo {{
                                hasNextPage
                                endCursor
                            }}
                        }}
                    }}
                    userErrors {{
                        field
                        message
                    }}
                }}
            }}
        """
