from firebase_admin import firestore
from typing import Optional
from models.partner import Partnership, PartnershipStatus
from constants.collections import partnership_collection

class PartnersQueryBuilder:
    def __init__(self, db: Optional[firestore.Client] = None):
        self.db = db or firestore.client()
        self._query = self.db.collection(partnership_collection)
        
    def for_store_id(self, store_id):
        self._query = self._query\
            .where(Partnership.STORE_ID_FIELD, '==', store_id)
        return self
    
    def for_producer_id(self, producer_id):
        self._query = self._query\
            .where(Partnership.PRODUCER_ID_FIELD, '==', producer_id)
        return self

    def with_status(self, status):
        self._query = self._query\
            .where(Partnership.STATUS_FIELD, '==', status)
        return self

    def for_id(self, document_id):
        self._query = self.db.collection(partnership_collection)
        self._query = self._query.document(document_id)
        return self

    def for_agreement_id(self, agreement_id):
        self._query = self._query\
            .where(Partnership.AGREEMENT_ID_FIELD, '==', agreement_id)
        return self

    def build(self):
        return self._query
