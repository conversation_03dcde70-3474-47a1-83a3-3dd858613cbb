from typing import Optional
from firebase_admin import firestore
from models.sanitized_sales import sales_silver_collection
from models.sales import SalesSilver

class SalesSilverQueryBuilder:
    def __init__(self, db: Optional[firestore.Client] = None):
        self.db = db or firestore.client()
        self._query = self.db.collection(sales_silver_collection)

    def for_store_id(self, store_id):
        self._query = self._query.where(SalesSilver.STORE_ID_FIELD, '==', store_id)
        return self

    def for_producer_id(self, producer_id):
        self._query = self._query.where(SalesSilver.PRODUCER_ID_FIELD, '==', producer_id)
        return self
        
    def between_dates(self, start_date=None, end_date=None):
        if start_date:
            self._query = self._query.where(SalesSilver.UPDATED_AT_FIELD, '>=', start_date)
        if end_date:
            self._query = self._query.where(SalesSilver.UPDATED_AT_FIELD, '<=', end_date)
        return self

    def build(self):
        return self._query

