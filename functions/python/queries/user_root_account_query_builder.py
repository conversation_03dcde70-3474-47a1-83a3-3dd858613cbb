from firebase_admin import firestore

class UserRootAccountQueryBuilder:
  def __init__(self, db: firestore.Client):
    self.db = db
    self.collection_name = "userRootAccounts"
    self._query = db.collection(self.collection_name)

  def for_id(self, id: str):
    self._query = self.db.collection(self.collection_name)
    self._query = self._query.document(id)
    return self

  def for_email(self, email: str):
    self._query = self._query.where("email", "==", email)
    return self
  
  def build(self):
    return self._query
