from firebase_admin import firestore
from typing import Optional
from models.producer import Producer

producers_collection = 'producersV2'

class ProducersQueryBuilderV2:
    def __init__(
            self, 
            db: Optional[firestore.Client] = None 
        ):
        self.db = db or firestore.client()
        self._query = self.db.collection(producers_collection)
      
    def for_id(self, producer_id):
        """Query a specific producer document by ID.
        this is needed because .dcoument() returns 
        DocumentReference while other query methods return Query
        object"""
        # Reset query to collection before getting document
        self._query = self.db.collection(producers_collection)
        self._query = self._query.document(producer_id)
        return self

    def for_email(self, email):
        self._query = self._query.where(Producer.EMAIL_FIELD, '==', email)
        return self

    def for_display_name(self, display_name):
        self._query = self._query.where(Producer.DISPLAY_NAME_FIELD, '==', display_name)
        return self

    def for_parent_id(self, parent_id):
        self._query = self._query.where(Producer.PARENT_ID_FIELD, '==', parent_id)
        return self

    def for_created_by(self, created_by):
        self._query = self._query.where(Producer.CREATED_BY_FIELD, '==', created_by)
        return self
    
    def build(self):
        return self._query
        