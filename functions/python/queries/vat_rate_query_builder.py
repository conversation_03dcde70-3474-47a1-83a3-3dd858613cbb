from typing import Optional
from firebase_admin import firestore

vat_rate_collection = 'vat_rate'

class VatRateQueryBuilder:
    def __init__(self, db: Optional[firestore.Client] = None):
        self.db = db or firestore.client()
        self._query = self.db.collection(vat_rate_collection)

    def for_country(self, a2):
        self._query = self.db.collection(vat_rate_collection)
        self._query = self._query.document(a2)
        return self
        
    def build(self):
        return self._query

