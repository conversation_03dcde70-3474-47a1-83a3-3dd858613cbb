from typing import Optional
from firebase_admin import firestore
from models.sales import sales_gold_collection
from models.sales import SalesGold


class SalesGoldQueryBuilder:
    def __init__(self, db: Optional[firestore.Client] = None):
        self.db = db or firestore.client()
        self._query = self.db.collection(sales_gold_collection)

    def for_store_id(self, store_id):
        self._query = self._query.where(SalesGold.STORE_ID_FIELD, '==', store_id)
        return self

    def for_producer_id(self, producer_id):
        self._query = self._query.where(SalesGold.PRODUCER_ID_FIELD, '==', producer_id)
        return self
        
    def between_dates(self, start_date=None, end_date=None):
        if start_date:
            self._query = self._query.where(SalesGold.UPDATED_AT_FIELD, '>=', start_date)
        if end_date:
            self._query = self._query.where(SalesGold.UPDATED_AT_FIELD, '<=', end_date)
        return self

    def get_document_by_id(self, document_id):
        doc_ref = self._query.document(document_id)
        doc = doc_ref.get()
        return doc if doc.exists else None

    def build(self):
        return self._query

