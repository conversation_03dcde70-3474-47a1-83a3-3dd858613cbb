# mypy.ini
[mypy]
python_version = 3.11 # Or your target Python version
warn_return_any = True
warn_unused_configs = True

# Be fairly strict about functions without types
disallow_untyped_defs = True

# But allow untyped calls for now to ease integration
disallow_untyped_calls = False 

# Ignore missing imports for libraries that don't have stubs yet
ignore_missing_imports = True 

# Specify the source directory
files = functions/python 

[mypy-firebase_admin.*]
ignore_missing_imports = True 