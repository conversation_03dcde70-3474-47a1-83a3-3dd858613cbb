from pydantic import BaseModel, field_validator
from typing import Optional
from datetime import datetime
from typing import ClassVar
from models.base import BaseModelConfig

sales_staging_collection = 'sales-staging'
sales_silver_collection = 'sales-silver'

class SanitizedSales(BaseModelConfig):
  collection: str = sales_staging_collection
  sale: dict

  @field_validator('collection')
  def validate_collection(cls, value):
      if value not in [sales_silver_collection, sales_staging_collection]:
          raise ValueError(
             f'Collection must be either {sales_silver_collection} or {sales_staging_collection}'
          )
      return value

  @field_validator('sale')
  def validate_sale(cls, value):
    if not isinstance(value, dict):
      raise ValueError('Sale must be a dictionary')
    return value
