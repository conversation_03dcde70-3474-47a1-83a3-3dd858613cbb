from models.base import BaseModelConfig, DateTime
from enum import Enum
from typing import ClassVar, Dict, Optional, List


class EnumUserType(str, Enum):
  USER = "user"


class UserRootAccount(BaseModelConfig):
  PRODUCERS_FIELD: ClassVar[str] = "producers"
  STORES_FIELD: ClassVar[str] = "stores"
  ROLE_FIELD: ClassVar[str] = "role"
  EMAIL_FIELD: ClassVar[str] = "email"
  CREATED_AT_FIELD: ClassVar[str] = "createdAt"
  UPDATED_AT_FIELD: ClassVar[str] = "updatedAt"

  created_at: DateTime
  email: str
  stores: Dict[str, str]
  producers: Dict[str, str]
  updated_at: Optional[DateTime] = None
