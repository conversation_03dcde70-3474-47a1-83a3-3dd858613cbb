from enum import Enum
from typing import Optional, ClassVar
from pydantic import BaseModel
from models.base import BaseModelConfig, DateTime
from models.validators import  CommissionValidatorMixin


class PartnershipStatus(str, Enum):
    ACTIVE = "active"
    TERMINATED = "terminated"
    EXPIRED = "expired"


partnership_collection = 'partnerships'
class Partnership(CommissionValidatorMixin, BaseModelConfig):
    STATUS_FIELD: ClassVar[str] = 'status'
    AGREEMENT_ID_FIELD: ClassVar[str] = 'agreementId'
    STORE_ID_FIELD: ClassVar[str] = 'storeId'
    PRODUCER_ID_FIELD: ClassVar[str] = 'producerId'
    UPDATED_AT_FIELD: ClassVar[str] = 'updatedAt'
    CREATED_BY_FIELD: ClassVar[str] = 'createdBy'
    DOCUMENT_URL_FIELD: ClassVar[str] = 'documentUrl'

    store_id: str
    producer_id: str
    
    status: str
    agreement_id: str
    commission: int
    document_url: Optional[str] = None

    # overwrite the start_date and end_date to be DateTime
    effective_date: DateTime
    expiration_date: Optional[DateTime] = None

    # who created the partner, user uuid
    created_by: str
    updated_at: DateTime