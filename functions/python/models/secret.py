from datetime import datetime
from typing import Optional
from enum import Enum
from pydantic import field_validator
from models.base import BaseModelConfig, DateTime

class Platform(str, Enum):
    SHOPIFY = "shopify"
    OTHER = "other"

class Secret(BaseModelConfig):
    store_id: str
    secret_name: str
    secret_version_path: str
    shop_name: str
    created_at: DateTime
    updated_at: DateTime
    description: Optional[str] = None

    class ConfigDict:
        from_attributes = True  # This allows conversion from ORM objects

class SecretLabels(BaseModelConfig):
    platform: str
    shopname: str

    @field_validator('platform')
    def validate_platform(cls, v):
        try:
            Platform(v)  # This will raise ValueError if invalid
            return v
        except ValueError:
            raise ValueError(f"Platform must be one of: {[p.value for p in Platform]}")