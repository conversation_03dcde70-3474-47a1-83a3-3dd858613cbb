from models.base import BaseModelConfig, DateTime
from typing import ClassVar, Optional


class Producer(BaseModelConfig):
  '''
  Represents a producer in the database.
  these key are necessary for the invoice data 
  computation
  '''
  EMAIL_FIELD: ClassVar[str] = "email"
  DISPLAY_NAME_FIELD: ClassVar[str] = "displayName"
  CREATED_BY_FIELD: ClassVar[str] = "createdBy"
  PARENT_ID_FIELD: ClassVar[str] = "parentId"
  CREATED_BY_STORE_ID_FIELD: ClassVar[str] = "createdByStoreId"

  created_at: DateTime
  display_name: str
  email: str
  tax_a2: str
  parent_id: str
  # this should be the root account document id 
  # which is the user UID
  created_by: str
  # this filed is for force creation flow of the producer
  # by the store
  created_by_store_id: Optional[str] = None
