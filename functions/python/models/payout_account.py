from pydantic import BaseModel
from models.base import BaseModelConfig
from typing import Optional, Dict

class Address(BaseModelConfig):
    street_number: Optional[str] = None
    street_name: str
    house_number: Optional[str] = None
    additional_info: Optional[str] = None
    zip_code: str
    city: str
    state: Optional[str] = None
    country: str

    @classmethod
    def from_request_data(cls, data: Dict):
        return cls(
            street_number=data.get("streetNumber"),
            street_name=data.get("streetName"),
            house_number=data.get("houseNumber"),
            additional_info=data.get("additionalInfo"),
            zip_code=data.get("zipCode"),
            city=data.get("city"),
            state=data.get("state"),
            country=data.get("country")
        )


# Model for any account that want to make payout
# All these fields are required, else the payout will fail
class PayoutAccount(BaseModelConfig):
    payment_reference_id: str  # Reference ID for tracking
    
    # Personal/Business Information
    account_holder_name: str  # Full legal name of account holder
    email: str
    phone_number: Optional[str] = None  # For verification and contact
    address: Address
    company_name: Optional[str] = None  # If business account
    company_number: Optional[str] = None  # Registration number
    vat_number: Optional[str] = None  # Tax ID number
    
    # Banking Information
    account_type: str  # e.g., "personal", "business", "savings", "checking"
    bank_name: str
    bank_country: str
    currency: str  # e.g., "USD", "EUR", "GBP"
    
    # Specific bank account details
    bank_account_number: str
    iban: Optional[str] = None  # International Bank Account Number
    routing_number: Optional[str] = None  # For US
    sort_code: Optional[str] = None  # For UK
    bic: Optional[str] = None  # Also known as SWIFT code
    
    # Additional information
    tax_residence_country: Optional[str] = None
    payout_method: str = "bank_transfer"  # Default method