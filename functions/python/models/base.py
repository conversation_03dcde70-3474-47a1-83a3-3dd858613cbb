from pydantic import BaseModel, ConfigDict, Field
from datetime import datetime
from typing import Annotated, TypeVar, Any

def to_camel(string: str) -> str:
    words = string.split('_')
    return words[0] + ''.join(word.capitalize() for word in words[1:])

# Custom DateTime type that works with both Python and TypeScript
DateTime = Annotated[
    datetime, 
    Field(json_schema_extra={"tsType": "Date"})
]

class BaseModelConfig(BaseModel):
    '''
    Base model config for all models
    '''
    model_config = ConfigDict(
        extra = 'ignore',
        from_attributes = True,
        alias_generator=to_camel,
        populate_by_name=True,
        populate_by_alias=True
    )

    def model_dump(self, **kwargs):
        """
        Override model_dump to convert to camelCase for JSON/external use
        """
        by_alias = kwargs.pop('by_alias', True)  # Default to camelCase for external use
        return super().model_dump(by_alias=by_alias, **kwargs)
    
    def model_dump_json(self, **kwargs):
        """
        Override model_dump_json to convert to camelCase for JSON/external use
        """
        by_alias = kwargs.pop('by_alias', True)  # Default to camelCase for external use
        return super().model_dump_json(by_alias=by_alias, **kwargs)
    
    @classmethod
    def model_validate(cls, obj, **kwargs):
        """
        Override model_validate to accept both snake_case and camelCase
        """
        return super().model_validate(obj, **kwargs)
