from pydantic import BaseModel, field_validator
from datetime import datetime
from enum import Enum
from typing import ClassVar
from models.validators import CommissionValidatorMixin
from models.base import BaseModelConfig, DateTime

class Status(str, Enum):
    ACCEPTED = "accepted"
    PENDING = "pending" 
    REJECTED = "rejected"

class Application(CommissionValidatorMixin, BaseModelConfig):
    SENDER_ID_FIELD: ClassVar[str] = 'senderId'
    RECIPIENT_ID_FIELD: ClassVar[str] = 'recipientId'
    STATUS_FIELD: ClassVar[str] = 'status'
    APPLIED_AT_FIELD: ClassVar[str] = 'appliedAt'
    COMMISSION_FIELD: ClassVar[str] = 'commission'

    senderId: str
    recipientId: str
    status: str
    appliedAt: DateTime
    commission: int
    
    @field_validator('status')
    def validate_status(cls, v):
        valid_statuses = {status.value for status in Status}
        if v not in valid_statuses:
            raise ValueError(f'Invalid status {v}. Must be one of {valid_statuses}')
        return v


class ApplicationMatcher(Application):
    producer_display_name: str
