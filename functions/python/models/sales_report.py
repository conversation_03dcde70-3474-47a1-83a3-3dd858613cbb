from models.sales import sales_gold_collection
from constants.gaco_values import bucket_name
# Start of Pydantic models for sales report output
from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any, ClassVar # Ensure Any is imported if not already
from datetime import datetime # Ensure datetime is imported if not already
from models.application import Status # For the status enum
from models.base import BaseModelConfig, DateTime
from enum import Enum

sales_report_collection = "sales-reports"


class SalesReportStatus(str, Enum):
    PENDING = "pending"
    ACCEPTED = "accepted"
    SENT = "sent"
    REVISE = "revise"


class ProducerLevelData(BaseModelConfig):
    producer_id: str
    subtotal: float
    store_total_gross_payout: float
    producer_total_gross_payout: float
    producer_display_name: str

class SalesLevelDataItem(BaseModelConfig):
    sale_id: str
    title: str
    variant_title: Optional[str] = None # Based on inspection, can be missing
    updated_at: datetime
    subtotal: float
    commission: float
    producer_gross_payout: float


class SalesReportModel(BaseModelConfig):
    SALES_REPORT_ID_FIELD: ClassVar[str] = "salesReportId"
    STORE_ID_FIELD: ClassVar[str] = "storeId"
    PRODUCER_ID_FIELD: ClassVar[str] = "producerId"
    PRODUCER_LEVEL_FIELD: ClassVar[str] = "producerLevel"
    SALES_LEVEL_DATA_FIELD: ClassVar[str] = "salesLevelData"
    SALES_REPORT_DATE_FIELD: ClassVar[str] = "salesReportDate"
    SALE_IDS_FIELD: ClassVar[str] = "saleIds"
    TITLE_FIELD: ClassVar[str] = "title"
    LOCAL_FILE_PATH_FIELD: ClassVar[str] = "localFilePath"
    URI_FIELD: ClassVar[str] = "uri"
    GCS_PATH_FIELD: ClassVar[str] = "gcsPath"
    DOWNLOAD_URL_FIELD: ClassVar[str] = "downloadUrl"
    CREATED_AT_FIELD: ClassVar[str] = "createdAt"
    UPDATED_AT_FIELD: ClassVar[str] = "updatedAt"
    STATUS_FIELD: ClassVar[str] = "status"
    TEMPLATE_USED_FIELD: ClassVar[str] = "templateUsed"
    START_DATE_FIELD: ClassVar[str] = "startDate"
    END_DATE_FIELD: ClassVar[str] = "endDate"


    sales_report_id: str
    store_id: str
    producer_id: str
    producer_level: ProducerLevelData
    sales_level_data: List[SalesLevelDataItem]
    sales_report_date: datetime
    sale_ids: List[str]
    title: str
    local_file_path: Optional[str] = None
    uri: Optional[str] = None
    gcs_path: Optional[str] = None
    download_url: Optional[str] = None
    created_at: datetime
    updated_at: datetime # Added this field as it's in _prepare_report_dict_common
    status: SalesReportStatus
    template_used: str
    start_date: Optional[DateTime] = None
    end_date: Optional[DateTime] = None
    # which backend updated the document
    updated_by_function: str = "python"
