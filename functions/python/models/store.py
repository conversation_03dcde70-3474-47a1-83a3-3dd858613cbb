from pydantic import field_validator
from models.base import BaseModelConfig, DateTime
from typing import Optional, ClassVar
from models.payout_account import Address
from models.validators import CommissionValidatorMixin

class StoreV2(BaseModelConfig, CommissionValidatorMixin):
    """
    new model, going to be used for the new store
    """
    SHOPIFY_API_KEY_REFERENCE_FIELD: ClassVar[str] = "shopifyApiKeyReference"
    DISPLAY_NAME_FIELD: ClassVar[str] = "displayName"
    EMAIL_FIELD: ClassVar[str] = "email"
    PARENT_ID_FIELD: ClassVar[str] = "parentId"


    created_at: DateTime
    display_name: str
    email: str
    parent_id: str
    tax_a2: str
    default_commission: Optional[int] = None
    # from the FE perspective, this is not true, but from BE
    # this must be optional, sice store must be created before
    # the api key is created
    shopify_api_key_reference: Optional[str] = None
    description: Optional[str] = None
    address: Optional[Address] = None
