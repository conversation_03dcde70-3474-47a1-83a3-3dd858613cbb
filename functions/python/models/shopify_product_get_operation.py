from enum import Enum
from pydantic import Field
from typing import Optional, ClassVar
from datetime import datetime, timezone
from pydantic import field_validator
from models.base import BaseModelConfig

shopify_product_fetch_operations_collection = "shopify-product-fetch-operations"

# --- Enums and Models (Ideally in models/shopify_product_get_operation.py) ---
class ShopifyGetOperationStatus(Enum):
    IN_PROGRESS = "IN_PROGRESS"
    COMPLETED = "COMPLETED"
    FAILED = "FAILED"

class ShopifyProductGetOperation(BaseModelConfig):
    product_getter_id: str = Field(..., alias="productGetterId")
    store_id: str = Field(..., alias="storeId")
    shop_name: str = Field(..., alias="shopName")
    status_filter: Optional[str] = Field(None, alias="statusFilter") # e.g., "ACTIVE", "ARCHIVED", "DRAFT"
    
    next_cursor: Optional[str] = Field(None, alias="nextCursor")
    has_next_page: bool = Field(False, alias="hasNextPage")
    
    base_path: str = Field(..., alias="basePath")
    pages_fetched: int = Field(0, alias="pagesFetched")
    total_products: int = Field(0, alias="totalProducts")
    
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc), alias="createdAt")
    last_updated_at: Optional[datetime] = Field(None, alias="lastUpdatedAt")
    completed_at: Optional[datetime] = Field(None, alias="completedAt")
    
    status: str = Field(ShopifyGetOperationStatus.IN_PROGRESS.value)
    error_message: Optional[str] = Field(None, alias="errorMessage")
    error_time: Optional[datetime] = Field(None, alias="errorTime")

    # For Firestore compatibility with Pydantic aliases
    class Config:
        populate_by_name = True 
        json_encoders = {
            datetime: lambda dt: dt.isoformat()
        }

    @field_validator('status')
    def validate_status_value(cls, value):
        if value not in [s.value for s in ShopifyGetOperationStatus]:
            raise ValueError(f"Invalid status: {value}")
        return value

    # Field name constants for Firestore updates (good practice)
    PRODUCT_GETTER_ID_FIELD: ClassVar[str] = "productGetterId"
    STORE_ID_FIELD: ClassVar[str] = "storeId"
    SHOP_NAME_FIELD: ClassVar[str] = "shopName"
    STATUS_FILTER_FIELD: ClassVar[str] = "statusFilter"
    NEXT_CURSOR_FIELD: ClassVar[str] = "nextCursor"
    HAS_NEXT_PAGE_FIELD: ClassVar[str] = "hasNextPage"
    BASE_PATH_FIELD: ClassVar[str] = "basePath"
    PAGES_FETCHED_FIELD: ClassVar[str] = "pagesFetched"
    TOTAL_PRODUCTS_FIELD: ClassVar[str] = "totalProducts"
    CREATED_AT_FIELD: ClassVar[str] = "createdAt"
    LAST_UPDATED_AT_FIELD: ClassVar[str] = "lastUpdatedAt"
    COMPLETED_AT_FIELD: ClassVar[str] = "completedAt"
    STATUS_FIELD: ClassVar[str] = "status"
    ERROR_MESSAGE_FIELD: ClassVar[str] = "errorMessage"
    ERROR_TIME_FIELD: ClassVar[str] = "errorTime"

    # For response to client (not stored in DB)
    DOWNLOAD_URL_FIELD: ClassVar[str] = "downloadUrl"
    FILE_PATH_FIELD: ClassVar[str] = "filePath"
    PRODUCTS_IN_FIRST_PAGE_FIELD: ClassVar[str] = "productsInFirstPage"
    HAS_MORE_PAGES_FIELD: ClassVar[str] = "hasMorePages"

