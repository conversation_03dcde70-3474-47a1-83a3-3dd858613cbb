from pydantic import BaseModel, field_validator
from datetime import datetime
from typing import Optional, List, ClassVar
from models.base import BaseModelConfig, DateTime

sales_gold_collection = 'sales-gold'

class Sale(BaseModelConfig):
  STORE_ID_FIELD: ClassVar[str] = 'storeId'
  ORDER_ID_FIELD: ClassVar[str] = 'orderId'
  LINE_ITEM_ID_FIELD: ClassVar[str] = 'lineItemId'
  PRODUCT_ID_FIELD: ClassVar[str] = 'productId'
  TITLE_FIELD: ClassVar[str] = 'title'
  VARIANT_TITLE_FIELD: ClassVar[str] = 'variantTitle'
  VARIANT_DISPLAY_NAME_FIELD: ClassVar[str] = 'variantDisplayName'
  QUANTITY_FIELD: ClassVar[str] = 'quantity'
  UNIT_PRICE_FIELD: ClassVar[str] = 'unitPrice'
  SUBTOTAL_FIELD: ClassVar[str] = 'subtotal'
  TOTAL_PRICE_FIELD: ClassVar[str] = 'totalPrice'
  CURRENCY_FIELD: ClassVar[str] = 'currency'
  DISCOUNT_FIELD: ClassVar[str] = 'discount'
  VENDOR_FIELD: ClassVar[str] = 'vendor'
  UPDATED_AT_FIELD: ClassVar[str] = 'updatedAt'
  STORE_DISPLAY_NAME_FIELD: ClassVar[str] = 'storeDisplayName'

  store_id: str
  order_id: str
  line_item_id: str
  product_id: Optional[str]
  title: str
  variant_title: Optional[str]
  variant_display_name: Optional[str]
  quantity: int
  unit_price: float
  subtotal: float
  total_price: float
  currency: str
  discount: float
  vendor: Optional[str]
  updated_at: DateTime
  store_display_name: str


class SalesSilver(Sale):
  DOCUMENT_ID_FIELD: ClassVar[str] = 'documentId'
  PRODUCER_DISPLAY_NAME_FIELD: ClassVar[str] = 'producerDisplayName'
  PRODUCER_ID_FIELD: ClassVar[str] = 'producerId'
  COMMISSION_FIELD: ClassVar[str] = 'commission'
  PRODUCER_TAX_A2_FIELD: ClassVar[str] = 'producerTaxA2'
  STORE_TAX_A2_FIELD: ClassVar[str] = 'storeTaxA2'
  AGREEMENT_ID_FIELD: ClassVar[str] = 'agreementId'

  document_id: str
  producer_display_name: str
  producer_id: str
  commission: int
  producer_tax_a2: str
  store_tax_a2: str
  agreement_id: str = None

  @field_validator('commission')
  def validate_commission(cls, value):
      if value < 0 or value > 100:
          raise ValueError('Commission must be between 0 and 100')
      return value

  def get_field_names(self):
      return list(self.model_fields.keys())


class SalesStaging(Sale):
  DOCUMENT_ID_FIELD: ClassVar[str] = 'documentId'
  AGREEMENTS_ID_FIELD: ClassVar[str] = 'agreementId'
  PRODUCER_ID_FIELD: ClassVar[str] = 'producerId'
  TAGGED_BY_FIELD: ClassVar[str] = 'taggedBy'

  document_id: str
  agreement_ids: Optional[List[str]] = None
  producer_id: Optional[str] = None
  tagged_by: Optional[str] = None
  

class SalesGold(SalesSilver):
  '''
  Adding fields for the invoice generation
  '''
  SALE_ID_FIELD: ClassVar[str] = 'saleId'
  ASSIGNED_VAT_RATE_FIELD: ClassVar[str] = 'assignedVatRate'
  VAT_EXCLUDED_SALE_FIELD: ClassVar[str] = 'vatExcludedSale'
  NET_SALES_FIELD: ClassVar[str] = 'netSales'
  STORE_NET_PAYOUT_FIELD: ClassVar[str] = 'storeNetPayout'
  VAT_ON_SALES_SERVICE_FIELD: ClassVar[str] = 'vatOnSalesService'
  STORE_GROSS_PAYOUT_FIELD: ClassVar[str] = 'storeGrossPayout'
  PRODUCER_GROSS_PAYOUT_FIELD: ClassVar[str] = 'producerGrossPayout'
  INVOICE_ID_FIELD: ClassVar[str] = 'invoiceId'
  SALES_REPORT_ID_FIELD: ClassVar[str] = 'salesReportId'
  SOURCE_SALES_DATE_FIELD: ClassVar[str] = 'originalSalesDate'

  sale_id: str
  assigned_vat_rate: Optional[int] = None
  vat_excluded_sale: Optional[float] = None
  net_sales: Optional[float] = None
  store_net_payout: Optional[float] = None
  vat_on_sales_service: Optional[float] = None
  store_gross_payout: float
  producer_gross_payout: float
  invoice_id: Optional[str] = None
  sales_report_id: Optional[str] = None
  source_sales_date: Optional[DateTime] = None
