from typing import Optional, List, ClassVar
from pydantic import Field, field_validator
from models.base import BaseModelConfig, DateTime # Assuming DateTime and BaseModelConfig are in models/base.py

products_collection = 'products'
product_variants_collection = 'product_variants'


class ProductVariant(BaseModelConfig):
    # Field name constants for consistency
    PRODUCT_VARIANT_ID_FIELD: ClassVar[str] = 'productVariantId' # Shopify GID
    PRODUCT_ID_FIELD: ClassVar[str] = 'productId' # Shopify GID of the parent product
    TITLE_FIELD: ClassVar[str] = 'title' # Variant-specific title (e.g., "Small", "Red")
    SKU_FIELD: ClassVar[str] = 'sku'
    PRICE_FIELD: ClassVar[str] = 'price'
    COMPARE_AT_PRICE_FIELD: ClassVar[str] = 'compareAtPrice'
    INVENTORY_QUANTITY_FIELD: ClassVar[str] = 'inventoryQuantity'
    BARCODE_FIELD: ClassVar[str] = 'barcode'
    CREATED_AT_FIELD: ClassVar[str] = 'createdAt'
    UPDATED_AT_FIELD: ClassVar[str] = 'updatedAt'

    product_variant_id: Optional[str] = None
    product_id: Optional[str] = None # This will be populated with the parent product's GID during processing
    title: str
    sku: Optional[str] = None
    price: float # Will be converted from string
    compare_at_price: Optional[float] = None # Will be converted from string
    inventory_quantity: Optional[int] = None
    barcode: Optional[str] = None
    created_at: DateTime = Field(..., alias='createdAt')
    updated_at: DateTime = Field(..., alias='updatedAt')

    @field_validator('price', 'compare_at_price', mode='before')
    def convert_string_prices_to_float(cls, value):
        if value is None:
            return None
        if isinstance(value, str):
            try:
                return float(value)
            except ValueError:
                # Or raise a more specific error, or return a default
                raise ValueError(f"Invalid price string: {value}")
        return value

class Product(BaseModelConfig):
    # Field name constants
    PRODUCT_ID_FIELD: ClassVar[str] = 'productId' # Shopify GID
    TITLE_FIELD: ClassVar[str] = 'title'
    VENDOR_FIELD: ClassVar[str] = 'vendor' # Key for linking to Producer
    HANDLE_FIELD: ClassVar[str] = 'handle'
    PRODUCT_TYPE_FIELD: ClassVar[str] = 'productType'
    STATUS_FIELD: ClassVar[str] = 'status'
    CREATED_AT_FIELD: ClassVar[str] = 'createdAt'
    UPDATED_AT_FIELD: ClassVar[str] = 'updatedAt'
    VARIANTS_FIELD: ClassVar[str] = 'variants'
    
    # Contextual fields, similar to Sales model
    STORE_ID_FIELD: ClassVar[str] = 'storeId'
    STORE_DISPLAY_NAME_FIELD: ClassVar[str] = 'storeDisplayName'
    PRODUCER_ID_FIELD: ClassVar[str] = 'producerId'
    PRODUCER_DISPLAY_NAME_FIELD: ClassVar[str] = 'producerDisplayName'

    product_id: Optional[str] = None # Maps from 'id' in the Shopify product node
    title: str
    vendor: Optional[str] = None
    handle: Optional[str] = None
    product_type: Optional[str] = Field(default=None, alias='productType')
    status: str
    created_at: DateTime = Field(..., alias='createdAt')
    updated_at: DateTime = Field(..., alias='updatedAt')
    variants: List[ProductVariant] = []

    # These fields would be populated during an enrichment step
    store_id: Optional[str] = None
    store_display_name: Optional[str] = None
    producer_id: Optional[str] = None # To be derived from vendor
    producer_display_name: Optional[str] = None # To be derived from producer_id

    def get_field_names(self): # Added for consistency with Sales model
        return list(self.model_fields.keys())
