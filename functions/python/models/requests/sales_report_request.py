from models.base import BaseModelConfig, DateTime
from typing import Optional, ClassVar


class CreateSalesReportRequest(BaseModelConfig):
    STORE_ID_FIELD: ClassVar[str] = "storeId"
    PRODUCER_ID_FIELD: ClassVar[str] = "producerId"
    START_DATE_FIELD: ClassVar[str] = "startDate"
    END_DATE_FIELD: ClassVar[str] = "endDate"

    store_id: str
    producer_id: Optional[str] = None
    start_date: DateTime
    end_date: DateTime


class CreateSalesReportBySaleIdRequest(BaseModelConfig):
    SALE_ID_FIELD: ClassVar[str] = "saleId"

    sale_id: str


class DeleteSalesReportRequest(BaseModelConfig):
    SALES_REPORT_ID_FIELD: ClassVar[str] = "salesReportId"

    sales_report_id: str