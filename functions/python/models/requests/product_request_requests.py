
from pydantic import Field
from models.base import BaseModelConfig
from typing import Optional
from models.allocations import DeliveryMethod, ProductRequestStatus # Assuming these are defined in models.allocations

# For create_request, the models.allocations.StoreProductRequest model itself will be used directly in the cloud function.

class ApproveStoreProductRequest(BaseModelConfig):
    request_id: str = Field(..., description="The ID of the store product request to approve.")
    # acting_producer_id will be derived from the authenticated user's context (e.g., custom claims)
    # or passed if a system/admin is making the call on behalf of a producer.
    # For simplicity here, we'll assume it might be passed, but in a real scenario,
    # you'd likely link it to the authenticated producer.
    acting_producer_id: str = Field(..., description="The ID of the producer approving the request.")
    producer_response_notes: Optional[str] = Field(default=None, description="Optional notes from the producer.")
    delivery_method_for_allocation: str = Field(
        default=DeliveryMethod.PRODUCER_SHIPMENT.value, # Or your preferred default
        description="Delivery method for the allocation that will be created."
    )
    allocation_producer_notes: Optional[str] = Field(default=None, description="Specific notes for the auto-created product allocation.")


class RejectStoreProductRequest(BaseModelConfig):
    request_id: str = Field(..., description="The ID of the store product request to reject.")
    acting_producer_id: str = Field(..., description="The ID of the producer rejecting the request.")
    producer_response_notes: Optional[str] = Field(default=None, description="Reason for rejection or other notes from the producer.")

class CancelStoreProductRequest(BaseModelConfig):
    request_id: str = Field(..., description="The ID of the store product request to cancel.")
    # acting_store_id will typically be derived from the authenticated user's context
    acting_store_id: str = Field(..., description="The ID of the store cancelling the request.")

# No request models needed for list operations if clients query Firestore directly.
# No request model for get_request if clients query Firestore directly.