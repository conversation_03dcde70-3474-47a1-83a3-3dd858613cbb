from models.base import BaseModelConfig, BaseModel
from pydantic import field_validator
from typing import ClassVar, Optional, Any
import re


email_requests_collection = "email_requests"
email_templates_collection = "email_templates"

class Email(BaseModelConfig):
    from_alias: str 
    from_email: str
    to_alias: str
    to_email: str
    subject: str
    text: str

    @field_validator('to_email', 'from_email')
    def validate_email(cls, v, info):
        field_name = info.field_name
        if not v:
            raise ValueError(f"{field_name} is required")
        # Basic email validation using a common regex
        # For more robust validation, consider a library like email_validator
        if not re.match(r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$", v):
            raise ValueError(f"{field_name} must be a valid email address")
        return v


class SendEmailRequest(BaseModelConfig):
    email: Email
    sender_id:str
    recipient_id:str

class SendEmailTaskRequest(BaseModelConfig):
    EMAIL_REQUEST_ID_FIELD: ClassVar[str] = "emailRequestId"

    email_request_id: str
    email_request: SendEmailRequest


class SendEmailTask(BaseModel):
    EMAIL_REQUEST_ID_FIELD: ClassVar[str] = "email_request_id"

    email_request_id: str



class EmailTemplate(BaseModelConfig):
    template_display_name: str
    store_id: str
    subject: str
    text: str

default_sales_report_email_template_id = 'default-sales-report'

class SendSalesReportEmailRequest(BaseModelConfig):
    template_id: str = default_sales_report_email_template_id
    store_id: str
    sales_report_id: str


default_invite_email_template_id = 'default-signup-invite'

class SendInviteEmailRequest(BaseModelConfig):
    template_id: str = default_invite_email_template_id
    store_id: str
    producer_id: str
