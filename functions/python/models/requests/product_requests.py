from pydantic import Field
from models.base import BaseModelConfig
from typing import Dict, Any, Optional

# Note: create_product uses models.products.Product directly
# Note: create_variant uses models.products.ProductVariant directly

class GetProductRequest(BaseModelConfig):
    logical_product_id: str = Field(..., description="The logical ID of the product to retrieve.")

class UpdateProductRequest(BaseModelConfig):
    logical_product_id: str = Field(..., description="The logical ID of the product to update.")
    update_data: Dict[str, Any] = Field(..., description="A dictionary containing the fields to update and their new values.")

class DeleteProductRequest(BaseModelConfig):
    logical_product_id: str = Field(..., description="The logical ID of the product to delete.")
    delete_variants: Optional[bool] = Field(default=True, description="Whether to delete associated variants. Defaults to True.")

class GetVariantRequest(BaseModelConfig):
    logical_variant_id: str = Field(..., description="The logical ID of the variant to retrieve.")

class UpdateVariantRequest(BaseModelConfig):
    logical_variant_id: str = Field(..., description="The logical ID of the variant to update.")
    update_data: Dict[str, Any] = Field(..., description="A dictionary containing the fields to update and their new values.")

class DeleteVariantRequest(BaseModelConfig):
    logical_variant_id: str = Field(..., description="The logical ID of the variant to delete.")

class GetVariantsForProductRequest(BaseModelConfig):
    logical_product_id: str = Field(..., description="The logical ID of the parent product whose variants are to be retrieved.")
