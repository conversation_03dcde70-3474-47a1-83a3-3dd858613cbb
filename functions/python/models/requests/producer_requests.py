from models.base import BaseModelConfig
from pydantic import Field
from typing import ClassVar, Dict
from models.access_right import AccessRight

class CreateProducerRequest(BaseModelConfig):
  """
  Contract between the FE and BE
  """
  display_name: str
  email: str
  tax_a2: str
  access_right: str = AccessRight.VIEWER.value

  FIELD_MAP: ClassVar[Dict[str, str]] = {
    "displayName": "display_name",
    "email": "email",
    "taxA2": "tax_a2"
  }

  @classmethod
  def from_request_data(cls, data: Dict):
    return cls(
      display_name=data.get("displayName"),
      email=data.get("email"),
      tax_a2=data.get("taxA2")
    )


class DeleteProducerRequest(BaseModelConfig):
  producer_id: str
  hard_delete: bool = False
