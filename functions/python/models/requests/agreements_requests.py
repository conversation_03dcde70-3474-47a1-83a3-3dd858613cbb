from models.base import BaseModelConfig, DateTime
from models.validators import CommissionValidatorMixin
from typing import Optional
from models.agreement import Role
from typing import ClassVar
from gaco_framework.models import GacoRequest, BaseGacoModel


class CreateAgreementRequest(BaseModelConfig, CommissionValidatorMixin):
    STORE_ID_FIELD: ClassVar[str] = 'storeId'
    PRODUCER_ID_FIELD: ClassVar[str] = 'producerId'
    TITLE_FIELD: ClassVar[str] = 'title'
    EFFECTIVE_DATE_FIELD: ClassVar[str] = 'effectiveDate'
    EXPIRATION_DATE_FIELD: ClassVar[str] = 'expirationDate'
    COMMISSION_FIELD: ClassVar[str] = 'commission'
    DOCUMENT_URL_FIELD: ClassVar[str] = 'documentUrl'
    CREATED_BY_ROLE_FIELD: ClassVar[str] = 'createdByRole'

    store_id: str
    producer_id: str
    title: Optional[str] = None
    effective_date: DateTime
    expiration_date: Optional[DateTime] = None
    commission: int
    document_url: Optional[str] = None  # Cloud Storage URL
    created_by_role: Role


class SanitizeSalesStagingWithActiveAgreementRequest(CreateAgreementRequest):
    SALE_ID_FIELD: ClassVar[str] = 'saleId'

    sale_id: str
    created_by_role: Role = Role.STORE.value


class SanitizeSalesStagingWithExistingAgreementRequest(GacoRequest):
    SALE_IDS_FIELD: ClassVar[str] = 'saleIds'
    AGREEMENT_ID_FIELD: ClassVar[str] = 'agreementId'

    sale_ids: list[str]
    agreement_id: str


class SanitizeSaleStagingWithExistingAgreementRequest(GacoRequest):
    SALE_ID_FIELD: ClassVar[str] = 'saleId'
    AGREEMENT_ID_FIELD: ClassVar[str] = 'agreementId'

    sale_id: str
    agreement_id: str


class SanitizeSaleWithAgreementTaskRequest(BaseGacoModel):
    """
    Content of the task to be executed.
    """
    sanitization_request_id: str
    data: SanitizeSaleStagingWithExistingAgreementRequest


class SanitizeSaleWithAgreementTask(BaseGacoModel):
    """
    Actual task payload that is conencted to a document with data.
    This is used to enqueue and execute the task.
    """
    SANITIZATION_REQUEST_ID_FIELD: ClassVar[str] = 'sanitizationRequestId'
    sanitization_request_id: str
    

class UpdateDraftAgreementRequest(BaseModelConfig):
    AGREEMENT_ID_FIELD: ClassVar[str] = 'agreementId'
    TITLE_FIELD: ClassVar[str] = 'title'
    EFFECTIVE_DATE_FIELD: ClassVar[str] = 'effectiveDate'
    EXPIRATION_DATE_FIELD: ClassVar[str] = 'expirationDate'
    COMMISSION_FIELD: ClassVar[str] = 'commission'
    DOCUMENT_URL_FIELD: ClassVar[str] = 'documentUrl'

    agreement_id: str
    title: Optional[str] = None
    effective_date: Optional[DateTime] = None
    expiration_date: Optional[DateTime] = None
    commission: Optional[int] = None
    document_url: Optional[str] = None


class SubmitAgreementForApprovalRequest(BaseModelConfig):
    agreement_id: str
    role: str


class ApproveAgreementRequest(BaseModelConfig):
    agreement_id: str
    store_or_producer_id: str
    role: str
    comments: Optional[str] = None


class TerminateAgreementRequest(BaseModelConfig):
    agreement_id: str


class RejectAgreementRequest(BaseModelConfig):
    agreement_id: str
    role: str
    comments: Optional[str] = None


class DeleteAgreementRequest(BaseModelConfig):
    agreement_id: str
