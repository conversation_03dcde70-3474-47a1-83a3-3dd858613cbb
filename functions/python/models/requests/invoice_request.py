from pydantic import BaseModel
from datetime import datetime
from models.base import BaseModelConfig, DateTime
from typing import Optional, ClassVar


class CreateInvoiceRequest(BaseModelConfig):
    STORE_ID_FIELD: ClassVar[str] = "storeId"
    PRODUCER_ID_FIELD: ClassVar[str] = "producerId"
    START_DATE_FIELD: ClassVar[str] = "startDate"
    END_DATE_FIELD: ClassVar[str] = "endDate"

    store_id: str
    producer_id: Optional[str] = None
    start_date: DateTime
    end_date: DateTime



class CreateInvoiceBySaleIdRequest(BaseModelConfig):
    SALE_ID_FIELD: ClassVar[str] = "saleId"

    sale_id: str


class DeleteInvoiceRequest(BaseModelConfig):
    invoice_doc_id: str
