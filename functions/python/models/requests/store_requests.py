from models.base import BaseModelConfig, DateTime
from models.validators import CommissionValidatorMixin
from typing import Optional, Dict
from models.payout_account import Address
from models.access_right import AccessRight

class ShopifyStoreCredentials(BaseModelConfig):
  shop_name: str
  shopify_api_key: str

  @classmethod
  def from_request_data(cls, data: Dict):
    return cls(
      shop_name=data.get("shopName"),
      shopify_api_key=data.get("shopifyApiKey")
    )


class CreateStoreRequest(BaseModelConfig, CommissionValidatorMixin):
  """
  This is the request object for the create store function,
  contract between the FE and BE,
  with request, we should keep it as flat as possible.
  """
  display_name: str
  email: str
  tax_a2: str
  default_commission: Optional[int] = None
  description: Optional[str] = None
  address: Optional[Address] = None
  shopify_api_key_object: ShopifyStoreCredentials
  access_right: str = AccessRight.VIEWER.value

  @classmethod
  def from_request_data(cls, data: Dict):
    return cls(
      display_name=data.get("displayName"),
      email=data.get("email"),
      tax_a2=data.get("taxA2"),
      default_commission=data.get("defaultCommission") if "defaultCommission" in data else None,
      description=data.get("description") if "description" in data else None,
      address=Address.model_validate(data.get("address")),
      shopify_api_key_object=ShopifyStoreCredentials.from_request_data(
        data.get("shopifyApiKeyObject")
      )
    )
  
  
class DeleteStoreRequest(BaseModelConfig):
  store_id: str
  hard_delete: bool = False