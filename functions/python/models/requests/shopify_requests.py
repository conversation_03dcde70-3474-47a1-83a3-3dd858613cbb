from typing import Dict, ClassVar, Optional
from models.base import BaseModelConfig
from models.secret import Platform


class GetShopifyOrdersRequest(BaseModelConfig):
    """Request model for getting Shopify orders."""
    store_id: str
    start_date: str
    end_date: str
    

class GetShopifyOrdersWithDynamicQueryRequest(BaseModelConfig):
    """Request model for getting store orders that will be coming
      from the client."""
    store_id: str
    type: Platform = Platform.SHOPIFY.value
    force_refresh: Optional[bool] = False
    days_back: Optional[int] = None
    