from pydantic import Field
from models.base import BaseModelConfig
from typing import Optional, Dict, Any
from models.allocations import AllocationStatus # Assuming AllocationStatus is in models.allocations

# For create_allocation, the models.allocations.ProductAllocation model itself will be used directly in the cloud function.

class GetAllocationRequest(BaseModelConfig):
    allocation_id: str = Field(..., description="The ID of the product allocation to retrieve.")

class UpdateAllocationStatusRequest(BaseModelConfig):
    allocation_id: str = Field(..., description="The ID of the product allocation to update.")
    new_status: str = Field(..., description="The new status for the allocation.")
    details: Optional[Dict[str, Any]] = Field(default=None, description="Optional dictionary for additional details like tracking_number, notes, etc.")
    # updated_by_user_id: Optional[str] = Field(default=None, description="ID of the user performing the update.") # Optional, if you track this

class ListAllocationsByProducerRequest(BaseModelConfig):
    producer_id: str = Field(..., description="The logical ID of the producer whose allocations are to be listed.")
    status: Optional[AllocationStatus] = Field(default=None, description="Optional status to filter allocations by.")

class ListAllocationsByStoreRequest(BaseModelConfig): # Assuming you might want this too
    store_id: str = Field(..., description="The logical ID of the store whose allocations are to be listed.")
    status: Optional[AllocationStatus] = Field(default=None, description="Optional status to filter allocations by.")
