from pydantic import BaseModel
from typing import ClassVar, Optional
from models.validators import CommissionValidatorMixin
from models.base import BaseModelConfig, DateTime


class ForceAgreementRequest(BaseModelConfig, CommissionValidatorMixin):
    STORE_ID_FIELD: ClassVar[str] = "storeId"
    TITLE_FIELD: ClassVar[str] = "title"
    EFFECTIVE_DATE_FIELD: ClassVar[str] = "effectiveDate"
    EXPIRATION_DATE_FIELD: ClassVar[str] = "expirationDate"
    COMMISSION_FIELD: ClassVar[str] = "commission"
    DOCUMENT_URL_FIELD: ClassVar[str] = "documentUrl"
    DISPLAY_NAME_FIELD: ClassVar[str] = "displayName"
    EMAIL_FIELD: ClassVar[str] = "email"
    TAX_A2_FIELD: ClassVar[str] = "taxA2"

    # for agreement
    store_id: str
    title: Optional[str] = None
    effective_date: DateTime
    expiration_date: Optional[DateTime] = None
    commission: int
    document_url: Optional[str] = None

    # for producer
    display_name: str
    email: str
    tax_a2: str


class SanitizeSalesStagingWithAgreementRequest(ForceAgreementRequest):
    SALE_ID_FIELD: ClassVar[str] = "saleId"

    sale_id: str