from pydantic import Field
from models.products import Product
from typing import Optional
from models.base import BaseModelConfig

class CreateShopifyProductRequest(BaseModelConfig):
    store_id: str = Field(..., description="The internal ID of the store to identify Shopify credentials.")
    product_data: Product = Field(..., description="The product data to create in Shopify, conforming to the internal Product model.")
    shopify_api_version: Optional[str] = Field(default=None, description="Optional Shopify API version to target. If None, service default is used.")
    shopify_location_gid: Optional[str] = Field(default=None, description="Optional Shopify Location GID for inventory. If None, service may attempt to use the default.")

    class Config:
        json_schema_extra = {
            "example": {
                "store_id": "your_internal_store_id",
                "product_data": {
                    "title": "Awesome New Product from Separate Endpoint",
                    "vendor": "My Brand",
                    "product_type": "Gadgets",
                    "status": "ACTIVE",
                    "handle": "awesome-new-product-sep",
                    "created_at": "2023-10-27T10:00:00Z",
                    "updated_at": "2023-10-27T10:00:00Z",
                    "variants": [
                        {
                            "title": "Standard",
                            "price": 39.99,
                            "sku": "ANS-SEP-STD-001",
                            "inventory_quantity": 25,
                            "created_at": "2023-10-27T10:00:00Z",
                            "updated_at": "2023-10-27T10:00:00Z"
                        }
                    ],
                    "store_id": "some_store_id_for_context", 
                    "store_display_name": "Some Store Name" 
                },
                "shopify_api_version": "2024-04",
                "shopify_location_gid": "gid://shopify/Location/1234567890"
            }
        }