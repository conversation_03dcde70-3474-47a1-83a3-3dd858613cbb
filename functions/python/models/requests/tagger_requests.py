from pydantic import field_validator
from typing import List, Dict, ClassVar
from gaco_framework.models import BaseGacoModel


class SalesStagingProducerTagRequest(BaseGacoModel):
    """Request model for sales staging data tagging configuration"""
    STORE_ID_FIELD: ClassVar[str] = 'storeId'

    store_id: str
    fields: List[str]  # Fields in the sales model to check against tag rules
    tag_rule: Dict[str, List[str]]  # producer_id -> list of keywords to match
    override_vendor: bool
    update_vendor_if_null: bool

    @field_validator('override_vendor', 'update_vendor_if_null')
    @classmethod
    def validate_vendor_flags(cls, v, info):
        """Validate that at least one vendor flag is True or both are False"""
        if info.field_name == 'update_vendor_if_null':
            # This runs after override_vendor is validated
            override_vendor = info.data.get('override_vendor', False)
            update_vendor_if_null = v

            # Valid combinations: (True, True), (True, False), (False, True), (False, False)
            # Invalid: none (all combinations are actually valid)
            # But let's enforce the business rule: at least one True OR both False
            if not override_vendor and not update_vendor_if_null:
                # Both False is allowed
                pass
            elif override_vendor or update_vendor_if_null:
                # At least one True is allowed
                pass
            # All combinations are valid based on requirements
        return v