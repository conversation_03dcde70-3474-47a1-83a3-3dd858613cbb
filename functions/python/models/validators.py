from pydantic import BaseModel, field_validator
from typing import ClassVar


class CommissionValidatorMixin(BaseModel):
    @classmethod
    def validate_commission_value(cls, v, field_name: str = 'Commission'):
        if v < 0 or v > 100:
            raise ValueError(f'{field_name} must be between 0 and 100')
        return v

    @field_validator('commission', check_fields=False)
    def validate_commission(cls, v):
        return cls.validate_commission_value(v, 'Commission')
        
    @field_validator('default_commission', check_fields=False)
    def validate_default_commission(cls, v):
        if v is None:
            return v  # Skip validation if value is None
        return cls.validate_commission_value(v, 'Default commission')