from typing import Optional, Literal, List, Dict, ClassVar # Ensure List and Dict are imported
from pydantic import BaseModel, Field # Ensure BaseModel and Field are imported
from datetime import datetime, timezone
from models.base import BaseModelConfig, DateTime
from enum import Enum

# --- Existing ProductAllocation related models (ensure they are here or imported) ---
product_allocations_collection = "product-allocations"

class AllocationStatus(Enum):
    PENDING_CONFIRMATION = "PENDING_CONFIRMATION"
    PENDING_SHIPMENT = "PENDING_SHIPMENT"
    IN_TRANSIT = "IN_TRANSIT"
    DELIVERED = "DELIVERED"
    RECEIVED_WITH_ISSUES = "RECEIVED_WITH_ISSUES"
    CANCELLED = "CANCELLED"



class DeliveryMethod(Enum):
    PRODUCER_SHIPMENT = "PRODUCER_SHIPMENT"
    PRODUCER_DELIVERY = "PRODUCER_DELIVERY"
    STORE_PICKUP = "STORE_PICKUP"

class ProductAllocationItem(BaseModelConfig):
    product_id: str = Field(..., description="Logical ID of the product.")
    product_variant_id: Optional[str] = Field(default=None, description="Logical ID of the product variant.")
    # In ProductAllocation, this is allocated_quantity.
    # In StoreProductRequest, this will be interpreted as requested_quantity.
    quantity: int = Field(..., gt=0, description="Number of units of this variant.")


class ProductAllocation(BaseModelConfig):
    ALLOCATION_ID_FIELD: ClassVar[str] = "allocationId"
    PRODUCER_ID_FIELD: ClassVar[str] = "producerId"
    STORE_ID_FIELD: ClassVar[str] = "storeId"
    ITEMS_FIELD: ClassVar[str] = "items"
    STATUS_FIELD: ClassVar[str] = "status"
    DELIVERY_METHOD_FIELD: ClassVar[str] = "deliveryMethod"
    ALLOCATION_DATE_FIELD: ClassVar[str] = "allocationDate"
    SHIPPED_ON_FIELD: ClassVar[str] = "shippedOn"
    DELIVERED_ON_FIELD: ClassVar[str] = "deliveredOn"
    PRODUCER_NOTES_FIELD: ClassVar[str] = "producerNotes"
    TRACKING_NUMBER_FIELD: ClassVar[str] = "trackingNumber"


    allocation_id: Optional[str] = Field(default=None, description="Firestore auto-generated document ID.") 
    producer_id: str = Field(..., description="Logical ID of the producer initiating the allocation.")
    store_id: str = Field(..., description="Logical ID of the store receiving the products.")
    items: List[ProductAllocationItem] = Field(..., min_length=1, description="List of product variants and quantities allocated.")
    status: str = Field(default=AllocationStatus.PENDING_CONFIRMATION.value)
    delivery_method: str 
    allocation_date: DateTime = Field(default_factory=lambda: datetime.now(timezone.utc).isoformat())
    shipped_on: Optional[DateTime] = None
    delivered_on: Optional[DateTime] = None
    producer_notes: Optional[str] = None
    store_notes: Optional[str] = None
    tracking_number: Optional[str] = None
    # created_by_user_id: Optional[str] = None # User from producer side
    # last_updated_by_user_id: Optional[str] = None


# --- New StoreProductRequest related models ---

store_product_requests_collection = "store-product-requests"

class ProductRequestStatus(Enum):
    PENDING_PRODUCER_APPROVAL = "PENDING_PRODUCER_APPROVAL"
    APPROVED_BY_PRODUCER = "APPROVED_BY_PRODUCER"
    REJECTED_BY_PRODUCER = "REJECTED_BY_PRODUCER"
    CANCELLED_BY_STORE = "CANCELLED_BY_STORE"


class StoreProductRequest(BaseModelConfig):
    REQUEST_ID_FIELD: ClassVar[str] = "requestId"
    STORE_ID_FIELD: ClassVar[str] = "storeId"
    PRODUCER_ID_FIELD: ClassVar[str] = "producerId"
    REQUESTED_ITEMS_FIELD: ClassVar[str] = "requestedItems"
    STATUS_FIELD: ClassVar[str] = "status"
    REQUEST_DATE_FIELD: ClassVar[str] = "requestDate"
    DESIRED_DELIVERY_DATE_FIELD: ClassVar[str] = "desiredDeliveryDate"
    STORE_REQUEST_NOTES_FIELD: ClassVar[str] = "storeRequestNotes"
    PRODUCER_RESPONSE_NOTES_FIELD: ClassVar[str] = "producerResponseNotes"
    RELATED_ALLOCATION_ID_FIELD: ClassVar[str] = "relatedAllocationId"
    # created_by_user_id: Optional[str] = None # User from store side
    # last_updated_by_user_id: Optional[str] = None # User who last changed status (producer or store)


    request_id: Optional[str] = Field(default=None, description="Firestore auto-generated document ID. Populated after creation.")
    store_id: str = Field(..., description="Logical ID of the store making the request.")
    producer_id: str = Field(..., description="Logical ID of the producer the request is for.")
    
    # Reusing ProductAllocationItem. 'quantity' field will mean 'requested_quantity'.
    requested_items: List[ProductAllocationItem] = Field(..., min_length=1, description="List of product variants and quantities requested.")

    status: str = Field(default=ProductRequestStatus.PENDING_PRODUCER_APPROVAL.value, description="Current status of the request.")
    
    request_date: DateTime = Field(default_factory=datetime.now, description="Timestamp when the request was created.")
    desired_delivery_date: Optional[DateTime] = Field(default=None, description="Optional: Store's desired delivery date for the items.")
    
    store_request_notes: Optional[str] = Field(default=None, description="General notes from the store for this request.")
    producer_response_notes: Optional[str] = Field(default=None, description="Notes from the producer when approving/rejecting.")
    
    # Links to the ProductAllocation document if this request is approved and converted
    related_allocation_id: Optional[str] = Field(default=None, description="ID of the ProductAllocation created from this request, if approved.")

    # created_by_user_id: Optional[str] = None # User from store side
    # last_updated_by_user_id: Optional[str] = None # User who last changed status (producer or store)
