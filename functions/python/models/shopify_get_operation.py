from models.base import BaseModelConfig
from models.base import DateTime
from enum import Enum
from typing import ClassVar

shopify_fetch_operations_collection = "shopify_fetch_operations"

class ShopifyGetOperationStatus(str, Enum):
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"

class ShopifyGetOperation(BaseModelConfig):
    FETCH_ID_FIELD: ClassVar[str] = "fetchId"
    STORE_ID_FIELD: ClassVar[str] = "storeId"
    BASE_PATH_FIELD: ClassVar[str] = "basePath"
    PAGES_FETCHED_FIELD: ClassVar[str] = "pagesFetched"
    TOTAL_ORDERS_FIELD: ClassVar[str] = "totalOrders"
    CREATED_AT_FIELD: ClassVar[str] = "createdAt"
    COMPLETED_AT_FIELD: ClassVar[str] = "completedAt"
    STATUS_FIELD: ClassVar[str] = "status"
    DOWNLOAD_URL_FIELD: ClassVar[str] = "downloadUrl"
    FILE_PATH_FIELD: ClassVar[str] = "filePath"
    ORDERS_IN_FIRST_PAGE_FIELD: ClassVar[str] = "ordersInFirstPage"
    HAS_MORE_PAGES_FIELD: ClassVar[str] = "hasMorePages"
    NEXT_CURSOR_FIELD: ClassVar[str] = "nextCursor"
    LAST_UPDATED_FIELD: ClassVar[str] = "lastUpdated"
    HAS_NEXT_PAGE_FIELD: ClassVar[str] = "hasNextPage"
    ERROR_MESSAGE_FIELD: ClassVar[str] = "errorMessage"
    ERROR_TIME_FIELD: ClassVar[str] = "errorTime"
    START_DATE_FIELD: ClassVar[str] = "startDate"
    END_DATE_FIELD: ClassVar[str] = "endDate"

    order_getter_id: str
    store_id: str
    shop_name: str
    start_date: str 
    end_date: str
    next_cursor: str | None = None
    has_next_page: bool
    base_path: str
    pages_fetched: int
    total_orders: int
    created_at: DateTime
    completed_at: DateTime | None = None
    status: ShopifyGetOperationStatus
    next_cursor: str | None = None
    has_next_page: bool | None = None
    last_updated: DateTime | None = None
    error_message: str | None = None
    error_time: DateTime | None = None
    