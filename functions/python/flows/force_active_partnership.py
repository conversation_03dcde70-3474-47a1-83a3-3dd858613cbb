from typing import Optional

from gaco_framework.auth import AuthContext
from models.requests.agreements_requests import CreateAgreementRequest
from models.agreement import Role
from services.producer_manager import ProducerManager
from services.agreement_manager import AgreementManager
from firebase_admin import firestore
from models.requests.producer_requests import CreateProducerRequest
from models.requests.sanitization_request import ForceAgreementRequest
from queries.producers_query_builder_v2 import ProducersQueryBuilderV2
from gaco_framework.exceptions import AuthorizationError
from models.access_right import AccessRight, STORE_ACCESS_RIGHTS

class ForceActivePartnershipFlow:
    """
    This flow is used to force create an active partnership.
    Only possible to initiate by the store.
    """

    def __init__(self, db: firestore.Client, auth_context: Optional[AuthContext] = None):
        self.db = db
        self.auth_context = auth_context
        self.producer_manager = ProducerManager(self.db, self.auth_context)
        self.agreement_manager = AgreementManager(self.db, self.auth_context)
        self.producers_query_builder = ProducersQueryBuilderV2(self.db)

    def force_producer(
            self,
            request: CreateProducerRequest,
            store_id: str
    ) -> str:
        """
        Force create a producer with no parent_id.
        If the producer already exists, return the producer_id.
        """
        try:
            producer_id = self.producer_manager \
                .create_producer_no_parentId(request, store_id)
            return producer_id
        except Exception:
            producer_doc = self.producers_query_builder \
                .for_email(request.email) \
                .build() \
                .get()
            if producer_doc:
                return producer_doc[0].id
            raise

    def force_create_active_agreement(
            self,
            request: CreateAgreementRequest,
    ) -> dict:
        """
        Force create an active agreement.
        """
        # overwrite the created_by_role to store since we are forcing the agreement
        request.created_by_role = Role.STORE.value
        draft_agreement_id = self.agreement_manager \
            .create_draft_agreement(request)
        self.agreement_manager.submit_for_approval(
            agreement_id=draft_agreement_id,
            role=Role.STORE.value
        )

        self.agreement_manager.approve_agreement(
            agreement_id=draft_agreement_id,
            role=Role.PRODUCER.value,
        )

        return draft_agreement_id

    def force_create_active_partnership(
            self,
            request: ForceAgreementRequest,
    ) -> dict:
        """
        Force create an active partnership.
        """

        # TODO: create cloud function to same as in dua where get trigered on the document update
        # if self.auth_context:
        #     store_access_rights = self.auth_context.custom_claims.get(STORE_ACCESS_RIGHTS)
        #     if store_access_rights[request.store_id] not in [AccessRight.ADMIN.value, AccessRight.EDITOR.value]:
        #         raise AuthorizationError("You are not authorized to create a partnership")

        create_producer_request = CreateProducerRequest(
            display_name=request.display_name,
            email=request.email,
            tax_a2=request.tax_a2
        )
        producer_id = self.force_producer(
            request=create_producer_request,
            store_id=request.store_id
        )

        create_agreement_request = CreateAgreementRequest(
            store_id=request.store_id,
            producer_id=producer_id,
            title=request.title,
            effective_date=request.effective_date,
            expiration_date=request.expiration_date,
            commission=request.commission,
            document_url=request.document_url,
            created_by_role=Role.STORE.value
        )

        agreement_id = self.force_create_active_agreement(
            create_agreement_request
        )

        return {
            "producer_id": producer_id,
            "agreement_id": agreement_id
        }
