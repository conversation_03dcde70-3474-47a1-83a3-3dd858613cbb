import requests
import os
from typing import List
from models.requests.email_requests import (
    email_requests_collection, 
    Email, 
    SendEmailTaskRequest, 
    SendEmailRequest, 
    SendEmailTask, 
    SendSalesReportEmailRequest,
    SendInviteEmailRequest
)
from firebase_admin import firestore, functions
from firebase_functions import logger
from datetime import datetime, timezone
from constants.gaco_values import email_task_queue
from services.email_templater import EmailTemplater
from services.sales_report_manager import SalesReportManager
from models.sales_report import SalesReportStatus


class EmailManager:
    def __init__(self, db: firestore.Client):
        self.db = db
        self.api_key = os.getenv('MAILGUN_API_KEY')
        self.email_collection = self.db.collection(email_requests_collection)
        self.email_templater = EmailTemplater(db)


    def send_email(self, email: Email) -> dict:
        response = requests.post(
            "https://api.mailgun.net/v3/gastart.nl/messages",
            auth=("api", self.api_key),
            data={
                "from": f"{email.from_alias} <{email.from_email}>",
                "to": f"{email.to_alias} <{email.to_email}>",
                "subject": email.subject,
                "text": email.text
            }
        )
        
        response_json = response.json()

        return {
            "id": response_json['id'],
            "message": response_json['message'],
            "status_code": response.status_code
        }


    def trigger_send_email(self, email_task_request: SendEmailTaskRequest) -> str:
        doc_ref = self.email_collection.document()
        response = self.send_email(email_task_request.email_request.email)
        doc_ref = self.db.collection(email_requests_collection)\
          .document(email_task_request.email_request_id)
        doc_ref.update({
            'status': 'sent',
            'response': response
        })
        return doc_ref.id


    def enqueue_send_email(self, email_request: SendEmailRequest) -> str:

        email_request_id = (
          f"{email_request.sender_id}_"
          f"{email_request.recipient_id}_"
          f"{datetime.now(timezone.utc).strftime('%Y%m%d_%H%M%S')}"
        )

        email_task_request = SendEmailTaskRequest(
          email_request_id=email_request_id,
          email_request=email_request
        )

        db = firestore.client()
        db.collection(email_requests_collection).document(email_request_id)\
          .set(email_task_request.model_dump())

        data = SendEmailTask.model_validate({
          SendEmailTask.EMAIL_REQUEST_ID_FIELD: email_request_id
        })

        task_payload = {
          'data': data.model_dump()
        }

        logger.info(f"task_payload: {task_payload}")

        task_queue = functions.task_queue(email_task_queue)
        task_queue.enqueue(task_payload)

        return task_payload


    def enqueue_send_sales_report_email(
            self, 
            request: SendSalesReportEmailRequest
        ) -> str:
        email = self.email_templater.populate_sales_report_template(
            request=request
        )

        sales_report_manager = SalesReportManager(self.db)
        sales_report_manager.update_sales_report_status(
            request.sales_report_id,
            SalesReportStatus.SENT.value
        )

        email_request = SendEmailRequest(
            sender_id=request.store_id,
            recipient_id=request.sales_report_id, # special for sales report
            email=email
        )

        logger.info(f"email_request: {email_request}")

        return self.enqueue_send_email(email_request)


    def enqueue_send_invite_email(
            self, 
            request: SendInviteEmailRequest
        ) -> str:
        email = self.email_templater.populate_invite_email_template(
            request=request
        )

        email_request = SendEmailRequest(
            sender_id=request.store_id,
            recipient_id=request.producer_id,
            email=email
        )

        return self.enqueue_send_email(email_request)
