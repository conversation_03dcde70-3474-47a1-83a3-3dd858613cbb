from firebase_admin import firestore
from datetime import datetime, timezone
from firebase_functions import logger 
from typing import Optional, Tu<PERSON>, Dict, Any, Union, List
from gaco_framework.auth import AuthContext
from gaco_framework.exceptions import NotFoundError
from gaco_framework.managers import BaseGacoManager
from gaco_framework.security import ResourceAccess
from models.partner import Partnership, PartnershipStatus, partnership_collection
from models.agreement import Agreement, AgreementStatus, agreement_collection
from queries.producers_query_builder_v2 import ProducersQueryBuilderV2
from queries.stores_query_builder_v2 import StoresQueryBuilderV2
from queries.partners_query_builder import PartnersQueryBuilder
from queries.sales_gold_query_builder import SalesGoldQueryBuilder
from models.agreement import Role


class PartnershipManager(BaseGacoManager):
    """
    Manager for partnerships between entities (stores, producers, etc.)
    Represents on going business relationship between entities.
    System level service that user does not have to interact with much.
    """

    def __init__(self, db: firestore.Client, auth_context: Optional[AuthContext] = None):
        super().__init__(db, auth_context)
        self.collection_name = partnership_collection
        self.partnership_collection = self._get_collection(self.collection_name)
        self.partnership_query_builder = PartnersQueryBuilder(self.db)
        self.producer_query_builder = ProducersQueryBuilderV2(self.db)
        self.store_query_builder = StoresQueryBuilderV2(self.db)
        self.sales_gold_query_builder = SalesGoldQueryBuilder(self.db)

    def fetch_active_partnerships(self, store_id: str) -> List[Partnership]:
        """
        Fetch all active partnerships for a store
        """
        query = (self.partnership_query_builder
                .for_store_id(store_id)
                .with_status(PartnershipStatus.ACTIVE.value)
                .build())
        
        active_partnerships = []
        for partnership_doc in query.stream():
            partnership_data = partnership_doc.to_dict()
            active_partnerships.append(partnership_data)

        return active_partnerships

    def check_if_partnership_has_expired(
        self, partnership: Partnership, partner_document_id: str
    ) -> bool:
        """
        Check if a partnership has expired
        """
        if partnership.end_date and datetime.now(timezone.utc) > partnership.end_date:
            docs = self.partnership_query_builder\
                .for_id(partner_document_id)\
                .build()\
                .get()

            if not docs:
                return False

            partnership_doc = docs[0]
            partnership_data = partnership_doc.to_dict()
            # self._check_access("partnership", partnership_data, ResourceAccess.WRITE)

            partnership_doc.reference.update({
                Partnership.STATUS_FIELD: PartnershipStatus.EXPIRED.value
            })
            return True
        return False

    def create_partnership(self, agreement_ref: firestore.DocumentReference) -> str:
        """
        Create a new partnership
        """

        agreement_doc = agreement_ref.get()
        agreement_data = Agreement.model_validate(agreement_doc.to_dict())

        if agreement_data.status != AgreementStatus.ACTIVE.value:
            raise ValueError("Agreement is not active")

        a_partner = Partnership(
            store_id=agreement_data.store_id,
            producer_id=agreement_data.producer_id,
            status=PartnershipStatus.ACTIVE.value,
            agreement_id=agreement_ref.id,
            commission=agreement_data.commission,
            document_url=agreement_data.document_url,
            effective_date=agreement_data.effective_date,
            expiration_date=agreement_data.expiration_date,
            created_by=Role.SYSTEM.value,
            updated_at=datetime.now(timezone.utc),
        )
        partnership_data = a_partner.model_dump()
        # self._check_access("partnership", partnership_data, ResourceAccess.WRITE)

        # Check if partnership with same agreement already exists
        existing_partnerships = self.partnership_query_builder\
            .for_agreement_id(agreement_ref.id)\
            .with_status(PartnershipStatus.ACTIVE.value)\
            .build()\
            .get()
        
        if len(existing_partnerships) > 0:
            raise ValueError(f"Partnership for agreement {agreement_ref.id} already exists")
            
        partner_ref = self.partnership_collection.document()
        partner_ref.set(partnership_data)

        return partner_ref

    def terminate_partnership_on_agreement(self, agreement_id: str) -> Dict[str, Any]:
        """
        Terminate a partnership
        """
        partnership_docs = self.partnership_query_builder\
            .for_agreement_id(agreement_id)\
            .build()\
            .get()

        if not partnership_docs:
            raise NotFoundError("Partnership not found")

        if len(partnership_docs) > 1:
            raise ValueError(
                f"Multiple partnerships found for the same agreement {agreement_id}"
            )

        partnership_doc = partnership_docs[0]
        partnership_data = partnership_doc.to_dict()
        # self._check_access("partnership", partnership_data, ResourceAccess.DELETE)
        partnership_doc.reference.delete()

        return {"success": True, "message": "Partnership deleted successfully"}


    def delete_partnership_and_agreement(self, partnership_id: str) -> str:
        """
        Delete a partnership and the agreement.
        Only for Dev purposes.
        """
        a_partnership = Partnership.model_validate(
            self.partnership_collection.document(partnership_id).get().to_dict()
        )

        self.terminate_partnership_on_agreement(a_partnership.agreement_id)

        logger.info(f"Terminated partnership {a_partnership.agreement_id}")

        #check if producer got any sales in staging or gold
        sales_gold_query = self.sales_gold_query_builder\
            .for_producer_id(a_partnership.producer_id).build()

        sales_gold_query = sales_gold_query.get()

        if len(sales_gold_query) > 0:
            raise ValueError("Producer has sales in staging or gold")

        self.partnership_collection.document(partnership_id).delete()
        self.db.collection(agreement_collection)\
            .document(a_partnership.agreement_id).delete()

        return partnership_id
