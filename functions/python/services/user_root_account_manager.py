from models.user_root_account import User<PERSON>ootAccount, EnumUserType
from models.base import DateTime
from firebase_admin import auth, firestore
from firebase_functions import logger
from datetime import datetime
from queries.user_root_account_query_builder import UserRootAccountQueryBuilder
from gaco_framework.exceptions import NotFoundError
from typing import List
import json
from models.access_right import AccessRight
import warnings


class UserRootAccountManager:
  def __init__(self, db: firestore.Client = None):
    self.db = db
    self.collection_name = "userRootAccounts"
    self.user_root_account_collection = db.collection(self.collection_name)
    self.user_root_account_query_builder = UserRootAccountQueryBuilder(db = db)

  def check_if_user_root_account_exists(self, user_uuid: str):
    doc_snapshot = self.user_root_account_collection.document(user_uuid).get()
    return doc_snapshot.exists

  def check_if_user_is_actual_user(self, user_uuid: str):
    try:
      user = auth.get_user(user_uuid)
      return user
    except auth.UserNotFoundError:
      logger.error(f"User not found, cant create user root account: {user_uuid}")
      raise Exception("User not found, cant create user root account")

  def delete_user_root_account(self, user_uuid: str) -> None:
    try:
      # Before deleting the user root account, consider clearing their custom claims
      # to prevent lingering permissions if the account is recreated quickly.
      auth.set_custom_user_claims(user_uuid, None) 
      logger.info(f"Custom claims cleared for user {user_uuid} during root account deletion.")
      self.user_root_account_collection.document(user_uuid).delete()
    except Exception as e:
      logger.error(f"Error deleting user root account or clearing claims: {e}")
      raise Exception("Error deleting user root account")

  def _set_item_access_right_in_user_root_account(
        self,
        user_uuid: str,
        item_id: str,
        access_right: str, # Changed from role
        field_name: str, # e.g., UserRootAccount.STORES_FIELD
        item_type: str # e.g., "store" or "producer"
      ) -> None:
    try:
      # Basic role validation (optional, expand as needed)
      valid_access_rights = [
        AccessRight.VIEWER.value, 
        AccessRight.EDITOR.value, 
        AccessRight.ADMIN.value
      ]

      if access_right not in valid_access_rights:
        logger.error(f"Invalid access_right '{access_right}' for {item_type} {item_id}.")
        raise ValueError(f"Invalid access_right: {access_right}. Must be one of {valid_access_rights}")

      user_root_account_ref = self.user_root_account_collection.document(user_uuid)
      user_root_account_snapshot = user_root_account_ref.get()

      if not user_root_account_snapshot.exists:
        logger.error(f"User root account not found: {user_uuid}")
        raise NotFoundError(f"User root account not found, {user_uuid}")
      
      # Max items check - this logic might need to change if it's based on the number of roles
      # For now, assuming it's still a general limit on associations.
      # items_map = user_root_account_snapshot.get(field_name) or {}
      # if len(items_map) >= 10 and item_id not in items_map: # Check if adding a new item would exceed
      #   logger.error(f"Max {item_type} associations reached for user: {user_uuid}")
      #   raise Exception(f"Max {item_type} associations reached")

      # Update the map field with the new role
      # For example, if field_name is "stores", this becomes {"stores.item_id": role}
      update_data = {f"{field_name}.{item_id}": access_right}
      try:
        user_root_account_ref.update(update_data)
      except Exception as e:
        logger.error(
          f"Error setting access_right for {item_type} {item_id} for user {user_uuid}: {e}"
          f"update_data: {update_data}, "
          f"possible that string contains non alpha numeric characters"
        )
        raise Exception(f"Error setting {item_type} access_right in user root account")
      logger.info(f"Successfully set {item_type} {item_id} to access_right '{access_right}' for user {user_uuid}.")
      self._update_user_custom_claims(user_uuid)

    except Exception as e:
      logger.error(f"Error setting access_right for {item_type} {item_id} for user {user_uuid}: {e}")
      # Re-raise with more context or handle as appropriate
      raise Exception(f"Error setting {item_type} access_right in user root account")

  def add_producer_with_access_right(self, user_uuid: str, producer_id: str, access_right: str) -> None: # Renamed method
    self._set_item_access_right_in_user_root_account(user_uuid, producer_id, access_right, UserRootAccount.PRODUCERS_FIELD, "producer")

  def add_store_with_access_right(self, user_uuid: str, store_id: str, access_right: str) -> None: # Renamed method
    self._set_item_access_right_in_user_root_account(user_uuid, store_id, access_right, UserRootAccount.STORES_FIELD, "store")


  def _remove_item_from_user_root_account(
        self,
        user_uuid: str,
        item_id: str,
        field_name: str, # e.g., UserRootAccount.STORES_FIELD
        item_type: str
      ) -> None:
    try:
      user_root_account_ref = self.user_root_account_collection.document(user_uuid)
      user_root_account_snapshot = user_root_account_ref.get()
      
      if not user_root_account_snapshot.exists:
        logger.error(f"User root account not found: {user_uuid}")
        # Or simply return if removal is idempotent
        raise Exception(f"User root account not found: {user_uuid}")

      # Remove the item_id from the map using FieldValue.delete()
      # For example, if field_name is "stores", this becomes {"stores.item_id": firestore.DELETE_FIELD}
      update_data = {f"{field_name}.{item_id}": firestore.DELETE_FIELD}
      user_root_account_ref.update(update_data)
      logger.info(f"Successfully removed {item_type} {item_id} association for user {user_uuid}.")
      self._update_user_custom_claims(user_uuid)
    except Exception as e:
      message = f"Error removing {item_type} from user root account: {e}"
      logger.error(message)
      warnings.warn(message)

  def remove_producer_association(self, user_uuid: str, producer_id: str) -> None: # Renamed method
    self._remove_item_from_user_root_account(user_uuid, producer_id, UserRootAccount.PRODUCERS_FIELD, "producer")

  def remove_store_association(self, user_uuid: str, store_id: str) -> None: # Renamed method
    self._remove_item_from_user_root_account(user_uuid, store_id, UserRootAccount.STORES_FIELD, "store")

  def get_user_store_access_right(self, user_uuid: str, store_id: str) -> str | None: # Renamed method
    access_rights_map = self.get_user_root_account_field_as_map(user_uuid, UserRootAccount.STORES_FIELD)
    return access_rights_map.get(store_id)

  def get_user_producer_access_right(self, user_uuid: str, producer_id: str) -> str | None: # Renamed method
    access_rights_map = self.get_user_root_account_field_as_map(user_uuid, UserRootAccount.PRODUCERS_FIELD)
    return access_rights_map.get(producer_id)

  def get_user_stores_with_access_rights(self, user_uuid: str) -> dict: # Renamed method
    return self.get_user_root_account_field_as_map(user_uuid, UserRootAccount.STORES_FIELD)

  def get_user_producers_with_access_rights(self, user_uuid: str) -> dict: # Renamed method
    return self.get_user_root_account_field_as_map(user_uuid, UserRootAccount.PRODUCERS_FIELD)
  
  def get_user_root_account_field_as_map(self, user_uuid: str, field_name: str) -> dict:
    user_root_account_snapshot = self.user_root_account_query_builder\
      .for_id(user_uuid)\
      .build()\
      .get()
      
    if not user_root_account_snapshot.exists:
      logger.error(f"User root account not found: {user_uuid}")
      # Depending on desired behavior, could return empty dict or raise
      return {} 
      # raise Exception("User root account not found")

    return user_root_account_snapshot.get(field_name) or {}


  def _update_user_custom_claims(self, user_uuid: str) -> None:
    try:
      # Verify user exists in Firebase Auth
      auth_user = auth.get_user(user_uuid) # Renamed to auth_user to avoid conflict if you have a User model
      existing_claims = dict(auth_user.custom_claims or {})

      # Fetch the latest store and producer role maps from Firestore
      ura_snapshot = self.user_root_account_collection.document(user_uuid).get()
      if not ura_snapshot.exists:
        logger.warning(
            f"UserRootAccount {user_uuid} not found when trying to update claims. Claims not updated."
        )
        # Potentially clear claims if the root account is gone?
        # auth.set_custom_user_claims(user_uuid, None)
        return

      current_store_access_rights = ura_snapshot.get(UserRootAccount.STORES_FIELD) or {} # Changed from current_store_roles
      current_producer_access_rights = ura_snapshot.get(UserRootAccount.PRODUCERS_FIELD) or {} # Changed from current_producer_roles

      # Prepare the new set of claims
      # We are replacing store_roles and producer_roles entirely.
      # If you have other custom claims, ensure they are preserved.
      updated_claims = existing_claims 
      updated_claims["store_access_rights"] = current_store_access_rights # Changed from store_roles
      updated_claims["producer_access_rights"] = current_producer_access_rights # Changed from producer_roles
      
      # Firebase custom claims payload must not exceed 1000 bytes
      claims_json = json.dumps(updated_claims)
      if len(claims_json.encode('utf-8')) > 1000:
          logger.error(f"Custom claims for user {user_uuid} would exceed 1000 byte limit. Claims not updated. Store access_rights: {len(current_store_access_rights)}, Producer access_rights: {len(current_producer_access_rights)}")
          # This is a critical error. You might want to raise an exception
          # or implement a strategy to trim less critical claims if possible.
          # For now, we log and do not update the claims.
          # raise Exception("Custom claims payload too large to set.") 
          return

      auth.set_custom_user_claims(user_uuid, updated_claims)
      logger.info(
          f"Custom claims updated for user {user_uuid}: {len(current_store_access_rights)} store access_rights, {len(current_producer_access_rights)} producer access_rights."
      )

    except auth.UserNotFoundError:
      logger.error(f"Firebase Auth user {user_uuid} not found. Cannot set custom claims.")
    except Exception as e:
      logger.error(f"Error updating custom claims for user {user_uuid}: {e}")
      # Optionally re-raise, depending on desired error handling
      # raise

  def is_user_in_store(self, user_uuid: str, store_id: str) -> bool:
    return store_id in self.get_user_stores_with_access_rights(user_uuid).keys()

  def is_user_in_producer(self, user_uuid: str, producer_id: str) -> bool:
    return producer_id in self.get_user_producers_with_access_rights(user_uuid).keys()
