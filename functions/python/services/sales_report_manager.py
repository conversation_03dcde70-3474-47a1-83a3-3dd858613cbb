from google.cloud import storage
from firebase_admin import firestore
from typing import Dict, List, Optional, Tuple, Any

from gaco_framework.auth import AuthContext
from gaco_framework.exceptions import NotFoundError, ConflictError
from gaco_framework.managers import BaseGacoManager
from models.requests.sales_report_request import CreateSalesReportRequest
from data_generator.sales_report_data_generator import generate_sales_report_data_handler
from reporter.sales_reporter import SalesReporter
from models.sales import sales_gold_collection, SalesGold
from models.sales_report import sales_report_collection, SalesReportModel
from constants.gaco_values import bucket_name
from firebase_functions import logger
from models.sales_report import SalesReportStatus


class SalesReportManager(BaseGacoManager):
    """
    Manages sales report operations like deletion and creation.
    Separates business logic from cloud function handlers for better testability.
    """
    
    def __init__(self, 
                 db: firestore.Client,
                 auth_context: Optional[AuthContext] = None,
                 bucket_name: str = bucket_name,
                 template_file_name: str = 'sales-report-template.html'
                ):
        """
        Initialize the InvoiceManager with a Firestore client and bucket name.
        
        Args:
            db: Firestore client.
            auth_context: The authentication context.
            bucket_name: Name of the Cloud Storage bucket containing sales reports.
            template_file_name: Name of the sales report template file.
        """
        super().__init__(db, auth_context)
        self.bucket_name = bucket_name
        self.template_file_name = template_file_name


    def create_sales_reports(self, sales_report_request: CreateSalesReportRequest) -> List[Dict[str, Any]]:
        """
        Create sales reports for a given store and date range.
        
        Args:
            sales_report_request: The sales report request containing store_id, start_date, and end_date.
            
        Returns:
            A list of created sales report data dictionaries.
            
        Raises:
            NotFoundError: If no sales data is found for the specified period.
            Exception: For any other errors during report creation.
        """
        try:
            df = generate_sales_report_data_handler(self.db, sales_report_request)
            
            if df.height == 0:
                raise NotFoundError("No sales data found for the specified period")
            
            reporter = SalesReporter(
                sales_report_data_frame=df,
                template_file_name=self.template_file_name,
                db=self.db,
                bucket_name=self.bucket_name
            )
            
            report_dicts = reporter.generate_report(
                start_date=sales_report_request.start_date, 
                end_date=sales_report_request.end_date
            )
            
            # Format the output data for easier consumption
            formatted_sales_reports = [
                {
                    'sales_report_id': f"{sales_report['store_id']}-{sales_report['sales_report_id']}",
                    'download_url': sales_report['download_url'], 
                    'object_path': sales_report['uri'],
                    'status': sales_report['status'],
                    'store_id': sales_report['store_id'],
                    'affected_sales': sales_report['sale_ids'],
                    'start_date': sales_report_request.start_date,
                    'end_date': sales_report_request.end_date
                } for sales_report in report_dicts
            ]
            
            logger.info(f"Created {len(formatted_sales_reports)} sales reports for store {sales_report_request.store_id}")
            return formatted_sales_reports
            
        except Exception as e:
            error_message = f"Error creating sales reports: {str(e)}"
            logger.error(error_message)
            # Re-raise the original exception to preserve its type and error code
            raise
    
    def delete_sales_report(self, sales_report_doc_id: str) -> Tuple[bool, Dict[str, Any], str]:
        """
        Delete a sales report and revert all related changes.
        
        Args:
            sales_report_doc_id: The document ID of the sales report to delete
                           (in format {store_id}-{sales_report_id})
        
        Returns:
            Tuple containing:
            - Success flag (bool)
            - Result data (dict)
            - Error message if any (str)
        """
        try:
            # 1. Get the invoice document from Firestore
            sales_report_data, error = self._get_sales_report_document(sales_report_doc_id)
            if error:
                return False, {}, error

            # only pending sales reports can be deleted
            if sales_report_data.status != SalesReportStatus.PENDING.value:
                msg = f"Sales report {sales_report_doc_id} has status {sales_report_data.status}"
                logger.warn(msg)
                raise ConflictError(msg)
            
            store_id = sales_report_data.store_id
            sales_report_id = sales_report_data.sales_report_id
            sale_ids = sales_report_data.sale_ids
            
            # 2. Remove invoice reference from all related sales-gold documents
            self._remove_sales_report_references(sales_report_doc_id, sale_ids)
            
            # 3. Delete the invoice document from Firestore
            self._delete_sales_report_document(sales_report_doc_id)
            
            # 4. Delete the HTML file from Cloud Storage
            if store_id and sales_report_id:
                self._delete_sales_report_file(store_id, sales_report_id)
            
            # Prepare result data
            result_data = {
                'sales_report_doc_id': sales_report_doc_id,
                'store_id': store_id,
                'sales_report_id': sales_report_id,
                'affected_sales': sale_ids
            }
            
            return True, result_data, "Successfully deleted sales report"
            
        except Exception as e:
            error_message = f"Error deleting sales report: {str(e)}"
            logger.error(error_message)
            return False, {}, error_message
    
    def _get_sales_report_document(self, sales_report_doc_id: str) -> Tuple[SalesReportModel, str]:
        """
        Get the sales report document from Firestore.
        
        Args:
            sales_report_doc_id: The document ID of the sales report
            
        Returns:
            Tuple containing:
            - Sales report data (dict)
            - Error message if any (str)
        """
        sales_report_ref = self.db.collection(sales_report_collection).document(sales_report_doc_id)
        sales_report_doc = sales_report_ref.get()
        
        if not sales_report_doc.exists:
            return {}, f'Sales report document {sales_report_doc_id} not found'
        
        return SalesReportModel.model_validate(sales_report_doc.to_dict()), ""
    
    def _remove_sales_report_references(self, sales_report_doc_id: str, sale_ids: List[str]) -> None:
        """
        Remove sales report references from all related sales-gold documents.
        
        Args:
            sales_report_doc_id: The document ID of the sales report
            sale_ids: List of sale IDs associated with the sales report
        """
        for sale_id in sale_ids:
            sale_doc_ref = self.db.collection(sales_gold_collection).document(sale_id)
            sale_doc = sale_doc_ref.get()
            
            if sale_doc.exists:
                sale_doc_data = sale_doc.to_dict()
                if sale_doc_data.get(SalesGold.SALES_REPORT_ID_FIELD) == sales_report_doc_id:
                    # Remove the sales_report_id field
                    sale_doc_ref.update({SalesGold.SALES_REPORT_ID_FIELD: firestore.DELETE_FIELD})
                    logger.info(f"Removed sales report reference from sale {sale_id}")
            else:
                logger.warn(f"Sale document {sale_id} not found in sales-gold collection")
    
    def _delete_sales_report_document(self, sales_report_doc_id: str) -> None:
        """
        Delete the sales report document from Firestore.
        
        Args:
            invoice_doc_id: The document ID of the invoice
        """
        sales_report_ref = self.db.collection(sales_report_collection).document(sales_report_doc_id)
        sales_report_ref.delete()
        logger.info(f"Deleted sales report document {sales_report_doc_id}")
    
    def _delete_sales_report_file(self, store_id: str, sales_report_id: str) -> None:
        """
        Delete the sales report HTML file from Cloud Storage.
        
        Args:
            store_id: The store ID
            invoice_id: The invoice ID
        """
        storage_client = storage.Client()
        bucket = storage_client.bucket(self.bucket_name)
        
        blob_path = f'stores/{store_id}/sales_reports/{sales_report_id}.html'
        blob = bucket.blob(blob_path)
        
        if blob.exists():
            blob.delete()
            logger.info(f"Deleted sales report file: {blob_path}")
        else:
            logger.warn(f"Sales report file not found: {blob_path}") 


    def find_sales_report_by_producer_id(self, producer_id: str) -> Tuple[bool, Dict[str, Any], str]:
        """
        Find a sales report by producer_id.
        """
        sales_reports = self.db.collection(sales_report_collection)\
            .where(SalesReportModel.PRODUCER_ID_FIELD, '==', producer_id)\
            .get()
        if not sales_reports:
            return False, {}, "No sales reports found for producer_id"
        return True, sales_reports, ""

    def update_sales_report_status(self, sales_report_doc_id: str, status: str) -> None:
        """
        Update the status of a sales report.
        """
        sales_report_ref = self.db.collection(sales_report_collection)\
            .document(sales_report_doc_id)

        if not sales_report_ref.get().exists:
            raise NotFoundError(f"Sales report document {sales_report_doc_id} not found")

        sales_report_ref.update({SalesReportModel.STATUS_FIELD: status})
        logger.info(f"Updated sales report status to {status} for {sales_report_doc_id}")
