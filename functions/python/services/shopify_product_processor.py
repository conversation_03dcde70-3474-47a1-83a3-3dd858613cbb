from firebase_functions import logger
from firebase_admin import firestore
from typing import Union, List
import json

from parsers.shopify_raw_products import ShopifyProductParser
from models.products import Product, products_collection
from extractor.store_id import extract_store_id # Assuming this utility exists and is correctly placed
from hasher.shopify_gid import shopify_gid_to_hash
from queries.stores_query_builder_v2 import StoresQueryBuilderV2
from queries.producers_query_builder_v2 import ProducersQueryBuilderV2
from models.store import StoreV2


entity_type_stores = "stores"
entity_type_producers = "producers"


class ShopifyProductProcessor:
    """Class to process Shopify product files and store them in Firestore."""

    def __init__(self, db: firestore.client = None):
        """Initialize with optional firestore client for testing."""
        self.db = db
        self.shopify_product_parser = ShopifyProductParser()
        self.store_query = StoresQueryBuilderV2(db)
        self.producer_query = ProducersQueryBuilderV2(db)

    def _fetch_display_name_parse_and_store_products(
        self,
        file_content: Union[str, bytes],
        entity_id: str,
        file_path: str,
        entity_type: str
    ) -> None:
        """
        Fetches display name, parses product data from file_content, and stores it in Firestore.
        This method is designed to be reusable for both 'stores' and 'producers'.
        """
        display_name = ""
        try:
            if entity_type == entity_type_stores:
                store_doc_snapshot = self.store_query.for_id(entity_id).build().get()

                if not store_doc_snapshot.exists:
                    logger.error(f"Store document does not exist for store_id: {entity_id}")
                    raise ValueError(f"Store document not found for store_id: {entity_id}")

                store_model = StoreV2.model_validate(store_doc_snapshot.to_dict())
                display_name = store_model.display_name
                logger.info(f"Retrieved store_display_name: {display_name} for store_id: {entity_id}")
            elif entity_type == entity_type_producers:
                producer_doc_snapshot = self.producer_query.for_id(entity_id).build().get()

                if not producer_doc_snapshot.exists:
                    logger.error(f"Producer document does not exist for producer_id: {entity_id}")
                    raise ValueError(f"Producer document not found for producer_id: {entity_id}")

                producer_data = producer_doc_snapshot.to_dict()
                display_name = producer_data.get("display_name", entity_id) # Fallback to entity_id if no display_name
                logger.info(f"Retrieved producer_display_name: '{display_name}' for producer_id: {entity_id}")
            else:
                logger.error(f"Unknown entity_type: {entity_type} for entity_id: {entity_id}")
                raise ValueError(f"Unknown entity_type: {entity_type}")
        except Exception as e:
            logger.error(f"Error retrieving display name for {entity_type} {entity_id}: {e}")
            raise

        try:
            if isinstance(file_content, bytes):
                file_content_str = file_content.decode('utf-8')
            else:
                file_content_str = file_content
            
            parse_args = {
                "json_string_data": file_content_str,
                "db": self.db
                # db and hash_salt can be added here if needed, using defaults for now
            }
            if entity_type == entity_type_stores:
                parse_args["store_id"] = entity_id
                parse_args["store_display_name"] = display_name
            elif entity_type == entity_type_producers:
                parse_args["producer_id"] = entity_id
                parse_args["producer_display_name"] = display_name
            
            parsed_products: List[Product] = self.shopify_product_parser.parse_shopify_products(**parse_args)
            logger.info(f"Successfully parsed {len(parsed_products)} products from file: {file_path} for {entity_type} {entity_id}")

        except Exception as e:
            logger.error(f"Error parsing Shopify products from file {file_path} for {entity_type} {entity_id}: {e}")
            raise


    def process_product_file(self, file_content: Union[str, bytes], file_path: str) -> None:
        """Process a Shopify product file and store the data in Firestore.
        Handles both store and producer product files based on file_path.
        """
        logger.info(f"Processing Shopify products file: {file_path}")

        if self.db is None:
            logger.warning("Firestore client (db) is not initialized. Cannot store products.")
            raise NotImplementedError("Firestore client is not available, local dump not implemented.")

        try:
            entity_id = extract_store_id(file_path) # This function extracts the ID part of the path
            if not entity_id:
                raise ValueError("entity_id (store_id/producer_id) could not be extracted or is empty.")
        except Exception as e:
            logger.error(f"Error extracting entity_id from path {file_path}: {e}")
            raise
        
        logger.info(f"Extracted entity_id: {entity_id} from file path: {file_path}")

        # Determine if it's a store or producer based on the file path structure
        # e.g., "stores/store_id/..." or "producers/producer_id/..."
        path_parts = file_path.split("/")
        if not path_parts:
            logger.error(f"File path is empty or invalid: {file_path}")
            raise ValueError("Invalid file path structure for determining entity type.")
        
        entity_type = path_parts[0] # "stores" or "producers"

        if entity_type not in [entity_type_stores, entity_type_producers]:
            logger.error(f"Unknown entity type '{entity_type}' derived from path: {file_path}")
            raise ValueError(f"Invalid entity type '{entity_type}'. Must be 'stores' or 'producers'.")

        # Call the consolidated method
        self._fetch_display_name_parse_and_store_products(
            file_content,
            entity_id,
            file_path,
            entity_type
        )
