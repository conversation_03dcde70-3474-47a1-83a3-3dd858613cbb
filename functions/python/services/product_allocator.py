from firebase_admin import firestore
from typing import Optional, List, Dict, Any
from datetime import datetime, timezone

from gaco_framework import BaseGacoManager
from gaco_framework.exceptions import ValidationError, NotFoundError, PermissionError

from models.allocations import ProductAllocation, ProductAllocationItem, product_allocations_collection, AllocationStatus
from models.products import Product # For type hinting if we fetch product
from services.product_manager import ProductManager 
from gaco_framework.auth import AuthContext
# from services.producer_manager import ProducerManager # If needed for producer validation
# from services.store_manager import StoreManager     # If needed for store validation

class ProductAllocationManager(BaseGacoManager):
    def __init__(self, db: firestore.Client, auth_context: Optional[AuthContext] = None):
        super().__init__(db, auth_context)
        self.collection_name = product_allocations_collection
        self.allocations_ref = self._get_collection(self.collection_name)

    def _setup_dependencies(self) -> None:
        self.product_manager = ProductManager(db=self.db, auth_context=self.auth_context)

    def _validate_product_ownership_and_variants(
        self, 
        producer_id: str, 
        items: List[ProductAllocationItem]
    ) -> None:
        """
        Validates producer ownership and item existence/stock.
        - If product_variant_id is given, checks variant and its parent product.
        - If product_variant_id is None (product-level):
            - Product must exist.
            - Product must NOT have variants (for clarity).
            - For such "no-variant" products, allocated quantity must be 1 (for "1 of a kind" items).
        """
        if not items:
            raise ValidationError("Allocation must contain at least one item.")

        for item in items:
            product_to_check_ownership: Optional[Product] = None
            inventory_available: Optional[int] = None
            error_subject_id = item.product_id # Default for error messages if variant_id is None

            if item.product_variant_id:
                error_subject_id = item.product_variant_id
                variant_model = self.product_manager.get_variant(item.product_variant_id)
                if not variant_model:
                    raise NotFoundError(f"Product variant with logical ID '{item.product_variant_id}' not found.")
                
                parent_product_model = self.product_manager.get_product(variant_model.product_id)
                if not parent_product_model:
                    # This state (variant exists but parent product doesn't) should ideally be prevented by data integrity rules elsewhere.
                    raise NotFoundError(f"Parent product (ID: {variant_model.product_id}) for variant '{item.product_variant_id}' not found.")
                product_to_check_ownership = parent_product_model
                inventory_available = variant_model.inventory_quantity
            
            else: # Product-level allocation (product_variant_id is None)
                direct_product_model = self.product_manager.get_product(item.product_id)
                if not direct_product_model:
                    raise NotFoundError(f"Product with logical ID '{item.product_id}' not found for product-level allocation.")
                
                if direct_product_model.variants:
                    raise ValidationError(f"Product '{item.product_id}' has variants. A product_variant_id must be specified for allocation of products with variants.")
                
                # For product-level allocation of a product with NO variants (e.g., "1 of a kind")
                product_to_check_ownership = direct_product_model
                if item.quantity > 1:
                    raise ValidationError(f"For product-level allocation of product '{item.product_id}' (which has no variants), quantity must be 1.")
                inventory_available = 1 # Conceptual inventory for a "1 of a kind" item with no variants.
                                        # If Product model had its own inventory_quantity, we'd use that.

            # Perform ownership check
            if product_to_check_ownership.producer_id != producer_id:
                raise PermissionError(
                    f"Producer '{producer_id}' does not own product '{product_to_check_ownership.product_id}' "
                    f"(owned by '{product_to_check_ownership.producer_id}') associated with item '{error_subject_id}'. Allocation denied."
                )

            # Perform stock check
            if inventory_available is not None and inventory_available < item.quantity:
                raise ValidationError(f"Insufficient stock for item '{error_subject_id}'. "
                                 f"Available: {inventory_available}, Requested: {item.quantity}")

    def create_allocation(self, allocation_data: ProductAllocation) -> ProductAllocation:
        """
        Creates a new product allocation record.
        The producer_id in allocation_data must match the producer_id on the Product(s) being allocated.
        The caller must have permissions to act on behalf of the producer_id.
        """
        # Pydantic model validation is assumed to have run for required fields like producer_id and store_id.
        if not allocation_data.items:
            raise ValidationError("At least one item must be specified for allocation.")

        # Validate product ownership and existence of variants
        self._validate_product_ownership_and_variants(allocation_data.producer_id, allocation_data.items)
        
        # TODO: Validate store_id using StoreManager if available
        # TODO: Validate producer_id using ProducerManager if available

        # Set allocation_date if not already set (Pydantic default_factory handles this)
        if allocation_data.allocation_date is None: # Should be handled by pydantic
             allocation_data.allocation_date = datetime.now(timezone.utc)

        # Create document with auto-generated ID
        doc_ref = self.allocations_ref.document()

        # Persist. The allocation_id is not part of the model YET.
        doc_ref.set(allocation_data.model_dump(exclude_none=True))
        
        # Update the model with the generated ID and return
        allocation_data.allocation_id = doc_ref.id
        return allocation_data

    def get_allocation(self, allocation_id: str) -> ProductAllocation:
        """Retrieves a specific product allocation by its Firestore document ID."""
        doc_ref = self.allocations_ref.document(allocation_id)
        doc = doc_ref.get()
        if not doc.exists:
            raise NotFoundError(f"Allocation with ID '{allocation_id}' not found.")
        
        data = doc.to_dict()
        data['allocation_id'] = doc.id # Add the document ID to the data
        return ProductAllocation.model_validate(data)

    def update_allocation_status(
        self, 
        allocation_id: str, 
        new_status: str, 
        updated_by_user_id: Optional[str] = None, # Example of tracking user
        details: Optional[Dict[str, Any]] = None # e.g., tracking_number, notes
    ) -> ProductAllocation:
        """Updates the status and optionally other details of an allocation."""
        # First, ensure the allocation exists by fetching it.
        # get_allocation will raise NotFoundError if it doesn't exist.
        self.get_allocation(allocation_id)
        doc_ref = self.allocations_ref.document(allocation_id)
        
        allocation_status_values = [status.value for status in AllocationStatus]
        if new_status not in allocation_status_values:
            raise ValidationError(f"Invalid status value: '{new_status}'. Must be one of: {allocation_status_values}")
        
        update_data = {ProductAllocation.STATUS_FIELD: new_status}
        if new_status == AllocationStatus.IN_TRANSIT.value:
            update_data[ProductAllocation.SHIPPED_ON_FIELD] = datetime.now(timezone.utc).isoformat()
        elif new_status == AllocationStatus.DELIVERED.value:
            update_data[ProductAllocation.DELIVERED_ON_FIELD] = datetime.now(timezone.utc).isoformat()
        
        if details:
            update_data.update(details)
        
        # With BaseGacoManager, you can access auth context to track who made the change.
        # if self.auth_context:
        #     update_data["last_updated_by"] = self.auth_context.user_id
        # update_data["last_updated_at"] = firestore.SERVER_TIMESTAMP

        doc_ref.update(update_data)
        return self.get_allocation(allocation_id)

    def list_allocations_by_producer(
        self, 
        producer_id: str, 
        status: Optional[AllocationStatus] = None
    ) -> List[ProductAllocation]:
        """Lists allocations for a given producer, optionally filtered by status."""
        query = self.allocations_ref.where(ProductAllocation.PRODUCER_ID_FIELD, "==", producer_id)

        if status:
            query = query.where(ProductAllocation.STATUS_FIELD, "==", status.value)
        
        allocations = []
        for doc in query.order_by(
            ProductAllocation.ALLOCATION_DATE_FIELD, 
            direction=firestore.Query.DESCENDING
        ).stream():
            data = doc.to_dict()
            data[ProductAllocation.ALLOCATION_ID_FIELD] = doc.id
            allocations.append(ProductAllocation.model_validate(data))
        return allocations

    # Similar methods for list_allocations_by_store, etc. can be added.
