from typing import Optional

from models.producer import Producer
from models.requests.producer_requests import CreateProducerRequest, DeleteProducerRequest
from firebase_admin import firestore
from datetime import datetime, timezone
from queries.producers_query_builder_v2 import ProducersQueryBuilderV2
from firebase_functions import logger
from services.sales_report_manager import SalesReportManager
from services.user_root_account_manager import UserRootAccountManager
from gaco_framework.managers import BaseGacoManager
from gaco_framework.auth import AuthContext
from gaco_framework.exceptions import AuthorizationError, NotFoundError, GacoError, ConflictError, ValidationError
from gaco_framework.security import ResourceAccess
from constants.collections import producer_collection
from services.email_manager import EmailManager
from models.requests.email_requests import SendInviteEmailRequest


class ProducerManager(BaseGacoManager):

    def __init__(self, db: firestore.Client, auth_context: Optional[AuthContext] = None):
        super().__init__(db, auth_context)
        self.producer_collection = self._get_collection(producer_collection)
        self.query_builder = ProducersQueryBuilderV2(db=self.db)

    def _setup_dependencies(self) -> None:
        self.sales_report_manager = SalesReportManager(db=self.db)
        self.user_root_account_manager = UserRootAccountManager(db=self.db)
        self.email_manager = EmailManager(db=self.db)

    def _check_producer_exists(self, request: CreateProducerRequest) -> None:
        existing_producers = self.query_builder\
            .for_email(request.email)\
            .build()\
            .get()

        if existing_producers:
            msg = f"Producer with this email already exists {request.email}"
            logger.warn(msg)
            raise ConflictError(msg)

    def create_producer_no_parentId(
        self,
        request: CreateProducerRequest,
        store_id: str
    ) -> str:
        """
        This flow can only be used by the store.
        With this flow, we create producer anyways with the store_id as the parent_id
        and parent id set !
        We can expect a scenario where multiple differnt store create the same producer.
        But lets handle this in different flow -> account claim flow.
        There is no check for email or display name uniqueness.
        """
        if not self.auth_context or not self.auth_context.user_id:
            raise AuthorizationError("Authentication required to create a producer.")

        user_id = self.auth_context.user_id

        producer = Producer(
            created_at=datetime.now(timezone.utc),
            display_name=request.display_name,
            email=request.email,
            tax_a2=request.tax_a2,
            parent_id="",
            created_by=user_id,
            created_by_store_id=store_id
        )

        producer_data = producer.model_dump()

        # Actually we don't need the check here since below
        # is not needed
        # self._check_access("producer", producer_data, ResourceAccess.WRITE)

        doc_ref = self.producer_collection.document()
        doc_ref.set(producer_data)

        # send invite email to the producer when producer is created by the store.
        self.email_manager.enqueue_send_invite_email(
            request=SendInviteEmailRequest(
                store_id=store_id,
                producer_id=doc_ref.id
            )
        )

        try:
            self.user_root_account_manager\
                .add_producer_with_access_right(
                    user_uuid=user_id,
                    producer_id=doc_ref.id,
                    access_right=request.access_right
                )
        except Exception as e:
            logger.error(f"Error adding producer to user root account, rolling back producer creation: {e}")
            self.delete_producer(DeleteProducerRequest(producer_id=doc_ref.id))
            raise GacoError("Failed to add producer access rights.") from e

        return doc_ref.id

    def create_producer(self, request: CreateProducerRequest) -> str:
        if not self.auth_context or not self.auth_context.user_id:
            raise AuthorizationError("Authentication required.")
        user_id = self.auth_context.user_id

        self._check_producer_exists(request)

        producer = Producer(
            created_at=datetime.now(timezone.utc),
            display_name=request.display_name,
            email=request.email,
            tax_a2=request.tax_a2,
            parent_id=user_id,
            created_by=user_id
        )

        producer_data = producer.model_dump()
        # self._check_access("producer", producer_data, ResourceAccess.WRITE)

        doc_ref = self.producer_collection.document()
        doc_ref.set(producer_data)
        doc_id = doc_ref.id

        logger.info(f"Producer created with id: {doc_id}")

        try:
            self.user_root_account_manager\
                .add_producer_with_access_right(
                    user_uuid=user_id,
                    producer_id=doc_id,
                    access_right=request.access_right
                )
        except Exception as e:
            logger.error(f"Error adding producer to user root account, rolling back: {e}")
            self.delete_producer(DeleteProducerRequest(producer_id=doc_id))
            raise GacoError("Error adding producer to user root account") from e

        return doc_id

    def delete_producer(self, request: DeleteProducerRequest) -> None:
        producer_docs = self.query_builder.for_id(request.producer_id).build().get()

        if not producer_docs.exists:
            raise NotFoundError(f"Cannot delete producer, producer not found: {request.producer_id}")

        producer_data = producer_docs.to_dict()
        producer_data['id'] = producer_docs.id

        # self._check_access("producer", producer_data, ResourceAccess.DELETE)

        # check if producer_id is linked to any invoice
        # TODO: this is not needed anymore since we have sales_report instead of invoice
        has_sales_reports, _, _ = self.sales_report_manager\
            .find_sales_report_by_producer_id(request.producer_id)

        if not request.hard_delete and has_sales_reports:
            raise ValidationError("Cannot delete producer, it is linked to a sales report.")

        # remove producer_id from user_root_account
        # Assuming created_by holds the owner's user_id
        owner_id = producer_data.get(Producer.CREATED_BY_FIELD)
        if owner_id:
            self.user_root_account_manager.remove_producer_association(
                user_uuid=owner_id,
                producer_id=request.producer_id
            )

        self.producer_collection.document(request.producer_id).delete()
        logger.info(f"Producer deleted with id: {request.producer_id}")
        return request.producer_id
