from datetime import datetime, timezone
from typing import List
from firebase_functions import logger
from firebase_admin import firestore
from models.secret import Secret
from gaco_secrets.secret_manager import SecretManager

class SecretService:
    def __init__(self, db: firestore.Client, secret_manager: SecretManager):
        self.db = db
        self.secret_manager = secret_manager
        self.collection = self.db.collection('customer_api_keys')

    # TODO: create model for a document stored in the secret collection 
    def create_secret(
        self,
        store_id: str,
        secret_name: str,
        secret_value: str,
        shop_name: str,
        description: str = None
    ) -> Secret:
        """
        Creates a new secret for a customer
        """
        # Generate a unique secret ID in Secret Manager
        secret_id = (
            f"{store_id}-"
            f"{secret_name}-"
            f"{int(datetime.now(timezone.utc).timestamp())}"
        )
        secret_id = secret_id.replace("-", "_").replace(".", "_")
        
        # Store the actual secret in Secret Manager
        secret_version_path = self.secret_manager.create_secret(secret_id, secret_value)
        
        # Create secret reference in Firestore
        secret = Secret(
            store_id=store_id,
            secret_name=secret_name,
            secret_version_path=secret_version_path,
            shop_name=shop_name,
            created_at=datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc),
            description=description
        )
        
        # Store reference in Firestore
        self.collection.document(store_id).set(secret.__dict__)
        
        return secret

    def get_secret_value(self, secret_version_path: str) -> str:
        """
        Retrieves the actual secret value
        """
        return self.secret_manager.get_secret(secret_version_path)

    def list_customer_secrets(self, store_id: str) -> List[Secret]:
        """
        Lists all secrets for a customer
        """
        docs = self.collection.where('store_id', '==', store_id).stream()
        return [Secret(**doc.to_dict()) for doc in docs]

    def delete_secret(self, store_id: str):
        """
        Deletes a secret
        """
        try:
        # Find the secret reference
            doc_ref = self.collection.document(store_id)

            secret_data = doc_ref.get().to_dict()
            secret_id = (
                secret_data['secret_version_path']
                .split('/secrets/')[1]
                .split('/versions/')[0]
            )
            self.secret_manager.delete_secret(secret_id)
            doc_ref.delete()
        except Exception as e:
            logger.error(f'Error deleting secret {store_id}: {e}')
            print(f'Error deleting secret {store_id}: {e}')
            