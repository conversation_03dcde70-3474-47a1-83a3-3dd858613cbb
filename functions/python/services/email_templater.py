from firebase_admin import firestore
from models.requests.email_requests import (
    email_templates_collection, 
    EmailTemplate, 
    Email,
    SendSalesReportEmailRequest,
    SendInviteEmailRequest
)
from firebase_functions import logger
from typing import Dict, Any
from models.sales_report import SalesReportModel
from models.producer import Producer
from models.store import StoreV2
from queries.producers_query_builder_v2 import producers_collection
from models.sales_report import sales_report_collection
from services.store_manager import store_collection
from models.requests.email_requests import (
    default_invite_email_template_id,
    default_sales_report_email_template_id
)


class EmailTemplater:
    def __init__(self, db: firestore.Client):
        self.db = db
        self.template_collection = self.db.collection(email_templates_collection)
        self.store_collection = self.db.collection(store_collection)
        self.producer_collection = self.db.collection(producers_collection)
        self.sales_report_collection = self.db.collection(sales_report_collection)


    def create_default_invite_email_template(self) -> None:
        subject = f"Hi ya ! {{producerDisplayName}}, {{storeDisplayName}} has invited you to join gaco"
        text = f"""Hope we are knocking you at the right time :) {{storeDisplayName}} has sent you an invite to join gaco ! 
                Here, you can check your sales report, stock and contract that is linked to the {{storeDisplayName}} ! ✌️ 

                Follow the {{signupLink}} 🐇 to continue ;)

                Already have an account .. ? You can link your accounts here {{claimAccountLink}}"""
        
        self.template_collection.document(default_invite_email_template_id).set({
            'subject': subject,
            'text': text
        })


    def create_default_sales_report_email_template(self) -> None:
        subject = f"Sales report for {{storeDisplayName}} from {{startDate}} to {{endDate}}"
        text = f"""Here is the sales report for {{storeDisplayName}} from {{startDate}} to {{endDate}} ! 
                You can find the report in the following link : {{gcsPath}}"""
        
        self.template_collection.document(default_sales_report_email_template_id).set({
            'subject': subject,
            'text': text
        })


    def get_email_template(self, template_id: str) -> Dict[str, Any]:
        template_doc = self.template_collection.document(template_id).get()

        if not template_doc.exists:
          msg = f'template {template_id} does not exist'
          logger.error(msg)
          raise ValueError(msg)

        return template_doc.to_dict()
        

    def get_formated_email_template(self, template_id: str) -> Dict[str, str]:

        def _remove_quotes(text: str) -> str:
            if (text.startswith('"') and text.endswith('"')) or \
               (text.startswith("'") and text.endswith("'")):
                return text[2:-1]
            return text

        template_data = self.get_email_template(template_id)

        raw_template_text = template_data.get('text')
        raw_template_subject = template_data.get('subject')

        if not raw_template_text:
            return "Error: Email template text is empty."


        format_ready_subject_template = _remove_quotes(raw_template_subject)
        format_ready_text_template = _remove_quotes(raw_template_text)

        return {
            'subject': format_ready_subject_template,
            'text': format_ready_text_template
        } 


    def populate_sales_report_template(
            self, 
            request: SendSalesReportEmailRequest
        ) -> Email:
        """
        Populate the sales report template controller.
        """

        template_data = self.get_formated_email_template(
            request.template_id
        )

        store_doc = self.store_collection.document(request.store_id).get()
        if not store_doc.exists:
            raise ValueError(f"Store {request.store_id} not found")

        store = StoreV2.model_validate(store_doc.to_dict())


        sales_report_doc = self.sales_report_collection\
          .document(request.sales_report_id).get()
        if not sales_report_doc.exists:
            raise ValueError(f"Sales report {request.sales_report_id} not found")
        
        sales_report = SalesReportModel.model_validate(sales_report_doc.to_dict())


        producer_id = sales_report.producer_id
        producer_doc = self.producer_collection.document(producer_id).get()
        if not producer_doc.exists:
            raise ValueError(f"Producer {request.producer_id} not found")

        producer = Producer.model_validate(producer_doc.to_dict())

        try:
            template_data['subject'] = template_data['subject'].format(
                storeDisplayName=store.display_name,
            )
            template_data['text'] = template_data['text'].format(
                storeDisplayName=store.display_name,
                producerDisplayName=producer.display_name,
                startDate=sales_report.start_date.date().isoformat(),
                endDate=sales_report.end_date.date().isoformat(),
                gcsPath=sales_report.download_url
            )
            return Email(
                from_alias=store.display_name,
                from_email=store.email,
                to_alias=producer.display_name,
                to_email=producer.email,
                subject=template_data['subject'],
                text=template_data['text']
            ) 
        except ValueError as e:
            raise ValueError(f"Missing data for template placeholder: {e}")
        except Exception as e:
            raise ValueError(f"Error formatting email template: {e}")


    def populate_invite_email_template(
            self, 
            request: SendInviteEmailRequest
        ) -> Email:
        """
        Populate the invite email template controller.
        """

        template_data = self.get_formated_email_template(
            request.template_id
        )

        store_doc = self.store_collection.document(request.store_id).get()
        if not store_doc.exists:
            raise ValueError(f"Store {request.store_id} not found")

        store = StoreV2.model_validate(store_doc.to_dict())

        producer_doc = self.producer_collection.document(request.producer_id).get()
        if not producer_doc.exists:
            raise ValueError(f"Producer {request.producer_id} not found")

        producer = Producer.model_validate(producer_doc.to_dict())

        try:
            template_data['subject'] = template_data['subject'].format(
                storeDisplayName=store.display_name,
                producerDisplayName=producer.display_name
            )
            template_data['text'] = template_data['text'].format(
                storeDisplayName=store.display_name,
                producerDisplayName=producer.display_name,
                signupLink="signup link",
                claimAccountLink="claim account link"
            )
            return Email(
                from_alias=store.display_name,
                from_email=store.email,
                to_alias=producer.display_name,
                to_email=producer.email,
                subject=template_data['subject'],
                text=template_data['text']
            )
        except ValueError as e:
            raise ValueError(f"Missing data for template placeholder: {e}")
        except Exception as e:
            raise ValueError(f"Error formatting email template: {e}")
        
