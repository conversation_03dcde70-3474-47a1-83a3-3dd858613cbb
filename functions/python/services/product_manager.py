import warnings
from firebase_admin import firestore
from firebase_functions import logger
from typing import Optional, List, Dict, Any
from datetime import datetime, timezone
import uuid

from gaco_framework.auth import AuthContext
from gaco_framework.managers import BaseGacoManager
from gaco_framework.exceptions import ValidationError, NotFoundError, PermissionError, ConflictError
from models.products import Product, ProductVariant, products_collection, product_variants_collection
from hasher.shopify_gid import shopify_gid_to_hash

SHOPIFY_PRODUCT_GID_PREFIX = "gid://shopify/Product/"
SHOPIFY_VARIANT_GID_PREFIX = "gid://shopify/ProductVariant/"

class ProductManager(BaseGacoManager):
    """
    Manages CRUD operations for Products and ProductVariants in Firestore.
    - If a Shopify GID is provided for product_id/product_variant_id, its hash is used as the Firestore document ID.
    - If a custom ID is provided, it's used as the logical ID, and Firestore auto-generates the document ID.
    - If no ID is provided, a UUID-based logical ID is generated, and Firestore auto-generates the document ID.
    Logical IDs (product_id, product_variant_id) are stored as fields and used for querying.
    """

    def __init__(self, db: firestore.Client, auth_context: Optional[AuthContext] = None):
        super().__init__(db, auth_context)
        self.hash_salt = 'shopify_gid_v1'

    def _setup_collections(self):
        """Initializes Firestore collections."""
        super()._setup_collections()
        self.products_ref = self._get_collection(products_collection)
        self.variants_ref = self._get_collection(product_variants_collection)

    def _validate_context(self, product_data: Product) -> None:
        """
        Validates that either store or producer context is sufficiently provided.
        """
        store_info_present = product_data.store_id is not None and product_data.store_display_name is not None
        producer_info_present = product_data.producer_id is not None and product_data.producer_display_name is not None

        if not (store_info_present or producer_info_present):
            raise ValidationError(
                f"Product (ID: {product_data.product_id}) must have either (store_id AND store_display_name) "
                f"OR (producer_id AND producer_display_name) provided."
            )

    def _find_doc_ref_by_logical_id(self, collection_ref, logical_id_field_name: str, logical_id: str) -> Optional[firestore.DocumentReference]:
        """Helper to find a document reference by its logical ID field."""
        query = collection_ref.where(logical_id_field_name, "==", logical_id).limit(1)
        docs = list(query.stream()) # Use list to consume stream and check length
        if docs:
            return docs[0].reference
        return None

    def create_product(self, product_data: Product) -> Product:
        """
        Creates a new product and its variants in Firestore.
        Handles product_id generation or hashing for Shopify GIDs as document ID.

        Args:
            product_data: A Product Pydantic model instance.

        Returns:
            The created Product Pydantic model instance, including its variants.

        Raises:
            ValueError: If context validation fails, or a product/variant with the same logical ID already exists.
        """
        self._validate_context(product_data)
        
        logical_product_id: str

        if not product_data.product_id:
            logical_product_id = f"prod_{uuid.uuid4().hex}"
            product_data.product_id = logical_product_id # Update model with generated ID
            # Check for collision (highly unlikely for UUID, but good practice)
            if self._find_doc_ref_by_logical_id(self.products_ref, Product.PRODUCT_ID_FIELD, logical_product_id):
                raise ValidationError(f"Generated Product ID '{logical_product_id}' collided. This should be rare.")
            self.product_doc_ref = self.products_ref.document() # Firestore auto-generates document ID
        else:
            logical_product_id = product_data.product_id
            if self._find_doc_ref_by_logical_id(self.products_ref, Product.PRODUCT_ID_FIELD, logical_product_id):
                raise ConflictError(f"Product with logical ID '{logical_product_id}' already exists.")

            if logical_product_id.startswith(SHOPIFY_PRODUCT_GID_PREFIX):
                hashed_doc_id = shopify_gid_to_hash(logical_product_id, self.hash_salt)
                self.product_doc_ref = self.products_ref.document(hashed_doc_id)
                # It's good practice to ensure the specific hashed document ID isn't already taken,
                # though the logical ID check above should generally cover conflicts.
                # if product_doc_ref.get().exists:
                #     raise ValueError(f"Product with hashed GID '{logical_product_id}' (doc ID '{hashed_doc_id}') already exists.")
            else: # Custom non-GID product_id provided
                self.product_doc_ref = self.products_ref.document() # Firestore auto-generates document ID
        
        product_dict_for_firestore = product_data.model_dump(exclude={'variants'}, exclude_none=False)
        
        self.product_doc_ref.set(product_dict_for_firestore)

        created_variants = []
        if product_data.variants:
            for variant_model in product_data.variants:
                variant_logical_id: str
                if not variant_model.product_variant_id:
                    variant_logical_id = f"var_{uuid.uuid4().hex}"
                    variant_model.product_variant_id = variant_logical_id # Update model
                else:
                    variant_logical_id = variant_model.product_variant_id

                # Ensure parent linkage is correct with the final logical_product_id
                if variant_model.product_id != logical_product_id: 
                    logger.warn(
                        f"Variant {variant_logical_id} (original parent: {variant_model.product_id}) "
                        f"is being associated with product {logical_product_id}. Overwriting variant's product_id."
                    )
                    variant_model.product_id = logical_product_id
                
                if self._find_doc_ref_by_logical_id(self.variants_ref, ProductVariant.PRODUCT_VARIANT_ID_FIELD, variant_logical_id):
                    # If ID was generated and collided, this is extremely rare. If provided and collided, it's an error.
                    raise ConflictError(f"Variant with logical ID '{variant_logical_id}' already exists.")

                if variant_logical_id.startswith(SHOPIFY_VARIANT_GID_PREFIX):
                    hashed_variant_doc_id = shopify_gid_to_hash(variant_logical_id, self.hash_salt)
                    self.variant_doc_ref = self.variants_ref.document(hashed_variant_doc_id)
                else: # Custom or generated non-GID variant_id
                    self.variant_doc_ref = self.variants_ref.document()
                
                self.variant_doc_ref.set(variant_model.model_dump())
                created_variants.append(variant_model)
        
        
        product_data.variants = created_variants 
        return product_data

    def create_product_simple(self, product_data: Product) -> Product:
        """
        Creates a single new product in Firestore without any variants.
        Handles product_id generation or hashing for Shopify GIDs as document ID.
        Any 'variants' list in the input product_data model will be ignored.
        """
        self._validate_context(product_data)

        product_doc_ref: firestore.DocumentReference
        logical_product_id: str

        if not product_data.product_id:
            logical_product_id = f"prod_{uuid.uuid4().hex}"
            product_data.product_id = logical_product_id # Update model
            if self._find_doc_ref_by_logical_id(self.products_ref, Product.PRODUCT_ID_FIELD, logical_product_id):
                raise ConflictError(f"Generated Product ID '{logical_product_id}' collided.")
            product_doc_ref = self.products_ref.document()
        else:
            logical_product_id = product_data.product_id
            if self._find_doc_ref_by_logical_id(self.products_ref, Product.PRODUCT_ID_FIELD, logical_product_id):
                raise ConflictError(f"Product with logical ID '{logical_product_id}' already exists.")

            if logical_product_id.startswith(SHOPIFY_PRODUCT_GID_PREFIX):
                hashed_doc_id = shopify_gid_to_hash(logical_product_id, self.hash_salt)
                product_doc_ref = self.products_ref.document(hashed_doc_id)
            else: # Custom non-GID
                product_doc_ref = self.products_ref.document()
        
        product_dict_for_firestore = product_data.model_dump(exclude={'variants'}, exclude_none=False)
        product_doc_ref.set(product_dict_for_firestore)

        created_product_state = product_data.model_copy(deep=True)
        created_product_state.variants = []
        
        return created_product_state

    def get_product(self, logical_product_id: str) -> Optional[Product]:
        """
        Retrieves a product and its variants from Firestore using its logical product_id.
        """
        product_doc_ref = self._find_doc_ref_by_logical_id(self.products_ref, Product.PRODUCT_ID_FIELD, logical_product_id)
        if not product_doc_ref:
            return None
        
        product_doc = product_doc_ref.get()
        if not product_doc.exists: # Should not happen if _find_doc_ref_by_logical_id returned a ref
            return None 
        
        product_data_dict = product_doc.to_dict()
        # Ensure the logical product_id is correctly set (it should already be from Firestore)
        product_data_dict[Product.PRODUCT_ID_FIELD] = logical_product_id 

        variants = self.get_variants_for_product_by_logical_id(logical_product_id)
        product_data_dict['variants'] = variants
        
        return Product.model_validate(product_data_dict)

    def get_variants_for_product_by_logical_id(self, logical_product_id: str) -> List[ProductVariant]:
        """
        Retrieves all variants associated with a given logical product ID.
        (This method's core logic remains unchanged as it queries by field value)
        """
        query = self.variants_ref.where(ProductVariant.PRODUCT_ID_FIELD, "==", logical_product_id)
        variant_docs = query.stream()
        
        variants_list = []
        for doc in variant_docs:
            variant_data = doc.to_dict()
            # The logical product_variant_id should be present in variant_data from Firestore.
            # If not, and an 'id' field exists (e.g. from older Shopify GID alias), use it.
            if ProductVariant.PRODUCT_VARIANT_ID_FIELD not in variant_data and 'id' in variant_data:
                 variant_data[ProductVariant.PRODUCT_VARIANT_ID_FIELD] = variant_data['id']
            variants_list.append(ProductVariant.model_validate(variant_data))
        return variants_list

    def update_product(self, logical_product_id: str, product_update_data: Dict[str, Any]) -> Optional[Product]:
        """
        Updates an existing product in Firestore, found by its logical product_id.
        """
        if Product.PRODUCT_ID_FIELD in product_update_data or 'variants' in product_update_data:
            raise ValueError(f"Cannot update '{Product.PRODUCT_ID_FIELD}' or 'variants' list directly. "
                             "Use dedicated variant management or a full product overwrite logic.")

        product_doc_ref = self._find_doc_ref_by_logical_id(self.products_ref, Product.PRODUCT_ID_FIELD, logical_product_id)
        if not product_doc_ref:
            return None

        product_update_data['updated_at'] = datetime.now(timezone.utc)
        product_doc_ref.update(product_update_data)
        return self.get_product(logical_product_id)

    def delete_product(self, logical_product_id: str, delete_variants: bool = True) -> None:
        """
        Deletes a product (found by logical_product_id) and, optionally, all its associated variants.
        """
        product_doc_ref = self._find_doc_ref_by_logical_id(self.products_ref, Product.PRODUCT_ID_FIELD, logical_product_id)

        if not product_doc_ref:
            logger.info(f"Product with logical ID '{logical_product_id}' not found for deletion.")
            return

        if delete_variants:
            variants_to_delete_query = self.variants_ref.where(ProductVariant.PRODUCT_ID_FIELD, "==", logical_product_id)
            for variant_doc_snapshot in variants_to_delete_query.stream():
                variant_doc_snapshot.reference.delete()
        
        product_doc_ref.delete()
        logger.info(f"Product '{logical_product_id}' and its variants (if requested) deleted.")
        return product_doc_ref.id

    # --- Variant specific methods ---

    def create_variant(self, variant_data: ProductVariant) -> ProductVariant:
        """
        Creates a single product variant.
        Handles product_variant_id generation or hashing for Shopify GIDs as document ID.
        Requires variant_data.product_id (parent's logical ID).
        If variant_data.product_variant_id is not provided, it will be generated.
        """
        if not variant_data.product_id:
            raise ValidationError("ProductVariant.product_id (parent's logical ID) is required.")
        
        variant_logical_id: str
        if not variant_data.product_variant_id:
            variant_logical_id = f"var_{uuid.uuid4().hex}"
            variant_data.product_variant_id = variant_logical_id # Update model
        else:
            variant_logical_id = variant_data.product_variant_id

        parent_product_ref = self._find_doc_ref_by_logical_id(self.products_ref, Product.PRODUCT_ID_FIELD, variant_data.product_id)
        if not parent_product_ref or not parent_product_ref.get().exists:
            raise NotFoundError(f"Parent product with logical ID '{variant_data.product_id}' not found. Cannot create variant.")

        if self._find_doc_ref_by_logical_id(self.variants_ref, ProductVariant.PRODUCT_VARIANT_ID_FIELD, variant_logical_id):
            raise ConflictError(f"Variant with logical ID '{variant_logical_id}' already exists.")

        variant_doc_ref: firestore.DocumentReference
        if variant_logical_id.startswith(SHOPIFY_VARIANT_GID_PREFIX):
            hashed_variant_doc_id = shopify_gid_to_hash(variant_logical_id, self.hash_salt)
            variant_doc_ref = self.variants_ref.document(hashed_variant_doc_id)
        else: # Custom or generated non-GID variant_id
            variant_doc_ref = self.variants_ref.document()
        
        variant_dict = variant_data.model_dump(exclude_none=False)
        variant_doc_ref.set(variant_dict)
        return variant_data

    def get_variant(self, logical_variant_id: str) -> Optional[ProductVariant]:
        """
        Retrieves a specific variant by its logical_variant_id.
        """
        variant_doc_ref = self._find_doc_ref_by_logical_id(self.variants_ref, ProductVariant.PRODUCT_VARIANT_ID_FIELD, logical_variant_id)
        if not variant_doc_ref:
            return None
        
        variant_doc = variant_doc_ref.get()
        if not variant_doc.exists:
            return None
            
        variant_data_dict = variant_doc.to_dict()
        variant_data_dict[ProductVariant.PRODUCT_VARIANT_ID_FIELD] = logical_variant_id
        return ProductVariant.model_validate(variant_data_dict)

    def update_variant(self, logical_variant_id: str, variant_update_data: Dict[str, Any]) -> Optional[ProductVariant]:
        """
        Updates an existing product variant, found by its logical_variant_id.
        """
        if ProductVariant.PRODUCT_VARIANT_ID_FIELD in variant_update_data or ProductVariant.PRODUCT_ID_FIELD in variant_update_data:
            raise ConflictError(f"Cannot update '{ProductVariant.PRODUCT_VARIANT_ID_FIELD}' or '{ProductVariant.PRODUCT_ID_FIELD}' using this method.")

        variant_key_fields = [
            ProductVariant.SKU_FIELD,
            ProductVariant.TITLE_FIELD,
            ProductVariant.PRICE_FIELD,
            ProductVariant.INVENTORY_QUANTITY_FIELD,
        ]

        allowed_keys = set(variant_key_fields)
        allowed_keys.add(ProductVariant.COMPARE_AT_PRICE_FIELD) # Allow compare_at_price as it's optional
        allowed_keys.add(ProductVariant.BARCODE_FIELD) # Allow barcode as it's optional
        
        for key in variant_update_data.keys():
            if key not in allowed_keys:
                raise ValidationError(f"Invalid field '{key}' in update_data. Only {', '.join(sorted(list(allowed_keys)))} are allowed for variant updates.")



        variant_doc_ref = self._find_doc_ref_by_logical_id(self.variants_ref, ProductVariant.PRODUCT_VARIANT_ID_FIELD, logical_variant_id)
        if not variant_doc_ref:
            return None
        
        variant_update_data[ProductVariant.UPDATED_AT_FIELD] = datetime.now(timezone.utc)
        variant_doc_ref.update(variant_update_data)
        logger.info(f"Variant '{logical_variant_id}' updated with data: {variant_update_data}")
        return self.get_variant(logical_variant_id)

    def delete_variant(self, logical_variant_id: str) -> None:
        """
        Deletes a specific product variant by its logical_variant_id.
        """
        variant_doc_ref = self._find_doc_ref_by_logical_id(self.variants_ref, ProductVariant.PRODUCT_VARIANT_ID_FIELD, logical_variant_id)

        if not variant_doc_ref:
            print(f"Variant with logical ID '{logical_variant_id}' not found for deletion.")
            return
            
        variant_doc_ref.delete()
        print(f"Variant '{logical_variant_id}' deleted.")
        return variant_doc_ref.id
