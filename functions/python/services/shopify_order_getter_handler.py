from typing import Optional
from firebase_admin import firestore

from gaco_framework.managers import BaseGacoManager
from gaco_framework.auth import AuthContext
from services.shopify_order_getter import ShopifyOrderGetter
from gaco_secrets.secret_manager import SecretManager
from constants.gaco_values import project_id
from services.shopify_order_query_maker import ShopifyOrderQueryMaker
from models.requests.shopify_requests import GetShopifyOrdersWithDynamicQueryRequest


class ShopifyOrder<PERSON>etter<PERSON><PERSON><PERSON>(BaseGacoManager):
    def __init__(self, db: firestore.Client, auth_context: Optional[AuthContext] = None):
        super().__init__(db, auth_context)

    def _setup_dependencies(self):
        self.query_maker = ShopifyOrderQueryMaker(self.db)
        self.secret_manager = SecretManager(project_id=project_id)
        self.shopify_order_getter = ShopifyOrderGetter(
            self.db, 
            self.secret_manager
        )

    def fetch_initial_orders(
            self, 
            request: GetShopifyOrdersWithDynamicQueryRequest
        ) -> dict:
        shopify_request = self.query_maker.get_store_orders(request)

        return self.shopify_order_getter.fetch_initial_orders(
            store_id=shopify_request.store_id,
            start_date=shopify_request.start_date,
            end_date=shopify_request.end_date
        )
