from firebase_admin import firestore
from typing import Optional

from gaco_framework.managers import BaseGacoManager
from gaco_framework.auth import AuthContext
from gaco_framework.exceptions import AuthorizationError, NotFoundError, ConflictError
from queries.stores_query_builder_v2 import StoresQueryBuilderV2
from models.requests.store_requests import CreateStoreRequest, DeleteStoreRequest
from models.store import StoreV2
from datetime import datetime, timezone
from services.secret_service import SecretService
from services.user_root_account_manager import UserRootAccountManager
from constants.gaco_secret_constant import SecretVariants
from gaco_secrets.secret_manager import SecretManager
from constants.gaco_values import project_id,  bucket_name
from services.partner_manager import PartnershipManager
from queries.sales_staging_query_builder import SalesStagingQueryBuilder
from queries.sales_silver_query_builder import SalesSilverQueryBuilder
from queries.sales_gold_query_builder import SalesGoldQueryBuilder
from models.sanitized_sales import sales_staging_collection, sales_silver_collection
from models.sales import sales_gold_collection
from queries.invoices_query_builder import invoices_collection
from models.agreement import agreement_collection
from models.partner import partnership_collection
from firebase_functions import logger
from services.shopify_order_getter_handler import ShopifyOrderGetterHandler
from models.requests.shopify_requests import GetShopifyOrdersWithDynamicQueryRequest
from firebase_admin import storage


store_collection = "storesV2"


class StoreManager(BaseGacoManager):
  """
  CRUD manager for the storesV2 collection
  If you want work with StoreV2 pls talk to the manager
  """

  def __init__(self, db: firestore.Client, auth_context: Optional[AuthContext] = None):
    super().__init__(db, auth_context)
    self.store_collection = self._get_collection(store_collection)

  def _setup_dependencies(self):
    """Initializes all dependent services and managers."""
    self.query_builder = StoresQueryBuilderV2(db = self.db)
    self.secret_manager = SecretManager(project_id=project_id)
    self.secret_service = SecretService(db = self.db, secret_manager = self.secret_manager)
    self.user_root_account_manager = UserRootAccountManager(db = self.db)
    self.partner_manager = PartnershipManager(db = self.db, auth_context=self.auth_context)
    self.sales_staging_query_builder = SalesStagingQueryBuilder(db = self.db)
    self.sales_silver_query_builder = SalesSilverQueryBuilder(db = self.db)
    self.sales_gold_query_builder = SalesGoldQueryBuilder(db = self.db)
    self.shopify_order_getter_handler = ShopifyOrderGetterHandler(db = self.db, auth_context=self.auth_context)


  def create_store(self, request: CreateStoreRequest) -> str:
    """
    Create a new store in the storesV2 collection.
    
    Args:
        request (CreateStoreRequest): The request object containing store details
        
    Returns:
        str: The id of the created store
        
    Raises:
        ValueError: If a store with the same email already exists
        AuthorizationError: If user is not authenticated.
    """
    if not self.auth_context or not self.auth_context.user_id:
        raise AuthorizationError("User must be authenticated to create a store.")
    user_uuid = self.auth_context.user_id

    existing_stores = self.query_builder.for_display_name(request.display_name) \
      .for_email(request.email) \
      .build() \
      .get()

    if existing_stores:
      msg = f"Store with display name {request.display_name} and email {request.email} already exists"
      raise ConflictError(msg)
    
    store = StoreV2(
      created_at=datetime.now(timezone.utc),
      display_name=request.display_name,
      email=request.email,
      parent_id=user_uuid,
      tax_a2=request.tax_a2,
      default_commission=request.default_commission,
      description=request.description,
      address=request.address,
    ) 

    doc_ref = self.store_collection.add(store.model_dump())
    doc_ref_id = doc_ref[1].id

    self.user_root_account_manager.add_store_with_access_right(
      user_uuid = user_uuid,
      store_id = doc_ref_id,
      access_right = request.access_right
    )

    key_reference = self.secret_service.create_secret(
      store_id=doc_ref_id,
      secret_name=SecretVariants.SHOPIFY_API_KEY.value,
      secret_value=request.shopify_api_key_object.shopify_api_key,
      shop_name=request.shopify_api_key_object.shop_name,
    )
    
    self.store_collection.document(doc_ref_id).update({
      StoreV2.SHOPIFY_API_KEY_REFERENCE_FIELD: key_reference.secret_version_path
    })

    return doc_ref_id


  def delete_store(self, request: DeleteStoreRequest) -> None:
    """
    Delete a store from the storesV2 collection.
    """
    if not self.auth_context or not self.auth_context.user_id:
        raise AuthorizationError("User must be authenticated to delete a store.")
    user_uuid = self.auth_context.user_id

    doc_ref = self.store_collection.document(request.store_id)
    doc = doc_ref.get()
    if not doc.exists:
      raise NotFoundError(f"Store with id {request.store_id} not found")

    partnerships = self.partner_manager.fetch_active_partnerships(request.store_id)
    if partnerships and not request.hard_delete:
      raise ConflictError(f"Store with id {request.store_id} has active partnerships")
    
    self.secret_service.delete_secret(request.store_id)
    
    if request.hard_delete:
      self.delete_store_data_from_collections(request.store_id)

    self.user_root_account_manager.remove_store_association(
      user_uuid = user_uuid,
      store_id = request.store_id
    )

    self.db.collection('deletedStores').document(request.store_id)\
      .set(
        StoreV2.model_validate(doc.to_dict()).model_dump()
      )

    doc_ref.delete()


  def _batch_delete_collection_by_store_id(self, collection_name: str, store_id: str) -> int:
    """
    Deletes all documents in a specified collection matching the store_id using batch operations.

    Args:
        collection_name: The name of the Firestore collection.
        store_id: The store ID to filter documents by.

    Returns:
        The total number of documents deleted.
    """
    print(f"Starting batch delete for collection '{collection_name}' and store_id '{store_id}'...")
    docs_query = self.db.collection(collection_name).where('storeId', '==', store_id)
    docs = docs_query.stream() # Use stream for potentially large results

    batch = self.db.batch()
    deleted_count = 0
    batch_limit = 500 # Firestore batch commit limit

    for doc in docs:
        batch.delete(doc.reference)
        deleted_count += 1
        if deleted_count % batch_limit == 0:
            batch.commit()
            print(f"Committed batch of {batch_limit} deletes for {collection_name}.")
            batch = self.db.batch()

    if deleted_count % batch_limit != 0:
        batch.commit()
        print(f"Committed final batch of {deleted_count % batch_limit} deletes for {collection_name}.")

    if deleted_count == 0:
        print(f"No documents found to delete in '{collection_name}' for store_id '{store_id}'.")
    else:
         print(f"Finished batch delete for '{collection_name}'. Total documents deleted: {deleted_count}")

    return deleted_count

  def delete_store_data_from_collections(self, store_id: str):
    """
    Deletes sales data for a specific store from staging, silver, and gold collections,
    and also deletes associated objects from Google Cloud Storage.
    """
    logger.info(f"Initiating deletion of all sales data and GCS objects for store_id: {store_id}")

    staging_deleted = self._batch_delete_collection_by_store_id(sales_staging_collection, store_id)
    silver_deleted = self._batch_delete_collection_by_store_id(sales_silver_collection, store_id)
    invoices_deleted = self._batch_delete_collection_by_store_id(invoices_collection, store_id)
    agreements_deleted = self._batch_delete_collection_by_store_id(agreement_collection, store_id)
    partnerships_deleted = self._batch_delete_collection_by_store_id(partnership_collection, store_id)
    gold_deleted = self._batch_delete_collection_by_store_id(sales_gold_collection, store_id)

    gcs_deleted_count = 0
    try:
        bucket = storage.bucket(bucket_name)
        prefix = f"stores/{store_id}/"
        blobs_to_delete = list(bucket.list_blobs(prefix=prefix))
        
        if blobs_to_delete:
            logger.info(f"Found {len(blobs_to_delete)} GCS object(s) to delete under prefix: {prefix}")
            for blob in blobs_to_delete:
                try:
                    blob.delete()
                    gcs_deleted_count += 1
                except Exception as e_blob:
                    logger.error(f"Failed to delete GCS object {blob.name}: {e_blob}")
            logger.info(f"Successfully deleted {gcs_deleted_count} GCS object(s) for prefix: {prefix}")
        else:
            logger.info(f"No GCS objects found under prefix: {prefix} for store_id: {store_id}")
    except Exception as e_gcs:
        logger.error(f"Error during GCS object deletion for store_id {store_id}: {e_gcs}")

    logger.info(f"Completed deletion for store_id: {store_id}. Results:")
    logger.info(f"  - {sales_staging_collection}: {staging_deleted} deleted")
    logger.info(f"  - {sales_silver_collection}: {silver_deleted} deleted")
    logger.info(f"  - {sales_gold_collection}: {gold_deleted} deleted")
    logger.info(f"  - {invoices_collection}: {invoices_deleted} deleted")
    logger.info(f"  - {agreement_collection}: {agreements_deleted} deleted")
    logger.info(f"  - {partnership_collection}: {partnerships_deleted} deleted")
    logger.info(f"  - GCS objects under stores/{store_id}/: {gcs_deleted_count} deleted")

    return {
        sales_staging_collection: staging_deleted,
        sales_silver_collection: silver_deleted,
        sales_gold_collection: gold_deleted,
        invoices_collection: invoices_deleted,
        agreement_collection: agreements_deleted,
        partnership_collection: partnerships_deleted,
        "gcs_deleted_count": gcs_deleted_count
    }
