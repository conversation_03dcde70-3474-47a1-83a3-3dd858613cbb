from firebase_admin import firestore
from datetime import datetime, timezone
from firebase_functions import logger
from typing import Optional, List, Dict, Any

from gaco_framework.auth import AuthContext # this is find from .types
from gaco_framework.exceptions import (
  AuthorizationError, 
  NotFoundError, 
  ConflictError
)
from gaco_framework.managers import BaseGacoManager
from gaco_framework.security import ResourceAccess
from models.agreement import (
    Agreement, AgreementStatus, ApprovalStatus, agreement_collection, ApprovalStep, Role
)
from models.requests.agreements_requests import CreateAgreementRequest
from services.user_root_account_manager import UserRootAccountManager
from services.partner_manager import PartnershipManager
from models.agreement import NegotiationChange
from queries.sales_gold_query_builder import SalesGoldQueryBuilder
from models.sales import Sale
from constants.collections import producer_collection, agreement_collection, store_collection


class AgreementManager(BaseGacoManager):
    """
    Basic manager for contract agreements between stores and producers.
    Client level service that user will interact with.
    """

    def __init__(self, db: firestore.Client, auth_context: Optional[AuthContext] = None):
        super().__init__(db, auth_context)
        self.agreements_collection = self._get_collection(agreement_collection)
        # TODO: Refactor UserRootAccountManager to use gaco_framework
        self.user_root_account_manager = UserRootAccountManager(self.db)
        self.partner_manager = PartnershipManager(self.db, self.auth_context)
        self.sales_gold_query_builder = SalesGoldQueryBuilder(self.db)

    def _does_store_exist(self, store_id: str) -> bool:
        """Check if a store exists"""
        store_ref = self.db.collection(store_collection).document(store_id).get()
        return store_ref.exists

    def _does_producer_exist(self, producer_id: str) -> bool:
        """Check if a producer exists"""
        producer_ref = self.db.collection(producer_collection).document(producer_id).get()
        return producer_ref.exists


    def _update_agreement_status(self, agreement: Agreement) -> Agreement:
        """Update the status of an agreement based on the approval workflow"""
        if not agreement.approval_workflow:
            return agreement

        if len(agreement.approval_workflow) == 1:
            raise ConflictError("Agreement must have at least 2 approval steps")
        
        # Determine the workflow state for pattern matching
        has_all_approved = all(step.status == ApprovalStatus.APPROVED.value for step in agreement.approval_workflow)
        has_any_rejected = any(step.status == ApprovalStatus.REJECTED.value for step in agreement.approval_workflow)
        has_any_approved = any(step.status == ApprovalStatus.APPROVED.value for step in agreement.approval_workflow)
        has_all_pending = all(step.status == ApprovalStatus.PENDING.value for step in agreement.approval_workflow)

        # Use tuple pattern matching for workflow state
        match (has_all_approved, has_any_rejected, has_any_approved, has_all_pending):
            case (True, _, _, _):
                # All steps are approved
                agreement.status = AgreementStatus.APPROVED.value
            case (_, True, _, _):
                # Any step is rejected
                agreement.status = AgreementStatus.REJECTED.value
            case (_, _, True, _):
                # Any step is approved or pending (partial approval state)
                agreement.status = AgreementStatus.PENDING_APPROVAL.value
            case (_, _, _, True):
                agreement.status = AgreementStatus.DRAFT.value
            case _:
                # Default case - no status change
                pass

        return agreement


    def create_draft_agreement(self, data: CreateAgreementRequest) -> str:
        """Create a new draft agreement"""
        if not self.auth_context or not self.auth_context.user_id:
            raise AuthorizationError("Authentication is required.")

        if not self._does_store_exist(data.store_id):
            raise NotFoundError(f"Store {data.store_id} not found")

        if not self._does_producer_exist(data.producer_id):
            raise NotFoundError(f"Producer {data.producer_id} not found")

        user_id = self.auth_context.user_id
        now = datetime.now(timezone.utc)

        # Create basic approval workflow with both parties
        store_approval_step = ApprovalStep(
            role=Role.STORE.value,
            timestamp=now
        )

        producer_approval_step = ApprovalStep(
            role=Role.PRODUCER.value,
            timestamp=now
        )

        # Update the approval step for the creator's role to approved
        match data.created_by_role:
            case Role.STORE.value:
                store_approval_step = ApprovalStep(
                    role=Role.STORE.value,
                    approver_id=self.auth_context.user_id,
                    timestamp=now
                )
            case Role.PRODUCER.value:
                producer_approval_step = ApprovalStep(
                    role=Role.PRODUCER.value,
                    approver_id=self.auth_context.user_id,
                    timestamp=now
                )
            case _:
                # Handle unexpected role values
                raise ValueError(f"Invalid created_by_role: {data.created_by_role}")

        approval_workflow = [
            store_approval_step.model_dump(),
            producer_approval_step.model_dump(),
        ]
        
        agreement = Agreement(
            store_id=data.store_id,
            producer_id=data.producer_id,
            title=data.title,
            effective_date=data.effective_date,
            expiration_date=data.expiration_date,
            commission=data.commission,
            document_url=data.document_url if data.document_url else None,
            approval_workflow=approval_workflow,
            created_by=user_id,
            created_by_role=data.created_by_role,
            created_at=now,
        )

        agreement = self._update_agreement_status(agreement)

        agreement_ref = self.agreements_collection.document()
        agreement_data = agreement.model_dump()
        agreement_ref.set(agreement_data)
        
        return agreement_ref.id
    
    def create_renewal_draft(
            self, 
            original_agreement_id: str,
            custom_changes: dict = None,
            new_effective_date: str = datetime.now(timezone.utc).isoformat(), 
            new_expiration_date: Optional[str] = None,
            created_by_role: str = Role.STORE.value
        ) -> str:
        """
        Create a renewal draft based on an expired or terminated agreement.
        """
        if not self.auth_context or not self.auth_context.user_id:
            raise AuthorizationError("Authentication is required.")
        user_id = self.auth_context.user_id
        
        # Get the original agreement
        agreement_ref = self.agreements_collection.document(original_agreement_id)
        agreement_doc = agreement_ref.get()


        if not agreement_doc.exists:
            raise NotFoundError("Original agreement not found")
            
        original_data = agreement_doc.to_dict()
        # self._check_access("agreement", original_data, ResourceAccess.READ)
        original_agreement = Agreement.model_validate(original_data)

        # Check if the agreement can be renewed (must be EXPIRED or TERMINATED)
        if original_agreement.status not in [AgreementStatus.EXPIRED.value, AgreementStatus.TERMINATED.value]:
            raise ConflictError(
                f"Only expired or terminated agreements can be renewed. "
                f"Current status: {original_agreement.status}"
            )
        
        # Parse dates
        try:
            effective_date = datetime.fromisoformat(new_effective_date)
            expiration_date = datetime.fromisoformat(new_expiration_date) if new_expiration_date else None
        except ValueError:
            raise ValueError("Invalid date format. Use ISO format (YYYY-MM-DDTHH:MM:SS+00:00)")
        
        
        approval_workflow = [
            ApprovalStep(
                role=Role.STORE.value,
                approver_id=None,
            ).model_dump(),
            ApprovalStep(
                role=Role.PRODUCER.value,
                approver_id=None,
            ).model_dump(),
        ]
        
        # Increment version
        current_version = original_agreement.version
        try:
            major, minor = current_version.split(".")
            new_version = f"{major}.{int(minor) + 1}"
        except:
            new_version = "1.1"  # Fallback if version parsing fails

        now = datetime.now(timezone.utc)
        renewal_model = Agreement(
            store_id=original_agreement.store_id,
            producer_id=original_agreement.producer_id,
            title=f"Renewal: {original_agreement.title}",
            effective_date=effective_date,
            expiration_date=expiration_date,
            commission=original_agreement.commission,
            document_url=original_agreement.document_url,
            approval_workflow=approval_workflow,
            renewed_by=original_agreement_id,
            version=new_version,
            created_by=user_id,
            created_at=now,
            created_by_role=created_by_role
        )

        renewal_model = self._update_agreement_status(renewal_model)
        
        renewal = renewal_model.model_dump()

        allowed_fields = [
            Agreement.TITLE_FIELD,
            Agreement.EFFECTIVE_DATE_FIELD,
            Agreement.EXPIRATION_DATE_FIELD,
            Agreement.COMMISSION_FIELD,
            Agreement.DOCUMENT_URL_FIELD
        ]
        
        # Apply any custom changes
        if custom_changes:
            for key, value in custom_changes.items():
                if key in allowed_fields:
                    renewal[key] = value
        
        # self._check_access("agreement", renewal, ResourceAccess.WRITE)
        
        # Create the new agreement document
        new_agreement_ref = self.agreements_collection.document()
        new_agreement_ref.set(renewal)
        
        # Add renewal reference to the original agreement
        agreement_ref.update({
            Agreement.RENEWED_BY_FIELD: new_agreement_ref.id,
            Agreement.UPDATED_BY_FIELD: user_id,
            Agreement.UPDATED_AT_FIELD: now
        })
        
        return new_agreement_ref.id

    def update_agreement(self, agreement_id: str, data: dict) -> dict:
        """Update a draft agreement"""
        if not self.auth_context or not self.auth_context.user_id:
            raise AuthorizationError("Authentication is required.")
        user_id = self.auth_context.user_id

        agreement_ref = self.agreements_collection.document(agreement_id)
        agreement_doc = agreement_ref.get()
        
        if not agreement_doc.exists:
            raise NotFoundError("Agreement not found")

            
        agreement_data = agreement_doc.to_dict()
        # self._check_access("agreement", agreement_data, ResourceAccess.WRITE)
        agreement_model = Agreement.model_validate(agreement_data)

        if agreement_model.status == AgreementStatus.ACTIVE.value:
            raise ConflictError("Cannot update an active agreement")

        if agreement_model.status not in [
            AgreementStatus.DRAFT.value, 
        ]:
            raise ConflictError("Only draft and pending approval agreements can be updated")
            
        update_data = {
            Agreement.UPDATED_BY_FIELD: user_id,
            Agreement.UPDATED_AT_FIELD: datetime.now(timezone.utc)
        }
        
        # Only allow updating specific fields
        allowed_fields = [
            Agreement.TITLE_FIELD,
            Agreement.EFFECTIVE_DATE_FIELD,
            Agreement.EXPIRATION_DATE_FIELD,
            Agreement.COMMISSION_FIELD,
            Agreement.DOCUMENT_URL_FIELD
        ]
        
        for field in allowed_fields:
            if field in data:
                update_data[field] = data[field]
                
        agreement_ref.update(update_data)
        
        return {
            "success": True, 
            "message": f"Agreement updated successfully", 
            "agreement_id": agreement_id
        }
    
    def submit_for_approval(self, agreement_id: str, role: str) -> dict:
        """Submit agreement for approval, put validation logic in the endpoint level?"""
        if not self.auth_context or not self.auth_context.user_id:
            raise AuthorizationError("Authentication is required.")
        user_id = self.auth_context.user_id

        agreement_ref = self.agreements_collection.document(agreement_id)
        agreement_doc = agreement_ref.get()
        
        if not agreement_doc.exists:
            raise NotFoundError("Agreement not found")
            
        agreement_data = agreement_doc.to_dict()
        agreement_model = Agreement.model_validate(agreement_data)

        if agreement_model.status != AgreementStatus.DRAFT.value:
            raise ConflictError("Only draft agreements can be submitted for approval")

        for i, step in enumerate(agreement_model.approval_workflow):
            if step.role.value == role and step.status == ApprovalStatus.PENDING.value:
                if role != agreement_model.created_by_role:
                    raise ConflictError(f"Only {agreement_model.created_by_role} can submit for approval")
                agreement_model.approval_workflow[i].status = ApprovalStatus.APPROVED.value
                agreement_model.approval_workflow[i].approver_id = user_id
                agreement_model.approval_workflow[i].timestamp = datetime.now(timezone.utc)
                agreement_model.status = AgreementStatus.PENDING_APPROVAL.value
                agreement_model.updated_by = user_id
                agreement_ref.update(agreement_model.model_dump())
                break
            if step.role.value == role and step.status == ApprovalStatus.APPROVED.value:
                raise ConflictError(f"Submit for {role} is already approved")
            if step.role.value == role and step.status == ApprovalStatus.REJECTED.value:
                raise ConflictError(f"Submit for {role} is already rejected")
            
        return {
            "success": True, 
            "message": "Agreement submitted for approval", 
            "agreement_id": agreement_id
        }
    
    def approve_agreement(
            self, 
            agreement_id: str, 
            role: str, 
            comments: Optional[str] = None
        ) -> dict:
        """Approve an agreement step"""
        if not self.auth_context or not self.auth_context.user_id:
            raise AuthorizationError("Authentication is required.")
        user_id = self.auth_context.user_id

        agreement_ref = self.agreements_collection.document(agreement_id)
        agreement_doc = agreement_ref.get()
        
        if not agreement_doc.exists:
            raise NotFoundError("Agreement not found")
            
        agreement_data = agreement_doc.to_dict()
        """
        stop baking in this in the logic
        make it available in the Manager for ecomplex validation
        but in general we should do it in endpoint leve.
        """
        # self._check_access("agreement", agreement_data, ResourceAccess.WRITE)
        agreement_model = Agreement.model_validate(agreement_data)

        if agreement_model.status != AgreementStatus.PENDING_APPROVAL.value:
            raise ConflictError("Agreement is not pending approval")
            
        # Find and update the approval step for this role
        approval_workflow = [
          ApprovalStep.model_validate(step) for step in agreement_model.approval_workflow
        ]

        step_updated = False
        
        for i, approval_step in enumerate(approval_workflow):
            if approval_step.role.value == role and approval_step.status == ApprovalStatus.PENDING.value:
                approval_workflow[i].status = ApprovalStatus.APPROVED.value
                approval_workflow[i].approver_id = user_id
                approval_workflow[i].timestamp = datetime.now(timezone.utc)
                approval_workflow[i].comments = comments
                step_updated = True
                break
            if approval_step.role.value == role and approval_step.status == ApprovalStatus.APPROVED.value:
                raise ConflictError(f"Approval step for {role} is already approved")

        if not step_updated:
            raise NotFoundError(f"No pending approval step found for role: {role}")
            
        # Check if all steps are approved
        all_approved = all(step.status == ApprovalStatus.APPROVED.value 
                          for step in approval_workflow)
        
        update_data = {
            Agreement.APPROVAL_WORKFLOW_FIELD: [step.model_dump() for step in approval_workflow],
            Agreement.UPDATED_BY_FIELD: user_id,
            Agreement.UPDATED_AT_FIELD: datetime.now(timezone.utc)
        }

        if all_approved:
            update_data[Agreement.STATUS_FIELD] = AgreementStatus.APPROVED.value

        agreement_ref.update(update_data)
        
        # If all steps approved, update status to APPROVED
        if all_approved:
            self.activate_agreement(agreement_id)
            return {
                "success": True, 
                "message": "Agreement fully approved", 
                "status": "approved", 
                "agreement_id": agreement_id
            }
        else:
            return {
                "success": True, 
                "message": f"Approval step completed for {role}", 
                "status": "partial", 
                "agreement_id": agreement_id
            }
    
    # on firestore update function.
    def activate_agreement(self, agreement_id: str) -> dict:
        """
        Expired agreements will be used to calculate the payouts.
        Only ACTIVE and EXPIRED agreements are valid for commission calculation
        """

        if not self.auth_context or not self.auth_context.user_id:
            raise AuthorizationError("Authentication is required.")
        user_id = self.auth_context.user_id

        def _update_old_active_agreement(agreement_data: Agreement, user_id: str) -> None:
            """Check if the agreement is a past agreement"""
            old_active_agreement_docs = self.agreements_collection.where(
                Agreement.STORE_ID_FIELD, "==", agreement_data.store_id
            ).where(
                Agreement.PRODUCER_ID_FIELD, "==", agreement_data.producer_id
            ).where(
                Agreement.STATUS_FIELD, "==", AgreementStatus.ACTIVE.value
            ).get()

            if len(old_active_agreement_docs) > 1:
                raise ConflictError("More than one active agreement found")

            if len(old_active_agreement_docs) == 1:
                old_active_agreement_doc = old_active_agreement_docs[0]
                self.expire_agreement(old_active_agreement_doc.id)
                self.agreements_collection.document(old_active_agreement_doc.id).update({
                    Agreement.RENEWED_BY_FIELD: agreement_id,
                    Agreement.EXPIRATION_DATE_FIELD: datetime.now(timezone.utc)
                })
        
        """Activate an approved agreement, this should be automatic, on update function"""

        agreement_ref = self.agreements_collection.document(agreement_id)
        agreement_doc = agreement_ref.get()
        
        if not agreement_doc.exists:
            raise NotFoundError("Agreement not found")
            
        agreement_data = agreement_doc.to_dict()
        # self._check_access("agreement", agreement_data, ResourceAccess.WRITE)
        agreement_model = Agreement.model_validate(agreement_data)

        _update_old_active_agreement(agreement_model, user_id)

        if (
            agreement_model.status != AgreementStatus.APPROVED.value and 
            agreement_model.status != AgreementStatus.ACTIVE.value
            ):
            raise ConflictError("Only approved agreements can be activated")

        agreement_ref.update({
            Agreement.STATUS_FIELD: AgreementStatus.ACTIVE.value,
            Agreement.UPDATED_BY_FIELD: user_id,
            Agreement.UPDATED_AT_FIELD: datetime.now(timezone.utc)
        })

        partner_ref = self.partner_manager.create_partnership(agreement_ref)

        agreement_ref.update({
            Agreement.PARTNERSHIP_ID_FIELD: partner_ref.id
        })
        
        return {"success": True, "message": "Agreement activated successfully"}
    
    def get_agreement(self, agreement_id: str) -> tuple[dict, str]:
        """Get an agreement by ID"""
        agreement_doc = self.agreements_collection.document(agreement_id).get()
        
        if not agreement_doc.exists:
            raise NotFoundError("Agreement not found")
        
        agreement_data = agreement_doc.to_dict()
        # self._check_access("agreement", agreement_data, ResourceAccess.READ)

        return agreement_data, agreement_doc.id
    
    def submit_for_negotiation(self, agreement_id: str) -> dict:
        """Submit a draft agreement for negotiation"""
        if not self.auth_context or not self.auth_context.user_id:
            raise AuthorizationError("Authentication is required.")
        user_id = self.auth_context.user_id

        agreement_ref = self.agreements_collection.document(agreement_id)
        agreement_doc = agreement_ref.get()
        
        if not agreement_doc.exists:
            raise NotFoundError("Agreement not found")
            
        agreement_data = agreement_doc.to_dict()
        # self._check_access("agreement", agreement_data, ResourceAccess.WRITE)
        agreement_model = Agreement.model_validate(agreement_data)
        
        if agreement_model.status != AgreementStatus.DRAFT.value:
            raise ConflictError("Only draft agreements can be submitted for negotiation")
            
        agreement_ref.update({
            Agreement.STATUS_FIELD: AgreementStatus.NEGOTIATION.value,
            Agreement.UPDATED_BY_FIELD: user_id,
            Agreement.UPDATED_AT_FIELD: datetime.now(timezone.utc)
        })
        
        return {"success": True, "message": "Agreement submitted for negotiation"}

    def propose_changes(self, agreement_id: str, changes: dict, 
                    comments: str, role: str) -> dict:
        """Propose changes to an agreement in negotiation"""
        if not self.auth_context or not self.auth_context.user_id:
            raise AuthorizationError("Authentication is required.")
        user_id = self.auth_context.user_id

        agreement_ref = self.agreements_collection.document(agreement_id)
        agreement_doc = agreement_ref.get()
        
        if not agreement_doc.exists:
            raise NotFoundError("Agreement not found")
            
        agreement_data = agreement_doc.to_dict()
        # self._check_access("agreement", agreement_data, ResourceAccess.WRITE)
        agreement_model = Agreement.model_validate(agreement_data)
        
        if agreement_model.status != AgreementStatus.NEGOTIATION.value:
            raise ConflictError("Agreement is not in negotiation phase")
        
        # Create negotiation change record
        negotiation_change = {
            NegotiationChange.USER_ID_FIELD: user_id,
            NegotiationChange.ROLE_FIELD: role,
            NegotiationChange.TIMESTAMP_FIELD: datetime.now(timezone.utc),
            NegotiationChange.CHANGES_FIELD: changes,
            NegotiationChange.COMMENTS_FIELD: comments
        }
        
        # Get existing negotiation history and append new change
        negotiation_history = agreement_model.negotiation_history
        negotiation_history.append(negotiation_change)
        
        # Prepare update data
        update_data = {
            Agreement.NEGOTIATION_HISTORY_FIELD: [h.model_dump() for h in negotiation_history],
            Agreement.UPDATED_BY_FIELD: user_id,
            Agreement.UPDATED_AT_FIELD: datetime.now(timezone.utc)
        }
        
        # Update the fields that were changed
        allowed_fields = [
            Agreement.TITLE_FIELD,
            Agreement.EFFECTIVE_DATE_FIELD,
            Agreement.EXPIRATION_DATE_FIELD,
            Agreement.COMMISSION_FIELD,
            Agreement.DOCUMENT_URL_FIELD
        ]
        
        for field in allowed_fields:
            if field in changes:
                update_data[field] = changes[field]
        
        agreement_ref.update(update_data)
        
        return {"success": True, "message": "Changes proposed successfully"}

    def accept_changes(self, agreement_id: str, role: str) -> dict:
        """Accept the current state of the agreement in negotiation"""
        if not self.auth_context or not self.auth_context.user_id:
            raise AuthorizationError("Authentication is required.")
        user_id = self.auth_context.user_id
        
        agreement_ref = self.agreements_collection.document(agreement_id)
        agreement_doc = agreement_ref.get()
        
        if not agreement_doc.exists:
            raise NotFoundError("Agreement not found")
            
        agreement_data = agreement_doc.to_dict()
        # self._check_access("agreement", agreement_data, ResourceAccess.WRITE)
        agreement_model = Agreement.model_validate(agreement_data)

        if agreement_model.status != AgreementStatus.NEGOTIATION.value:
            raise ConflictError("Agreement is not in negotiation phase")
        
        # Add acceptance to negotiation history
        # change field is empty since we want to accentuate the acceptance of the current state
        negotiation_change_model = NegotiationChange(
            user_id=user_id,
            role=role,
            timestamp=datetime.now(timezone.utc),
            changes={},
            comments="Accepted current agreement state"
        )
        
        negotiation_history = agreement_model.negotiation_history
        negotiation_history.append(negotiation_change_model)
        
        # Update the agreement
        agreement_ref.update({
            "negotiation_history": [h.model_dump() for h in negotiation_history],
            "status": AgreementStatus.PENDING_APPROVAL.value,  # Move to approval phase
            "updated_by": user_id,
            "updated_at": datetime.now(timezone.utc)
        })
        
        return {"success": True, "message": "Agreement negotiation completed, moved to approval phase"}

    # on update function
    def terminate_agreement(self, agreement_id: str) -> dict:
        """Terminate an agreement"""
        if not self.auth_context or not self.auth_context.user_id:
            raise AuthorizationError("Authentication is required.")
        user_id = self.auth_context.user_id

        agreement_ref = self.agreements_collection.document(agreement_id)
        agreement_doc = agreement_ref.get()

        if not agreement_doc.exists:
            raise NotFoundError("Agreement not found")

        agreement_data = agreement_doc.to_dict()
        # self._check_access("agreement", agreement_data, ResourceAccess.WRITE)
        
        agreement_ref.update({
            Agreement.STATUS_FIELD: AgreementStatus.TERMINATED.value,
            Agreement.UPDATED_BY_FIELD: user_id,
            Agreement.UPDATED_AT_FIELD: datetime.now(timezone.utc),
        })

        self.partner_manager.terminate_partnership_on_agreement(agreement_id)

        return {
            "success": True, 
            "message": "Agreement terminated successfully", 
            "agreement_id": agreement_id
        }

    def expire_agreement(self, agreement_id: str) -> dict:
        """Expire an agreement"""
        if not self.auth_context or not self.auth_context.user_id:
            raise AuthorizationError("Authentication is required.")

        user_id = self.auth_context.user_id

        agreement_ref = self.agreements_collection.document(agreement_id)
        agreement_doc = agreement_ref.get()
        
        if not agreement_doc.exists:
            raise NotFoundError("Agreement not found")

        agreement_ref.update({
            Agreement.STATUS_FIELD: AgreementStatus.EXPIRED.value,
            Agreement.UPDATED_BY_FIELD: user_id,
            Agreement.UPDATED_AT_FIELD: datetime.now(timezone.utc)
        }) 

        self.partner_manager.terminate_partnership_on_agreement(agreement_id)

        return {
            "success": True, 
            "message": "Agreement expired successfully", 
            "agreement_id": agreement_id
        }

    def reject_agreement(self, agreement_id: str, role: str, comments: str) -> dict:
        """Reject an agreement"""
        if not self.auth_context or not self.auth_context.user_id:
            raise AuthorizationError("Authentication is required.")
        user_id = self.auth_context.user_id
        
        agreement_ref = self.agreements_collection.document(agreement_id)
        agreement_doc = agreement_ref.get()
        
        if not agreement_doc.exists:
            raise NotFoundError("Agreement not found")
            
        agreement_data = agreement_doc.to_dict()
        agreement_model = Agreement.model_validate(agreement_data)
        
        if agreement_model.status != AgreementStatus.PENDING_APPROVAL.value:
            raise ConflictError("Agreement is not pending approval")
        
        # Find and update the approval step for this role
        approval_workflow = [
          ApprovalStep.model_validate(step) for step in agreement_model.approval_workflow
        ]
        
        for i, approval_step in enumerate(approval_workflow):
            if approval_step.role.value == role and approval_step.status == ApprovalStatus.PENDING.value:
                approval_workflow[i].status = ApprovalStatus.REJECTED.value
                approval_workflow[i].comments = comments
                break
            if approval_step.role.value == role and approval_step.status == ApprovalStatus.APPROVED.value:
                raise ConflictError(f"Approval step for {role} is already approved, can only terminate agreement")
        
        agreement_ref.update({
            Agreement.APPROVAL_WORKFLOW_FIELD: [step.model_dump() for step in approval_workflow],
            Agreement.UPDATED_BY_FIELD: user_id,
            Agreement.UPDATED_AT_FIELD: datetime.now(timezone.utc)
        })

        agreement_model = Agreement.model_validate(agreement_ref.get().to_dict())
        agreement_model = self._update_agreement_status(agreement_model)
        agreement_ref.update(agreement_model.model_dump())
        
        return {
            "success": True, 
            "message": "Agreement rejected successfully", 
            "agreement_id": agreement_id
        }

    def delete_agreement(self, agreement_id: str) -> dict:
        """Delete an agreement, only for Dev purposes"""
        if not self.auth_context or not self.auth_context.user_id:
            raise AuthorizationError("Authentication is required.")

        agreement_ref = self.agreements_collection.document(agreement_id)
        agreement_doc = agreement_ref.get()

        if not agreement_doc.exists:
            raise NotFoundError("Agreement not found")

        agreement_data = agreement_doc.to_dict()
        # self._check_access("agreement", agreement_data, ResourceAccess.DELETE)
        agreement_model = Agreement.model_validate(agreement_data)

        sales_gold_query = self.sales_gold_query_builder\
            .for_producer_id(agreement_model.producer_id).build()

        sales_gold_docs = sales_gold_query.get()

        if sales_gold_docs:
            raise ConflictError("Cannot delete agreement, producer has sales in gold.")

        agreement_ref.delete()

        return {
            "success": True, 
            "message": "Agreement deleted successfully", 
            "agreement_id": agreement_id
        }

    def delete_all_agreements_between_producer_and_store(self, producer_id: str, store_id: str) -> dict:
        """Delete all agreements between a producer and a store, only for testing purposes"""

        # Get all agreements between producer and store 
        agreement_docs = self.agreements_collection.where(
            Agreement.PRODUCER_ID_FIELD, "==", producer_id
        ).where(
            Agreement.STORE_ID_FIELD, "==", store_id
        ).get()

        # Use batch operation for efficient deletion
        batch = self.db.batch()
        
        for agreement_doc in agreement_docs:
            agreement_data = agreement_doc.to_dict()
            # self._check_access("agreement", agreement_data, ResourceAccess.DELETE)
            batch.delete(agreement_doc.reference)
            
        # Commit the batch operation
        batch.commit()
        return {"success": True, "message": f"Deleted {len(agreement_docs)} agreements."}

    
    def get_agreement_for_sale_doc(self, producer_id: str, store_id: str, sale_dict: dict) -> List[firestore.DocumentSnapshot]:
        """Get all valid agreements (active or terminated within sale date) for a sale document."""
        
        sale_updated_at = sale_dict[Sale.UPDATED_AT_FIELD]

        # Initial query without expiration date filter
        query = self.agreements_collection.where(
            Agreement.PRODUCER_ID_FIELD, "==", producer_id
        ).where(
            Agreement.STORE_ID_FIELD, "==", store_id
        ).where(
            Agreement.STATUS_FIELD, "in", [
                AgreementStatus.ACTIVE.value, 
                AgreementStatus.TERMINATED.value
            ]
        ).where(
            Agreement.EFFECTIVE_DATE_FIELD, "<=", sale_updated_at
        )
        
        potential_agreements = query.stream() # Use stream() for efficiency
        
        valid_agreements = []
        for agreement_doc in potential_agreements:
            agreement_data = agreement_doc.to_dict()
            # No need to check access here as this is an internal function for processing sales
            
            expiration_date = agreement_data.get(Agreement.EXPIRATION_DATE_FIELD)
            
            # Check expiration condition in Python
            if expiration_date is None or (expiration_date is not None and expiration_date >= sale_updated_at):
                if agreement_data[Agreement.STATUS_FIELD] == AgreementStatus.ACTIVE.value:
                    return [agreement_doc]

                valid_agreements.append(agreement_doc)
                
        return valid_agreements
            
