import requests
from typing import Op<PERSON>, <PERSON><PERSON>
from firebase_functions import logger
from firebase_admin import firestore # Added for type hinting and usage in get_access_token

# Assuming SecretManager is in gaco_secrets.secret_manager as in ShopifyProductGetter
from gaco_secrets.secret_manager import SecretManager # Added for secret management

from models.products import Product # Make sure this path is correct for your structure
from queries.shopify_query_builder import ShopifyQueryBuilder # Make sure this path is correct

class ShopifyProductCreator:
    def __init__(self, db: firestore.Client, secret_manager: SecretManager):
        if db is None:
            raise ValueError("Firestore client 'db' is required.")
        if secret_manager is None:
            raise ValueError("SecretManager instance 'secret_manager' is required.")
        self.db = db
        self.secret_manager = secret_manager

    def get_access_token(self, store_id: str) -> Tuple[str, str]:
        """Get Shopify API access token and shop name from Secret Manager."""
        try:
            secret_doc_ref = self.db.collection('customer_api_keys').document(store_id)
            secret_doc = secret_doc_ref.get()
            
            if not secret_doc.exists:
                raise ValueError(f"No API key found for shop {store_id}")
            
            secret_data = secret_doc.to_dict()
            secret_version_path = secret_data.get('secret_version_path')
            shop_name = secret_data.get('shop_name')
            
            if not secret_version_path or not shop_name:
                raise ValueError(f"Invalid secret data for shop {store_id}")
            
            api_key = self.secret_manager.get_secret(secret_version_path)
            
            return api_key, shop_name
        
        except Exception as e:
            logger.error(f"Error getting access token for {store_id}: {str(e)}")
            # Consider re-raising a more specific custom exception if needed
            raise ValueError(f"Failed to fetch access token for {store_id}: {str(e)}")

    def _fetch_default_shopify_location_gid(self, store_id: str, api_version: str) -> Optional[str]:
        """
        Fetches the GID of the primary Shopify location for the given store.
        Returns the GID string or None if not found or an error occurs.
        """
        logger.info(f"Fetching primary Shopify location GID for store {store_id}.")
        try:
            api_key, shop_name = self.get_access_token(store_id)
            shop_domain = f"{shop_name}.myshopify.com"
            
            query = ShopifyQueryBuilder.get_default_location_query() # Uses the updated query
            graphql_url = f"https://{shop_domain}/admin/api/{api_version}/graphql.json"
            headers = {
                'Content-Type': 'application/json',
                'X-Shopify-Access-Token': api_key
            }

            response = requests.post(graphql_url, headers=headers, json={'query': query})
            response.raise_for_status()
            data = response.json()

            if data.get('errors'):
                logger.error(f"GraphQL errors when fetching primary location for store {store_id}: {data['errors']}")
                return None

            # The primary location is directly under data.location (if the query is successful and 'location' field is valid for the API version)
            location_node = data.get('data', {}).get('location') 
            
            if location_node and location_node.get('id'):
                location_gid = location_node['id']
                logger.info(f"Found primary location GID for store {store_id}: {location_gid} (Name: {location_node.get('name', 'N/A')})")
                return location_gid
            else:
                logger.warning(f"No primary location data found in Shopify response for store {store_id}, or 'location' field is missing/null. Response: {data}")
                # As a last resort, try to get the first active location from the 'locations' connection
                logger.info(f"Falling back to fetching first active location for store {store_id} via 'locations' query.")
                all_locations_query = ShopifyQueryBuilder.get_all_locations_query(first=1) # Get first active one
                response_all = requests.post(graphql_url, headers=headers, json={'query': all_locations_query})
                try:
                    response_all.raise_for_status()
                    data_all = response_all.json()
                    if data_all.get('errors'):
                        logger.error(f"GraphQL errors when fetching all locations (fallback) for store {store_id}: {data_all['errors']}")
                        return None
                    all_locations_edges = data_all.get('data', {}).get('locations', {}).get('edges', [])
                    if all_locations_edges:
                        first_active_node = all_locations_edges[0].get('node')
                        if first_active_node and first_active_node.get('id'):
                            fallback_gid = first_active_node['id']
                            logger.info(f"Using first active location (fallback) for store {store_id}: {fallback_gid} (Name: {first_active_node.get('name', 'N/A')})")
                            return fallback_gid
                except Exception as fallback_e:
                    logger.error(f"Error during fallback attempt to fetch first active location for store {store_id}: {fallback_e}", exc_info=True)

                logger.warning(f"Both primary location query and fallback to first active location failed for store {store_id}.")
                return None

        except Exception as e:
            logger.error(f"Error fetching primary Shopify location GID for store {store_id}: {e}", exc_info=True)
            return None

    def add_product_to_shopify(
        self,
        store_id: str,
        product_to_add: Product,
        api_version: str = "2024-04", # Or your target Shopify API version
        default_location_id: Optional[str] = None
    ) -> str:
        """
        Adds a product (defined by our internal Product model) to Shopify
        by generating and executing a GraphQL mutation using requests.post.
        API key and shop domain are fetched using store_id.

        Args:
            store_id: The ID of the store to retrieve Shopify credentials for.
            product_to_add: The Product object (our internal model) to add to Shopify.
            api_version: The Shopify API version to use (e.g., "2024-04").
            default_location_id: Optional Shopify GID for the location to set inventory.
                                 Example: "gid://shopify/Location/1234567890".

        Returns:
            The Shopify GID (e.g., "gid://shopify/Product/12345") of the created product.

        Raises:
            ValueError: If API key retrieval fails, or Shopify API returns userErrors.
            requests.exceptions.HTTPError: If the Shopify API returns an HTTP error status.
            Exception: For other API call failures, network issues, or unexpected response structures.
        """
        try:
            api_key, shop_name = self.get_access_token(store_id)
        except ValueError as ve: # Catch error from get_access_token
            logger.error(f"Failed to add product to Shopify due to credential retrieval error for store {store_id}: {ve}")
            raise # Re-raise the ValueError
            
        shop_domain = f"{shop_name}.myshopify.com"

        effective_location_id = default_location_id

        # Check if product has variants with inventory and if no location ID is provided
        if product_to_add.variants:
            has_inventory_to_set = any(
                variant.inventory_quantity is not None for variant in product_to_add.variants
            )
            if has_inventory_to_set and effective_location_id is None:
                logger.info(f"Product for store {store_id} has inventory but no location ID provided. Attempting to fetch default location.")
                fetched_loc_id = self._fetch_default_shopify_location_gid(store_id, api_version)

                if fetched_loc_id:
                    effective_location_id = fetched_loc_id
                else:
                    logger.warn(
                        f"Could not fetch default Shopify location for store {store_id}. "
                        f"Proceeding to create product without setting inventory by specific location. "
                        f"Shopify may use its overall default or not set specific quantities."
                    )
        
        mutation_query = ShopifyQueryBuilder.create_product_mutation(
            product=product_to_add,
            default_location_id=effective_location_id
        )

        graphql_url = f"https://{shop_domain}/admin/api/{api_version}/graphql.json"
        headers = {
            'Content-Type': 'application/json',
            'X-Shopify-Access-Token': api_key
        }

        logger.info(f"Attempting to add product '{product_to_add.title}' (Logical ID: {product_to_add.product_id}) to Shopify store '{shop_name}' ({store_id}) at {graphql_url}.")
        logger.debug(f"Shopify productCreate mutation for store {store_id} (Location ID used: {effective_location_id}): {mutation_query}") # Log the mutation
        
        try:
            response = requests.post(
                graphql_url,
                headers=headers,
                json={'query': mutation_query}
            )
            
            # Log status code and raw text response immediately
            logger.info(f"Shopify API response status code for store {store_id}: {response.status_code}")
            logger.info(f"Shopify API raw response text for store {store_id}: {response.text}")

            response.raise_for_status() 
            
            response_data = response.json() # This might fail if response.text is not valid JSON

            # Check response_data after attempting to parse
            if not response_data or 'data' not in response_data:
                logger.error(f"Invalid or empty parsed JSON response structure from Shopify API for store {store_id}. Parsed data: {response_data}")
                raise Exception("Invalid or empty response from Shopify API when creating product.")

            shopify_response_data_field = response_data['data']

            if not shopify_response_data_field or 'productCreate' not in shopify_response_data_field:
                if 'errors' in response_data: # Top-level errors in the JSON
                    logger.error(f"Shopify API returned top-level GraphQL errors for store {store_id}: {response_data['errors']}")
                    raise Exception(f"Shopify API GraphQL errors: {response_data['errors']}")
                logger.error(f"Shopify API response missing 'productCreate' payload for store {store_id}. Data field: {shopify_response_data_field}")
                raise Exception("Shopify API response did not contain 'productCreate' data.")

            product_create_payload = shopify_response_data_field['productCreate']
            
            if product_create_payload is None:
                if response_data.get('errors'): # Check again for errors if payload is null
                    logger.error(f"Shopify API returned errors, productCreate payload is null for store {store_id}: {response_data['errors']}")
                    raise Exception(f"Shopify API GraphQL errors: {response_data['errors']}")
                else:
                    logger.error(f"ProductCreate payload is null without specific errors for '{product_to_add.title}' (store {store_id}). Response: {response_data}")
                    raise Exception("ProductCreate payload was null in Shopify response without explicit errors.")

            user_errors = product_create_payload.get('userErrors')
            if user_errors:
                error_messages = [f"Field: {err.get('field', 'N/A')}, Message: {err.get('message', 'Unknown error')}" for err in user_errors]
                logger.error(f"Shopify userErrors on productCreate for '{product_to_add.title}' (store {store_id}): {error_messages}")
                raise ValueError(f"Shopify API returned user errors: {'; '.join(error_messages)}")
            
            product_node = product_create_payload.get('product')
            if product_node and product_node.get('id'):
                created_product_gid = product_node['id']
                logger.info(f"Product '{product_to_add.title}' successfully created in Shopify store '{shop_name}' ({store_id}) with GID: {created_product_gid}")
                return created_product_gid
            else:
                logger.error(f"Shopify productCreate response missing created product ID for '{product_to_add.title}' (store {store_id}): {product_create_payload}")
                raise Exception("Shopify productCreate response did not contain the created product's GID, despite no userErrors.")

        except requests.exceptions.HTTPError as http_err:
            # response.text is already logged above, but good to have it in the specific error context too
            logger.error(f"HTTP error occurred while adding product '{product_to_add.title}' to Shopify (store {store_id}): {http_err}. Response body: {response.text}")
            raise
        except requests.exceptions.JSONDecodeError as json_err:
            logger.error(f"JSONDecodeError parsing Shopify response for store {store_id}: {json_err}. Response text was: {response.text}")
            raise Exception(f"Failed to parse Shopify API response as JSON. Response text: {response.text}")
        except ValueError: 
            raise
        except requests.exceptions.RequestException as req_err:
            logger.error(f"Request exception occurred while adding product '{product_to_add.title}' to Shopify (store {store_id}): {req_err}")
            raise
        except Exception as e: 
            logger.error(f"Unexpected error processing Shopify product creation for '{product_to_add.title}' (store {store_id}): {str(e)}. Raw response text if available: {response.text if 'response' in locals() else 'Response object not available'}")
            raise Exception(f"Shopify API call failed for product '{product_to_add.title}' (store {store_id}): {str(e)}") from e
