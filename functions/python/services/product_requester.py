from firebase_admin import firestore
from firebase_functions import logger
from typing import Optional, List, Dict, Any
from datetime import datetime, timezone

from gaco_framework import BaseGacoManager
from gaco_framework.exceptions import AuthorizationError, NotFoundError, ValidationError

from models.allocations import (
    StoreProductRequest, 
    ProductAllocationItem,
    store_product_requests_collection, 
    ProductRequestStatus,
    ProductAllocation, 
    AllocationStatus,
    DeliveryMethod # Make sure this is imported
)
from models.access_right import AccessRight
from models.products import Product # For type hinting
from services.product_manager import ProductManager
from services.producer_manager import ProducerManager # To validate producer
from services.store_manager import StoreManager     # To validate store
from services.product_allocator import ProductAllocationManager



class ProductRequester(BaseGacoManager):
    """Manages store product requests, approvals, and rejections."""
    collection_name = store_product_requests_collection

    def _setup_dependencies(self):
        """Initializes dependent managers."""
        self.product_manager = ProductManager(db=self.db)
        self.producer_manager = ProducerManager(db=self.db)
        self.store_manager = StoreManager(db=self.db)
        self.product_allocator = ProductAllocationManager(db=self.db)

    def _validate_request_details(
        self, 
        producer_id: str, 
        items: List[ProductAllocationItem]
    ) -> None:
        """
        Validates:
        1. Store exists (conceptual check).
        2. Producer exists (conceptual check).
        3. All requested product variants exist.
        4. Products associated with variants are linked to the target producer_id.
        
        We should not validate the quantity of items here.
        Because, request itself is a signal to the producer to create a new allocation.
        Even if the product is out of stock, the request can still be made.
        By creating the validation, we loose this information.
        """
        if not items:
            raise ValidationError("Request must contain at least one item.")

        # Conceptual validation - replace with actual manager calls if they exist
        # Example:
        # if not self.store_manager.get_store(store_id): # Assuming method name
        #     raise ValueError(f"Store with ID '{store_id}' not found.")
        # if not self.producer_manager.get_producer(producer_id): # Assuming method name
        #     raise ValueError(f"Producer with ID '{producer_id}' not found.")

        for item in items:
            variant_model = self.product_manager.get_variant(item.product_variant_id)
            if not variant_model:
                raise NotFoundError(f"Requested product variant with logical ID '{item.product_variant_id}' not found.")
            
            if not variant_model.product_id:
                raise ValidationError(f"Parent product ID missing for variant '{item.product_variant_id}'. Cannot verify producer link.")

            parent_product_model = self.product_manager.get_product(variant_model.product_id)
            if not parent_product_model:
                raise NotFoundError(f"Parent product for variant '{item.product_variant_id}' (Product ID: {variant_model.product_id}) not found.")

            if parent_product_model.producer_id != producer_id:
                raise AuthorizationError(
                    f"Product '{parent_product_model.product_id}' (variant: {item.product_variant_id}) "
                    f"is not offered by producer '{producer_id}' (offered by: '{parent_product_model.producer_id}')."
                )

    def create_request(self, request_data: StoreProductRequest) -> StoreProductRequest:
        """Store creates a new product allocation request for a producer."""

        # TODO: create cloud function to same as in dua where get trigered on the document update
        # try:
        #     user_store_right = self.auth_context.custom_claims\
        #         .get("store_access_rights")\
        #         .get(request_data.store_id)

        #     user_allowed = user_store_right in [
        #         AccessRight.ADMIN.value, 
        #         AccessRight.EDITOR.value,
        #     ]
        # except Exception as e:
        #     logger.warn(f"Error getting store access right: {e}")
        #     raise AuthorizationError("You are not authorized to create requests for this store.")

        # if not user_allowed:
        #     raise AuthorizationError("You are not authorized to create requests for this store.")    
        
        if not request_data.producer_id:
            raise ValidationError("producer_id is required.")
        if not request_data.requested_items:
            raise ValidationError("At least one item must be requested.")

        self._validate_request_details(
            producer_id=request_data.producer_id,
            items=request_data.requested_items
        )
        
        request_data.status = ProductRequestStatus.PENDING_PRODUCER_APPROVAL.value
        request_data.request_date = datetime.now(timezone.utc)

        doc_ref = self.collection.document()
        doc_ref.set(request_data.model_dump(exclude_none=True, exclude={'request_id', 'related_allocation_id'}))
        
        request_data.request_id = doc_ref.id
        return request_data

    def get_request(self, request_id: str) -> StoreProductRequest:
        doc_ref = self.collection.document(request_id)
        doc = doc_ref.get()
        if not doc.exists:
            raise NotFoundError(f"Request with ID '{request_id}' not found.")
        data = doc.to_dict()
        data['request_id'] = doc.id
        return StoreProductRequest(**data)

    def approve_request(
        self, 
        request_id: str, 
        producer_response_notes: Optional[str],
        delivery_method_for_allocation: DeliveryMethod,
        allocation_producer_notes: Optional[str]
    ) -> StoreProductRequest:
        """Producer approves a store's product request, creating a ProductAllocation."""
        request_doc_ref = self.collection.document(request_id)
        request_doc = request_doc_ref.get()

        if not request_doc.exists:
            raise NotFoundError(f"Request with ID '{request_id}' not found.")
        
        request_obj = StoreProductRequest.model_validate(request_doc.to_dict())
        request_obj.request_id = request_doc.id

        logger.info(f"request_obj: {request_obj}")

        # try:
        #     user_producer_right = self.auth_context.custom_claims\
        #         .get("producer_access_rights")\
        #         .get(request_obj.producer_id)

        #     user_allowed = user_producer_right in [
        #         AccessRight.ADMIN.value, 
        #         AccessRight.EDITOR.value,
        #     ]
        # except Exception as e:
        #     logger.warn(f"Error getting producer access right: {e}")
        #     raise AuthorizationError("You are not authorized to approve requests for this producer.")

        # if not user_allowed:
        #     raise AuthorizationError("You are not authorized to approve requests for this producer.")
        
        if request_obj.status != ProductRequestStatus.PENDING_PRODUCER_APPROVAL.value:
            raise ValidationError(f"Request is not pending approval (current status: {request_obj.status}).")

        # Create the ProductAllocation
        allocation_to_create = ProductAllocation(
            producer_id=request_obj.producer_id,
            store_id=request_obj.store_id,
            items=request_obj.requested_items,
            status=AllocationStatus.PENDING_CONFIRMATION.value,
            delivery_method=delivery_method_for_allocation,
            allocation_date=datetime.now(timezone.utc),
            producer_notes=allocation_producer_notes or f"Auto-created from approved Store Request ID: {request_id}."
        )
        
        created_allocation = self.product_allocator.create_allocation(allocation_to_create)
        if not created_allocation or not created_allocation.allocation_id:
             raise Exception("Failed to create corresponding product allocation upon request approval.")

        # Update the StoreProductRequest
        update_fields = {
            "status": ProductRequestStatus.APPROVED_BY_PRODUCER.value,
            "producer_response_notes": producer_response_notes,
            "related_allocation_id": created_allocation.allocation_id,
            "last_updated_by_user_id": self.auth_context.user_id,
            "last_updated_at": firestore.SERVER_TIMESTAMP,
        }
        request_doc_ref.update({k: v for k, v in update_fields.items() if v is not None})
        
        # Return the updated request object
        request_obj.status = ProductRequestStatus.APPROVED_BY_PRODUCER.value
        request_obj.producer_response_notes = producer_response_notes
        request_obj.related_allocation_id = created_allocation.allocation_id
        return request_obj

    def reject_request(
        self, 
        request_id: str, 
        producer_response_notes: Optional[str]
    ) -> StoreProductRequest:
        request_doc_ref = self.collection.document(request_id)
        request_doc = request_doc_ref.get()
        if not request_doc.exists: 
            raise NotFoundError(f"Request ID '{request_id}' not found.")
        
        request_obj = StoreProductRequest.model_validate(request_doc.to_dict())
        request_obj.request_id = request_doc.id

        # try:
        #     user_producer_right = self.auth_context.custom_claims\
        #         .get("producer_access_rights")\
        #         .get(request_obj.producer_id)

        #     user_allowed = user_producer_right in [
        #             AccessRight.ADMIN.value, 
        #             AccessRight.EDITOR.value,
        #         ]
        # except Exception as e:
        #     logger.warn(f"Error getting producer access right: {e}")
        #     raise AuthorizationError("You are not authorized to reject requests for this producer.")

        # if not user_allowed:
        #     raise AuthorizationError("You are not authorized to reject requests for this producer.")
        
        if request_obj.status != ProductRequestStatus.PENDING_PRODUCER_APPROVAL.value:
            raise ValidationError(f"Request status is not '{ProductRequestStatus.PENDING_PRODUCER_APPROVAL.value}'.")

        update_fields = {
            "status": ProductRequestStatus.REJECTED_BY_PRODUCER.value,
            "producer_response_notes": producer_response_notes,
            "last_updated_by_user_id": self.auth_context.user_id,
            "last_updated_at": firestore.SERVER_TIMESTAMP,
        }
        request_doc_ref.update({k: v for k, v in update_fields.items() if v is not None})

        request_obj.status = ProductRequestStatus.REJECTED_BY_PRODUCER.value
        request_obj.producer_response_notes = producer_response_notes
        return request_obj

    def cancel_request(
        self, 
        request_id: str
    ) -> StoreProductRequest:
        request_doc_ref = self.collection.document(request_id)
        request_doc = request_doc_ref.get()
        if not request_doc.exists: 
            raise NotFoundError(f"Request ID '{request_id}' not found.")

        request_obj = StoreProductRequest(**request_doc.to_dict())
        request_obj.request_id = request_doc.id
        
        # try:
        #     user_store_right = self.auth_context.custom_claims\
        #         .get("store_access_rights")\
        #         .get(request_obj.store_id)

        #     user_allowed = user_store_right in [
        #         AccessRight.ADMIN.value, 
        #         AccessRight.EDITOR.value,
        #     ]
        # except Exception as e:
        #     logger.warn(f"Error getting store access right: {e}")
        #     raise AuthorizationError("You are not authorized to cancel requests for this store.")

        # if not user_allowed:
        #     raise AuthorizationError("You are not authorized to cancel requests for this store.")
        
        if request_obj.status != ProductRequestStatus.PENDING_PRODUCER_APPROVAL.value:
            raise ValidationError(f"Request status is not '{ProductRequestStatus.PENDING_PRODUCER_APPROVAL.value}'.")

        update_fields = {
            "status": ProductRequestStatus.CANCELLED_BY_STORE.value,
            "last_updated_by_user_id": self.auth_context.user_id,
            "last_updated_at": firestore.SERVER_TIMESTAMP,
        }
        request_doc_ref.update(update_fields)

        request_obj.status = ProductRequestStatus.CANCELLED_BY_STORE.value
        return request_obj

    def list_requests_for_producer(
        self, producer_id: str, status: Optional[ProductRequestStatus] = None, limit: int = 50
    ) -> List[StoreProductRequest]:
        query = self.collection.where(StoreProductRequest.PRODUCER_ID_FIELD, "==", producer_id)
        if status: query = query.where(StoreProductRequest.STATUS_FIELD, "==", status)
        
        requests = []
        for doc in query.order_by(
            StoreProductRequest.REQUEST_DATE_FIELD, 
            direction=firestore.Query.DESCENDING
        ).limit(limit).stream():
            data = doc.to_dict()
            data[StoreProductRequest.REQUEST_ID_FIELD] = doc.id
            requests.append(
                StoreProductRequest.model_validate(data)
            )
        return requests

    def list_requests_by_store(
        self, store_id: str, status: Optional[ProductRequestStatus] = None, limit: int = 50
    ) -> List[StoreProductRequest]:
        query = self.collection.where(StoreProductRequest.STORE_ID_FIELD, "==", store_id)
        if status: query = query.where(StoreProductRequest.STATUS_FIELD, "==", status)

        requests = []
        for doc in query.order_by(
            StoreProductRequest.REQUEST_DATE_FIELD, 
            direction=firestore.Query.DESCENDING
        ).limit(limit).stream():
            data = doc.to_dict()
            data[StoreProductRequest.REQUEST_ID_FIELD] = doc.id
            requests.append(
                StoreProductRequest.model_validate(data)
            )
        return requests