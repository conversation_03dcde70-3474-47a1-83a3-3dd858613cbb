import json
from datetime import datetime, timedelta, timezone
import requests
from typing import Dict, <PERSON>, Tu<PERSON>, Optional
from firebase_admin import storage, firestore, functions
from firebase_functions import logger
from gaco_secrets.secret_manager import SecretManager
from queries.shopify_query_builder import ShopifyQueryBuilder
import time
from pydantic import BaseModel, Field, field_validator
from enum import Enum
import os
from google.oauth2 import service_account
from models.shopify_product_get_operation import (
    ShopifyProductGetOperation, ShopifyGetOperationStatus, shopify_product_fetch_operations_collection
)
from constants.gaco_values import bucket_name, product_getter_task_queue

# --- Constants (Ideally in constants/gaco_values.py) ---
BUCKET_NAME = bucket_name # Assuming bucket_name is similar to ShopifyOrderGetter or defined elsewhere
SHOPIFY_PRODUCT_FETCH_OPERATIONS_COLLECTION = shopify_product_fetch_operations_collection # Firestore collection for product operations

class ShopifyProductGetter:
    """Service for getting products from Shopify."""
    
    def __init__(self, db: firestore.Client, secret_manager: SecretManager):
        self.db = db
        self.secret_manager = secret_manager
        self.storage_bucket = storage.bucket(BUCKET_NAME) # Use the defined BUCKET_NAME
        
    def get_access_token(self, store_id: str) -> Tuple[str, str]:
        """Get Shopify API access token and shop name from Secret Manager."""
        try:
            secret_doc_ref = self.db.collection('customer_api_keys').document(store_id)
            secret_doc = secret_doc_ref.get()
            
            if not secret_doc.exists:
                raise ValueError(f"No API key found for shop {store_id}")
            
            secret_data = secret_doc.to_dict()
            secret_version_path = secret_data.get('secret_version_path')
            shop_name = secret_data.get('shop_name')
            
            if not secret_version_path or not shop_name:
                raise ValueError(f"Invalid secret data for shop {store_id}")
            
            api_key = self.secret_manager.get_secret(secret_version_path)
            
            return api_key, shop_name
        
        except Exception as e:
            logger.error(f"Error getting access token for {store_id}: {str(e)}")
            raise ValueError(f"Failed to fetch access token for {store_id}: {str(e)}")

    def fetch_initial_products(
            self, 
            store_id: str, 
            product_status: str = 'ACTIVE', # Default to ACTIVE status
            num_products_per_page: int = 100, # Shopify default is 50, max 250
            num_variants_per_product: int = 10 # Shopify default is 10, max 250
        ) -> Dict[str, Any]:
        """
        Fetch the first page of products and initiate background processing for the rest.
        """
        api_key, shop_name = self.get_access_token(store_id)
        shop_domain = f"{shop_name}.myshopify.com"
        
        logger.info(f"Fetching initial page of products: {{'productStatus': {product_status}, 'shopId': {store_id}}}")
        
        query = ShopifyQueryBuilder.get_products_query(
            cursor=None, 
            num_products=num_products_per_page, 
            num_variants=num_variants_per_product,
            status=product_status
        )
        
        response = requests.post(
            f"https://{shop_domain}/admin/api/2024-10/graphql.json", # Use the latest stable or appropriate API version
            headers={
                'Content-Type': 'application/json',
                'X-Shopify-Access-Token': api_key
            },
            json={'query': query}
        )
        response.raise_for_status()
        result = response.json()
        
        if 'errors' in result:
            error_msg = result['errors'][0]['message'] if result['errors'] else 'Unknown GraphQL error'
            logger.error(f"GraphQL error fetching products for {store_id}: {error_msg}")
            raise ValueError(f"Shopify GraphQL error: {error_msg}")
        
        data = result.get('data')
        if not data or 'products' not in data:
            logger.error(f"No products data returned from Shopify for {store_id}. Response: {result}")
            raise ValueError('No products data returned from Shopify')
        
        products_data_node = data['products']
        first_page_products = products_data_node['edges']
        
        product_getter_id = f"shopify_products_{store_id}_{datetime.now(timezone.utc).strftime('%Y%m%d_%H%M%S_%f')}"
        base_path = f"stores/{store_id}/products/{datetime.now(timezone.utc).strftime('%Y/%m/%d')}/{product_getter_id}"
        
        first_page_path = f"{base_path}/page_1.json"
        first_page_content = json.dumps({"products": {"edges": first_page_products}}, indent=2)
        
        blob = self.storage_bucket.blob(first_page_path)
        blob.upload_from_string(first_page_content, content_type='application/json')
        logger.info(f"First page of products saved to GCS: {first_page_path} for {store_id}")

        page_info = products_data_node['pageInfo']
        has_next_page = page_info.get('hasNextPage', False)
        next_cursor = page_info.get('endCursor') if has_next_page else None

        operation_data = ShopifyProductGetOperation(
            product_getter_id=product_getter_id,
            store_id=store_id,
            shop_name=shop_name,
            status_filter=product_status,
            next_cursor=next_cursor,
            has_next_page=has_next_page,
            base_path=base_path,
            pages_fetched=1,
            total_products=len(first_page_products),
            created_at=datetime.now(timezone.utc),
            status=ShopifyGetOperationStatus.IN_PROGRESS.value if has_next_page else ShopifyGetOperationStatus.COMPLETED.value,
            completed_at=datetime.now(timezone.utc) if not has_next_page else None
        )
        
        self.db.collection(SHOPIFY_PRODUCT_FETCH_OPERATIONS_COLLECTION)\
            .document(product_getter_id)\
            .set(operation_data.model_dump(by_alias=True))

        if has_next_page:
            # we don't have task queue yet
            task_payload = {
                'data': {
                    ShopifyProductGetOperation.PRODUCT_GETTER_ID_FIELD: product_getter_id
                }
            }
            # # Ensure PRODUCT_GETTER_TASK_QUEUE is correctly configured in your Firebase project
            task_queue = functions.task_queue(product_getter_task_queue)
            task_queue.enqueue(task_payload)
            logger.info(f"Enqueued task for continuing product fetch for {product_getter_id} of store {store_id}")
        else:
            logger.info(f"Product fetch completed in a single page for {product_getter_id} of store {store_id}")

        download_url = None
        key_path = os.getenv("GOOGLE_APPLICATION_CREDENTIALS")
        if key_path:
            try:
                credentials = service_account.Credentials.from_service_account_file(
                    key_path,
                    scopes=["https://www.googleapis.com/auth/devstorage.read_only"]
                )
                download_url = blob.generate_signed_url(
                    version="v4",
                    expiration=datetime.now(timezone.utc) + timedelta(hours=24),
                    method="GET",
                    credentials=credentials
                )
            except Exception as e:
                logger.warning(f"Could not generate signed URL for {first_page_path}: {e}")
        
        return {
            ShopifyProductGetOperation.PRODUCT_GETTER_ID_FIELD: product_getter_id,
            ShopifyProductGetOperation.DOWNLOAD_URL_FIELD: download_url,
            ShopifyProductGetOperation.FILE_PATH_FIELD: first_page_path,
            ShopifyProductGetOperation.PRODUCTS_IN_FIRST_PAGE_FIELD: len(first_page_products),
            ShopifyProductGetOperation.HAS_MORE_PAGES_FIELD: has_next_page,
            ShopifyProductGetOperation.STATUS_FIELD: operation_data.status,
            ShopifyProductGetOperation.STORE_ID_FIELD: store_id,
            ShopifyProductGetOperation.STATUS_FILTER_FIELD: product_status
        }
    
    def continue_getting_products(self, product_getter_id: str, num_products_per_page: int = 100, num_variants_per_product: int = 10) -> Dict[str, Any]:
        """
        Continue getting remaining pages of products.
        This function is meant to be called as a background process.
        """
        operation_doc_ref = self.db.collection(SHOPIFY_PRODUCT_FETCH_OPERATIONS_COLLECTION).document(product_getter_id)
        operation_doc = operation_doc_ref.get()
        
        if not operation_doc.exists:
            logger.error(f"Product fetch operation {product_getter_id} not found.")
            return {'error': f"Product fetch operation {product_getter_id} not found"}
        
        try:
            state = ShopifyProductGetOperation.model_validate(operation_doc.to_dict())
        except Exception as e:
            logger.error(f"Invalid operation state for {product_getter_id}: {e}")
            # Optionally update Firestore with a FAILED status here if model validation fails severely
            operation_doc_ref.update({
                ShopifyProductGetOperation.STATUS_FIELD: ShopifyGetOperationStatus.FAILED.value,
                ShopifyProductGetOperation.ERROR_MESSAGE_FIELD: f"Invalid operation state: {str(e)}",
                ShopifyProductGetOperation.ERROR_TIME_FIELD: datetime.now(timezone.utc)
            })
            return {'error': f"Invalid operation state for {product_getter_id}: {str(e)}"}


        if state.status == ShopifyGetOperationStatus.COMPLETED.value:
             logger.info(f"Product fetch operation {product_getter_id} is already completed.")
             return {
                ShopifyProductGetOperation.PRODUCT_GETTER_ID_FIELD: product_getter_id,
                ShopifyProductGetOperation.STATUS_FIELD: ShopifyGetOperationStatus.COMPLETED.value,
                ShopifyProductGetOperation.TOTAL_PRODUCTS_FIELD: state.total_products,
                ShopifyProductGetOperation.PAGES_FETCHED_FIELD: state.pages_fetched
            }
        
        if state.status == ShopifyGetOperationStatus.FAILED.value:
            logger.warning(f"Product fetch operation {product_getter_id} is already marked as FAILED. Error: {state.error_message}")
            return {
                ShopifyProductGetOperation.PRODUCT_GETTER_ID_FIELD: product_getter_id,
                ShopifyProductGetOperation.STATUS_FIELD: ShopifyGetOperationStatus.FAILED.value,
                ShopifyProductGetOperation.ERROR_MESSAGE_FIELD: state.error_message
            }
            
        store_id = state.store_id
        shop_name = state.shop_name
        product_status_filter = state.status_filter
        cursor = state.next_cursor
        base_path = state.base_path
        pages_fetched = state.pages_fetched
        total_products = state.total_products
        
        api_key, _ = self.get_access_token(store_id) # shop_name already in state
        shop_domain = f"{shop_name}.myshopify.com"
        
        current_has_next_page = state.has_next_page
        
        try:
            while current_has_next_page and cursor:
                pages_fetched += 1
                logger.info(f"Fetching product page {pages_fetched} for {product_getter_id} (shop: {store_id}, cursor: {cursor})")
                
                query = ShopifyQueryBuilder.get_products_query(
                    cursor=cursor, 
                    num_products=num_products_per_page,
                    num_variants=num_variants_per_product,
                    status=product_status_filter
                )
                
                response = requests.post(
                    f"https://{shop_domain}/admin/api/2024-10/graphql.json",
                    headers={'Content-Type': 'application/json', 'X-Shopify-Access-Token': api_key},
                    json={'query': query}
                )
                response.raise_for_status()
                result = response.json()

                if 'errors' in result:
                    error_msg = result['errors'][0]['message'] if result['errors'] else 'Unknown GraphQL error'
                    raise ValueError(f"GraphQL error on page {pages_fetched} for {product_getter_id}: {error_msg}")
                
                data = result.get('data')
                if not data or 'products' not in data:
                    raise ValueError(f'No products data returned from Shopify on page {pages_fetched} for {product_getter_id}')
                
                products_data_node = data['products']
                page_products = products_data_node['edges']
                total_products += len(page_products)
                
                page_path = f"{base_path}/page_{pages_fetched}.json"
                page_content = json.dumps({"products": {"edges": page_products}}, indent=2)
                
                blob = self.storage_bucket.blob(page_path)
                blob.upload_from_string(page_content, content_type='application/json')
                
                page_info = products_data_node['pageInfo']
                current_has_next_page = page_info.get('hasNextPage', False)
                cursor = page_info.get('endCursor') if current_has_next_page else None
                
                update_data = {
                    ShopifyProductGetOperation.NEXT_CURSOR_FIELD: cursor,
                    ShopifyProductGetOperation.HAS_NEXT_PAGE_FIELD: current_has_next_page,
                    ShopifyProductGetOperation.PAGES_FETCHED_FIELD: pages_fetched,
                    ShopifyProductGetOperation.TOTAL_PRODUCTS_FIELD: total_products,
                    ShopifyProductGetOperation.LAST_UPDATED_AT_FIELD: datetime.now(timezone.utc)
                }
                operation_doc_ref.update(update_data)
                
                if current_has_next_page:
                    time.sleep(0.6) # Shopify API rate limit: 2 requests/second for standard shops, 20 for Plus. (GraphQL points system is more complex)
            
            final_status = ShopifyGetOperationStatus.COMPLETED.value
            completion_time = datetime.now(timezone.utc)
            operation_doc_ref.update({
                ShopifyProductGetOperation.STATUS_FIELD: final_status,
                ShopifyProductGetOperation.COMPLETED_AT_FIELD: completion_time,
                ShopifyProductGetOperation.PAGES_FETCHED_FIELD: pages_fetched, # ensure final count is set
                ShopifyProductGetOperation.TOTAL_PRODUCTS_FIELD: total_products, # ensure final count is set
                ShopifyProductGetOperation.HAS_NEXT_PAGE_FIELD: False, # ensure this is false on completion
                ShopifyProductGetOperation.NEXT_CURSOR_FIELD: None # ensure this is null on completion
            })
            
            logger.info(f"Completed product fetch operation {product_getter_id} for store {store_id}. Total products: {total_products}, Pages: {pages_fetched}")
            return {
                ShopifyProductGetOperation.PRODUCT_GETTER_ID_FIELD: product_getter_id,
                ShopifyProductGetOperation.TOTAL_PRODUCTS_FIELD: total_products,
                ShopifyProductGetOperation.PAGES_FETCHED_FIELD: pages_fetched,
                ShopifyProductGetOperation.STATUS_FIELD: final_status
            }
            
        except Exception as e:
            error_message = f"Error processing product fetch {product_getter_id} for store {store_id}: {str(e)}"
            logger.error(error_message, exc_info=True)
            operation_doc_ref.update({
                ShopifyProductGetOperation.STATUS_FIELD: ShopifyGetOperationStatus.FAILED.value,
                ShopifyProductGetOperation.ERROR_MESSAGE_FIELD: str(e), # Store concise error
                ShopifyProductGetOperation.ERROR_TIME_FIELD: datetime.now(timezone.utc),
                ShopifyProductGetOperation.LAST_UPDATED_AT_FIELD: datetime.now(timezone.utc)
            })
            return {
                ShopifyProductGetOperation.PRODUCT_GETTER_ID_FIELD: product_getter_id,
                ShopifyProductGetOperation.STATUS_FIELD: ShopifyGetOperationStatus.FAILED.value,
                ShopifyProductGetOperation.ERROR_MESSAGE_FIELD: str(e)
            }
