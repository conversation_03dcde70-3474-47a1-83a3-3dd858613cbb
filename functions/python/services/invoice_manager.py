from google.cloud import storage
from firebase_admin import firestore
import logging
from typing import Dict, List, Optional, Tuple, Any
import polars as pl
from models.response import ResponseData
from models.requests.invoice_request import CreateInvoiceRequest
from data_generator.invoice_data_generator import generate_invoice_data_handler
from reporter.invoice_reporter import InvoiceReporter
from models.sales import sales_gold_collection
from constants.gaco_values import bucket_name
from firebase_functions import logger


class InvoiceManager:
    """
    Manages invoice operations like deletion and creation.
    Separates business logic from cloud function handlers for better testability.
    """
    
    def __init__(self, 
                 db: Optional[firestore.Client] = None,
                 bucket_name: str = bucket_name,
                 template_file_name: str = 'invoice-template-v2.html'):
        """
        Initialize the InvoiceManager with a Firestore client and bucket name.
        
        Args:
            db: Firestore client. If None, will create a new client.
            bucket_name: Name of the Cloud Storage bucket containing invoices.
            template_file_name: Name of the invoice template file.
        """
        self.db = db or firestore.client()
        self.bucket_name = bucket_name
        self.template_file_name = template_file_name
        
    def create_invoices(self, invoice_request: CreateInvoiceRequest) -> Tuple[bool, List[Dict[str, Any]], str]:
        """
        Create invoices for a given store and date range.
        
        Args:
            invoice_request: The invoice request containing store_id, start_date, and end_date.
            
        Returns:
            Tuple containing:
            - Success flag (bool)
            - List of created invoice data (list of dicts)
            - Error message if any (str)
        """
        try:
            # Initialize InvoiceDataGenerator to get the sales data
            df = generate_invoice_data_handler(self.db, invoice_request)
            
            if df.height == 0:
                return False, [], "No sales data found for the specified period"
            
            # Use InvoiceReporter to generate invoice reports
            reporter = InvoiceReporter(
                invoice_data_frame=df,
                template_file_name=self.template_file_name,
                db=self.db,
                bucket_name=self.bucket_name
            )
            
            report_dicts = reporter.generate_report()
            
            # Format the output data for easier consumption
            formatted_invoices = [
                {
                    'invoice_doc_id': f"{invoice['store_id']}-{invoice['invoice_id']}",
                    'invoice_id': invoice['invoice_id'],
                    'download_url': invoice['download_url'], 
                    'object_path': invoice['uri'],
                    'status': invoice['status'],
                    'store_id': invoice['store_id'],
                    'affected_sales': invoice['sale_ids']
                } for invoice in report_dicts
            ]
            
            logger.info(f"Created {len(formatted_invoices)} invoices for store {invoice_request.store_id}")
            return True, formatted_invoices, ""
            
        except Exception as e:
            error_message = f"Error creating invoices: {str(e)}"
            logger.error(error_message)
            return False, [], error_message
    
    def delete_invoice(self, invoice_doc_id: str) -> Tuple[bool, Dict[str, Any], str]:
        """
        Delete an invoice and revert all related changes.
        
        Args:
            invoice_doc_id: The document ID of the invoice to delete
                           (in format {store_id}-{invoice_id})
        
        Returns:
            Tuple containing:
            - Success flag (bool)
            - Result data (dict)
            - Error message if any (str)
        """
        try:
            # 1. Get the invoice document from Firestore
            invoice_data, error = self._get_invoice_document(invoice_doc_id)
            if error:
                return False, {}, error
            
            store_id = invoice_data.get('store_id')
            invoice_id = invoice_data.get('invoice_id')
            sale_ids = invoice_data.get('sale_ids', [])
            
            # 2. Remove invoice reference from all related sales-gold documents
            self._remove_invoice_references(invoice_doc_id, sale_ids)
            
            # 3. Delete the invoice document from Firestore
            self._delete_invoice_document(invoice_doc_id)
            
            # 4. Delete the HTML file from Cloud Storage
            if store_id and invoice_id:
                self._delete_invoice_file(store_id, invoice_id)
            
            # Prepare result data
            result_data = {
                'invoice_doc_id': invoice_doc_id,
                'store_id': store_id,
                'invoice_id': invoice_id,
                'affected_sales': sale_ids
            }
            
            return True, result_data, ""
            
        except Exception as e:
            error_message = f"Error deleting invoice: {str(e)}"
            logger.error(error_message)
            return False, {}, error_message
    
    def _get_invoice_document(self, invoice_doc_id: str) -> Tuple[Dict[str, Any], str]:
        """
        Get the invoice document from Firestore.
        
        Args:
            invoice_doc_id: The document ID of the invoice
            
        Returns:
            Tuple containing:
            - Invoice data (dict)
            - Error message if any (str)
        """
        invoice_ref = self.db.collection('invoices').document(invoice_doc_id)
        invoice_doc = invoice_ref.get()
        
        if not invoice_doc.exists:
            return {}, f'Invoice document {invoice_doc_id} not found'
        
        return invoice_doc.to_dict(), ""
    
    def _remove_invoice_references(self, invoice_doc_id: str, sale_ids: List[str]) -> None:
        """
        Remove invoice references from all related sales-gold documents.
        
        Args:
            invoice_doc_id: The document ID of the invoice
            sale_ids: List of sale IDs associated with the invoice
        """
        for sale_id in sale_ids:
            sale_doc_ref = self.db.collection(sales_gold_collection).document(sale_id)
            sale_doc = sale_doc_ref.get()
            
            if sale_doc.exists:
                sale_doc_data = sale_doc.to_dict()
                if sale_doc_data.get('invoice_id') == invoice_doc_id:
                    # Remove the invoice_id field
                    sale_doc_ref.update({'invoice_id': firestore.DELETE_FIELD})
                    logger.info(f"Removed invoice reference from sale {sale_id}")
            else:
                logger.warning(f"Sale document {sale_id} not found in sales-gold collection")
    
    def _delete_invoice_document(self, invoice_doc_id: str) -> None:
        """
        Delete the invoice document from Firestore.
        
        Args:
            invoice_doc_id: The document ID of the invoice
        """
        invoice_ref = self.db.collection('invoices').document(invoice_doc_id)
        invoice_ref.delete()
        logger.info(f"Deleted invoice document {invoice_doc_id}")
    
    def _delete_invoice_file(self, store_id: str, invoice_id: str) -> None:
        """
        Delete the invoice HTML file from Cloud Storage.
        
        Args:
            store_id: The store ID
            invoice_id: The invoice ID
        """
        storage_client = storage.Client()
        bucket = storage_client.bucket(self.bucket_name)
        
        blob_path = f'stores/{store_id}/invoices/{invoice_id}.html'
        blob = bucket.blob(blob_path)
        
        if blob.exists():
            blob.delete()
            logger.info(f"Deleted invoice file: {blob_path}")
        else:
            logger.warning(f"Invoice file not found: {blob_path}") 


    def find_invoice_by_producer_id(self, producer_id: str) -> Tuple[bool, Dict[str, Any], str]:
        """
        Find an invoice by producer_id.
        """
        invoices = self.db.collection('invoices')\
            .where('producer_id', '==', producer_id)\
            .get()
        if not invoices:
            return False, {}, "No invoices found for producer_id"
        return True, invoices, ""
