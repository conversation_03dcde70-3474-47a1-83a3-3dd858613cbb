from firebase_functions import storage_fn, logger, options
from firebase_admin import storage, firestore
from hasher.shopify_gid import shopify_gid_to_hash
from columns.shopify_raw_order_columns import ShopifyOrderColumnNames
from parsers.shopify_raw_orders import ShopifyOrderParser
from extractor.store_id import extract_store_id
from models.sales import Sale
from sanitizer.sales_sanitizer_factory import SalesSanitizerFactory
import json
import pandas as pd
from typing import Union, Optional
from firebase_admin import firestore
from queries.stores_query_builder_v2 import StoresQueryBuilderV2
from models.store import StoreV2


class ShopifyOrderProcessor:
    """Class to process Shopify order files and store them in Firestore."""
    
    def __init__(self, db: firestore.client =None):
        """Initialize with optional firestore client for testing."""
        self.db = db
        self.shopify_order_parser = ShopifyOrderParser()
        self.sales_sanitizer = SalesSanitizerFactory.create(db)
        self.store_query = StoresQueryBuilderV2(db)
        
    def process_file(self, file_content: Union[str, bytes], file_path: str) -> None:
        """Process a Shopify order file and store the data in Firestore.
        
        Args:
            file_content: The content of the file as string or bytes
            file_path: The path of the file (used to extract store_id)
        """
        logger.info(f"Processing Shopify orders file: {file_path}")
        
        # Extract store ID from file path
        store_id = extract_store_id(file_path)
        
        # Parse the orders
        try:
            if isinstance(file_content, bytes):
                file_content = file_content.decode('utf-8')
                
            df = self.shopify_order_parser.parse_shopify_orders(
                file_content, 
                store_id
            )
        except Exception as e:
            logger.error(f"Error parsing Shopify orders: {e}")
            raise

        if self.db is None:
            self._dump_orders_locally(df, store_id)
        else:
            # Process the orders
            self._store_orders_in_firestore(df, store_id)
        
    def _store_orders_in_firestore(self, df: pd.DataFrame, store_id: str) -> None:
        """Store the parsed orders in Firestore. Execution of this function is per
        store_id purpose, as trigger is on raw_json order creation !"""
        # Convert all rows to dicts and prepare batch
        batch = self.db.batch()

        store_doc = self.store_query.for_id(store_id).build().get()
        logger.info(f"store_id: {store_id}")
        logger.info(f"Store doc: {store_doc.to_dict()}")

        if not store_doc.exists:
            logger.error(f"Store doc does not exist for store_id: {store_id}")
            raise ValueError(f"Store doc does not exist for store_id: {store_id}")

        store_doc = StoreV2.model_validate(store_doc.to_dict())

        store_display_name = store_doc.display_name
        
        sales_sanitizer = self.sales_sanitizer
        
        for _, row in df.iterrows():
            # Convert row to dict, handling any non-serializable objects
            sale_data = json.loads(row.to_json())
            sale_data['store_display_name'] = store_display_name
            sale = Sale(**sale_data)

            # Add to batch with auto-generated document reference
            document_id = shopify_gid_to_hash(
                row[ShopifyOrderColumnNames.LINE_ITEM_ID.value]
            )

            sanitized_sale = sales_sanitizer.sanitize_sales_from_sale_object(
                sale,
                document_id=document_id
            )

            logger.info(f"Sanitized sale: {sanitized_sale}")

            sales_collection = self.db.collection(sanitized_sale['collection'])
            doc_ref = sales_collection.document(document_id)
            batch.set(doc_ref, sanitized_sale['sale'])
        
        # Commit the batch
        batch.commit()

    def _dump_orders_locally(self, df: pd.DataFrame, store_id: str) -> None:
        """Dump the parsed orders to a local file."""
        df.to_csv(f'{store_id}_orders.csv', index=False)
