import json
from datetime import datetime, timedelta, timezone
import requests
from typing import Dict, <PERSON>, <PERSON><PERSON>
from firebase_admin import storage, firestore, functions
from firebase_functions import logger
from gaco_secrets.secret_manager import SecretManager
from queries.shopify_query_builder import ShopifyQueryBuilder
import time
from models.shopify_get_operation import (
  ShopifyGetOperation, ShopifyGetOperationStatus, shopify_fetch_operations_collection
)
from google.oauth2 import service_account
import os
from constants.gaco_values import bucket_name, order_getter_task_queue


class ShopifyOrderGetter:
    """Service for getting orders from Shopify."""
    
    def __init__(self, db: firestore.Client, secret_manager: SecretManager):
        self.db = db
        self.secret_manager = secret_manager
        self.storage_bucket = storage.bucket(bucket_name)
        
    def get_access_token(self, store_id: str) -> Tuple[str, str]:
        """Get Shopify API access token and shop name from Secret Manager."""
        try:
            # Retrieve the stored secret document
            secret_doc = self.db.collection('customer_api_keys').document(store_id).get()
            
            if not secret_doc.exists:
                raise ValueError(f"No API key found for shop {store_id}")
            
            secret_data = secret_doc.to_dict()
            secret_version_path = secret_data.get('secret_version_path')
            shop_name = secret_data.get('shop_name')
            
            if not secret_version_path or not shop_name:
                raise ValueError(f"Invalid secret data for shop {store_id}")
            
            # Get the actual secret value
            api_key = self.secret_manager.get_secret(secret_version_path)
            
            return api_key, shop_name
        
        except Exception as e:
            logger.error(f"Error getting access token: {str(e)}")
            raise ValueError(f"Failed to fetch access token: {str(e)}")

    def fetch_initial_orders(
            self, 
            store_id: str, 
            start_date: str, 
            end_date: str,
            cursor: str | None = None
        ) -> Dict[str, Any]:
        """
        Fetch the first page of orders and initiate background processing for the rest.
        
        This function is meant to be called directly from an HTTPS function.
        """
        # Get Shopify API access token
        api_key, shop_name = self.get_access_token(store_id)
        shop_domain = f"{shop_name}.myshopify.com"
        
        logger.info(f"Fetching initial page of orders: {{'startDate': {start_date}, 'endDate': {end_date}, 'shopId': {store_id}}}")
        
        # Get first page of orders
        query = ShopifyQueryBuilder.get_orders_query(start_date, end_date, cursor=None)
        
        # Make the request to Shopify API
        response = requests.post(
            f"https://{shop_domain}/admin/api/2024-10/graphql.json",
            headers={
                'Content-Type': 'application/json',
                'X-Shopify-Access-Token': api_key
            },
            json={
                'query': query
            }
        )

        response.raise_for_status()

        result = response.json()
        
        # Handle errors
        if 'errors' in result:
            error_msg = result['errors'][0]['message'] if result['errors'] else 'Unknown GraphQL error'
            logger.error(f"GraphQL error: {error_msg}")
            raise ValueError(f"Shopify GraphQL error: {error_msg}")
        
        # Extract data
        data = result.get('data')
        if not data or 'orders' not in data:
            raise ValueError('No orders data returned from Shopify')
        
        # Save first page to storage
        orders_data = data['orders']
        first_page_orders = orders_data['edges']
        
        # Generate a unique ID for this fetch operation
        order_getter_id = f"shopify_orders_{store_id}_{datetime.now(timezone.utc).strftime('%Y%m%d_%H%M%S')}"
        
        # Create the base path for all files in this fetch operation
        base_path = f"stores/{store_id}/orders/{datetime.now(timezone.utc).strftime('%Y/%m/%d')}/{order_getter_id}"
        
        # Save first page
        first_page_path = f"{base_path}/page_1.json"
        first_page_data = json.dumps({"orders": {"edges": first_page_orders}}, indent=2)
        
        blob = self.storage_bucket.blob(first_page_path)
        blob.upload_from_string(
            first_page_data,
            content_type='application/json'
        )
        
        logger.info(f"Blob saved at: {blob}")

        # Check if we need to fetch more pages
        page_info = orders_data['pageInfo']
        has_next_page = page_info.get('hasNextPage', False)
        next_cursor = page_info.get('endCursor') if has_next_page else None

        logger.info(f"Page info: {page_info}")
        logger.info(f"Has next page: {has_next_page}")
        
        # If more pages exist, trigger background processing
        if has_next_page:
            # Save pagination state to Firestore for the background process

            pagination_state = ShopifyGetOperation(
                order_getter_id=order_getter_id,
                store_id=store_id,
                shop_name=shop_name,
                start_date=start_date,
                end_date=end_date,
                next_cursor=next_cursor,
                has_next_page=has_next_page,
                base_path=base_path,
                pages_fetched=1,
                total_orders=len(first_page_orders),
                created_at=datetime.now(timezone.utc),
                status=ShopifyGetOperationStatus.IN_PROGRESS.value
            )
            
            logger.info(f"Pagination state: {pagination_state}")

            # Store pagination state
            self.db.collection(shopify_fetch_operations_collection)\
                .document(order_getter_id)\
                .set(pagination_state.model_dump())
            
            
            # Create a task for the background process
            task_payload = {
                'data': {
                    'order_getter_id': order_getter_id
                }
            }
            
            # Get a reference to the task queue function
            # this is necessary due to some odd reason, 
            # the documentation is old.
            # TODO: clean up
            task_queue = functions.task_queue(order_getter_task_queue)

            logger.info(f"Task name: continueGettingOrders")
            # Enqueue the task
            task_queue.enqueue(task_payload)
            
            logger.info(f"Initiated background processing for remaining pages. Fetch ID: {order_getter_id}")
        else:
            # No more pages, mark as complete
            self.db.collection(shopify_fetch_operations_collection).document(order_getter_id).set({
                ShopifyGetOperation.FETCH_ID_FIELD: order_getter_id,
                ShopifyGetOperation.STORE_ID_FIELD: store_id,
                ShopifyGetOperation.BASE_PATH_FIELD: base_path,
                ShopifyGetOperation.PAGES_FETCHED_FIELD: 1,
                ShopifyGetOperation.TOTAL_ORDERS_FIELD: len(first_page_orders),
                ShopifyGetOperation.CREATED_AT_FIELD: datetime.now(timezone.utc),
                ShopifyGetOperation.COMPLETED_AT_FIELD: datetime.now(timezone.utc),
                ShopifyGetOperation.STATUS_FIELD: ShopifyGetOperationStatus.COMPLETED.value,
                ShopifyGetOperation.START_DATE_FIELD: start_date,
                ShopifyGetOperation.END_DATE_FIELD: end_date
            })
        
        # Before generating the signed URL:
        credentials = None
        key_path = os.getenv("GOOGLE_APPLICATION_CREDENTIALS")
        
        download_url = None

        if key_path:
            try:
                # lets change to this to be based on the firestore security rules
                credentials = service_account.Credentials.from_service_account_file(
                    key_path,
                    scopes=["https://www.googleapis.com/auth/devstorage.read_only"]
                )
                download_url = blob.generate_signed_url(
                    version="v4",
                    expiration=datetime.now(timezone.utc) + timedelta(hours=24),
                    method="GET",
                    credentials=credentials
                )

            except Exception as e:
                logger.warning(f"Could not load service account from {key_path}: {e}")
        
        # Generate signed URL with explicit credentials
        logger.info(f"Download URL: {download_url}")
        
        # Return information about the first page and the background process
        return {
            ShopifyGetOperation.FETCH_ID_FIELD: order_getter_id,
            ShopifyGetOperation.DOWNLOAD_URL_FIELD: download_url,
            ShopifyGetOperation.FILE_PATH_FIELD: first_page_path,
            ShopifyGetOperation.ORDERS_IN_FIRST_PAGE_FIELD: len(first_page_orders),
            ShopifyGetOperation.HAS_MORE_PAGES_FIELD: has_next_page,
            ShopifyGetOperation.STATUS_FIELD: (
                ShopifyGetOperationStatus.IN_PROGRESS.value 
                if has_next_page 
                else ShopifyGetOperationStatus.COMPLETED.value
            ),
            ShopifyGetOperation.STORE_ID_FIELD: store_id,
            ShopifyGetOperation.START_DATE_FIELD: start_date,
            ShopifyGetOperation.END_DATE_FIELD: end_date
        }
    
    def continue_getting_orders(self, order_getter_id: str) -> Dict[str, Any]:
        """
        Continue getting remaining pages of orders.
        
        This function is meant to be called as a background process.
        """
        # Get the pagination state
        fetch_doc = self.db.collection(shopify_fetch_operations_collection).document(order_getter_id).get()
        
        if not fetch_doc.exists:
            logger.error(f"Fetch operation {order_getter_id} not found")
            return {'error': 'Fetch operation not found'}
        
        # Get state data
        state = fetch_doc.to_dict()

        logger.info(f"State: {state}")

        state = ShopifyGetOperation.model_validate(state)
        store_id = state.store_id
        shop_name = state.shop_name
        start_date = state.start_date
        end_date = state.end_date
        cursor = state.next_cursor
        base_path = state.base_path
        pages_fetched = state.pages_fetched
        total_orders = state.total_orders
        
        # Securely fetch the API key from Secret Manager
        api_key, _ = self.get_access_token(store_id)
        
        # Shop domain for API requests
        shop_domain = f"{shop_name}.myshopify.com"
        
        # Flag to continue pagination
        has_next_page = True
        
        try:
            # Fetch remaining pages
            while has_next_page and cursor:
                pages_fetched += 1
                logger.info(f"Fetching page {pages_fetched} for {order_getter_id} (cursor: {cursor})")
                
                # Get next page of orders
                query = ShopifyQueryBuilder.get_orders_query(start_date, end_date, cursor)
                
                # Make API request
                response = None
                response = requests.post(
                    f"https://{shop_domain}/admin/api/2024-10/graphql.json",
                    headers={
                        'Content-Type': 'application/json',
                        'X-Shopify-Access-Token': api_key
                    },
                    json={
                        'query': query
                    }
                )

                response.raise_for_status()
                
                result = response.json()
                # Handle errors
                if 'errors' in result:
                    error_msg = result['errors'][0]['message'] if result['errors'] else 'Unknown GraphQL error'
                    raise ValueError(f"GraphQL error: {error_msg}")
                
                # Extract data
                data = result.get('data')
                if not data or 'orders' not in data:
                    raise ValueError('No orders data returned from Shopify')
                
                # Get orders from this page
                page_orders = data['orders']['edges']
                total_orders += len(page_orders)
                
                # Save this page
                page_path = f"{base_path}/page_{pages_fetched}.json"
                page_data = json.dumps({"orders": {"edges": page_orders}}, indent=2)
                
                blob = self.storage_bucket.blob(page_path)
                blob.upload_from_string(
                    page_data,
                    content_type='application/json'
                )
                
                # Update pagination state
                page_info = data['orders']['pageInfo']
                has_next_page = page_info.get('hasNextPage', False)
                cursor = page_info.get('endCursor') if has_next_page else None
                
                # Update state in Firestore
                self.db.collection(shopify_fetch_operations_collection).document(order_getter_id).update({
                    ShopifyGetOperation.NEXT_CURSOR_FIELD: cursor,
                    ShopifyGetOperation.HAS_NEXT_PAGE_FIELD: has_next_page,
                    ShopifyGetOperation.PAGES_FETCHED_FIELD: pages_fetched,
                    ShopifyGetOperation.TOTAL_ORDERS_FIELD: total_orders,
                    ShopifyGetOperation.LAST_UPDATED_FIELD: datetime.now(timezone.utc)
                })
                
                # Add delay to avoid rate limiting
                if has_next_page:
                    time.sleep(0.5)
            
            # All pages fetched, create combined file (optional)
            # This could be another background task for very large datasets
            
            # Mark operation as complete
            self.db.collection(shopify_fetch_operations_collection).document(order_getter_id).update({
                ShopifyGetOperation.STATUS_FIELD: ShopifyGetOperationStatus.COMPLETED.value,
                ShopifyGetOperation.COMPLETED_AT_FIELD: datetime.now(timezone.utc),
                ShopifyGetOperation.PAGES_FETCHED_FIELD: pages_fetched,
                ShopifyGetOperation.TOTAL_ORDERS_FIELD: total_orders
            })
            
            logger.info(f"Completed fetch operation {order_getter_id}. Total orders: {total_orders}, Pages: {pages_fetched}")
            
            return {
                ShopifyGetOperation.FETCH_ID_FIELD: order_getter_id,
                ShopifyGetOperation.TOTAL_ORDERS_FIELD: total_orders,
                ShopifyGetOperation.PAGES_FETCHED_FIELD: pages_fetched,
                ShopifyGetOperation.STATUS_FIELD: ShopifyGetOperationStatus.COMPLETED.value
            }
            
        except Exception as e:
            # Log and record error
            logger.error(f"Error getting orders for {order_getter_id}: {str(e)}")
            
            self.db.collection(shopify_fetch_operations_collection).document(order_getter_id).update({
                ShopifyGetOperation.STATUS_FIELD: ShopifyGetOperationStatus.FAILED.value,
                ShopifyGetOperation.ERROR_MESSAGE_FIELD: str(e),
                ShopifyGetOperation.ERROR_TIME_FIELD: datetime.now(timezone.utc)
            })
            
            return {
                ShopifyGetOperation.FETCH_ID_FIELD: order_getter_id,
                ShopifyGetOperation.STATUS_FIELD: ShopifyGetOperationStatus.FAILED.value,
                ShopifyGetOperation.ERROR_MESSAGE_FIELD: str(e)
            }