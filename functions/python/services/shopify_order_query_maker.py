from datetime import datetime, timedelta, timezone
from firebase_admin import firestore
from firebase_functions import logger
from models.requests.shopify_requests import GetShopifyOrdersWithDynamicQueryRequest, GetShopifyOrdersRequest
from typing import Dict, Any
from models.sales import SalesSilver, SalesStaging


class ShopifyOrderQueryMaker:
    """
    Service for intelligently fetching Shopify orders based on existing data
    and request parameters.
    """
    
    def __init__(self, db: firestore.Client):
        """
        Initialize the SmartOrderService.
        
        Args:
            db: Firestore client
            secret_manager: Secret manager service for accessing API keys
        """
        self.db = db
        
    def get_store_orders(
            self, 
            request: GetShopifyOrdersWithDynamicQueryRequest
        ) -> GetShopifyOrdersRequest:
        """
        Get orders for a store using smart fetching logic.
        
        Will determine the appropriate date range based on:
        - force_refresh flag
        - days_back parameter
        - existing order data
        
        Args:
            request: The request containing store_id and optional parameters
            
        Returns:
            Dictionary containing fetch operation results
        """
        store_id = request.store_id
        force_refresh = request.force_refresh
        days_back = request.days_back
        
        logger.info(f"Smart fetching orders for store: {store_id}, force_refresh: {force_refresh}, days_back: {days_back}")
        
        # Current date as end date
        end_date = datetime.now(timezone.utc).strftime('%Y-%m-%d')
        
        # Determine start date based on the request parameters and existing data
        if force_refresh:
            # If force refresh, use default 90 days back or specified days_back
            days_to_fetch = days_back if days_back is not None else 90
            start_date = (datetime.now(timezone.utc) - timedelta(days=days_to_fetch)).strftime('%Y-%m-%d')
            logger.info(f"Force refresh: fetching orders from {start_date} to {end_date}")
        
        elif days_back is not None:
            # If days_back specified but not force_refresh, use days_back
            start_date = (datetime.now(timezone.utc) - timedelta(days=days_back)).strftime('%Y-%m-%d')
            logger.info(f"Using specified days_back: fetching orders from {start_date} to {end_date}")
        
        else:
            # Check if we have any existing orders for this store
            latest_order_date = self._get_latest_order_date(store_id)
            
            if latest_order_date:
                # don't add 1 day, this can cause missing orders
                start_date = (latest_order_date).strftime('%Y-%m-%d')
                logger.info(f"Incremental fetch: getting orders from {start_date} to {end_date}")
            else:
                # If no orders exist, fetch the last 90 days
                start_date = (datetime.now(timezone.utc) - timedelta(days=180)).strftime('%Y-%m-%d')
                logger.info(f"No existing orders: fetching past 90 days from {start_date} to {end_date}")
        
        return GetShopifyOrdersRequest(
            start_date=start_date,
            end_date=end_date,
            store_id=store_id
        )
    
    def _get_latest_order_date(self, store_id: str) -> datetime:
        """
        Get the most recent order date across both sales-silver and sales-staging.
        
        Args:
            store_id: The store ID to check
            
        Returns:
            The date of the most recent order, or None if no orders exist
        """
        try:
            latest_updated_at = None
            
            # Query sales-silver for the latest order
            gold_query = self.db.collection('sales-silver')\
                .where(SalesSilver.STORE_ID_FIELD, '==', store_id)\
                .order_by(SalesSilver.UPDATED_AT_FIELD, direction='DESCENDING')\
                .limit(1)
            
            gold_results = gold_query.get()
            
            # Check if we have any results from gold
            if gold_results and len(gold_results) > 0:
                latest_gold_sale = gold_results[0].to_dict()
                if SalesSilver.UPDATED_AT_FIELD in latest_gold_sale:
                    updated_at = latest_gold_sale[SalesSilver.UPDATED_AT_FIELD]
                    if hasattr(updated_at, 'timestamp'):
                        latest_updated_at = datetime.fromtimestamp(updated_at.timestamp())
                    else:
                        latest_updated_at = updated_at
            
            # Query sales-staging for the latest order
            staging_query = self.db.collection('sales-staging')\
                .where(SalesStaging.STORE_ID_FIELD, '==', store_id)\
                .order_by(SalesStaging.UPDATED_AT_FIELD, direction='DESCENDING')\
                .limit(1)
            
            staging_results = staging_query.get()
            
            # Check if we have any results from staging
            if staging_results and len(staging_results) > 0:
                latest_staging_sale = staging_results[0].to_dict()
                if SalesStaging.UPDATED_AT_FIELD in latest_staging_sale:
                    updated_at = latest_staging_sale[SalesStaging.UPDATED_AT_FIELD]
                    staging_updated_at = None
                    if hasattr(updated_at, 'timestamp'):
                        staging_updated_at = datetime.fromtimestamp(updated_at.timestamp())
                    else:
                        staging_updated_at = updated_at
                    
                    # Compare with current latest (if any) and keep the most recent
                    if latest_updated_at is None or (staging_updated_at and staging_updated_at > latest_updated_at):
                        latest_updated_at = staging_updated_at
            
            return latest_updated_at
            
        except Exception as e:
            logger.error(f"Error getting latest order date: {str(e)}")
            return None