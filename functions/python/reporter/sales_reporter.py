import polars as pl
from jinja2 import Environment, FileSystemLoader, BaseLoader
from pathlib import Path
from functools import partial
from typing import Optional, List, Dict, Any
from google.cloud import storage
from datetime import datetime, timedelta, timezone
from models.application import Status
from firebase_functions import logger
from models.sales import sales_gold_collection
from constants.gaco_values import bucket_name
from models.sales_report import SalesReportModel, sales_report_collection
from models.requests.sales_report_request import CreateSalesReportRequest
from models.base import DateTime
from firebase_admin import firestore
from gaco_framework.exceptions import NotFoundError


class SalesReporter:
    def __init__(
            self,
            sales_report_data_frame: pl.DataFrame,
            template_file_name: str,
            template_dir: str = None,
            use_cloud_storage: bool = True,
            db = None,
            bucket_name: str = bucket_name,
            local_output_dir: str = './output',
            local_default_template_dir: str = './templates/sales_report'
        ):
        if sales_report_data_frame.is_empty():
            raise ValueError("Input sales_report_data_frame cannot be empty.")

        self.sales_report_data_frame = sales_report_data_frame
        self.template_file_name = template_file_name
        self.template_dir = template_dir
        self.use_cloud_storage = use_cloud_storage
        self.db = db
        self.bucket_name = bucket_name
        self.store_id = self._get_store_id(sales_report_data_frame)
        self.seed_sales_report_id = None

        self.local_output_dir = Path(local_output_dir)
        self.local_default_template_path = Path(local_default_template_dir) / 'sales-report-template.html'

        self.start_date = None
        self.end_date = None

        if self.use_cloud_storage and self.db is None:
             logger.error("Firestore database instance (db) is required when use_cloud_storage is True.")
             raise ValueError("Database connection is required for cloud storage.")

    def _get_store_id(self, df: pl.DataFrame) -> str:
        unique_store_ids = df.get_column('store_id').unique()
        if unique_store_ids.len() != 1:
            logger.error(f"Input data contains multiple store IDs: {unique_store_ids.to_list()}. Expected only one.")
            raise ValueError("Sales report generation currently supports only one store_id per DataFrame.")
        store_id = unique_store_ids[0]
        if not store_id:
            raise ValueError("Store ID is missing or invalid in the input data.")
        return store_id

    def set_seed_report_id(self, store_id: str) -> None:
        if self.db is None:
            logger.error(f"Cannot set seed report ID for store {store_id}: Firestore DB connection is None.")
            raise ConnectionError("Firestore database connection is missing.")

        def _set_new_yearly_seed(current_year: int):
            self.seed_sales_report_id = f"{current_year}-0"
            logger.info(f"Setting new yearly seed report ID for store {store_id}: {self.seed_sales_report_id}")

        current_year = datetime.now(timezone.utc).year
        latest_report_doc = None

        try:
            latest_sales_report = self.db.collection(sales_report_collection)\
                .where(SalesReportModel.STORE_ID_FIELD, '==', store_id)\
                .order_by(SalesReportModel.CREATED_AT_FIELD, direction='DESCENDING')\
                .limit(1)\
                .get()

            if latest_sales_report:
                latest_report_doc = latest_sales_report[0].to_dict()
                latest_report_doc = SalesReportModel.model_validate(latest_report_doc)
                logger.debug(
                    f"Latest report document found for store {store_id}: ",
                    f"{latest_report_doc.sales_report_id}"
                )

        except Exception as e:
             logger.error(f"Error querying latest report for store {store_id}: {e}", exc_info=True)
             _set_new_yearly_seed(current_year)
             return

        if not latest_report_doc:
            logger.info(f"No previous sales report found for store {store_id}. Starting new sequence.")
            _set_new_yearly_seed(current_year)
            return

        try:
            report_id_parts = latest_report_doc.sales_report_id.split('-')
            report_year = int(report_id_parts[0])

            if current_year != report_year:
                logger.info(f"New year detected ({current_year} vs {report_year}). Resetting report sequence for store {store_id}.")
                _set_new_yearly_seed(current_year)
            else:
                self.seed_sales_report_id = latest_report_doc.sales_report_id
                logger.info(f"Setting seed report ID for store {store_id} from latest: {self.seed_sales_report_id}")

        except (IndexError, ValueError, TypeError) as e:
             logger.error(f"Error parsing latest sales_report_id '{latest_report_doc.sales_report_id}' for store {store_id}: {e}. Defaulting.", exc_info=True)
             _set_new_yearly_seed(current_year)

    def increment_sales_report_id(self) -> Optional[str]:
        if self.seed_sales_report_id is None:
            logger.error("Cannot increment sales report ID: seed_sales_report_id is not set. Call set_seed_report_id first.")
            return None
        try:
            year, number = self.seed_sales_report_id.split('-')
            new_number = int(number) + 1
            self.seed_sales_report_id = f"{year}-{new_number}"
            return self.seed_sales_report_id
        except (ValueError, TypeError) as e:
             logger.error(f"Error incrementing sales report ID from seed '{self.seed_sales_report_id}': {e}", exc_info=True)
             return None

    def _prepare_report_dict_common(self, report_dict: Dict[str, Any]) -> Dict[str, Any]:
        now = datetime.now(timezone.utc)
        report_dict['created_at'] = now
        report_dict['updated_at'] = now
        report_dict['status'] = Status.PENDING.value
        return report_dict

    def _update_firestore(self, report_dict: Dict[str, Any]):
        sales_report_doc_name = f'{report_dict["store_id"]}-{report_dict["sales_report_id"]}'
        try:
            sales_report = SalesReportModel.model_validate(report_dict)
            self.db.collection(sales_report_collection).document(sales_report_doc_name).set(sales_report.model_dump())
            logger.info(f"Sales report metadata saved to Firestore: invoices/{sales_report_doc_name}")
        except Exception as e:
             logger.error(f"Failed to save sales report metadata to Firestore for {sales_report_doc_name}: {e}", exc_info=True)

        if 'sale_ids' in report_dict:
            for sale_id in report_dict['sale_ids']:
                doc_ref = self.db.collection(sales_gold_collection).document(sale_id)
                try:
                    doc = doc_ref.get()
                    if doc.exists:
                        doc_ref.update({
                            SalesReportModel.SALES_REPORT_ID_FIELD: sales_report_doc_name,
                            SalesReportModel.UPDATED_AT_FIELD: report_dict['updated_at']
                        })
                    else:
                        logger.warn(
                            f"Sale document {sale_id} not found in {sales_gold_collection} collection. Cannot link sales report {sales_report_doc_name}"
                        )
                except Exception as e:
                     logger.error(f"Failed to update Firestore for sale {sale_id} with report {sales_report_doc_name}: {e}", exc_info=True)

    def _use_cloud_storage(self, report_dict: Dict[str, Any]) -> Dict[str, Any]:
        storage_client = storage.Client()
        bucket = storage_client.bucket(self.bucket_name)

        template_content = None
        template_source_path = ""

        if self.template_dir:
            custom_template_path = f'stores/{report_dict["store_id"]}/{self.template_dir}/{self.template_file_name}'
            try:
                template_blob = bucket.blob(custom_template_path)
                template_content = template_blob.download_as_text()
                template_source_path = f"gs://{self.bucket_name}/{custom_template_path}"
                logger.info(f"Using custom cloud template: {template_source_path}")
            except Exception as e:
                logger.warn(f"Failed to load custom cloud template from {custom_template_path}: {e}. Falling back to default.")
                template_content = None

        if template_content is None:
            default_template_path = 'default_assets/sales_report_template/sales-report-template.html'
            template_blob = bucket.blob(default_template_path)
            try:
                template_content = template_blob.download_as_text()
                template_source_path = f"gs://{self.bucket_name}/{default_template_path}"
                logger.info(f"Using default cloud template: {template_source_path}")
            except Exception as e:
                logger.error(f"CRITICAL: Failed to load default cloud template {default_template_path}: {e}", exc_info=True)
                raise FileNotFoundError(f"Could not load default cloud template: {default_template_path}") from e

        try:
            env = Environment(loader=BaseLoader())
            template = env.from_string(template_content)
            rendered_report = template.render(report_dict)
        except Exception as e:
             logger.error(f"Failed to render template {template_source_path} with data: {e}", exc_info=True)
             raise RuntimeError("Template rendering failed.") from e

        sales_report_file_name = f'sales_report_{report_dict["sales_report_id"]}.html'
        output_blob_path = f'stores/{report_dict["store_id"]}/sales_reports/{sales_report_file_name}'
        output_blob = bucket.blob(output_blob_path)
        try:
            output_blob.upload_from_string(rendered_report, content_type='text/html')
            logger.info(f"Report saved to GCS: gs://{self.bucket_name}/{output_blob_path}")
        except Exception as e:
             logger.error(f"Failed to upload report to GCS at {output_blob_path}: {e}", exc_info=True)
             raise ConnectionError("Failed to save report to Cloud Storage.") from e

        report_dict['uri'] = output_blob.public_url
        report_dict['gcs_path'] = f"gs://{self.bucket_name}/{output_blob_path}"

        # In python its very hacky to do this part, delegate to typescript.
        # generateDownloadUrl.ts function will be called by onWritten trigger.
        report_dict['download_url'] = None

        report_dict = self._prepare_report_dict_common(report_dict)
        report_dict['template_used'] = template_source_path

        self._update_firestore(report_dict)

        return report_dict

    def _use_local_storage(self, report_dict: Dict[str, Any]) -> Dict[str, Any]:

        template_search_paths = []
        template_source_path = ""

        if self.template_dir:
            custom_template_dir = Path(self.template_dir)
            if custom_template_dir.is_dir():
                 template_search_paths.append(custom_template_dir)
            else:
                 logger.warn(f"Custom local template directory not found: {self.template_dir}")

        default_dir = self.local_default_template_path.parent
        if default_dir.is_dir():
            template_search_paths.append(default_dir)
        else:
             logger.warn(f"Default local template directory not found: {default_dir}")

        if not template_search_paths:
             logger.error("No valid local template directories found (custom or default). Cannot proceed.")
             raise FileNotFoundError("No template directories available.")

        env = Environment(loader=FileSystemLoader(searchpath=template_search_paths))
        template = None

        try:
            template = env.get_template(self.template_file_name)
            template_source_path = Path(template.filename).resolve()
            logger.info(f"Using local template: {template_source_path}")
        except Exception as e:
            logger.warn(f"Failed to load specified local template '{self.template_file_name}' from {template_search_paths}: {e}. Trying default name.")
            template = None

        if template is None:
            try:
                default_template_name = self.local_default_template_path.name
                template = env.get_template(default_template_name)
                template_source_path = Path(template.filename).resolve()
                logger.info(f"Using default local template: {template_source_path}")
            except Exception as e:
                 logger.error(f"CRITICAL: Failed to load default local template '{default_template_name}' from {template_search_paths}: {e}", exc_info=True)
                 raise FileNotFoundError(f"Could not load specified or default local template.") from e

        try:
            rendered_report = template.render(report_dict)
        except Exception as e:
             logger.error(f"Failed to render local template {template_source_path} with data: {e}", exc_info=True)
             raise RuntimeError("Template rendering failed.") from e

        output_dir = self.local_output_dir / f'stores/{report_dict["store_id"]}/sales_reports'
        output_path = output_dir / f'sales_report_{report_dict["sales_report_id"]}.html'

        try:
            output_dir.mkdir(parents=True, exist_ok=True)
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(rendered_report)
            logger.info(f"Report saved locally to: {output_path}")
        except OSError as e:
            logger.error(f"Failed to write report to local file {output_path}: {e}", exc_info=True)
            raise

        report_dict['local_file_path'] = str(output_path.resolve())
        report_dict = self._prepare_report_dict_common(report_dict)
        report_dict['template_used'] = str(template_source_path)

        logger.info("Skipping GCS and Firestore updates in local storage mode.")

        return report_dict

    def generate_single_report(self, producer_id: str) -> Optional[Dict[str, Any]]:
        logger.info(f"Starting report generation for producer: {producer_id}, store: {self.store_id}")

        producer_sales_data = self.sales_report_data_frame.filter(
            pl.col('producer_id') == producer_id
        )

        if producer_sales_data.is_empty():
            msg = f"No sales data found for producer {producer_id} in store {self.store_id}. Skipping report."
            logger.warn(msg)
            return NotFoundError(msg)

        producer_level_agg = producer_sales_data.group_by("producer_id").agg(
            [
                pl.sum("subtotal").round(2).alias("subtotal"),
                pl.sum("store_gross_payout").round(2).alias("store_total_gross_payout"),
                pl.sum("producer_gross_payout").round(2).alias("producer_total_gross_payout"),
                pl.first("producer_display_name").alias("producer_display_name")
            ]
        )

        if producer_level_agg.is_empty():
             logger.error(f"Producer level aggregation failed for producer {producer_id}.")
             return None
        producer_level_dict = producer_level_agg.to_dicts()[0]

        sales_level_grouped = producer_sales_data.select([
             'sale_id',
             'title',
             'variant_title',
             'updated_at',
             'subtotal',
             'commission',
             'producer_gross_payout'
        ])

        sales_level_list = sales_level_grouped.to_dicts()

        sale_ids = producer_sales_data['sale_id'].unique().to_list()

        sales_report_id = self.increment_sales_report_id()
        if sales_report_id is None:
             logger.error(f"Failed to generate next sales_report_id for store {self.store_id}, producer {producer_id}")
             return None

        report_dict = {
            'sales_report_id': sales_report_id,
            'store_id': self.store_id,
            'producer_id': producer_id,
            'producer_level': producer_level_dict,
            'sales_level_data': sales_level_list,
            'sales_report_date': datetime.now(timezone.utc).isoformat(),
            'sale_ids': sale_ids,
            'title': f"Sales Report {sales_report_id} for {producer_level_dict.get('producer_display_name', producer_id)}",
            'start_date': self.start_date,
            'end_date': self.end_date
        }

        try:
            if self.use_cloud_storage:
                logger.info(f"Generating report {sales_report_id} for producer {producer_id} using cloud storage.")
                return self._use_cloud_storage(report_dict)
            else:
                logger.info(f"Generating report {sales_report_id} for producer {producer_id} using local storage.")
                return self._use_local_storage(report_dict)
        except Exception as e:
             logger.error(f"Failed during storage/finalization for report {sales_report_id} (Producer: {producer_id}): {e}", exc_info=True)
             return None

    def generate_report(self, start_date: Optional[DateTime] = None, end_date: Optional[DateTime] = None) -> List[Dict[str, Any]]:
        logger.info(f"Starting sales report generation process for store: {self.store_id}")

        self.start_date = start_date
        self.end_date = end_date

        try:
            self.set_seed_report_id(self.store_id)
            if self.seed_sales_report_id is None and self.use_cloud_storage:
                 logger.error("Failed to set seed report ID. Aborting report generation.")
                 return []
        except Exception as e:
             logger.error(f"Failed to set seed report ID for store {self.store_id}: {e}. Aborting.", exc_info=True)
             return []

        producer_ids = self.sales_report_data_frame\
            .get_column('producer_id').unique().drop_nulls().to_list()

        if not producer_ids:
             logger.warn(f"No valid producer IDs found in the data for store {self.store_id}. No reports generated.")
             return []

        logger.info(f"Found {len(producer_ids)} unique producer(s) for store {self.store_id}.")

        output_reports = []
        for producer_id in producer_ids:
             try:
                report_result = self.generate_single_report(producer_id=producer_id)
                if report_result:
                    output_reports.append(report_result)
                else:
                     logger.warn(f"Skipped report generation for producer {producer_id} (see previous logs for details).")
             except Exception as e:
                  logger.error(f"Unhandled exception during report generation for producer {producer_id} in store {self.store_id}: {e}", exc_info=True)

        logger.info(f"Finished sales report generation for store {self.store_id}. Generated {len(output_reports)} report(s).")
        return output_reports
