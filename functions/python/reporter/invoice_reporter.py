import polars as pl
from jinja2 import Environment, FileSystemLoader, BaseLoader
from pathlib import PurePath
from functools import partial
from typing import Optional
from google.cloud import storage
from datetime import datetime, timedelta, timezone
from models.application import Status
from columns.invoice_columns import InvoiceSalesLevelReportColumnNames, InvoiceSalesLevelColumnNames
from queries.invoices_query_builder import InvoicesQueryBuilder
from queries.sales_gold_query_builder import SalesGoldQueryBuilder
import logging
from models.sales import sales_gold_collection
from constants.gaco_values import bucket_name

logger = logging.getLogger(__name__)

class InvoiceReporter:
    def __init__(
            self, 
            invoice_data_frame: pl.DataFrame, 
            template_file_name: str,
            template_dir: str = None,
            db = None,
            bucket_name: str = bucket_name,
        ):
        self.invoice_data_frame = invoice_data_frame
        self.template_file_name = template_file_name
        self.template_dir = template_dir
        self.db = db
        self.bucket_name = bucket_name
        self.store_id = None
        self.seed_invoice_id = None
        self.invoices_query_builder = InvoicesQueryBuilder(db)
        self.sales_invoice_query_builder = SalesGoldQueryBuilder(db)

    def aggregate_on_producer_id(self):
        return self.invoice_data_frame.group_by("producer_id").agg(
            [
                pl.col("subtotal").sum().round(2),
                pl.col("net_sales").sum().round(2),
                pl.col("vat_excluded_sale").sum().round(2),
                pl.col("vat_on_sales_service").sum().round(2),
                pl.col("store_total_gross_payout").sum().round(2),
                pl.col("store_net_payout").sum().round(2),
                pl.col("producer_gross_payout").sum().round(2)
            ]
        )

    def set_seed_invoice_id(self, store_id: str) -> str:
        """
        Generate a new invoice ID by incrementing the number after the hyphen.
        
        Args:
            store_id: The ID of the store

        Returns:
            str: New invoice ID in format 'YYYY-XX' where XX is a number
        """
        def _get_new_yearly_invoice(current_year: int):
            self.seed_invoice_id = f"{current_year}-0"
            return None

        latest_invoice = self.invoices_query_builder\
            .for_store_id(store_id)\
            .get_latest()\
            .build()\
            .get()

        current_year = datetime.now(timezone.utc).year

        if len(latest_invoice) == 0:
            return _get_new_yearly_invoice(current_year)

        latest_invoice_dict = latest_invoice[0].to_dict()
        invoice_id = latest_invoice_dict['invoice_id'].split('-')

        if (current_year != int(invoice_id[0])):
            return _get_new_yearly_invoice(current_year)
        
        self.seed_invoice_id = latest_invoice_dict['invoice_id']

    def increment_invoice_id(self):
        if self.seed_invoice_id is None:
            raise ValueError("seed_invoice_id is not set")
        else:
            year, number = self.seed_invoice_id.split('-')
            new_number = int(number) + 1
            self.seed_invoice_id = f"{year}-{new_number}"
            return self.seed_invoice_id

    def _use_cloud_storage(self, report_dict: dict):
        """
        save data in both cloud storage and fire store 
        """
        storage_client = storage.Client()
        bucket = storage_client.bucket(self.bucket_name)

        template_content = None
        try:
            template_blob = bucket.blob(
                f'stores/{report_dict["store_id"]}/invoice_template/{self.template_file_name}'
            )
            template_content = template_blob.download_as_text()
        except Exception as e:
            logger.warning(f"Failed to load custom template: {e}. Using default template instead.")
            template_blob = bucket.blob(
                f'default_assets/invoice_template/invoice-template-v2.html'
            )
            template_content = template_blob.download_as_text()


        # Create Jinja environment with string loader instead of file system loader
        env = Environment(loader=BaseLoader())
        template = env.from_string(template_content)
        rendered_report = template.render(report_dict)

        # Save rendered report to cloud storage
        output_blob = bucket.blob(
            f'stores/{report_dict["store_id"]}/invoices/{report_dict["invoice_id"]}.html'
        )
        output_blob.upload_from_string(rendered_report, content_type='text/html')

        report_dict['uri'] = output_blob.public_url
        try:
            report_dict['download_url'] = output_blob.generate_signed_url(
                expiration=604800, 
                version='v4'
            )
        except Exception as e:
            print(f"Error generating signed URL: {e}")
            report_dict['download_url'] = None

        # immutable fields
        report_dict['created_at'] = datetime.now(timezone.utc)
        # mutable fields when factuur data was updated
        report_dict['updated_at'] = datetime.now(timezone.utc)

        report_dict['status'] = Status.PENDING.value

        invoice_doc_name = f'{report_dict["store_id"]}-{report_dict["invoice_id"]}'

        self.db.collection('invoices').document(invoice_doc_name).set(report_dict)

        # for each sale_id, add an element in a list in the firestore.
        # We migh change this and check if the invoice already exists.
        for sale in report_dict['sale_ids']:
            # Check if document exists before updating
            doc_ref = self.db.collection(sales_gold_collection).document(sale)
            doc = doc_ref.get()
            
            if doc.exists:
                # Document exists, update it
                doc_ref.update({
                    'invoice_id': invoice_doc_name
                })
            else:
                logger.warning(
                    f"Sale document {sale} not found in sales-gold collection"
                )

        return report_dict

    def _use_local_storage():
        # use local storage for development purposes.
        # this should be the default behaviour
        pass

    def generate_single_report(
            self, 
            producer_id: str,
            producer_level_aggregate: pl.DataFrame
        ) -> dict:
        """Generate a single report for a given producer_id
        
        Args:
            producer_id: The ID of the producer
            producer_level_aggregate: Pre-calculated aggregate data for all producers
            template: Pre-loaded Jinja2 template
        """
        producer_level_data = producer_level_aggregate.filter(
            pl.col('producer_id') == producer_id
        )

        if producer_level_data.shape[0] != 1:
            raise ValueError("aggregation must end up as 1 row")
        
        sales_level_data = self.invoice_data_frame.filter(
            pl.col('producer_id') == producer_id
        )

        sale_ids = sales_level_data['sale_id'].unique().to_list()

        sales_level_data = sales_level_data.with_columns([
            pl.col("subtotal").round(2),
            pl.col("vat_excluded_sale").round(2),
            pl.col("net_sales").round(2),
            pl.col("store_net_payout").round(2),
            pl.col("vat_on_sales_service").round(2),
            pl.col("store_total_gross_payout").round(2),
            pl.col("producer_gross_payout").round(2)
        ])

        # First group and count identical items
        sales_level_data = sales_level_data.group_by([
            col.value for col in InvoiceSalesLevelColumnNames 
        ]).agg([
            pl.len().alias('item_count')
        ]).select([
            col.value for col in InvoiceSalesLevelReportColumnNames 
        ])

        invoice_id = self.increment_invoice_id()

        report_dict = {
            'invoice_id': invoice_id,
            'store_id': self.store_id,
            'producer_level': producer_level_data.to_dicts()[0],
            'sales_level_data': sales_level_data.to_dicts(),
            'factuur_date': datetime.now(timezone.utc).isoformat(),
            'payout_due_date': (
                datetime.now(timezone.utc) + timedelta(days=14)
            ).isoformat(),
            'sale_ids': sale_ids
        }

        return self._use_cloud_storage(report_dict)


    # TODO: add option for using the local files instead of cloud.
    def generate_report(self):
        self.store_id = self.invoice_data_frame[
            InvoiceSalesLevelReportColumnNames.STORE_ID.value
        ].unique()[0]

        self.set_seed_invoice_id(self.store_id)

        producer_level_aggregate = self.aggregate_on_producer_id()

        producer_ids = self.invoice_data_frame\
            .unique('producer_id')['producer_id']

        generate_report_with_static = partial(
            self.generate_single_report,
            producer_level_aggregate=producer_level_aggregate
        )
        
        # Map over producer_ids with the partial function
        output = list(map(generate_report_with_static, producer_ids))

        return output
