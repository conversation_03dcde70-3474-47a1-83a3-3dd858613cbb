from google.cloud import secretmanager
from google.api_core import exceptions
from models.secret import SecretLabels

class SecretManager:
    def __init__(self, project_id):
        self.client = secretmanager.SecretManagerServiceClient()
        self.project_id = project_id
        self.project_path = f"projects/{project_id}"

    def create_secret(
            self, 
            secret_id: str, 
            secret_value: str, 
            labels: SecretLabels = None
        ) -> str:
        """
        Creates a new secret in Secret Manager
        Returns the secret version path
        """
        try:
            # Create the secret
            secret = self.client.create_secret(
                request={
                    "parent": self.project_path,
                    "secret_id": secret_id,
                    "secret": {
                        "replication": {
                            "automatic": {},
                        },
                        "labels": labels.model_dump() if labels else {},
                    },
                }
            )
            
            # Add the secret version
            version = self.client.add_secret_version(
                request={
                    "parent": secret.name,
                    "payload": {"data": secret_value.encode("UTF-8")},
                }
            )
            
            return version.name
        except exceptions.AlreadyExists:
            # If secret already exists, just add new version
            secret_path = f"{self.project_path}/secrets/{secret_id}"
            version = self.client.add_secret_version(
                request={
                    "parent": secret_path,
                    "payload": {"data": secret_value.encode("UTF-8")},
                }
            )
            return version.name

    def get_secret(self, secret_version_path: str) -> str:
        """
        Retrieves a secret from Secret Manager
        """
        response = self.client.access_secret_version(
            request={"name": secret_version_path}
        )
        return response.payload.data.decode("UTF-8")

    def delete_secret(self, secret_id: str):
        """
        Deletes a secret and all its versions
        """
        secret_path = f"{self.project_path}/secrets/{secret_id}"
        self.client.delete_secret(request={"name": secret_path}) 
