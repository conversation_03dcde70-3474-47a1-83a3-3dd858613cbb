from typing import <PERSON>ple, Optional
from firebase_functions import https_fn
from firebase_admin import auth

def verify_firebase_auth(req: https_fn.Request) -> Tuple[bool, Optional[str]]:
    """
    Verify Firebase authentication token from request headers
    Returns: Tuple of (success: bool, error_message: Optional[str])
    """
    auth_header = req.headers.get('Authorization')
    if not auth_header:
        return False, 'No authorization header'

    try:
        id_token = auth_header.split('Bearer ')[1]
        auth.verify_id_token(id_token)
        return True, None
    except Exception as e:
        return False, str(e)