# data_sources/sales_data_source.py
import polars as pl
from typing import Dict, Any
from columns.sales_columns import SalesColumns # Use your constants enum/class
from models.sales import SalesSilver # For field name constants if needed
from models.requests.invoice_request import CreateInvoiceRequest
from queries.sales_silver_query_builder import SalesSilverQueryBuilder
from data_source.data_source_protocol import DataSource # Import the protocol
from firebase_functions import logger

class SalesDataSource: # Implements DataSource implicitly
    def __init__(self, db):
        self.db = db
        self.sales_silver_query_builder = SalesSilverQueryBuilder(db)
        # Define schema using constants from SalesColumns enum
        self.sales_schema = {
            SalesColumns.SALE_ID.value: pl.Utf8, 
            SalesColumns.TITLE.value: pl.Utf8,
            SalesColumns.VARIANT_TITLE.value: pl.Utf8,
            SalesColumns.VARIANT_DISPLAY_NAME.value: pl.Utf8,
            SalesColumns.DOCUMENT_ID.value: pl.Utf8,
            SalesColumns.PRODUCER_ID.value: pl.Utf8,
            SalesColumns.PRODUCER_DISPLAY_NAME.value: pl.Utf8,
            SalesColumns.COMMISSION.value: pl.Float64,
            SalesColumns.PRODUCER_TAX_A2.value: pl.Utf8,
            SalesColumns.STORE_TAX_A2.value: pl.Utf8,
            SalesColumns.DISCOUNT.value: pl.Float64,
            SalesColumns.SUBTOTAL.value: pl.Float64,
            SalesColumns.STORE_ID.value: pl.Utf8, 
            SalesColumns.UPDATED_AT.value: pl.Datetime,
        }

    def fetch_data(self, criteria: Dict[str, Any]) -> pl.DataFrame:
        # Ensure required criteria are present
        if not all(k in criteria for k in [
            CreateInvoiceRequest.STORE_ID_FIELD, 
            CreateInvoiceRequest.START_DATE_FIELD, 
            CreateInvoiceRequest.END_DATE_FIELD
        ]):
             raise ValueError(
                 "Missing required criteria: "
                 f"{CreateInvoiceRequest.STORE_ID_FIELD}, "
                 f"{CreateInvoiceRequest.START_DATE_FIELD}, "
                 f"{CreateInvoiceRequest.END_DATE_FIELD}"
             )
             
        query_builder = self.sales_silver_query_builder \
            .for_store_id(criteria[CreateInvoiceRequest.STORE_ID_FIELD]) \
            .between_dates(
                 criteria[CreateInvoiceRequest.START_DATE_FIELD], 
                 criteria[CreateInvoiceRequest.END_DATE_FIELD]
            )

        producer_id = criteria.get(CreateInvoiceRequest.PRODUCER_ID_FIELD)
        if producer_id:
            query_builder = query_builder.for_producer_id(producer_id)
            logger.info(f"Fetching sales for producer_id: {producer_id}")
        else:
            logger.info("No producer_id provided, fetching for all producers.")


        sales_stream = query_builder.build().stream()

        # don't use list comprehension, for loop is easier to debug
        sales_data = []
        for sale in sales_stream:
             sale_dict = sale.to_dict()
             sales_data.append({ 
                 SalesColumns.SALE_ID.value: sale.id,
                 SalesColumns.TITLE.value: sale_dict.get(SalesSilver.TITLE_FIELD),
                 SalesColumns.VARIANT_TITLE.value: sale_dict.get(SalesSilver.VARIANT_TITLE_FIELD),
                 SalesColumns.VARIANT_DISPLAY_NAME.value: sale_dict.get(SalesSilver.VARIANT_DISPLAY_NAME_FIELD),
                 SalesColumns.DOCUMENT_ID.value: sale_dict.get(SalesSilver.DOCUMENT_ID_FIELD),
                 SalesColumns.PRODUCER_ID.value: sale_dict.get(SalesSilver.PRODUCER_ID_FIELD),
                 SalesColumns.PRODUCER_DISPLAY_NAME.value: sale_dict.get(SalesSilver.PRODUCER_DISPLAY_NAME_FIELD),
                 SalesColumns.COMMISSION.value: sale_dict.get(SalesSilver.COMMISSION_FIELD),
                 SalesColumns.PRODUCER_TAX_A2.value: sale_dict.get(SalesSilver.PRODUCER_TAX_A2_FIELD),
                 SalesColumns.STORE_TAX_A2.value: sale_dict.get(SalesSilver.STORE_TAX_A2_FIELD),
                 SalesColumns.DISCOUNT.value: sale_dict.get(SalesSilver.DISCOUNT_FIELD),
                 SalesColumns.SUBTOTAL.value: sale_dict.get(SalesSilver.SUBTOTAL_FIELD),
                 SalesColumns.STORE_ID.value: sale_dict.get(SalesSilver.STORE_ID_FIELD),
                 SalesColumns.UPDATED_AT.value: sale_dict.get(SalesSilver.UPDATED_AT_FIELD),
             })

        if not sales_data:
             print(f"No sales found for criteria: {criteria}")
             # Return empty DF with correct schema
             return pl.DataFrame(schema=self.sales_schema) 
                 
        return pl.DataFrame(data=sales_data, schema=self.sales_schema)