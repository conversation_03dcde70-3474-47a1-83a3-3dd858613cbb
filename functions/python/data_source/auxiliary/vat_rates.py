# data_sources/auxiliary/vat_rates.py
from typing import List, Dict
from firebase_admin import firestore
from queries.vat_rate_query_builder import VatRateQueryBuilder
from firebase_functions import logger

class VatRatesAuxiliaryDataSource: # Renamed class
    """Fetches auxiliary VAT rate information based on country codes."""
    def __init__(self, db: firestore.Client):
        self.db = db
        self.vat_rate_query_builder = VatRateQueryBuilder(db)

    def get_rates(self, country_codes: List[str]) -> Dict[str, Dict]:
        """
        Fetches VAT rates for given country codes.
        Returns a dictionary mapping country code to its VAT rate document data.
        Raises ValueError if a country code is not found.
        """
        vat_rates_dict = {}
        unique_codes = set(filter(None, country_codes)) 

        logger.info(f"Fetching VAT rates for country codes: {unique_codes}")
        for country_code in unique_codes:
            if not country_code: continue 
            
            vat_rates_query = self.vat_rate_query_builder \
                .for_country(country_code) \
                .build() \
                .get() 

            if not vat_rates_query or not vat_rates_query.exists:
                 logger.error(f"No VAT rates found for country code: {country_code}")
                 raise ValueError(
                     f"Configuration Error: No VAT rates found for country code: {country_code}",
                     f"VAT rates calculation not supported for this country code: {country_code}"
                )
            
            vat_rates_doc = vat_rates_query 
            vat_rates_dict[country_code] = vat_rates_doc.to_dict()
            
        logger.info(f"Successfully fetched VAT rates for {len(vat_rates_dict)} countries.")
        return vat_rates_dict