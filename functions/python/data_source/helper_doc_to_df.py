from models.sales import SalesSilver
import polars as pl
from columns.sales_columns import SalesColumns


class HelperDocToDf:
  def _convert_to_df_row(self, data: list[dict]) -> pl.DataFrame:
    sales_schame = {
        SalesColumns.SALE_ID.value: pl.Utf8, 
        SalesColumns.TITLE.value: pl.Utf8,
        SalesColumns.VARIANT_TITLE.value: pl.Utf8,
        SalesColumns.VARIANT_DISPLAY_NAME.value: pl.Utf8,
        SalesColumns.DOCUMENT_ID.value: pl.Utf8,
        SalesColumns.PRODUCER_ID.value: pl.Utf8,
        SalesColumns.PRODUCER_DISPLAY_NAME.value: pl.Utf8,
        SalesColumns.COMMISSION.value: pl.Float64,
        SalesColumns.PRODUCER_TAX_A2.value: pl.Utf8,
        SalesColumns.STORE_TAX_A2.value: pl.Utf8,
        SalesColumns.DISCOUNT.value: pl.Float64,
        SalesColumns.SUBTOTAL.value: pl.Float64,
        SalesColumns.STORE_ID.value: pl.Utf8, 
        SalesColumns.UPDATED_AT.value: pl.Datetime,
    }
    return pl.DataFrame(data=data, schema=sales_schame)


  def sales_silver_doc_to_df(self, doc: SalesSilver, doc_id: str) -> pl.DataFrame:

    sales_row = {
        SalesColumns.SALE_ID.value: doc_id,
        SalesColumns.TITLE.value: doc.title,
        SalesColumns.VARIANT_TITLE.value: doc.variant_title,
        SalesColumns.VARIANT_DISPLAY_NAME.value: doc.variant_display_name,
        SalesColumns.DOCUMENT_ID.value: doc.document_id,
        SalesColumns.PRODUCER_ID.value: doc.producer_id,
        SalesColumns.PRODUCER_DISPLAY_NAME.value: doc.producer_display_name,
        SalesColumns.COMMISSION.value: doc.commission,
        SalesColumns.PRODUCER_TAX_A2.value: doc.producer_tax_a2,
        SalesColumns.STORE_TAX_A2.value: doc.store_tax_a2,
        SalesColumns.DISCOUNT.value: doc.discount,
        SalesColumns.SUBTOTAL.value: doc.subtotal,
        SalesColumns.STORE_ID.value: doc.store_id,
        SalesColumns.UPDATED_AT.value: doc.updated_at,
    }

    return self._convert_to_df_row([sales_row])


