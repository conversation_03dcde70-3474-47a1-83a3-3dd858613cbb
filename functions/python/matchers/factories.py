from typing import List, Dict
from enum import Enum
from matchers.base import ApplicationMatcher
from matchers.application_matchers import (
    VendorIdMatcher,
    TitleMatcher,
    CompositeApplicationMatcher,
    DisplayNameMatcher
)

class MatchingStrategy(Enum):
    """Predefined matching strategies"""
    VENDOR_ID_ONLY = "vendor_id_only"
    TITLE_ONLY = "title_only"
    VENDOR_ID_FIRST = "vendor_id_first"
    TITLE_FIRST = "title_first"
    ALL_STRATEGIES = "all_strategies"
    VENDOR_PRODUCER_DISPLAY_NAME = "vendor_producer_display_name"


class ApplicationMatcherFactory:
    """
    Factory for creating application matchers with different configurations.
    Provides predefined matcher combinations and custom configuration options.
    """

    @staticmethod
    def create(strategy: MatchingStrategy | str = MatchingStrategy.VENDOR_ID_FIRST) -> ApplicationMatcher:
        """
        Create a matcher based on the specified strategy.

        Args:
            strategy: The matching strategy to use

        Returns:
            Configured ApplicationMatcher instance
        """
        if isinstance(strategy, str):
            strategy = MatchingStrategy(strategy)

        strategies_map = {
            MatchingStrategy.VENDOR_ID_ONLY: 
                CompositeApplicationMatcher([VendorIdMatcher()]),
                
            MatchingStrategy.TITLE_ONLY: 
                CompositeApplicationMatcher([TitleMatcher()]),
                
            MatchingStrategy.VENDOR_ID_FIRST: 
                CompositeApplicationMatcher([
                    VendorIdMatcher(),
                    TitleMatcher()
                ]),
                
            MatchingStrategy.TITLE_FIRST: 
                CompositeApplicationMatcher([
                    TitleMatcher(),
                    VendorIdMatcher()
                ]),
                
            MatchingStrategy.ALL_STRATEGIES: 
                CompositeApplicationMatcher([
                    VendorIdMatcher(),
                    TitleMatcher(),
                    DisplayNameMatcher()
                ]),

            MatchingStrategy.VENDOR_PRODUCER_DISPLAY_NAME:
                CompositeApplicationMatcher([
                    DisplayNameMatcher()
                ])
        }

        return strategies_map[strategy]

    @staticmethod
    def create_custom(matchers: List[ApplicationMatcher]) -> ApplicationMatcher:
        """
        Create a custom matcher with the specified list of matchers.

        Args:
            matchers: List of matcher instances in desired order

        Returns:
            CompositeApplicationMatcher with the specified matchers
        """
        return CompositeApplicationMatcher(matchers)

    @staticmethod
    def from_config(config: Dict) -> ApplicationMatcher:
        """
        Create a matcher from a configuration dictionary.

        Args:
            config: Dictionary containing matcher configuration
            Example:
            {
                "strategy": "vendor_id_first",
                "options": {
                    "title_match_threshold": 0.8,
                    "case_sensitive": False
                }
            }

        Returns:
            Configured ApplicationMatcher instance
        """
        strategy = config.get("strategy", "vendor_id_first")
        options = config.get("options", {})

        # You could use options to configure individual matchers
        # For example, if TitleMatcher had configurable options:
        if strategy == MatchingStrategy.TITLE_ONLY:
            title_matcher = TitleMatcher(
                threshold=options.get("title_match_threshold", 0.8),
                case_sensitive=options.get("case_sensitive", False)
            )
            return CompositeApplicationMatcher([title_matcher])

        # Default to standard factory method if no special configuration needed
        return ApplicationMatcherFactory.create(strategy)
