from abc import ABC, abstractmethod
from typing import Dict, List, Optional
import pydantic

class ApplicationMatcher(ABC):
    """Base class for application matching strategies"""
    
    @abstractmethod
    def match(self, sale: pydantic.BaseModel, applications: List[Dict]) -> Optional[Dict]:
        """
        Attempt to match a sale with an application.
        
        Args:
            sale: Sale data dictionary
            applications: List of application dictionaries
            
        Returns:
            Matched application or None if no match found
        """
        pass