from typing import Dict, List, Optional
from models.sales import SalesStaging
from matchers.base import ApplicationMatcher
from models.sales import SalesSilver
from models.partner import Partnership

class VendorIdMatcher(ApplicationMatcher):
    """Matches applications based on vendor ID"""
    
    def match(self, sale: SalesStaging, applications: List[Dict]) -> Optional[Dict]:
        """
        Matches an application based on the vendor ID.
        scenario where sales staging got a vendor id
        """
        vendor_id = sale.vendor if sale.vendor else sale.vendor
        if not vendor_id:
            return None
            
        return next(
            (app for app in applications if app.get(Partnership.PRODUCER_ID_FIELD) == vendor_id),
            None
        )

class TitleMatcher(ApplicationMatcher):
    """Matches applications based on producer name in sale title"""
    
    def match(self, sale: SalesStaging, applications: List[Dict]) -> Optional[Dict]:
        sale_title = sale.title.lower() if sale.title else sale.title
        
        for app in applications:
            producer_name = app.get(SalesSilver.PRODUCER_DISPLAY_NAME_FIELD, '').lower()
            if producer_name and producer_name in sale_title:
                return app
        return None

class DisplayNameMatcher(ApplicationMatcher):
    """Matches applications based on vendor and producer display name"""
    
    def match(self, sale: SalesStaging, applications: List[Dict]) -> Optional[Dict]:
        vendor_name = sale.vendor.lower() if sale.vendor else sale.vendor

        for app in applications:
            producer_name = app.get(SalesSilver.PRODUCER_DISPLAY_NAME_FIELD, '').lower()
            if producer_name and producer_name == vendor_name:
                return app
        return None

class CompositeApplicationMatcher(ApplicationMatcher):
    """Tries multiple matching strategies in sequence"""
    
    def __init__(self, strategies: List[ApplicationMatcher]):
        self.strategies = strategies
    
    def match(self, sale: Dict, applications: List[Dict]) -> Optional[Dict]:
        for strategy in self.strategies:
            if match := strategy.match(sale, applications):
                return match
        return None