"""
Enhanced decorators for Gaco Framework with authorization
"""

import functools
import traceback
from typing import Any, Callable, List, Optional, TypeVar, Union

from firebase_admin import firestore
from firebase_functions import https_fn, firestore_fn, storage_fn, logger, options
from pydantic import ValidationError as PydanticValidationError

from .auth import AuthRequirement
from .config import get_config
from .context import GacoContext, GacoEventContext
from .exceptions import (
    GacoError,
    ValidationError,
    AuthenticationError,
    AuthorizationError,
)
from .models import GacoResponse
from .security import ResourceAccess, check_resource_access
from .trigger_types import DocumentTriggerType

F = TypeVar("F", bound=Callable[..., Any])


def gaco_endpoint(
    region: Optional[str] = None,
    timeout_sec: Optional[int] = None,
    memory: Optional[options.MemoryOption] = None,
    require_auth: Optional[Union[bool, AuthRequirement]] = None,
    required_roles: Optional[List[str]] = None,
    required_permissions: Optional[List[str]] = None,
    cors: Optional[bool] = None,
) -> Callable[[F], F]:
    """
    Enhanced decorator for HTTP callable cloud functions with auth support.
    """

    def decorator(func: F) -> F:
        config = get_config()

        # Use config defaults if not specified
        actual_region = region or config.default_region
        actual_timeout = timeout_sec or config.default_timeout
        actual_memory = memory or config.default_memory

        # Determine auth requirement
        if require_auth is None:
            actual_require_auth = config.auth_required_by_default
        elif isinstance(require_auth, bool):
            actual_require_auth = (
                AuthRequirement.AUTHENTICATED if require_auth else AuthRequirement.NONE
            )
        else:
            actual_require_auth = require_auth

        @https_fn.on_call(
            region=actual_region, timeout_sec=actual_timeout, memory=actual_memory
        )
        @functools.wraps(func)
        def wrapper(request: https_fn.CallableRequest) -> dict:
            try:
                # Create enhanced context
                db = firestore.client()
                context = GacoContext(db=db, auth=request.auth, raw_request=request)

                # Authentication and authorization checks
                if actual_require_auth != AuthRequirement.NONE:
                    # For v1 compatibility, check both auth_context and user_id
                    if not context.is_authenticated():
                        return GacoResponse(
                            success=False, message=config.auth_error_message, code=401
                        ).model_dump()

                    # Check auth requirement level
                    if (
                        actual_require_auth == AuthRequirement.VERIFIED_EMAIL
                        and not context.auth_context.email_verified
                    ):
                        return GacoResponse(
                            success=False,
                            message="Email verification required",
                            code=401,
                        ).model_dump()

                    # Check required roles
                    if required_roles and not context.auth_context.has_any_role(
                        required_roles
                    ):
                        return GacoResponse(
                            success=False,
                            message=f"One of these roles required: {', '.join(required_roles)}",
                            code=403,
                        ).model_dump()
                    # Check required permissions
                    if (
                        required_permissions
                        and not context.auth_context.has_all_permissions(
                            required_permissions
                        )
                    ):
                        return GacoResponse(
                            success=False,
                            message=f"All of these permissions required: {', '.join(required_permissions)}",
                            code=403,
                        ).model_dump()

                # Call the actual function
                result = func(context, request.data)

                # Handle different return types
                if isinstance(result, GacoResponse):
                    return result.model_dump()
                elif isinstance(result, dict):
                    if "success" in result:
                        return result
                    else:
                        return GacoResponse(
                            success=True,
                            message="Operation completed successfully",
                            data=result,
                            code=config.default_success_code,
                        ).model_dump()
                else:
                    return GacoResponse(
                        success=True,
                        message="Operation completed successfully",
                        data={"result": result} if result is not None else None,
                        code=config.default_success_code,
                    ).model_dump()

            except AuthenticationError as e:
                logger.error(f"Authentication error in {func.__name__}: {e}")
                return GacoResponse(
                    success=False, message=e.message, code=e.code
                ).model_dump()
            except AuthorizationError as e:
                logger.error(f"Authorization error in {func.__name__}: {e}")
                return GacoResponse(
                    success=False, message=e.message, code=e.code
                ).model_dump()
            except ValidationError as e:
                logger.error(f"Validation error in {func.__name__}: {e}")
                return GacoResponse(
                    success=False, message=e.message, errors=e.errors, code=e.code
                ).model_dump()
            except PydanticValidationError as e:
                logger.error(f"Pydantic validation error in {func.__name__}: {e}")
                return GacoResponse(
                    success=False,
                    message="Validation error",
                    errors=[{"detail": str(err)} for err in e.errors()],
                    code=400,
                ).model_dump()
            except GacoError as e:
                logger.error(f"Gaco error in {func.__name__}: {e}")
                return GacoResponse(
                    success=False, message=e.message, data=e.details, code=e.code
                ).model_dump()
            except Exception as e:
                logger.error(f"Unexpected error in {func.__name__}: {e}")
                logger.error(traceback.format_exc())
                return GacoResponse(
                    success=False,
                    message=f"An unexpected error occurred",
                    code=config.default_error_code,
                ).model_dump()

        return wrapper

    return decorator


def gaco_firestore_trigger(
    document_path: str,
    region: Optional[str] = None,
    timeout_sec: Optional[int] = None,
    memory: Optional[options.MemoryOption] = None,
    trigger_type: str = "updated",
) -> Callable[[F], F]:
    """
    Decorator for Firestore trigger functions with standardized patterns.

    Args:
        document_path: Firestore document path pattern
        region: GCP region for deployment
        timeout_sec: Function timeout in seconds
        memory: Memory allocation for the function
        trigger_type: Type of trigger ("created", "updated", "deleted")

    Example:
        @gaco_firestore_trigger(
            document_path="users/{user_id}",
            trigger_type="created"
        )
        def on_user_created(context: GacoEventContext, event):
            user_data = event.data.after.to_dict()
            # Handle user creation
    """

    def decorator(func: F) -> F:
        config = get_config()

        # Use config defaults if not specified
        actual_region = region or config.default_region
        actual_timeout = timeout_sec or 300  # Default higher for triggers
        actual_memory = memory or config.default_memory

        # Select appropriate trigger decorator
        match trigger_type:
            case DocumentTriggerType.CREATED.value:
                trigger_decorator = firestore_fn.on_document_created
            case DocumentTriggerType.UPDATED.value:
                trigger_decorator = firestore_fn.on_document_updated
            case DocumentTriggerType.DELETED.value:
                trigger_decorator = firestore_fn.on_document_deleted
            case _:
                raise ValueError(f"Invalid trigger_type: {trigger_type}")

        @trigger_decorator(
            document=document_path,
            region=actual_region,
            timeout_sec=actual_timeout,
            memory=actual_memory,
        )
        @functools.wraps(func)
        def wrapper(event: Any) -> None:
            try:
                db = firestore.client()
                context = GacoEventContext(db=db, event=event, event_type=trigger_type)

                return func(context, event)

            except Exception as e:
                logger.error(f"Error in {func.__name__}: {e}")
                logger.error(traceback.format_exc())
                # Don't re-raise for triggers unless you want retries
                raise

        return wrapper

    return decorator


def gaco_storage_trigger(
    bucket: Optional[str] = None,
    region: Optional[str] = None,
    timeout_sec: Optional[int] = None,
    memory: Optional[options.MemoryOption] = None,
    trigger_type: str = "finalized",
) -> Callable[[F], F]:
    """
    Decorator for Cloud Storage trigger functions.

    Args:
        bucket: Storage bucket name
        region: GCP region for deployment
        timeout_sec: Function timeout in seconds
        memory: Memory allocation for the function
        trigger_type: Type of trigger ("finalized", "deleted", "metadata_updated")

    Example:
        @gaco_storage_trigger(trigger_type="finalized")
        def on_file_uploaded(context: GacoEventContext, event):
            file_name = event.data.name
            # Process uploaded file
    """

    def decorator(func: F) -> F:
        config = get_config()

        # Use config defaults if not specified
        actual_region = region or config.default_region
        actual_timeout = timeout_sec or 300
        actual_memory = memory or config.default_memory

        # Select appropriate trigger decorator
        if trigger_type == "finalized":
            trigger_decorator = storage_fn.on_object_finalized
        elif trigger_type == "deleted":
            trigger_decorator = storage_fn.on_object_deleted
        elif trigger_type == "metadata_updated":
            trigger_decorator = storage_fn.on_object_metadata_updated
        else:
            raise ValueError(f"Invalid trigger_type: {trigger_type}")

        # Build decorator kwargs
        kwargs = {
            "region": actual_region,
            "timeout_sec": actual_timeout,
            "memory": actual_memory,
        }
        if bucket:
            kwargs["bucket"] = bucket

        @trigger_decorator(**kwargs)
        @functools.wraps(func)
        def wrapper(event: Any) -> None:
            try:
                db = firestore.client()
                context = GacoEventContext(
                    db=db, event=event, event_type=f"storage_{trigger_type}"
                )

                return func(context, event)

            except Exception as e:
                logger.error(f"Error in {func.__name__}: {e}")
                logger.error(traceback.format_exc())
                raise

        return wrapper

    return decorator

