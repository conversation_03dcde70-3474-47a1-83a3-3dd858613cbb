"""
Shared types for the Gaco Framework to prevent circular imports.
"""
from dataclasses import dataclass, field
from typing import Any, Dict, List, Optional, Set

from firebase_admin import auth
from firebase_functions import logger


@dataclass
class AuthContext:
    """
    Enhanced authentication context with custom claims support.

    When instantiated with only a user_id, it will automatically fetch the
    full user record from Firebase Auth to populate claims, roles, etc.
    """

    user_id: str
    email: Optional[str] = None
    email_verified: Optional[bool] = None
    custom_claims: Optional[Dict[str, Any]] = None
    roles: Set[str] = field(default_factory=set)
    permissions: Set[str] = field(default_factory=set)
    is_service_account: bool = False
    raw_token: Optional[Dict[str, Any]] = None

    def __post_init__(self):
        """
        Initialize collections and fetch user data if not provided.
        """
        # If created with just a user_id, fetch the full user record.
        if self.user_id and self.raw_token is None:
            try:
                user_record = auth.get_user(self.user_id)
                self.email = self.email or user_record.email
                self.email_verified = self.email_verified or user_record.email_verified
                if self.custom_claims is None:
                    self.custom_claims = user_record.custom_claims
            except Exception as e:
                logger.warn(
                    f"Could not fetch user record for {self.user_id}: {e}"
                )

        if self.custom_claims is None:
            self.custom_claims = {}

        # Extract roles and permissions from custom claims
        if "roles" in self.custom_claims:
            roles = self.custom_claims["roles"]
            if isinstance(roles, list):
                self.roles.update(roles)
            elif isinstance(roles, str):
                self.roles.add(roles)

        if "permissions" in self.custom_claims:
            perms = self.custom_claims["permissions"]
            if isinstance(perms, list):
                self.permissions.update(perms)
            elif isinstance(perms, str):
                self.permissions.add(perms)

    def has_role(self, role: str) -> bool:
        """Check if user has a specific role"""
        return role in self.roles

    def has_any_role(self, roles: List[str]) -> bool:
        """Check if user has any of the specified roles"""
        return bool(self.roles.intersection(roles))

    def has_all_roles(self, roles: List[str]) -> bool:
        """Check if user has all specified roles"""
        return all(role in self.roles for role in roles)

    def has_permission(self, permission: str) -> bool:
        """Check if user has a specific permission"""
        return permission in self.permissions

    def has_any_permission(self, permissions: List[str]) -> bool:
        """Check if user has any of the specified permissions"""
        return bool(self.permissions.intersection(permissions))

    def has_all_permissions(self, permissions: List[str]) -> bool:
        """Check if user has all specified permissions"""
        return all(perm in self.permissions for perm in permissions)

    def get_claim(self, key: str, default: Any = None) -> Any:
        """Get a custom claim value"""
        return self.custom_claims.get(key, default)

    @classmethod
    def from_firebase_auth(cls, auth_data: Any) -> "AuthContext":
        """Create AuthContext from Firebase auth data"""
        if not auth_data:
            return cls(user_id=None)

        # Extract custom claims
        custom_claims = {}
        if hasattr(auth_data, "token") and auth_data.token:
            custom_claims = dict(auth_data.token)
            # Remove standard claims
            for std_claim in [
                "iss",
                "sub",
                "aud",
                "exp",
                "iat",
                "auth_time",
                "email",
                "email_verified",
                "uid",
            ]:
                custom_claims.pop(std_claim, None)

        return cls(
            user_id=getattr(auth_data, "uid", None),
            email=(
                getattr(auth_data, "email", None) or auth_data.token.get("email")
                if hasattr(auth_data, "token")
                else None
            ),
            email_verified=(
                getattr(auth_data, "email_verified", False)
                or auth_data.token.get("email_verified", False)
                if hasattr(auth_data, "token")
                else False
            ),
            custom_claims=custom_claims,
            is_service_account=(
                auth_data.token.get("service_account", False)
                if hasattr(auth_data, "token")
                else False
            ),
            raw_token=auth_data.token if hasattr(auth_data, "token") else None,
        )
