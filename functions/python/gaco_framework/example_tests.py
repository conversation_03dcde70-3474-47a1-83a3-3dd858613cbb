# Example: Testing Endpoints with Authorization

import pytest
from gaco_framework.testing import (
    GacoTestClient,
    assert_response_success,
    assert_auth_error,
    assert_authz_error,
    create_mock_auth_context,
)
from gaco_framework import AuthContext

# Import your endpoints
from dua_cloud_functions.portfolio_manager_endpoints import (
    create_portfolio_item,
    update_portfolio_item,
    delete_portfolio_item,
    get_portfolio_items,
    admin_audit_portfolios,
)


class TestPortfolioEndpoints:
    """Test portfolio endpoints with various auth scenarios"""

    def setup_method(self):
        """Setup test client and mock data"""
        self.client = GacoTestClient()
        self.creative_id = "test_creative_123"
        self.other_user_id = "other_user_456"
        self.admin_id = "admin_user_789"

        # Setup test data
        self.portfolio_data = {
            "type": "image",
            "position": 0,
            "imageUrl": "https://example.com/image.jpg",
            "alt": "Test image",
        }

    def test_create_portfolio_item_as_owner(self):
        """Test creating portfolio item as the owner"""
        # Set auth as creative owner
        self.client.set_auth(
            user_id=self.creative_id,
            roles={"creative"},
            custom_claims={"email_verified": True},
        )

        # Call endpoint
        response = self.client.call_endpoint(
            create_portfolio_item,
            {"creative_id": self.creative_id, "data": self.portfolio_data},
        )

        # Assert success
        assert_response_success(response, 201)
        assert response["data"]["creativeId"] == self.creative_id

    def test_create_portfolio_item_as_other_user(self):
        """Test creating portfolio item as different user (should fail)"""
        # Set auth as different user
        self.client.set_auth(
            user_id=self.other_user_id,
            roles={"creative"},
            custom_claims={"email_verified": True},
        )

        # Call endpoint - should fail
        response = self.client.call_endpoint(
            create_portfolio_item,
            {"creative_id": self.creative_id, "data": self.portfolio_data},
        )

        # Assert authorization error
        assert_authz_error(response)

    def test_create_portfolio_item_without_auth(self):
        """Test creating portfolio item without authentication"""
        # Don't set auth

        # Call endpoint - should fail
        response = self.client.call_endpoint(
            create_portfolio_item,
            {"creative_id": self.creative_id, "data": self.portfolio_data},
        )

        # Assert authentication error
        assert_auth_error(response)

    def test_create_portfolio_item_unverified_email(self):
        """Test creating with unverified email"""
        # Set auth with unverified email
        self.client.set_auth(
            user_id=self.creative_id,
            roles={"creative"},
            custom_claims={"email_verified": False},
        )

        # Call endpoint - should fail
        response = self.client.call_endpoint(
            create_portfolio_item,
            {"creative_id": self.creative_id, "data": self.portfolio_data},
        )

        # Assert authentication error (email not verified)
        assert_auth_error(response)

    def test_delete_portfolio_item_as_admin(self):
        """Test admin can delete any portfolio item"""
        # Set auth as admin
        self.client.set_auth(
            user_id=self.admin_id,
            roles={"admin"},
            custom_claims={"email_verified": True},
        )

        # First create an item in the mock DB
        mock_item_id = "mock_item_123"
        self.client.db.collection("portfolioItems").document(mock_item_id).set(
            {"creative_id": self.creative_id, "type": "image", "position": 0}
        )

        # Call delete endpoint as admin
        response = self.client.call_endpoint(
            delete_portfolio_item,
            {"creative_id": self.creative_id, "item_id": mock_item_id},
        )

        # Assert success - admin can delete any item
        assert_response_success(response)

    def test_get_portfolio_items_public(self):
        """Test public access to view portfolio"""
        # Don't set auth - public endpoint

        # Call endpoint
        response = self.client.call_endpoint(
            get_portfolio_items, {"creative_id": self.creative_id}
        )

        # Assert success - public access allowed
        assert_response_success(response)

    def test_admin_audit_without_admin_role(self):
        """Test admin endpoint without admin role"""
        # Set auth as regular user
        self.client.set_auth(
            user_id=self.creative_id,
            roles={"creative"},
            custom_claims={"email_verified": True},
        )

        # Call admin endpoint
        response = self.client.call_endpoint(admin_audit_portfolios, {})

        # Assert authorization error
        assert_authz_error(response)

    def test_custom_claims_access(self):
        """Test endpoint that requires custom claims"""
        # Create auth with custom claims
        auth_context = create_mock_auth_context(
            user_id=self.creative_id,
            roles={"creative"},
            custom_claims={
                "email_verified": True,
                "subscription_tier": "premium",
                "beta_features": True,
            },
        )

        # Test with premium features
        self.client.default_auth_context = auth_context

        # Your premium endpoint test here
        # response = self.client.call_endpoint(premium_endpoint, {...})


# Example: Testing with middleware
def test_with_rate_limiting():
    """Test endpoint with rate limiting middleware"""
    from gaco_framework import gaco_endpoint_with_middleware, RateLimitMiddleware

    # Create endpoint with rate limiting
    @gaco_endpoint_with_middleware(
        middleware=[RateLimitMiddleware(max_requests_per_minute=5)]
    )
    def rate_limited_endpoint(context, data):
        return {"success": True}

    client = GacoTestClient()
    client.set_auth(user_id="test_user")

    # First 5 calls should succeed
    for _ in range(5):
        response = client.call_endpoint(rate_limited_endpoint, {})
        assert response.get("success") is True

    # 6th call should fail (if rate limiting was implemented)
    # response = client.call_endpoint(rate_limited_endpoint, {})
    # assert_response_error(response, 429)  # Rate limit error


if __name__ == "__main__":
    # Run basic test
    test = TestPortfolioEndpoints()
    test.setup_method()
    test.test_create_portfolio_item_as_owner()
    print("✅ Test passed: Owner can create portfolio item")

    test.test_create_portfolio_item_as_other_user()
    print("✅ Test passed: Other user cannot create portfolio item")

    test.test_create_portfolio_item_without_auth()
    print("✅ Test passed: Unauthenticated user cannot create portfolio item")

    print("\n🎉 All tests passed!")
