# Example: Updated Portfolio Manager Service with Authorization

from typing import List, Optional
from firebase_admin import firestore
from firebase_functions import logger

from gaco_framework import BaseGacoManager, AuthContext
from gaco_framework.exceptions import NotFoundError, AuthorizationError
from gaco_framework.security import ResourceAccess

from models.portfolio_item import (
    PortfolioItem,
    PortfolioItemCreateRequest,
    PortfolioItemUpdateRequest,
)


class PortfolioManagerService(BaseGacoManager):
    """
    Enhanced Portfolio Manager with built-in authorization.

    This service now receives auth_context from the framework and
    can make authorization decisions based on the current user.
    """

    def __init__(
        self, db: firestore.Client, auth_context: Optional[AuthContext] = None
    ):
        super().__init__(db, auth_context)
        self.collection_name = "portfolioItems"

    def create_portfolio_item(
        self, creative_id: str, data: PortfolioItemCreateRequest
    ) -> PortfolioItem:
        """
        Create a new portfolio item.

        Authorization: User must be the creative owner.
        """
        # Verify ownership at service level (redundant with endpoint check for defense in depth)
        if self.auth_context and self.auth_context.user_id != creative_id:
            raise AuthorizationError(
                f"User {self.auth_context.user_id} cannot create items for creative {creative_id}"
            )

        # Log the action
        self._log_access("create", "portfolio", creative_id)

        # Create the item
        item_data = {
            "creative_id": creative_id,
            "type": data.type,
            "position": data.position,
            "created_at": firestore.SERVER_TIMESTAMP,
            "updated_at": firestore.SERVER_TIMESTAMP,
            **data.model_dump(exclude={"creative_id"}),
        }

        # Add to Firestore
        doc_ref = self._get_collection(self.collection_name).document()
        doc_ref.set(item_data)

        # Return created item
        created_item = PortfolioItem(id=doc_ref.id, **item_data)

        logger.info(
            f"Portfolio item created",
            extra={
                "item_id": doc_ref.id,
                "creative_id": creative_id,
                "created_by": self.auth_context.user_id if self.auth_context else None,
            },
        )

        return created_item

    def update_portfolio_item(
        self, creative_id: str, item_id: str, data: PortfolioItemUpdateRequest
    ) -> PortfolioItem:
        """
        Update a portfolio item.

        Authorization: User must be the creative owner or admin.
        """
        # Get existing item
        doc_ref = self._get_collection(self.collection_name).document(item_id)
        doc = doc_ref.get()

        if not doc.exists:
            raise NotFoundError(f"Portfolio item {item_id} not found")

        existing_data = doc.to_dict()

        # Verify ownership
        if existing_data.get("creative_id") != creative_id:
            raise NotFoundError(
                f"Portfolio item {item_id} not found for creative {creative_id}"
            )

        # Check authorization
        if self.auth_context:
            is_owner = self.auth_context.user_id == creative_id
            is_admin = self.auth_context.has_role("admin")

            if not (is_owner or is_admin):
                raise AuthorizationError(
                    f"User {self.auth_context.user_id} cannot update items for creative {creative_id}"
                )

        # Update the item
        update_data = {
            **data.model_dump(exclude_unset=True),
            "updated_at": firestore.SERVER_TIMESTAMP,
        }

        doc_ref.update(update_data)

        # Return updated item
        updated_item = PortfolioItem(id=item_id, **{**existing_data, **update_data})

        logger.info(
            f"Portfolio item updated",
            extra={
                "item_id": item_id,
                "creative_id": creative_id,
                "updated_by": self.auth_context.user_id if self.auth_context else None,
                "is_admin": (
                    self.auth_context.has_role("admin") if self.auth_context else False
                ),
            },
        )

        return updated_item

    def delete_portfolio_item(self, creative_id: str, item_id: str) -> None:
        """
        Delete a portfolio item.

        Authorization: User must be the creative owner or admin.
        """
        # Get existing item
        doc_ref = self._get_collection(self.collection_name).document(item_id)
        doc = doc_ref.get()

        if not doc.exists:
            raise NotFoundError(f"Portfolio item {item_id} not found")

        existing_data = doc.to_dict()

        # Verify ownership
        if existing_data.get("creative_id") != creative_id:
            raise NotFoundError(
                f"Portfolio item {item_id} not found for creative {creative_id}"
            )

        # Check authorization using base class method
        self._check_access("portfolio", existing_data, ResourceAccess.DELETE)

        # Delete the item
        doc_ref.delete()

        logger.info(
            f"Portfolio item deleted",
            extra={
                "item_id": item_id,
                "creative_id": creative_id,
                "deleted_by": self.auth_context.user_id if self.auth_context else None,
                "is_admin": (
                    self.auth_context.has_role("admin") if self.auth_context else False
                ),
            },
        )

    def get_portfolio_items_by_creative(self, creative_id: str) -> List[PortfolioItem]:
        """
        Get all portfolio items for a creative.

        This is a public read operation - no auth required.
        """
        # For public reads, we might want to filter sensitive fields
        query = (
            self._get_collection(self.collection_name)
            .where("creative_id", "==", creative_id)
            .order_by("position")
        )

        items = []
        for doc in query.stream():
            item_data = doc.to_dict()

            # Filter fields based on viewer permissions
            if self.auth_context:
                is_owner = self.auth_context.user_id == creative_id
                is_admin = self.auth_context.has_role("admin")

                # Remove sensitive fields for non-owners
                if not (is_owner or is_admin):
                    # Example: Remove private fields
                    item_data.pop("private_notes", None)
                    item_data.pop("internal_metadata", None)

            items.append(PortfolioItem(id=doc.id, **item_data))

        return items

    def update_portfolio_items_order(
        self, creative_id: str, ordered_item_ids: List[str]
    ) -> List[PortfolioItem]:
        """
        Update the order of portfolio items.

        Authorization: User must be the creative owner.
        """
        # Check ownership
        if self.auth_context and self.auth_context.user_id != creative_id:
            raise AuthorizationError(
                f"User {self.auth_context.user_id} cannot reorder items for creative {creative_id}"
            )

        # Batch update positions
        batch = self.db.batch()
        updated_items = []

        for position, item_id in enumerate(ordered_item_ids):
            doc_ref = self._get_collection(self.collection_name).document(item_id)
            batch.update(
                doc_ref,
                {"position": position, "updated_at": firestore.SERVER_TIMESTAMP},
            )

            # Get item data for response
            doc = doc_ref.get()
            if doc.exists:
                updated_items.append(
                    PortfolioItem(id=doc.id, **{**doc.to_dict(), "position": position})
                )

        # Commit batch
        batch.commit()

        logger.info(
            f"Portfolio items reordered",
            extra={
                "creative_id": creative_id,
                "item_count": len(ordered_item_ids),
                "reordered_by": (
                    self.auth_context.user_id if self.auth_context else None
                ),
            },
        )

        return updated_items

    def get_all_portfolios_admin(self) -> List[dict]:
        """
        Admin-only method to get all portfolios with metadata.

        Authorization: Enforced at endpoint level - assumes admin access.
        """
        if not self.auth_context or not self.auth_context.has_role("admin"):
            raise AuthorizationError("Admin access required")

        # Get all portfolios with additional admin data
        all_items = []
        query = self._get_collection(self.collection_name).limit(1000)

        for doc in query.stream():
            item_data = doc.to_dict()
            # Add admin metadata
            item_data["_admin"] = {
                "document_id": doc.id,
                "document_path": doc.reference.path,
                "accessed_by": self.auth_context.user_id,
                "access_time": firestore.SERVER_TIMESTAMP,
            }
            all_items.append(item_data)

        logger.info(
            f"Admin portfolio audit performed",
            extra={
                "admin_user": self.auth_context.user_id,
                "items_retrieved": len(all_items),
            },
        )

        return all_items
