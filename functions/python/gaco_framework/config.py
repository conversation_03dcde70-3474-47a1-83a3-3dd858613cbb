"""
Configuration management for Gaco Framework
"""

from dataclasses import dataclass, field
from typing import Dict, Any, Optional
from firebase_functions import options


@dataclass
class GacoConfig:
    """Framework configuration with sensible defaults"""

    # Default Cloud Function settings
    default_region: str = "europe-west3"
    default_timeout: int = 60
    default_memory: options.MemoryOption = options.MemoryOption.GB_1

    # Project settings
    project_id: str = ""

    # Collection names mapping
    collections: Dict[str, str] = field(
        default_factory=lambda: {
            "users": "users",
            "creatives": "creatives",
            "bookers": "bookers",
            "projects": "projects",
            "offers": "offers",
            "bookings": "bookings",
            "portfolios": "portfolioItems",
            "reviews": "reviews",
        }
    )

    # Feature flags
    enable_auth_check: bool = True
    enable_logging: bool = True
    enable_metrics: bool = False
    enable_cors: bool = True

    # Authentication settings
    auth_required_by_default: bool = True
    auth_error_message: str = "User must be authenticated"

    # Response settings
    default_success_code: int = 200
    default_error_code: int = 500

    # Validation settings
    strict_validation: bool = True
    validate_responses: bool = False

    # Security settings
    enable_security_rules: bool = True
    log_security_violations: bool = True

    # Custom settings (for user extensions)
    custom: Dict[str, Any] = field(default_factory=dict)


# Global config instance
_config = GacoConfig()


def configure_gaco(**kwargs: Any) -> None:
    """
    Configure the Gaco framework with custom settings.

    Args:
        **kwargs: Configuration parameters to override

    Example:
        configure_gaco(
            default_region="us-central1",
            project_id="my-project",
            collections={"users": "app_users"},
            enable_metrics=True
        )
    """
    global _config

    for key, value in kwargs.items():
        if hasattr(_config, key):
            setattr(_config, key, value)
        else:
            # Store unknown configs in custom dict
            _config.custom[key] = value


def get_config() -> GacoConfig:
    """Get the current framework configuration"""
    return _config


def reset_config() -> None:
    """Reset configuration to defaults (mainly for testing)"""
    global _config
    _config = GacoConfig()
