"""
Enhanced Gaco Framework with Explicit Middleware Support
"""

import time
from typing import List, Callable, Any
from firebase_functions import logger

from .auth import AuthContext
from .context import GacoContext
from .decorators import gaco_endpoint
from .endpoint import GacoEndpoint
from .exceptions import RateLimitError, AuthorizationError
from .security import ResourceAccess, check_resource_access

# Middleware type definition
MiddlewareFunction = Callable[[GacoContext, dict, Callable], Any]


class GacoMiddleware:
    """Base class for middleware components"""

    def process_request(self, context: GacoContext, data: dict) -> dict:
        """Process request before it reaches the handler"""
        return data

    def process_response(self, context: GacoContext, response: Any) -> Any:
        """Process response after handler execution"""
        return response

    def process_exception(self, context: GacoContext, exception: Exception) -> Any:
        """Handle exceptions during processing"""
        raise exception


class LoggingMiddleware(GacoMiddleware):
    """Logging middleware"""

    def process_request(self, context: GacoContext, data: dict) -> dict:
        logger.info(f"Request: {data}")
        return data

    def process_response(self, context: GacoContext, response: Any) -> Any:
        logger.info(f"Response success")
        return response


class RateLimitMiddleware(GacoMiddleware):
    """Rate limiting middleware"""

    def __init__(self, max_requests_per_minute: int = 60):
        self.max_requests = max_requests_per_minute

    def process_request(self, context: GacoContext, data: dict) -> dict:
        # Check rate limit for user
        user_id = context.user_id
        if self._is_rate_limited(user_id):
            raise RateLimitError("Too many requests")
        return data

    def _is_rate_limited(self, user_id: str) -> bool:
        # Implementation would check Redis/Firestore for request count
        return False


class MetricsMiddleware(GacoMiddleware):
    """Metrics collection middleware"""

    def process_request(self, context: GacoContext, data: dict) -> dict:
        context.request_data = context.request_data or {}
        context.request_data["start_time"] = time.time()
        return data

    def process_response(self, context: GacoContext, response: Any) -> Any:
        if "start_time" in context.request_data:
            duration = time.time() - context.request_data["start_time"]
            logger.info(f"Request duration: {duration:.2f}s")
        return response


class AuthorizationMiddleware(GacoMiddleware):
    """
    Authorization middleware for resource-level access control

    Example:
        @gaco_endpoint_with_middleware([
            AuthorizationMiddleware(
                resource_type="portfolio",
                access_level=ResourceAccess.WRITE
            )
        ])
        def update_portfolio(context: GacoContext, data: dict):
            # Only users with write access can execute this
            pass
    """

    def __init__(self, resource_type: str, access_level: ResourceAccess):
        self.resource_type = resource_type
        self.access_level = access_level

    def process_request(self, context: GacoContext, data: dict) -> dict:
        if not context.auth_context:
            raise AuthorizationError("Authentication required")

        # Check resource access
        check_resource_access(
            context.auth_context, self.resource_type, data, self.access_level
        )

        return data


def gaco_endpoint_with_middleware(
    middleware: List[GacoMiddleware] = None, **decorator_kwargs
):
    """
    Enhanced endpoint decorator with middleware support

    Args:
        middleware: List of middleware to apply
        **decorator_kwargs: Standard gaco_endpoint arguments
    """
    middleware = middleware or []

    def decorator(func):
        @gaco_endpoint(**decorator_kwargs)
        def wrapper(context: GacoContext, data: dict):
            try:
                # Process request through middleware
                for mw in middleware:
                    data = mw.process_request(context, data)

                # Execute the actual function
                result = func(context, data)

                # Process response through middleware (in reverse order)
                for mw in reversed(middleware):
                    result = mw.process_response(context, result)

                return result

            except Exception as e:
                # Process exceptions through middleware
                for mw in middleware:
                    try:
                        result = mw.process_exception(context, e)
                        if result is not None:
                            return result
                    except Exception:
                        continue
                raise e

        return wrapper

    return decorator
