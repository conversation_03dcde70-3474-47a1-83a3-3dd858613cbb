# Example: Updated Portfolio Manager Endpoints with Authorization

from firebase_functions import logger

from gaco_framework import (
    GacoContext,
    GacoResponse,
    gaco_endpoint,
    GacoEndpoint,
    AuthRequirement,
    require_auth,
    require_custom_auth,
    ResourceAccess,
    check_resource_access,
)

from services.portfolio_manager import PortfolioManagerService
from models.portfolio_item import (
    PortfolioItemCreateRequest,
    PortfolioItemUpdateRequest,
    PortfolioItemsOrderUpdateRequest,
    CreatePortfolioItemFullRequest,
    UpdatePortfolioItemFullRequest,
    DeletePortfolioItemFullRequest,
    UpdatePortfolioItemsOrderFullRequest,
)
from gaco_framework.exceptions import NotFoundError, AuthorizationError


# Custom authorization check for portfolio ownership
def is_portfolio_owner(context: GacoContext) -> bool:
    """Check if the current user owns the portfolio being accessed"""
    creative_id = context.request_data.get("creative_id")
    return context.auth_context.user_id == creative_id


@gaco_endpoint(
    require_auth=AuthRequirement.VERIFIED_EMAIL,
    required_roles=["creative"],  # Only creatives can manage portfolios
)
@GacoEndpoint(CreatePortfolioItemFullRequest)
@require_custom_auth(is_portfolio_owner)  # Must be the portfolio owner
def create_portfolio_item(
    context: GacoContext,
    request: CreatePortfolioItemFullRequest,
) -> GacoResponse:
    """
    Creates a new portfolio item for a given creative.

    Authorization:
    - User must have verified email
    - User must have 'creative' role
    - User must be the owner of the portfolio (creative_id must match user_id)
    """
    logger.info(
        f"Creating portfolio item for creative_id: {request.creative_id}",
        extra={
            "user_id": context.auth_context.user_id,
            "creative_id": request.creative_id,
        },
    )

    portfolio_service = context.get_manager(PortfolioManagerService)

    # The service now has access to auth_context for additional checks
    new_item = portfolio_service.create_portfolio_item(
        creative_id=request.creative_id,
        data=request.data,
    )

    return GacoResponse(
        success=True,
        message="Portfolio item created successfully.",
        data=new_item.model_dump(),
        code=201,
    )


@gaco_endpoint(require_auth=AuthRequirement.VERIFIED_EMAIL, required_roles=["creative"])
@GacoEndpoint(UpdatePortfolioItemFullRequest)
def update_portfolio_item(
    context: GacoContext,
    request: UpdatePortfolioItemFullRequest,
) -> GacoResponse:
    """
    Updates an existing portfolio item.

    Authorization handled at service level using the auth context.
    """
    logger.info(
        f"Updating portfolio item: {request.item_id} for creative: {request.creative_id}",
        extra={
            "user_id": context.auth_context.user_id,
            "creative_id": request.creative_id,
            "item_id": request.item_id,
        },
    )

    # Check resource access
    check_resource_access(
        context.auth_context,
        "portfolio",
        {"creative_id": request.creative_id},
        ResourceAccess.WRITE,
    )

    portfolio_service = context.get_manager(PortfolioManagerService)

    updated_item = portfolio_service.update_portfolio_item(
        creative_id=request.creative_id,
        item_id=request.item_id,
        data=request.data,
    )

    return GacoResponse(
        success=True,
        message="Portfolio item updated successfully.",
        data=updated_item.model_dump(),
        code=200,
    )


@gaco_endpoint(
    require_auth=AuthRequirement.VERIFIED_EMAIL,
    required_roles=["creative", "admin"],  # Admins can also delete
)
@GacoEndpoint(DeletePortfolioItemFullRequest)
def delete_portfolio_item(
    context: GacoContext,
    request: DeletePortfolioItemFullRequest,
) -> GacoResponse:
    """
    Deletes a portfolio item.

    Authorization:
    - User must have verified email
    - User must be either the creative owner or an admin
    """
    logger.info(
        f"Deleting portfolio item: {request.item_id} for creative: {request.creative_id}",
        extra={
            "user_id": context.auth_context.user_id,
            "creative_id": request.creative_id,
            "item_id": request.item_id,
            "is_admin": context.auth_context.has_role("admin"),
        },
    )

    # Only check ownership if not admin
    if not context.auth_context.has_role("admin"):
        check_resource_access(
            context.auth_context,
            "portfolio",
            {"creative_id": request.creative_id},
            ResourceAccess.DELETE,
        )

    portfolio_service = context.get_manager(PortfolioManagerService)

    portfolio_service.delete_portfolio_item(
        creative_id=request.creative_id,
        item_id=request.item_id,
    )

    return GacoResponse(
        success=True,
        message="Portfolio item deleted successfully.",
        code=200,
    )


# Example: Public endpoint for viewing portfolios
@gaco_endpoint(require_auth=False)  # Public access for viewing
def get_portfolio_items(context: GacoContext, data: dict) -> GacoResponse:
    """
    Retrieves portfolio items for a creative (public access).

    No authentication required for viewing portfolios.
    """
    creative_id = data.get("creative_id")
    if not creative_id:
        raise ValidationError("creative_id is required")

    portfolio_service = context.get_manager(PortfolioManagerService)

    items = portfolio_service.get_portfolio_items_by_creative(creative_id)

    return GacoResponse(
        success=True,
        message="Portfolio items retrieved successfully.",
        data={"items": [item.model_dump() for item in items]},
        code=200,
    )


# Example: Admin-only endpoint
@gaco_endpoint(require_auth=AuthRequirement.ADMIN)  # Admin role required
def admin_audit_portfolios(context: GacoContext, data: dict) -> GacoResponse:
    """
    Admin endpoint to audit all portfolios.

    Only users with admin role can access this.
    """
    logger.info(f"Admin audit requested by user: {context.auth_context.user_id}")

    # Admin-specific logic here
    portfolio_service = context.get_manager(PortfolioManagerService)

    # Example: Get all portfolios with additional admin data
    all_portfolios = portfolio_service.get_all_portfolios_admin()

    return GacoResponse(
        success=True,
        message="Audit data retrieved successfully.",
        data={"portfolios": all_portfolios},
        code=200,
    )
