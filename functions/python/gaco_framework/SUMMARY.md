# GACO Framework - Authorization Update Summary

## What's New

The GACO Framework has been updated to v2 with comprehensive authorization and security features:

1. **Enhanced Authentication Context** - Automatic extraction of user tokens, custom claims, roles, and permissions
2. **Declarative Authorization** - Simple decorators for auth requirements
3. **Resource-Level Security** - Built-in access control for resources
4. **Custom Claims Support** - Full Firebase custom claims integration
5. **Testing Utilities** - Enhanced testing with auth mocking

## Quick Start

```python
from gaco_framework import (
    gaco_endpoint,
    GacoEndpoint,
    AuthRequirement,
    GacoContext,
    GacoResponse
)

# Basic authenticated endpoint
@gaco_endpoint(require_auth=AuthRequirement.VERIFIED_EMAIL)
@GacoEndpoint(MyRequestModel)
def my_endpoint(context: GacoContext, request: MyRequestModel) -> GacoResponse:
    # User is authenticated with verified email
    user_id = context.auth_context.user_id
    
    # Check roles
    if context.has_role("admin"):
        # Admin logic
    
    return GacoResponse(
        success=True,
        message="Success",
        data={"user_id": user_id},
        code=200
    )

# Role-based access
@gaco_endpoint(
    require_auth=True,
    required_roles=["creative", "booker"]
)
def creative_endpoint(context: GacoContext, data: dict):
    # User must have creative OR booker role
    pass
```
# Custom authorization
from gaco_framework_v2 import require_custom_auth

def is_owner(context: GacoContext) -> bool:
    return context.auth_context.user_id == context.request_data.get("owner_id")

@gaco_endpoint()
@require_custom_auth(is_owner)
def owner_only_endpoint(context: GacoContext, data: dict):
    # Only resource owner can access
    pass
```

## Key Components

- `AuthContext` - User authentication information
- `AuthRequirement` - Standard auth requirements (NONE, AUTHENTICATED, VERIFIED_EMAIL, ADMIN)
- `ResourceAccess` - Access levels (READ, WRITE, DELETE, ADMIN, OWNER)
- `SecurityRule` - Define custom security rules
- `@require_auth` - Enforce auth requirements
- `@require_claims` - Require specific custom claims
- `check_resource_access` - Validate resource access

## Migration Notes

1. Update imports to `gaco_framework_v2`
2. Add auth decorators to endpoints
3. Update managers to use `auth_context`
4. Replace manual auth checks with decorators
5. Test with enhanced testing utilities

See example_portfolio_endpoints.py for a complete implementation example.