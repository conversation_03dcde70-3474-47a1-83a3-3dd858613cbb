"""
GACO Framework v2 - Enhanced with Authorization
"""

from .auth import (
    Auth<PERSON>ontext,
    AuthRequirement,
    require_auth,
    require_claims,
    require_any_claims,
    require_all_claims,
    require_custom_auth,
)
from .config import GacoConfig, get_config
from .context import GacoContext, GacoEventContext
from .decorators import (
    gaco_endpoint,
    gaco_firestore_trigger,
    gaco_storage_trigger,
)
from .endpoint import GacoEndpoint, GacoEndpointNoValidation, validate_request
from .exceptions import (
    AuthenticationError,
    AuthorizationError,
    ConflictError,
    GacoError,
    NotFoundError,
    RateLimitError,
    ValidationError,
)
from .managers import BaseGacoManager
from .middleware import (
    GacoMiddleware,
    LoggingMiddleware,
    RateLimitMiddleware,
    MetricsMiddleware,
    gaco_endpoint_with_middleware,
)
from .models import BaseGacoModel, GacoResponse
from .security import (
    SecurityRule,
    SecurityRules,
    ResourceAccess,
    check_resource_access,
)
from .testing import GacoTestClient
from .trigger_types import DocumentTriggerType

__all__ = [
    # Auth
    "AuthContext",
    "AuthRequirement",
    "require_auth",
    "require_claims",
    "require_any_claims",
    "require_all_claims",
    "require_custom_auth",
    # Config
    "GacoConfig",
    "get_config",
    # Context
    "GacoContext",
    "GacoEventContext",
    # Decorators
    "gaco_endpoint",
    "gaco_firestore_trigger",
    "gaco_storage_trigger",
    # Endpoint
    "GacoEndpoint",
    "GacoEndpointNoValidation",
    "validate_request",
    # Exceptions
    "GacoError",
    "ValidationError",
    "AuthenticationError",
    "AuthorizationError",
    "NotFoundError",
    "ConflictError",
    "RateLimitError",
    # Managers
    "BaseGacoManager",
    # Middleware
    "GacoMiddleware",
    "LoggingMiddleware",
    "RateLimitMiddleware",
    "MetricsMiddleware",
    "gaco_endpoint_with_middleware",
    # Models
    "BaseGacoModel",
    "GacoResponse",
    # Security
    "SecurityRule",
    "SecurityRules",
    "ResourceAccess",
    "check_resource_access",
    # Testing
    "GacoTestClient",
    # Trigger types
    "DocumentTriggerType",
]

__version__ = "0.2.0"
