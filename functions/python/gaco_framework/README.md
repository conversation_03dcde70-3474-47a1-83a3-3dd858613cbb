# GACO Framework v2 - Authorization & Security Update

The GACO Framework has been enhanced with comprehensive authorization and security features, including user token validation, custom claims support, and resource-level access control.

## Key Features Added

### 1. Enhanced Authentication Context

The framework now includes a robust `AuthContext` that automatically extracts and manages:
- User ID and email verification status
- Custom claims from Firebase tokens
- Role-based access control (RBAC)
- Permission-based access control
- Service account detection

### 2. Authorization Decorators

Multiple authorization patterns are supported:

```python
# Basic authentication requirement
@gaco_endpoint(require_auth=AuthRequirement.VERIFIED_EMAIL)

# Role-based access
@gaco_endpoint(required_roles=["creative", "booker"])

# Permission-based access
@gaco_endpoint(required_permissions=["portfolio.write", "portfolio.delete"])

# Custom authorization logic
@require_custom_auth(is_resource_owner)

# Claim-based access
@require_claims({"subscription": "premium"})
```

### 3. Resource-Level Security

Security rules can be defined for different resource types:

```python
# Automatic ownership checks
check_resource_access(
    auth_context,
    "portfolio",
    {"creative_id": user_id},
    ResourceAccess.WRITE
)
```

### 4. Enhanced Manager Base Class

Managers now receive auth context for implementing business logic authorization:

```python
class PortfolioManager(BaseGacoManager):
    def create_item(self, data):
        # Access auth context
        if self.auth_context.user_id != data.creative_id:
            raise AuthorizationError("Can only create items for yourself")
```

## Migration Guide

### 1. Update Imports

```python
# Old
from gaco_framework import GacoContext, gaco_endpoint

# New
from gaco_framework_v2 import (
    GacoContext,
    gaco_endpoint,
    AuthRequirement,
    require_auth,
    check_resource_access,
    ResourceAccess
)
```

### 2. Update Endpoint Decorators

```python
# Old - No built-in auth
@gaco_endpoint()
def my_endpoint(context: GacoContext, data: dict):
    # Manual auth check
    if not context.user_id:
        raise AuthenticationError()

# New - Declarative auth
@gaco_endpoint(
    require_auth=AuthRequirement.VERIFIED_EMAIL,
    required_roles=["creative"]
)
def my_endpoint(context: GacoContext, data: dict):
    # Auth already validated
```

### 3. Access Auth Information

```python
# In endpoints
def my_endpoint(context: GacoContext, data: dict):
    # User information
    user_id = context.auth_context.user_id
    email = context.auth_context.email
    
    # Check roles/permissions
    if context.auth_context.has_role("admin"):
        # Admin logic
    
    # Get custom claims
    subscription = context.auth_context.get_claim("subscription_tier")
```

### 4. Implement Resource Security

```python
# Define security rules
from gaco_framework_v2 import SecurityRule, get_security_rules

# Add custom rule
get_security_rules().add_rule(SecurityRule(
    resource_type="project",
    access_level=ResourceAccess.READ,
    condition=lambda auth, data: (
        auth.user_id == data.get("owner_id") or
        auth.user_id in data.get("collaborators", [])
    )
))
```

## Setting Custom Claims

Custom claims must be set server-side using the Admin SDK:

```python
from gaco_framework_v2.auth import set_custom_claims, add_role_to_user

# Set custom claims
await set_custom_claims(user_id, {
    "roles": ["creative", "verified"],
    "subscription_tier": "premium",
    "permissions": ["portfolio.write", "portfolio.delete"]
})

# Add a role
await add_role_to_user(user_id, "admin")
```

## Testing with Auth

The testing utilities have been enhanced:

```python
from gaco_framework_v2.testing import GacoTestClient, create_mock_auth_context

# Create test client
client = GacoTestClient()

# Set auth for tests
client.set_auth(
    user_id="test_creative_123",
    roles={"creative", "verified"},
    permissions={"portfolio.write"}
)

# Test endpoint
response = client.call_endpoint(
    create_portfolio_item,
    {"creative_id": "test_creative_123", "data": {...}}
)
```

## Security Best Practices

1. **Always verify email for sensitive operations**
   ```python
   @gaco_endpoint(require_auth=AuthRequirement.VERIFIED_EMAIL)
   ```

2. **Use resource-level checks for data access**
   ```python
   check_resource_access(context.auth_context, "resource", data, ResourceAccess.WRITE)
   ```

3. **Implement defense in depth**
   - Firestore security rules as first line
   - Function-level auth as second line
   - Business logic validation as third line

4. **Log security events**
   ```python
   logger.info("Access granted", extra={
       "user_id": context.auth_context.user_id,
       "resource": resource_id,
       "action": "write"
   })
   ```

## Configuration

Update your configuration for security:

```python
from gaco_framework_v2 import configure_gaco

configure_gaco(
    auth_required_by_default=True,
    enable_security_rules=True,
    log_security_violations=True
)
```

## Common Patterns

### Owner-Only Access
```python
@require_custom_auth(lambda ctx: ctx.request_data.get("user_id") == ctx.auth_context.user_id)
```

### Admin Override
```python
if not context.has_role("admin"):
    check_resource_access(context.auth_context, "resource", data, access_level)
```

### Subscription Tiers
```python
@require_claims({"subscription_tier": "premium"})
```

### Multi-Role Access
```python
@gaco_endpoint(required_roles=["creative", "booker", "admin"])
```

## Error Handling

The framework provides specific error codes:
- 401: Authentication required or invalid
- 403: Authorization failed (insufficient permissions)
- 400: Validation errors

Errors include helpful messages for debugging while keeping security details hidden from clients.