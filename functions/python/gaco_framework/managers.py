"""
Enhanced base manager for Gaco Framework with authorization
"""

from typing import Any, Dict, List, Optional, Type, TypeVar
from firebase_admin import firestore
from firebase_functions import logger

from .auth import AuthContext
from .config import get_config
from .exceptions import AuthorizationError, NotFoundError
from .models import BaseGacoModel
from .security import ResourceAccess, check_resource_access

T = TypeVar("T", bound=BaseGacoModel)


class BaseGacoManager:
    """Enhanced base manager class with auth support"""

    def __init__(
        self, db: firestore.Client, auth_context: Optional[AuthContext] = None
    ):
        """
        Initialize the manager with database client and auth context.

        Args:
            db: Firestore database client
            auth_context: Optional authentication context
        """
        self.db = db
        self.auth_context = auth_context
        self.config = get_config()
        self._setup_collections()
        self._setup_dependencies()

    def _get_collection(self, collection_name: str) -> firestore.CollectionReference:
        """Get a collection reference using the configured name mapping"""
        actual_name = self.config.collections.get(collection_name, collection_name)
        return self.db.collection(actual_name)

    def _setup_collections(self) -> None:
        """
        Setup collection references. Override to define collection mappings.

        This method is called during initialization to set up any collection
        references that the manager needs.
        """
        if hasattr(self, "collection_name"):
            actual_name = self.config.collections.get(
                self.collection_name, self.collection_name
            )
            self.collection = self.db.collection(actual_name)

    def _setup_dependencies(self) -> None:
        """
        Setup dependent managers/services. Override to inject dependencies.

        This method is called during initialization to set up any dependent
        managers or services that this manager needs.

        Example:
            def _setup_dependencies(self):
                self.user_manager = UserManager(db=self.db)
                self.email_service = EmailService(db=self.db)
        """
        pass


    def _check_access(
        self,
        resource_type: str,
        resource_data: Dict[str, Any],
        access_level: ResourceAccess,
    ) -> None:
        """
        Check access permissions for a resource.

        Args:
            resource_type: Type of resource
            resource_data: Resource data
            access_level: Required access level

        Raises:
            AuthorizationError: If access is denied
        """
        if not self.config.enable_security_rules:
            return

        if not self.auth_context:
            raise AuthorizationError("Authentication required")

        check_resource_access(
            self.auth_context, resource_type, resource_data, access_level
        )

    def _is_owner(self, resource_data: Dict[str, Any]) -> bool:
        """Check if current user is the owner of a resource"""
        if not self.auth_context:
            return False

        owner_fields = ["owner_id", "user_id", "created_by"]
        for field in owner_fields:
            if field in resource_data:
                return resource_data[field] == self.auth_context.user_id

        return False

    def _filter_query_by_access(
        self, query: firestore.Query, resource_type: str
    ) -> firestore.Query:
        """
        Filter a query based on user access permissions.

        Args:
            query: Firestore query
            resource_type: Type of resource

        Returns:
            Filtered query
        """
        if not self.config.enable_security_rules:
            return query

        if not self.auth_context:
            raise AuthorizationError("Authentication required")
        # Admin users can see everything
        if self.auth_context.has_role("admin"):
            return query

        # Apply resource-specific filters
        if resource_type == "portfolio":
            # Users can only see their own portfolios
            return query.where("creative_id", "==", self.auth_context.user_id)
        elif resource_type == "project":
            # Users can see projects they're involved in
            return query.where(
                firestore.FieldFilter.OR(
                    firestore.FieldFilter("booker_id", "==", self.auth_context.user_id),
                    firestore.FieldFilter(
                        "creative_ids", "array_contains", self.auth_context.user_id
                    ),
                )
            )

        # Default: filter by user_id
        return query.where("user_id", "==", self.auth_context.user_id)

    def _log_access(
        self, action: str, resource_type: str, resource_id: Optional[str] = None
    ) -> None:
        """Log access attempts for audit purposes"""
        if self.config.log_security_violations:
            logger.info(
                f"Access log: {action} on {resource_type}",
                extra={
                    "user_id": self.auth_context.user_id if self.auth_context else None,
                    "resource_type": resource_type,
                    "resource_id": resource_id,
                    "action": action,
                },
            )
