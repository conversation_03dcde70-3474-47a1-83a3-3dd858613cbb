"""
Security rules and resource access control for GACO Framework
"""

from dataclasses import dataclass
from enum import Enum
from typing import Any, Callable, Dict, List, Optional, Union
from firebase_admin import firestore
from firebase_functions import logger

from .auth import AuthContext
from .exceptions import AuthorizationError


class ResourceAccess(Enum):
    """Standard resource access levels"""

    NONE = "none"
    READ = "read"
    WRITE = "write"
    DELETE = "delete"
    ADMIN = "admin"  # Full access
    OWNER = "owner"  # Resource owner access


@dataclass
class SecurityRule:
    """
    Security rule definition for resource access

    Attributes:
        resource_type: Type of resource (e.g., "portfolio", "project")
        access_level: Required access level
        condition: Optional function to evaluate custom conditions
        allow_fields: Optional list of allowed fields for partial access
        deny_fields: Optional list of denied fields
    """

    resource_type: str
    access_level: ResourceAccess
    condition: Optional[Callable[[AuthContext, Dict[str, Any]], bool]] = None
    allow_fields: Optional[List[str]] = None
    deny_fields: Optional[List[str]] = None

    def evaluate(
        self, auth_context: AuthContext, resource_data: Dict[str, Any]
    ) -> bool:
        """
        Evaluate if the security rule allows access

        Args:
            auth_context: Current user's auth context
            resource_data: Resource data being accessed

        Returns:
            True if access is allowed
        """
        # Check custom condition if provided
        if self.condition:
            try:
                return self.condition(auth_context, resource_data)
            except Exception as e:
                logger.error(f"Security rule condition error: {e}")
                return False

        # Default to checking ownership for owner access
        if self.access_level == ResourceAccess.OWNER:
            owner_fields = ["owner_id", "user_id", "created_by"]
            for field in owner_fields:
                if field in resource_data:
                    return resource_data[field] == auth_context.user_id

        return True


class SecurityRules:
    """
    Collection of security rules for different resources
    """

    def __init__(self):
        self.rules: Dict[str, List[SecurityRule]] = {}
        self._initialize_default_rules()

    def _initialize_default_rules(self):
        """Initialize default security rules"""
        # Portfolio rules
        self.add_rule(
            SecurityRule(
                resource_type="portfolio",
                access_level=ResourceAccess.OWNER,
                condition=lambda auth, data: auth.user_id == data.get("creative_id"),
            )
        )

        # Project rules
        self.add_rule(
            SecurityRule(
                resource_type="project",
                access_level=ResourceAccess.READ,
                condition=lambda auth, data: (
                    auth.user_id == data.get("booker_id")
                    or auth.user_id in data.get("creative_ids", [])
                ),
            )
        )

    def add_rule(self, rule: SecurityRule):
        """Add a security rule"""
        if rule.resource_type not in self.rules:
            self.rules[rule.resource_type] = []
        self.rules[rule.resource_type].append(rule)

    def check_access(
        self,
        auth_context: AuthContext,
        resource_type: str,
        resource_data: Dict[str, Any],
        access_level: ResourceAccess,
    ) -> bool:
        """
        Check if user has access to a resource

        Args:
            auth_context: User's auth context
            resource_type: Type of resource
            resource_data: Resource data
            access_level: Required access level

        Returns:
            True if access is allowed
        """
        # Admin users have full access
        if auth_context.has_role("admin"):
            return True

        # Get rules for resource type
        resource_rules = self.rules.get(resource_type, [])

        # Check each rule
        for rule in resource_rules:
            # Skip rules that don't match the required access level
            if access_level == ResourceAccess.WRITE and rule.access_level in [
                ResourceAccess.READ,
                ResourceAccess.NONE,
            ]:
                continue
            if access_level == ResourceAccess.DELETE and rule.access_level not in [
                ResourceAccess.DELETE,
                ResourceAccess.ADMIN,
                ResourceAccess.OWNER,
            ]:
                continue

            # Evaluate the rule
            if rule.evaluate(auth_context, resource_data):
                return True

        return False

    def filter_fields(
        self,
        auth_context: AuthContext,
        resource_type: str,
        resource_data: Dict[str, Any],
    ) -> Dict[str, Any]:
        """
        Filter resource fields based on security rules

        Args:
            auth_context: User's auth context
            resource_type: Type of resource
            resource_data: Resource data

        Returns:
            Filtered resource data
        """
        # Admin users see all fields
        if auth_context.has_role("admin"):
            return resource_data

        filtered_data = resource_data.copy()
        resource_rules = self.rules.get(resource_type, [])

        for rule in resource_rules:
            if rule.evaluate(auth_context, resource_data):
                # Apply field filters
                if rule.deny_fields:
                    for field in rule.deny_fields:
                        filtered_data.pop(field, None)

                if rule.allow_fields:
                    # Keep only allowed fields
                    filtered_data = {
                        k: v for k, v in filtered_data.items() if k in rule.allow_fields
                    }

        return filtered_data


# Global security rules instance
_security_rules = SecurityRules()


def get_security_rules() -> SecurityRules:
    """Get the global security rules instance"""
    return _security_rules


def check_resource_access(
    auth_context: AuthContext,
    resource_type: str,
    resource_data: Dict[str, Any],
    access_level: ResourceAccess,
) -> None:
    """
    Check resource access and raise exception if denied

    Args:
        auth_context: User's auth context
        resource_type: Type of resource
        resource_data: Resource data
        access_level: Required access level

    Raises:
        AuthorizationError: If access is denied
    """
    if not get_security_rules().check_access(
        auth_context, resource_type, resource_data, access_level
    ):
        raise AuthorizationError(
            f"Access denied: {access_level.value} access to {resource_type}"
        )
