"""
Authorization context and utilities for GACO Framework
"""

from dataclasses import dataclass
from enum import Enum
from typing import Any, Callable, Dict, List, Optional, Set, Union
from functools import wraps

from firebase_admin import auth
from firebase_functions import logger, https_fn

from .exceptions import AuthenticationError, AuthorizationError
from .context import GacoContext
from .types import AuthContext


class AuthRequirement(Enum):
    """Standard authentication requirements"""

    NONE = "none"
    AUTHENTICATED = "authenticated"
    VERIFIED_EMAIL = "verified_email"
    ADMIN = "admin"
    SERVICE_ACCOUNT = "service_account"


def require_auth(
    requirement: Union[AuthRequirement, str] = AuthRequirement.AUTHENTICATED
):
    """
    Decorator to enforce authentication requirements

    Args:
        requirement: Authentication requirement level

    Example:
        @require_auth(AuthRequirement.VERIFIED_EMAIL)
        def protected_function(context: GacoContext, data: dict):
            # Only accessible with verified email
            pass
    """

    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(context: GacoContext, *args, **kwargs):
            if not context.auth_context:
                raise AuthenticationError("Authentication required")

            req = (
                requirement
                if isinstance(requirement, AuthRequirement)
                else AuthRequirement(requirement)
            )

            if req == AuthRequirement.NONE:
                pass  # No auth required
            elif req == AuthRequirement.AUTHENTICATED:
                if not context.auth_context.user_id:
                    raise AuthenticationError("User must be authenticated")
            elif req == AuthRequirement.VERIFIED_EMAIL:
                if not context.auth_context.email_verified:
                    raise AuthenticationError("Email verification required")
            elif req == AuthRequirement.ADMIN:
                if not context.auth_context.has_role("admin"):
                    raise AuthorizationError("Admin role required")
            elif req == AuthRequirement.SERVICE_ACCOUNT:
                if not context.auth_context.is_service_account:
                    raise AuthorizationError("Service account required")

            return func(context, *args, **kwargs)

        return wrapper

    return decorator


def require_claims(claims: Dict[str, Any]):
    """
    Decorator to require specific custom claims

    Args:
        claims: Dictionary of required claims and their expected values

    Example:
        @require_claims({"subscription": "premium", "tier": "gold"})
        def premium_function(context: GacoContext, data: dict):
            pass
    """

    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(context: GacoContext, *args, **kwargs):
            if not context.auth_context:
                raise AuthenticationError("Authentication required")

            for key, expected_value in claims.items():
                actual_value = context.auth_context.get_claim(key)
                if actual_value != expected_value:
                    raise AuthorizationError(
                        f"Required claim '{key}' with value '{expected_value}' not found"
                    )

            return func(context, *args, **kwargs)

        return wrapper

    return decorator


def require_any_claims(claim_sets: List[Dict[str, Any]]):
    """
    Decorator to require any one of multiple claim sets

    Args:
        claim_sets: List of claim dictionaries - user must match at least one

    Example:
        @require_any_claims([
            {"role": "admin"},
            {"subscription": "premium"},
            {"beta_access": True}
        ])
        def flexible_access_function(context: GacoContext, data: dict):
            pass
    """

    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(context: GacoContext, *args, **kwargs):
            if not context.auth_context:
                raise AuthenticationError("Authentication required")

            for claim_set in claim_sets:
                match = True
                for key, expected_value in claim_set.items():
                    if context.auth_context.get_claim(key) != expected_value:
                        match = False
                        break
                if match:
                    return func(context, *args, **kwargs)

            raise AuthorizationError("User does not match any required claim sets")

        return wrapper

    return decorator


def require_all_claims(claims: List[str]):
    """
    Decorator to require all specified claims to be present

    Args:
        claims: List of claim keys that must be present

    Example:
        @require_all_claims(["subscription_id", "payment_method", "billing_address"])
        def billing_function(context: GacoContext, data: dict):
            pass
    """

    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(context: GacoContext, *args, **kwargs):
            if not context.auth_context:
                raise AuthenticationError("Authentication required")

            missing_claims = []
            for claim in claims:
                if claim not in context.auth_context.custom_claims:
                    missing_claims.append(claim)

            if missing_claims:
                raise AuthorizationError(
                    f"Missing required claims: {', '.join(missing_claims)}"
                )

            return func(context, *args, **kwargs)

        return wrapper

    return decorator


def require_custom_auth(auth_func: Callable[[GacoContext], bool]):
    """
    Decorator for custom authorization logic

    Args:
        auth_func: Function that takes GacoContext and returns True if authorized

    Example:
        def is_resource_owner(context: GacoContext) -> bool:
            resource_id = context.request_data.get("resource_id")
            return context.auth_context.user_id == get_resource_owner(resource_id)

        @require_custom_auth(is_resource_owner)
        def update_resource(context: GacoContext, data: dict):
            pass
    """

    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(context: GacoContext, *args, **kwargs):
            if not context.auth_context:
                raise AuthenticationError("Authentication required")

            try:
                if not auth_func(context):
                    raise AuthorizationError("Custom authorization check failed")
            except AuthorizationError:
                raise
            except Exception as e:
                logger.error(f"Custom auth function error: {e}")
                raise AuthorizationError("Authorization check error")

            return func(context, *args, **kwargs)

        return wrapper

    return decorator


# Helper functions for setting custom claims


async def set_custom_claims(user_id: str, claims: Dict[str, Any]) -> None:
    """
    Set custom claims for a user

    Args:
        user_id: Firebase user ID
        claims: Dictionary of claims to set
    """
    try:
        await auth.set_custom_user_claims(user_id, claims)
        logger.info(f"Set custom claims for user {user_id}: {list(claims.keys())}")
    except Exception as e:
        logger.error(f"Failed to set custom claims for user {user_id}: {e}")
        raise


async def add_role_to_user(user_id: str, role: str) -> None:
    """
    Add a role to a user's custom claims

    Args:
        user_id: Firebase user ID
        role: Role to add
    """
    user = await auth.get_user(user_id)
    current_claims = user.custom_claims or {}
    current_roles = current_claims.get("roles", [])

    if isinstance(current_roles, str):
        current_roles = [current_roles]

    if role not in current_roles:
        current_roles.append(role)
        current_claims["roles"] = current_roles
        await set_custom_claims(user_id, current_claims)


async def remove_role_from_user(user_id: str, role: str) -> None:
    """
    Remove a role from a user's custom claims

    Args:
        user_id: Firebase user ID
        role: Role to remove
    """
    user = await auth.get_user(user_id)
    current_claims = user.custom_claims or {}
    current_roles = current_claims.get("roles", [])

    if isinstance(current_roles, str):
        current_roles = [current_roles]

    if role in current_roles:
        current_roles.remove(role)
        current_claims["roles"] = current_roles if current_roles else []
        await set_custom_claims(user_id, current_claims)


def get_auth_context(request: https_fn.CallableRequest) -> AuthContext:
    # Implementation of get_auth_context function
    pass
