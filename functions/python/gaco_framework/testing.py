"""
Testing utilities for Gaco Framework with auth support
"""

from typing import Any, Dict, Optional, Type, Set, List
from unittest.mock import Mock, MagicMock
from firebase_admin import firestore

from .auth import AuthContext
from .context import GacoContext, GacoEventContext
from .models import GacoResponse


class MockFirestoreClient:
    """Mock Firestore client for testing"""

    def __init__(self):
        self.collections = {}
        self.documents = {}

    def collection(self, name: str):
        if name not in self.collections:
            self.collections[name] = MockCollection(name, self)
        return self.collections[name]


class MockCollection:
    """Mock Firestore collection for testing"""

    def __init__(self, name: str, client: MockFirestoreClient):
        self.name = name
        self.client = client
        self.docs = {}

    def document(self, doc_id: str):
        key = f"{self.name}/{doc_id}"
        if key not in self.client.documents:
            self.client.documents[key] = MockDocument(doc_id, self)
        return self.client.documents[key]

    def add(self, data: Dict[str, Any]):
        doc_id = f"mock_doc_{len(self.docs)}"
        doc = self.document(doc_id)
        doc.set(data)
        return None, doc

    def get(self):
        return [doc for doc in self.docs.values() if doc.exists]

    def where(self, field: str, op: str, value: Any):
        # Return a mock query object
        mock_query = Mock()
        filtered_docs = []
        for doc in self.docs.values():
            if doc.exists and doc._data and field in doc._data:
                if op == "==" and doc._data[field] == value:
                    filtered_docs.append(doc)
                elif op == "!=" and doc._data[field] != value:
                    filtered_docs.append(doc)
                elif op == ">" and doc._data[field] > value:
                    filtered_docs.append(doc)
                elif op == "<" and doc._data[field] < value:
                    filtered_docs.append(doc)
        mock_query.get.return_value = filtered_docs
        return mock_query

    def limit(self, count: int):
        # Return a mock query object
        mock_query = Mock()
        mock_query.get.return_value = list(self.docs.values())[:count]
        return mock_query


class MockDocument:
    """Mock Firestore document for testing"""

    def __init__(self, doc_id: str, collection: MockCollection):
        self.id = doc_id
        self.collection = collection
        self._data = None
        self.exists = False

    def set(self, data: Dict[str, Any]):
        self._data = data
        self.exists = True
        self.collection.docs[self.id] = self

    def update(self, data: Dict[str, Any]):
        if self.exists and self._data:
            self._data.update(data)

    def get(self):
        return self

    def to_dict(self):
        return self._data if self.exists else None

    def delete(self):
        self.exists = False
        if self.id in self.collection.docs:
            del self.collection.docs[self.id]


def create_mock_auth_context(
    user_id: str = "test_user_123",
    email: str = "<EMAIL>",
    email_verified: bool = True,
    roles: Optional[Set[str]] = None,
    permissions: Optional[Set[str]] = None,
    custom_claims: Optional[Dict[str, Any]] = None,
) -> AuthContext:
    """
    Create a mock AuthContext for testing.

    Args:
        user_id: User ID
        email: User email
        email_verified: Whether email is verified
        roles: Set of user roles
        permissions: Set of user permissions
        custom_claims: Additional custom claims

    Returns:
        Mock AuthContext instance
    """
    return AuthContext(
        user_id=user_id,
        email=email,
        email_verified=email_verified,
        roles=roles or set(),
        permissions=permissions or set(),
        custom_claims=custom_claims or {},
    )


def create_mock_context(
    auth_uid: Optional[str] = "test_user_123",
    db: Optional[firestore.Client] = None,
    auth_context: Optional[AuthContext] = None,
    roles: Optional[Set[str]] = None,
    permissions: Optional[Set[str]] = None,
) -> GacoContext:
    """
    Create a mock GacoContext for testing.

    Args:
        auth_uid: Mock user ID
        db: Mock database client (creates one if not provided)
        auth_context: Pre-built auth context
        roles: User roles (if auth_context not provided)
        permissions: User permissions (if auth_context not provided)

    Returns:
        Mock GacoContext instance
    """
    if db is None:
        db = MockFirestoreClient()

    mock_auth = None
    if auth_uid or auth_context:
        mock_auth = Mock()
        if auth_uid:
            mock_auth.uid = auth_uid
        if auth_context:
            mock_auth.token = auth_context.custom_claims

    context = GacoContext(db=db, auth=mock_auth, raw_request=Mock())

    # Override auth_context if provided
    if auth_context:
        context.auth_context = auth_context
    elif auth_uid:
        context.auth_context = create_mock_auth_context(
            user_id=auth_uid, roles=roles, permissions=permissions
        )

    return context


def create_mock_event_context(
    event_data: Optional[Dict[str, Any]] = None, db: Optional[firestore.Client] = None
) -> GacoEventContext:
    """
    Create a mock GacoEventContext for testing.

    Args:
        event_data: Mock event data
        db: Mock database client (creates one if not provided)

    Returns:
        Mock GacoEventContext instance
    """
    if db is None:
        db = MockFirestoreClient()

    mock_event = Mock()
    if event_data:
        mock_event.data = Mock()
        mock_event.data.after = Mock()
        mock_event.data.after.to_dict.return_value = event_data

    return GacoEventContext(db=db, event=mock_event, event_type="test")


class GacoTestClient:
    """Test client for Gaco endpoints"""

    def __init__(self, db: Optional[firestore.Client] = None):
        self.db = db or MockFirestoreClient()
        self.default_auth_context = None

    def set_auth(
        self,
        user_id: str = "test_user",
        roles: Optional[Set[str]] = None,
        permissions: Optional[Set[str]] = None,
        custom_claims: Optional[Dict[str, Any]] = None,
    ):
        """Set default authentication for requests"""
        self.default_auth_context = create_mock_auth_context(
            user_id=user_id,
            roles=roles,
            permissions=permissions,
            custom_claims=custom_claims,
        )

    def call_endpoint(
        self,
        endpoint_func: callable,
        data: Dict[str, Any],
        auth_context: Optional[AuthContext] = None,
    ) -> Dict[str, Any]:
        """
        Call an endpoint function with mock data.

        Args:
            endpoint_func: The endpoint function to test
            data: Request data
            auth_context: Optional auth context (uses default if not provided)

        Returns:
            Response dictionary
        """
        context = create_mock_context(
            db=self.db, auth_context=auth_context or self.default_auth_context
        )

        # Call the endpoint
        response = endpoint_func(context, data)

        # Convert response to dict if it's a GacoResponse
        if isinstance(response, GacoResponse):
            return response.model_dump()

        return response


def assert_response_success(response: Dict[str, Any], expected_code: int = 200):
    """
    Assert that a response indicates success.

    Args:
        response: Response dictionary
        expected_code: Expected status code
    """
    assert response.get("success") is True, f"Response not successful: {response}"
    assert (
        response.get("code") == expected_code
    ), f"Expected code {expected_code}, got {response.get('code')}"


def assert_response_error(response: Dict[str, Any], expected_code: int = 400):
    """
    Assert that a response indicates an error.

    Args:
        response: Response dictionary
        expected_code: Expected error code
    """
    assert (
        response.get("success") is False
    ), f"Response unexpectedly successful: {response}"
    assert (
        response.get("code") == expected_code
    ), f"Expected code {expected_code}, got {response.get('code')}"


def assert_auth_error(response: Dict[str, Any]):
    """Assert that a response indicates an authentication error."""
    assert_response_error(response, 401)


def assert_authz_error(response: Dict[str, Any]):
    """Assert that a response indicates an authorization error."""
    assert_response_error(response, 403)
