---
description: 
globs: 
alwaysApply: false
---
# GACO Framework - Cursor Rules

## Framework Overview
You are working with the GACO Framework v2, a Python framework for Firebase Cloud Functions with built-in authorization, security, middleware, and testing capabilities.

## Core Patterns & Conventions

### Import Structure
Always use these imports for GACO Framework:
```python
from gaco_framework import (
    gaco_endpoint,
    GacoEndpoint,
    GacoContext,
    GacoResponse,
    AuthRequirement,
    require_auth,
    require_custom_auth,
    require_claims,
    check_resource_access,
    ResourceAccess,
    BaseGacoManager,
    BaseGacoModel
)
```

### Endpoint Pattern
All endpoints MUST follow this pattern:
```python
@gaco_endpoint(
    require_auth=AuthRequirement.VERIFIED_EMAIL,  # Auth requirement
    required_roles=["creative"],                  # Optional: role requirements
    required_permissions=["portfolio.write"]      # Optional: permission requirements
)
@GacoEndpoint(RequestModel)  # For validation, or @GacoEndpointNoValidation()
def endpoint_name(context: GacoContext, request: RequestModel) -> GacoResponse:
    """Endpoint docstring describing purpose and auth requirements"""
    
    # Access auth context
    user_id = context.auth_context.user_id
    
    # Get manager
    manager = context.get_manager(ManagerClass)
    
    # Business logic
    result = manager.perform_operation(request)
    
    return GacoResponse(
        success=True,
        message="Operation completed successfully",
        data=result.dict() if hasattr(result, 'dict') else result,
        code=200
    )
```

### Authentication Levels
Use these authentication requirements:
- `AuthRequirement.NONE` - No authentication required (public endpoints)
- `AuthRequirement.AUTHENTICATED` - User must be logged in
- `AuthRequirement.VERIFIED_EMAIL` - User must have verified email (recommended for most endpoints)
- `AuthRequirement.ADMIN` - User must have admin role
- `AuthRequirement.SERVICE_ACCOUNT` - Must be service account

### Authorization Patterns

#### Role-Based Access
```python
@gaco_endpoint(required_roles=["creative", "admin"])  # OR logic
```

#### Permission-Based Access
```python
@gaco_endpoint(required_permissions=["resource.write", "resource.delete"])  # AND logic
```

#### Custom Authorization
```python
def is_resource_owner(context: GacoContext) -> bool:
    """Custom auth function - must return bool"""
    resource_owner_id = context.request_data.get("owner_id")
    return context.auth_context.user_id == resource_owner_id

@require_custom_auth(is_resource_owner)
@gaco_endpoint()
def protected_endpoint(context: GacoContext, data: dict):
    pass
```

#### Claims-Based Authorization
```python
@require_claims({"subscription": "premium", "verified": True})
@gaco_endpoint()
def premium_feature(context: GacoContext, data: dict):
    pass
```

### Manager Pattern
All business logic MUST be in managers extending BaseGacoManager:
```python
class ResourceManager(BaseGacoManager):
    """Manager for resource operations"""
    
    def create_resource(self, data: CreateResourceRequest) -> Resource:
        # Access auth context
        creator_id = self.auth_context.user_id
        
        # Validate business rules
        if not self.auth_context.has_permission("resource.create"):
            raise AuthorizationError("No create permission")
        
        # Database operations
        resource_data = {
            "creator_id": creator_id,
            "created_at": self.firestore.SERVER_TIMESTAMP,
            **data.dict()
        }
        
        doc_ref = self.firestore.collection('resources').add(resource_data)
        return Resource(id=doc_ref.id, **resource_data)
    
    def get_user_resources(self, user_id: str) -> List[Resource]:
        # Check if user can access these resources
        if (user_id != self.auth_context.user_id and 
            not self.auth_context.has_role("admin")):
            raise AuthorizationError("Can only access your own resources")
        
        # Query and return
        docs = self.firestore.collection('resources').where('creator_id', '==', user_id).stream()
        return [Resource(id=doc.id, **doc.to_dict()) for doc in docs]
```

### Model Pattern
Use Pydantic models for requests and responses:
```python
class CreateResourceRequest(BaseGacoModel):
    title: str
    description: Optional[str] = None
    tags: List[str] = []
    is_public: bool = True
    
    @validator('title')
    def title_not_empty(cls, v):
        if not v.strip():
            raise ValueError('Title cannot be empty')
        return v

class Resource(BaseGacoModel):
    id: str
    title: str
    description: Optional[str]
    creator_id: str
    created_at: datetime
    tags: List[str]
    is_public: bool
```

### Response Pattern
Always return GacoResponse:
```python
# Success response
return GacoResponse(
    success=True,
    message="Resource created successfully",
    data=resource.dict(),
    code=201
)

# Error response (prefer raising exceptions)
return GacoResponse(
    success=False,
    message="Validation failed",
    errors=["Title is required"],
    code=400
)
```

### Error Handling
Use framework exceptions:
```python
from gaco_framework.exceptions import (
    ValidationError,      # 400 - Bad request/validation
    AuthenticationError,  # 401 - Not authenticated
    AuthorizationError,   # 403 - Insufficient permissions
    NotFoundError,        # 404 - Resource not found
    ConflictError,        # 409 - Resource conflict
    RateLimitError       # 429 - Rate limit exceeded
)

# Raise exceptions instead of returning error responses
if not resource_exists:
    raise NotFoundError("Resource not found")

if not user_owns_resource:
    raise AuthorizationError("You don't own this resource")
```

### Security Rules Usage
For resource-level security:
```python
@gaco_endpoint(require_auth=AuthRequirement.VERIFIED_EMAIL)
def update_resource(context: GacoContext, data: dict):
    # Check resource access before proceeding
    check_resource_access(
        context.auth_context,
        "resource",
        {"resource_id": data["resource_id"]},
        ResourceAccess.WRITE
    )
    
    # Proceed with operation
    manager = context.get_manager(ResourceManager)
    result = manager.update_resource(data)
    return GacoResponse(success=True, data=result)
```

### Testing Pattern
Always write tests using GacoTestClient:
```python
from gaco_framework.testing import GacoTestClient
import pytest

class TestResourceEndpoints:
    def setup_method(self):
        self.client = GacoTestClient()
    
    def test_create_resource_success(self):
        # Set auth
        self.client.set_auth(
            user_id="user_123",
            email="<EMAIL>",
            email_verified=True,
            roles=["user"],
            permissions=["resource.create"]
        )
        
        # Test data
        test_data = {
            "title": "Test Resource",
            "description": "Test Description"
        }
        
        # Call endpoint
        response = self.client.call_endpoint(create_resource, test_data)
        
        # Assertions
        assert response.success
        assert response.code == 201
        assert "created successfully" in response.message
    
    def test_create_resource_unauthorized(self):
        # Set auth without required permission
        self.client.set_auth(
            user_id="user_123",
            roles=["user"]
            # Missing resource.create permission
        )
        
        test_data = {"title": "Test"}
        
        with pytest.raises(AuthorizationError):
            self.client.call_endpoint(create_resource, test_data)
```

### Firestore Triggers
For database triggers:
```python
from gaco_framework import gaco_firestore_trigger, DocumentTriggerType, GacoEventContext

@gaco_firestore_trigger("resources/{resource_id}", DocumentTriggerType.CREATE)
def on_resource_created(context: GacoEventContext, before, after):
    """Handle resource creation"""
    resource_data = after.to_dict()
    resource_id = context.params["resource_id"]
    
    logger.info(f"New resource created: {resource_id}")
    
    # Trigger related operations
    if resource_data.get("is_public"):
        trigger_indexing(resource_id, resource_data)
```

### Storage Triggers
For file upload triggers:
```python
from gaco_framework import gaco_storage_trigger

@gaco_storage_trigger()
def on_file_uploaded(context: GacoEventContext, cloud_event):
    """Handle file uploads"""
    file_data = cloud_event.data
    file_path = file_data["name"]
    
    logger.info(f"File uploaded: {file_path}")
    
    # Process based on file type/location
    if file_path.startswith("resources/"):
        process_resource_file(file_path)
```

### Middleware Usage
For cross-cutting concerns:
```python
from gaco_framework import (
    gaco_endpoint_with_middleware,
    LoggingMiddleware,
    RateLimitMiddleware,
    MetricsMiddleware
)

@gaco_endpoint_with_middleware([
    LoggingMiddleware(),
    RateLimitMiddleware(max_requests=100, window_seconds=3600),
    MetricsMiddleware()
])
def endpoint_with_middleware(context: GacoContext, data: dict):
    return GacoResponse(success=True, message="Success")
```

## Naming Conventions

### Files
- Endpoints: `{resource}_endpoints.py` (e.g., `portfolio_endpoints.py`)
- Managers: `{resource}_manager.py` (e.g., `portfolio_manager.py`)
- Models: `{resource}_models.py` (e.g., `portfolio_models.py`)
- Tests: `test_{resource}_endpoints.py` (e.g., `test_portfolio_endpoints.py`)

### Functions
- Endpoints: `{action}_{resource}` (e.g., `create_portfolio`, `get_portfolio`)
- Managers: `{action}_{resource}` (e.g., `create_portfolio_item`, `get_user_portfolios`)
- Authorization: `is_{condition}` or `can_{action}` (e.g., `is_resource_owner`, `can_edit_portfolio`)

### Classes
- Managers: `{Resource}Manager` (e.g., `PortfolioManager`)
- Models: `{Resource}` or `{Action}{Resource}Request` (e.g., `Portfolio`, `CreatePortfolioRequest`)

## Code Quality Rules

1. **Always use type hints** for function parameters and return types
2. **Include docstrings** for all endpoints and managers describing purpose and auth requirements
3. **Handle errors explicitly** - don't let exceptions bubble up without context
4. **Use managers for all business logic** - endpoints should be thin and delegate to managers
5. **Validate all inputs** using Pydantic models with @GacoEndpoint decorator
6. **Log important operations** with context (user_id, resource_id, action)
7. **Write tests for all endpoints** including success and failure cases
8. **Use consistent error messages** and status codes
9. **Document authorization requirements** in endpoint docstrings
10. **Follow the principle of least privilege** - require minimum necessary permissions

## Common Mistakes to Avoid

1. **Don't put business logic in endpoints** - use managers
2. **Don't access Firestore directly in endpoints** - use managers
3. **Don't forget to check authorization** - use framework decorators and functions
4. **Don't return raw exceptions** - use framework exceptions or GacoResponse
5. **Don't skip input validation** - always use @GacoEndpoint with request models
6. **Don't hardcode user IDs** - get from context.auth_context.user_id
7. **Don't forget to test authorization** - test both success and failure cases
8. **Don't mix authentication levels** - be consistent within related endpoints
9. **Don't skip error logging** - log errors with sufficient context
10. **Don't forget to handle edge cases** - null values, empty lists, etc.

## Security Best Practices

1. **Always require email verification** for sensitive operations
2. **Use resource-level security checks** with check_resource_access()
3. **Implement defense in depth** - client, function, and database level security
4. **Log security events** for audit trails
5. **Use custom claims sparingly** - prefer roles and permissions
6. **Validate all data** even from authenticated users
7. **Don't trust client-side validation** - always validate server-side
8. **Use least privilege principle** - minimum required permissions
9. **Audit authorization changes** - log role/permission modifications
10. **Test security scenarios** - unauthorized access, privilege escalation, etc.

When suggesting code, always follow these patterns and conventions. If you see code that doesn't follow these patterns, suggest refactoring to align with the GACO Framework conventions.
