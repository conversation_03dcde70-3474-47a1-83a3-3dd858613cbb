"""
Compatibility layer for GACO Framework v2 to work as drop-in replacement for v1
"""

from .managers import BaseGacoManager as BaseGacoManagerV2


class BaseGacoManager(BaseGacoManagerV2):
    """
    Backward-compatible BaseGacoManager that works with v1 manager signatures.

    This allows existing managers that only expect 'db' parameter to work unchanged.
    """

    def __init__(self, db, auth_context=None):
        # If called with single argument (v1 style), handle gracefully
        if auth_context is None and hasattr(db, "auth_context"):
            # Check if db is actually a v2 style call
            super().__init__(db, auth_context)
        else:
            # v1 style call - just db parameter
            super().__init__(db, None)
