"""
Custom exceptions for Gaco Framework
"""

from typing import Any, Dict, List, Optional


class GacoError(Exception):
    """Base exception for all Gaco Framework errors"""

    def __init__(
        self, message: str, code: int = 500, details: Optional[Dict[str, Any]] = None
    ):
        self.message = message
        self.code = code
        self.details = details or {}
        super().__init__(self.message)


class ValidationError(GacoError):
    """Raised when request validation fails"""

    def __init__(
        self, message: str = "Validation failed", errors: Optional[List[Dict]] = None
    ):
        super().__init__(message, code=400)
        self.errors = errors or []


class AuthenticationError(GacoError):
    """Raised when authentication fails"""

    def __init__(self, message: str = "Authentication required"):
        super().__init__(message, code=401)


class AuthorizationError(GacoError):
    """Raised when authorization fails"""

    def __init__(self, message: str = "Insufficient permissions"):
        super().__init__(message, code=403)


class PermissionError(AuthorizationError):
    """
    Raised when an authenticated user is not allowed to perform a specific action.
    This is a more specific type of AuthorizationError.
    """

    def __init__(self, message: str = "Operation not permitted"):
        super().__init__(message)


class NotFoundError(GacoError):
    """Raised when resource is not found"""

    def __init__(self, message: str = "Resource not found"):
        super().__init__(message, code=404)


class MethodNotAllowedError(GacoError):
    """Raised when an HTTP method is not allowed for a resource"""

    def __init__(self, message: str = "Method not allowed"):
        super().__init__(message, code=405)


class ConflictError(GacoError):
    """Raised when there's a conflict (e.g., duplicate resource)"""

    def __init__(self, message: str = "Resource conflict"):
        super().__init__(message, code=409)


class UnsupportedMediaTypeError(GacoError):
    """Raised when the request's media type is not supported"""

    def __init__(self, message: str = "Unsupported media type"):
        super().__init__(message, code=415)


class RateLimitError(GacoError):
    """Raised when rate limit is exceeded"""

    def __init__(self, message: str = "Rate limit exceeded"):
        super().__init__(message, code=429)


class InternalServerError(GacoError):
    """Raised for a generic internal server error"""

    def __init__(self, message: str = "Internal server error"):
        super().__init__(message, code=500)


class ServiceUnavailableError(GacoError):
    """Raised when the service is temporarily unavailable"""

    def __init__(self, message: str = "Service unavailable"):
        super().__init__(message, code=503)
