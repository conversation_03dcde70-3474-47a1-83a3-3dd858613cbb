"""
Pydantic models for Gaco Framework
"""

from pydantic import BaseModel, ConfigDict, Field
from datetime import datetime
from typing import Annotated, Any, Dict, List, Optional, Union
import json


def to_camel(string: str) -> str:
    """Convert snake_case to camelCase"""
    words = string.split("_")
    return words[0] + "".join(word.capitalize() for word in words[1:])


# Custom TimeStamp type for TypeScript compatibility
TimeStamp = Annotated[datetime, Field(json_schema_extra={"tsType": "Date"})]


class BaseGacoModel(BaseModel):
    """Base model with Gaco-specific configuration"""

    model_config = ConfigDict(
        extra="ignore",
        from_attributes=True,
        alias_generator=to_camel,
        populate_by_name=True,
        populate_by_alias=True,
        str_strip_whitespace=True,
        validate_assignment=True,
        json_encoders={
            datetime: lambda v: v.isoformat().replace('+00:00', 'Z')
        },
    )

    def model_dump(self, **kwargs: Any) -> Dict[str, Any]:
        """Override to default to camelCase for external use"""
        by_alias = kwargs.pop("by_alias", True)
        return super().model_dump(by_alias=by_alias, **kwargs)

    def model_dump_json(self, **kwargs: Any) -> str:
        """Override to default to camelCase for JSON"""
        by_alias = kwargs.pop("by_alias", True)
        return super().model_dump_json(by_alias=by_alias, **kwargs)

    @classmethod
    def model_validate(cls, obj: Any, **kwargs: Any) -> "BaseGacoModel":
        """Enhanced validation with better error messages"""
        return super().model_validate(obj, **kwargs)


class GacoRequest(BaseGacoModel):
    """Base class for all request models"""

    pass


class GacoResponse(BaseGacoModel):
    """Enhanced response model with standardized structure"""

    success: bool
    message: str
    data: Optional[Any] = None
    code: int
    errors: Optional[List[Dict[str, Any]]] = None
    meta: Optional[Dict[str, Any]] = None

    def model_dump(self, **kwargs: Any) -> Dict[str, Any]:
        """
        Special override for GacoResponse to ensure the output is a
        JSON-serializable dict, converting datetimes to ISO strings.
        This is because the firebase-functions wrapper will do its own
        JSON dump on the returned dict.
        """
        # We call model_dump_json() which correctly uses the json_encoders
        # from the model_config, then parse it back to a dict.
        json_string = self.model_dump_json(**kwargs)
        return json.loads(json_string)

    def to_json_response(self) -> str:
        """Convert to JSON string for Cloud Function response"""
        return self.model_dump_json()


# Legacy alias for backward compatibility
ResponseData = GacoResponse
