# GACO Framework v2 Documentation

## Overview

The GACO Framework is a comprehensive Python framework designed for developing Firebase Cloud Functions with built-in authorization, security, middleware, and testing capabilities. It provides a structured approach to building scalable, secure serverless applications with Firebase.

## Table of Contents

1. [Installation & Setup](#installation--setup)
2. [Core Concepts](#core-concepts)
3. [Authentication & Authorization](#authentication--authorization)
4. [Endpoints](#endpoints)
5. [Models & Validation](#models--validation)
6. [Managers](#managers)
7. [Middleware](#middleware)
8. [Security Rules](#security-rules)
9. [Testing](#testing)
10. [Error Handling](#error-handling)
11. [Examples](#examples)
12. [Migration Guide](#migration-guide)

## Installation & Setup

### Prerequisites

- Python 3.9+
- Firebase Admin SDK
- Firebase Functions SDK

### Basic Setup

```python
from gaco_framework import (
    gaco_endpoint,
    GacoEndpoint,
    GacoContext,
    GacoResponse,
    AuthRequirement
)

# Basic endpoint
@gaco_endpoint()
def hello_world(context: GacoContext, data: dict) -> GacoResponse:
    return GacoResponse(
        success=True,
        message="Hello, World!",
        data={"timestamp": context.timestamp}
    )
```

## Core Concepts

### GacoContext

The `GacoContext` is the central object that provides access to request information, authentication context, and framework utilities.

```python
class GacoContext:
    """
    Core context object passed to all GACO endpoints
    """
    
    # Request data
    request_data: dict
    raw_request: https_fn.Request
    
    # Authentication context
    auth_context: Optional[AuthContext]
    
    # Utilities
    timestamp: str
    request_id: str
    
    # Manager access
    def get_manager(self, manager_class: Type[BaseGacoManager]) -> BaseGacoManager:
        """Get an instance of a manager with auth context"""
```

### GacoResponse

Standardized response format for all endpoints.

```python
@dataclass
class GacoResponse:
    success: bool
    message: str
    data: Optional[dict] = None
    code: int = 200
    errors: Optional[List[str]] = None
```

## Authentication & Authorization

### AuthContext

The framework automatically extracts authentication information from Firebase tokens:

```python
@dataclass
class AuthContext:
    user_id: str
    email: Optional[str]
    email_verified: bool
    phone_number: Optional[str]
    custom_claims: Dict[str, Any]
    roles: Set[str]
    permissions: Set[str]
    is_service_account: bool
    
    def has_role(self, role: str) -> bool:
        """Check if user has a specific role"""
    
    def has_permission(self, permission: str) -> bool:
        """Check if user has a specific permission"""
    
    def get_claim(self, claim: str, default=None) -> Any:
        """Get a custom claim value"""
```

### Authentication Requirements

Use `AuthRequirement` enum to specify authentication levels:

```python
class AuthRequirement(Enum):
    NONE = "none"                    # No authentication required
    AUTHENTICATED = "authenticated"  # User must be logged in
    VERIFIED_EMAIL = "verified_email" # User must have verified email
    ADMIN = "admin"                  # User must have admin role
    SERVICE_ACCOUNT = "service_account" # Must be service account
```

### Authorization Decorators

#### Basic Authentication

```python
# Require verified email
@gaco_endpoint(require_auth=AuthRequirement.VERIFIED_EMAIL)
def protected_endpoint(context: GacoContext, data: dict):
    user_id = context.auth_context.user_id
    # Function logic here
```

#### Role-Based Access Control

```python
# Require specific roles (OR logic)
@gaco_endpoint(required_roles=["creative", "admin"])
def role_based_endpoint(context: GacoContext, data: dict):
    # User must have either 'creative' OR 'admin' role
    pass
```

#### Permission-Based Access Control

```python
# Require specific permissions
@gaco_endpoint(required_permissions=["portfolio.write", "portfolio.delete"])
def permission_based_endpoint(context: GacoContext, data: dict):
    # User must have all specified permissions
    pass
```

#### Custom Authorization

```python
from gaco_framework import require_custom_auth

def is_resource_owner(context: GacoContext) -> bool:
    """Custom authorization logic"""
    resource_owner_id = context.request_data.get("owner_id")
    return context.auth_context.user_id == resource_owner_id

@require_custom_auth(is_resource_owner)
@gaco_endpoint()
def owner_only_endpoint(context: GacoContext, data: dict):
    # Only resource owner can access
    pass
```

#### Claims-Based Authorization

```python
from gaco_framework import require_claims, require_any_claims

# Require specific claims
@require_claims({"subscription": "premium", "verified": True})
@gaco_endpoint()
def premium_feature(context: GacoContext, data: dict):
    pass

# Require any of the specified claims
@require_any_claims({"subscription": ["premium", "enterprise"]})
@gaco_endpoint()
def paid_feature(context: GacoContext, data: dict):
    pass
```

## Endpoints

### Basic Endpoint Creation

```python
@gaco_endpoint()
def simple_endpoint(context: GacoContext, data: dict) -> GacoResponse:
    return GacoResponse(
        success=True,
        message="Operation completed",
        data={"result": "example"}
    )
```

### Request Validation

Use `GacoEndpoint` decorator for automatic request validation:

```python
from pydantic import BaseModel

class CreateUserRequest(BaseModel):
    name: str
    email: str
    age: Optional[int] = None

@gaco_endpoint()
@GacoEndpoint(CreateUserRequest)
def create_user(context: GacoContext, request: CreateUserRequest) -> GacoResponse:
    # request is validated and typed
    user_data = {
        "name": request.name,
        "email": request.email,
        "age": request.age
    }
    return GacoResponse(success=True, data=user_data)
```

### No Validation Endpoint

For endpoints that don't need request validation:

```python
@gaco_endpoint()
@GacoEndpointNoValidation()
def flexible_endpoint(context: GacoContext, data: dict) -> GacoResponse:
    # No validation, data passed as-is
    return GacoResponse(success=True, data=data)
```

### Firestore Triggers

```python
from gaco_framework import gaco_firestore_trigger, DocumentTriggerType

@gaco_firestore_trigger("users/{user_id}", DocumentTriggerType.CREATE)
def on_user_created(context: GacoEventContext, before, after):
    user_data = after.to_dict()
    # Handle user creation
    logger.info(f"New user created: {user_data}")
```

### Storage Triggers

```python
from gaco_framework import gaco_storage_trigger

@gaco_storage_trigger()
def on_file_uploaded(context: GacoEventContext, cloud_event):
    file_path = cloud_event.data["name"]
    # Handle file upload
    logger.info(f"File uploaded: {file_path}")
```

## Models & Validation

### Base Model

```python
from gaco_framework import BaseGacoModel

class User(BaseGacoModel):
    id: str
    name: str
    email: str
    created_at: datetime
    
    class Config:
        # Pydantic configuration
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }
```

### Request Models

```python
class UpdateProfileRequest(BaseGacoModel):
    name: Optional[str] = None
    bio: Optional[str] = None
    
    @validator('name')
    def name_must_not_be_empty(cls, v):
        if v is not None and not v.strip():
            raise ValueError('Name cannot be empty')
        return v
```

## Managers

Managers handle business logic and have access to the authentication context:

```python
from gaco_framework import BaseGacoManager

class UserManager(BaseGacoManager):
    """
    Manager for user-related operations
    """
    
    def create_user(self, user_data: dict) -> User:
        # Access auth context
        creator_id = self.auth_context.user_id if self.auth_context else None
        
        # Business logic
        user = User(**user_data)
        user.created_by = creator_id
        
        # Save to database
        self.firestore.collection('users').add(user.dict())
        
        return user
    
    def get_user_profile(self, user_id: str) -> User:
        # Check if user can access this profile
        if not self.can_access_user_profile(user_id):
            raise AuthorizationError("Cannot access this user profile")
        
        # Fetch and return user
        doc = self.firestore.collection('users').document(user_id).get()
        return User(**doc.to_dict())
    
    def can_access_user_profile(self, user_id: str) -> bool:
        """Business logic for profile access"""
        if not self.auth_context:
            return False
        
        # Users can access their own profile
        if self.auth_context.user_id == user_id:
            return True
        
        # Admins can access any profile
        if self.auth_context.has_role("admin"):
            return True
        
        return False
```

### Using Managers in Endpoints

```python
@gaco_endpoint(require_auth=AuthRequirement.VERIFIED_EMAIL)
@GacoEndpoint(CreateUserRequest)
def create_user_endpoint(context: GacoContext, request: CreateUserRequest) -> GacoResponse:
    user_manager = context.get_manager(UserManager)
    
    new_user = user_manager.create_user(request.dict())
    
    return GacoResponse(
        success=True,
        message="User created successfully",
        data=new_user.dict(),
        code=201
    )
```

## Middleware

The framework supports middleware for cross-cutting concerns:

### Built-in Middleware

```python
from gaco_framework import (
    LoggingMiddleware,
    RateLimitMiddleware,
    MetricsMiddleware,
    gaco_endpoint_with_middleware
)

# Apply middleware to endpoint
@gaco_endpoint_with_middleware([
    LoggingMiddleware(),
    RateLimitMiddleware(max_requests=100, window_seconds=3600),
    MetricsMiddleware()
])
def endpoint_with_middleware(context: GacoContext, data: dict):
    return GacoResponse(success=True, message="Success")
```

### Custom Middleware

```python
from gaco_framework import GacoMiddleware

class CustomMiddleware(GacoMiddleware):
    def before_request(self, context: GacoContext, data: dict) -> None:
        # Pre-processing logic
        logger.info("Custom middleware: Before request")
    
    def after_request(self, context: GacoContext, response: GacoResponse) -> GacoResponse:
        # Post-processing logic
        logger.info("Custom middleware: After request")
        return response
    
    def on_error(self, context: GacoContext, error: Exception) -> Optional[GacoResponse]:
        # Error handling logic
        logger.error(f"Custom middleware: Error - {error}")
        return None  # Let framework handle the error
```

## Security Rules

Define resource-level security rules:

### Built-in Resource Access

```python
from gaco_framework import check_resource_access, ResourceAccess

@gaco_endpoint(require_auth=AuthRequirement.VERIFIED_EMAIL)
def update_portfolio(context: GacoContext, data: dict):
    # Check if user can write to this portfolio
    check_resource_access(
        context.auth_context,
        "portfolio",
        {"creative_id": data["creative_id"]},
        ResourceAccess.WRITE
    )
    
    # Proceed with update
    portfolio_manager = context.get_manager(PortfolioManager)
    result = portfolio_manager.update_portfolio(data)
    
    return GacoResponse(success=True, data=result)
```

### Custom Security Rules

```python
from gaco_framework import SecurityRule, get_security_rules

# Define custom security rule
def project_access_rule(auth_context: AuthContext, resource_data: dict) -> bool:
    """Custom logic for project access"""
    project_owner = resource_data.get("owner_id")
    collaborators = resource_data.get("collaborators", [])
    
    return (
        auth_context.user_id == project_owner or
        auth_context.user_id in collaborators or
        auth_context.has_role("admin")
    )

# Register the rule
get_security_rules().add_rule(SecurityRule(
    resource_type="project",
    access_level=ResourceAccess.READ,
    condition=project_access_rule
))
```

### Resource Access Levels

```python
class ResourceAccess(Enum):
    READ = "read"
    WRITE = "write"
    DELETE = "delete"
    ADMIN = "admin"
    OWNER = "owner"
```

## Testing

### Test Client

```python
from gaco_framework.testing import GacoTestClient

# Create test client
client = GacoTestClient()

# Set authentication for tests
client.set_auth(
    user_id="test_user_123",
    email="<EMAIL>",
    email_verified=True,
    roles={"user", "verified"},
    permissions={"read", "write"},
    custom_claims={"subscription": "premium"}
)

# Test endpoint
response = client.call_endpoint(
    my_endpoint,
    {"name": "Test User", "email": "<EMAIL>"}
)

assert response.success
assert response.code == 200
```

### Mock Authentication Context

```python
from gaco_framework.testing import create_mock_auth_context

# Create mock auth context
mock_auth = create_mock_auth_context(
    user_id="test_user",
    roles=["admin"],
    email_verified=True
)

# Use in tests
context = GacoContext(
    request_data=test_data,
    auth_context=mock_auth,
    raw_request=mock_request
)
```

### Testing Patterns

```python
import pytest
from gaco_framework.testing import GacoTestClient
from gaco_framework.exceptions import AuthorizationError

class TestPortfolioEndpoints:
    def setup_method(self):
        self.client = GacoTestClient()
    
    def test_create_portfolio_success(self):
        # Set up authenticated user
        self.client.set_auth(
            user_id="creative_123",
            roles=["creative"],
            email_verified=True
        )
        
        # Test data
        test_data = {
            "creative_id": "creative_123",
            "data": {
                "title": "Test Portfolio",
                "description": "Test Description"
            }
        }
        
        # Call endpoint
        response = self.client.call_endpoint(create_portfolio_item, test_data)
        
        # Assertions
        assert response.success
        assert response.code == 201
        assert "Portfolio item created" in response.message
    
    def test_create_portfolio_unauthorized(self):
        # Set up user without creative role
        self.client.set_auth(
            user_id="user_123",
            roles=["user"],
            email_verified=True
        )
        
        test_data = {
            "creative_id": "creative_123",
            "data": {"title": "Test"}
        }
        
        # Should raise authorization error
        with pytest.raises(AuthorizationError):
            self.client.call_endpoint(create_portfolio_item, test_data)
    
    def test_create_portfolio_not_owner(self):
        # User trying to create portfolio for someone else
        self.client.set_auth(
            user_id="creative_123",
            roles=["creative"],
            email_verified=True
        )
        
        test_data = {
            "creative_id": "different_creative_456",  # Different user
            "data": {"title": "Test"}
        }
        
        with pytest.raises(AuthorizationError):
            self.client.call_endpoint(create_portfolio_item, test_data)
```

## Error Handling

### Built-in Exceptions

```python
from gaco_framework.exceptions import (
    GacoError,           # Base exception
    ValidationError,     # Request validation failed
    AuthenticationError, # Authentication required/failed
    AuthorizationError,  # Insufficient permissions
    NotFoundError,       # Resource not found
    ConflictError,       # Resource conflict
    RateLimitError       # Rate limit exceeded
)
```

### Custom Error Handling

```python
@gaco_endpoint()
def endpoint_with_error_handling(context: GacoContext, data: dict):
    try:
        # Business logic
        result = perform_operation(data)
        return GacoResponse(success=True, data=result)
    
    except ValidationError as e:
        return GacoResponse(
            success=False,
            message="Validation failed",
            errors=[str(e)],
            code=400
        )
    
    except AuthorizationError as e:
        return GacoResponse(
            success=False,
            message="Access denied",
            code=403
        )
    
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        return GacoResponse(
            success=False,
            message="Internal server error",
            code=500
        )
```

### Error Response Format

All errors follow a consistent format:

```json
{
  "success": false,
  "message": "Error description",
  "code": 400,
  "errors": ["Detailed error messages"]
}
```
    
    # Initialize user profile
    firestore_client = context.get_firestore_client()
    profile_data = {
        "user_id": user_id,
        "display_name": user_data.get("display_name", ""),
        "bio": "",
        "avatar_url": "",
        "settings": {
            "notifications": True,
            "privacy": "public"
        },
        "created_at": firestore_client.SERVER_TIMESTAMP
    }
    
    firestore_client.collection('user_profiles').document(user_id).set(profile_data)
    
    # Send welcome email (if email service is configured)
    if user_data.get("email"):
        send_welcome_email(user_data["email"], user_data.get("display_name"))

# Portfolio update trigger
@gaco_firestore_trigger("portfolios/{portfolio_id}", DocumentTriggerType.UPDATE)
def on_portfolio_updated(context: GacoEventContext, before, after):
    """Handle portfolio updates"""
    before_data = before.to_dict() if before.exists else {}
    after_data = after.to_dict()
    portfolio_id = context.params["portfolio_id"]
    
    # Check if visibility changed
    was_public = before_data.get("is_public", False)
    is_public = after_data.get("is_public", False)
    
    if not was_public and is_public:
        # Portfolio just became public - trigger indexing
        logger.info(f"Portfolio {portfolio_id} became public, triggering search indexing")
        trigger_search_indexing(portfolio_id, after_data)
    
    elif was_public and not is_public:
        # Portfolio became private - remove from public search
        logger.info(f"Portfolio {portfolio_id} became private, removing from search")
        remove_from_search_index(portfolio_id)

# Storage trigger for file uploads
@gaco_storage_trigger()
def on_file_uploaded(context: GacoEventContext, cloud_event):
    """Handle file uploads"""
    file_data = cloud_event.data
    file_name = file_data["name"]
    bucket_name = file_data["bucket"]
    
    logger.info(f"File uploaded: {file_name} to bucket {bucket_name}")
    
    # Process different file types
    if file_name.startswith("portfolios/"):
        # Portfolio asset upload
        process_portfolio_asset(file_name, bucket_name)
    
    elif file_name.startswith("avatars/"):
        # Avatar upload
        process_avatar_upload(file_name, bucket_name)
    
    elif file_name.endswith(('.jpg', '.jpeg', '.png', '.gif')):
        # Image processing
        process_image_upload(file_name, bucket_name)

def process_portfolio_asset(file_path: str, bucket_name: str):
    """Process portfolio asset uploads"""
    # Extract portfolio ID from path: portfolios/{portfolio_id}/{asset_id}/file.jpg
    path_parts = file_path.split('/')
    if len(path_parts) >= 3:
        portfolio_id = path_parts[1]
        asset_id = path_parts[2]
        
        # Update portfolio with new asset
        firestore_client = admin.firestore.client()
        portfolio_ref = firestore_client.collection('portfolios').document(portfolio_id)
        
        # Add asset reference
        asset_data = {
            "id": asset_id,
            "file_path": file_path,
            "bucket": bucket_name,
            "uploaded_at": firestore_client.SERVER_TIMESTAMP,
            "type": "image" if file_path.lower().endswith(('.jpg', '.jpeg', '.png', '.gif')) else "file"
        }
        
        portfolio_ref.update({
            f"assets.{asset_id}": asset_data
        })

def trigger_search_indexing(portfolio_id: str, portfolio_data: dict):
    """Trigger search indexing for public portfolio"""
    # This would typically call an external search service
    # or add to a processing queue
    pass

def remove_from_search_index(portfolio_id: str):
    """Remove portfolio from search index"""
    # This would typically call an external search service
    pass
```

### Advanced Testing Example

```python
import pytest
from unittest.mock import Mock, patch
from gaco_framework.testing import GacoTestClient, create_mock_auth_context
from gaco_framework.exceptions import AuthorizationError, ValidationError

class TestPortfolioEndpoints:
    def setup_method(self):
        """Set up test environment"""
        self.client = GacoTestClient()
        
        # Mock Firestore client
        self.mock_firestore = Mock()
        self.client.set_firestore_client(self.mock_firestore)
    
    def test_create_portfolio_success(self):
        """Test successful portfolio creation"""
        # Set up authenticated creative user
        self.client.set_auth(
            user_id="creative_123",
            email="<EMAIL>",
            email_verified=True,
            roles=["creative"],
            permissions=["portfolio.create"]
        )
        
        # Mock Firestore response
        mock_doc_ref = Mock()
        mock_doc_ref.id = "portfolio_456"
        self.mock_firestore.collection.return_value.add.return_value = mock_doc_ref
        
        # Test data
        test_data = {
            "creative_id": "creative_123",
            "title": "My Amazing Portfolio",
            "description": "A collection of my best work",
            "tags": ["photography", "digital art"],
            "is_public": True
        }
        
        # Call endpoint
        response = self.client.call_endpoint(create_portfolio, test_data)
        
        # Assertions
        assert response.success
        assert response.code == 201
        assert "Portfolio created successfully" in response.message
        assert response.data["id"] == "portfolio_456"
        assert response.data["title"] == "My Amazing Portfolio"
        
        # Verify Firestore was called correctly
        self.mock_firestore.collection.assert_called_with('portfolios')
    
    def test_create_portfolio_unauthorized_role(self):
        """Test portfolio creation with wrong role"""
        # Set up user without creative role
        self.client.set_auth(
            user_id="user_123",
            email="<EMAIL>",
            email_verified=True,
            roles=["user"],  # Not a creative
            permissions=[]
        )
        
        test_data = {
            "creative_id": "user_123",
            "title": "Test Portfolio"
        }
        
        # Should raise authorization error
        with pytest.raises(AuthorizationError) as exc_info:
            self.client.call_endpoint(create_portfolio, test_data)
        
        assert "Insufficient roles" in str(exc_info.value)
    
    def test_create_portfolio_not_owner(self):
        """Test creating portfolio for different user"""
        self.client.set_auth(
            user_id="creative_123",
            email="<EMAIL>",
            email_verified=True,
            roles=["creative"]
        )
        
        test_data = {
            "creative_id": "different_creative_456",  # Different user
            "title": "Test Portfolio"
        }
        
        with pytest.raises(AuthorizationError) as exc_info:
            self.client.call_endpoint(create_portfolio, test_data)
        
        assert "Custom authorization failed" in str(exc_info.value)
    
    def test_get_public_portfolio_anonymous(self):
        """Test getting public portfolio without authentication"""
        # No authentication set
        self.client.clear_auth()
        
        # Mock Firestore response for public portfolio
        mock_doc = Mock()
        mock_doc.exists = True
        mock_doc.to_dict.return_value = {
            "title": "Public Portfolio",
            "creative_id": "creative_123",
            "is_public": True,
            "description": "A public portfolio"
        }
        
        self.mock_firestore.collection.return_value.document.return_value.get.return_value = mock_doc
        
        test_data = {"portfolio_id": "portfolio_456"}
        
        response = self.client.call_endpoint(get_portfolio, test_data)
        
        assert response.success
        assert response.data["title"] == "Public Portfolio"
        assert response.data["is_public"] is True
    
    def test_get_private_portfolio_anonymous(self):
        """Test getting private portfolio without authentication"""
        self.client.clear_auth()
        
        # Mock Firestore response for private portfolio
        mock_doc = Mock()
        mock_doc.exists = True
        mock_doc.to_dict.return_value = {
            "title": "Private Portfolio",
            "creative_id": "creative_123",
            "is_public": False,  # Private portfolio
            "description": "A private portfolio"
        }
        
        self.mock_firestore.collection.return_value.document.return_value.get.return_value = mock_doc
        
        test_data = {"portfolio_id": "portfolio_456"}
        
        with pytest.raises(AuthorizationError) as exc_info:
            self.client.call_endpoint(get_portfolio, test_data)
        
        assert "This portfolio is private" in str(exc_info.value)
    
    def test_admin_get_all_portfolios(self):
        """Test admin endpoint for getting all portfolios"""
        # Set up admin user
        self.client.set_auth(
            user_id="admin_123",
            email="<EMAIL>",
            email_verified=True,
            roles=["admin"],
            permissions=["admin.read"]
        )
        
        # Mock Firestore query response
        mock_docs = [
            Mock(id="portfolio_1", to_dict=lambda: {"title": "Portfolio 1", "creative_id": "user_1"}),
            Mock(id="portfolio_2", to_dict=lambda: {"title": "Portfolio 2", "creative_id": "user_2"})
        ]
        
        mock_query = Mock()
        mock_query.order_by.return_value = mock_query
        mock_query.limit.return_value = mock_query
        mock_query.stream.return_value = mock_docs
        
        self.mock_firestore.collection.return_value = mock_query
        
        test_data = {"limit": 10, "offset": 0}
        
        response = self.client.call_endpoint(admin_get_all_portfolios, test_data)
        
        assert response.success
        assert len(response.data["portfolios"]) == 2
        assert response.data["portfolios"][0]["title"] == "Portfolio 1"
        assert response.data["pagination"]["limit"] == 10
    
    def test_validation_error(self):
        """Test request validation error"""
        self.client.set_auth(
            user_id="creative_123",
            roles=["creative"],
            email_verified=True
        )
        
        # Missing required field
        test_data = {
            "creative_id": "creative_123",
            # Missing required 'title' field
            "description": "Test description"
        }
        
        with pytest.raises(ValidationError) as exc_info:
            self.client.call_endpoint(create_portfolio, test_data)
        
        assert "title" in str(exc_info.value).lower()
    
    @patch('time.time')
    def test_middleware_timing(self, mock_time):
        """Test timing middleware"""
        # Mock time progression
        mock_time.side_effect = [1000.0, 1001.5]  # 1.5 second request
        
        self.client.set_auth(user_id="user_123", roles=["user"])
        
        # Mock Firestore response
        mock_doc = Mock()
        mock_doc.exists = True
        mock_doc.to_dict.return_value = {"title": "Test", "is_public": True, "creative_id": "user_123"}
        self.mock_firestore.collection.return_value.document.return_value.get.return_value = mock_doc
        
        response = self.client.call_endpoint(get_portfolio, {"portfolio_id": "test_123"})
        
        # Check that timing information was added
        assert response.success
        assert "_timing" in response.data
        assert response.data["_timing"]["duration_ms"] == 1500.0

class TestPortfolioManager:
    def setup_method(self):
        """Set up manager tests"""
        self.mock_firestore = Mock()
        self.auth_context = create_mock_auth_context(
            user_id="creative_123",
            roles=["creative"],
            email_verified=True
        )
        
        self.manager = PortfolioManager(
            auth_context=self.auth_context,
            firestore_client=self.mock_firestore
        )
    
    def test_create_portfolio_manager(self):
        """Test portfolio creation through manager"""
        # Mock Firestore add response
        mock_doc_ref = Mock()
        mock_doc_ref.id = "new_portfolio_123"
        self.mock_firestore.collection.return_value.add.return_value = mock_doc_ref
        
        request_data = CreatePortfolioRequest(
            title="Test Portfolio",
            description="Test Description",
            tags=["art", "design"],
            is_public=True
        )
        
        result = self.manager.create_portfolio("creative_123", request_data)
        
        assert result["id"] == "new_portfolio_123"
        assert result["title"] == "Test Portfolio"
        assert result["creative_id"] == "creative_123"
        assert result["created_by"] == "creative_123"
        
        # Verify Firestore was called with correct data
        self.mock_firestore.collection.assert_called_with('portfolios')
    
    def test_update_portfolio_not_found(self):
        """Test updating non-existent portfolio"""
        # Mock Firestore response for non-existent portfolio
        mock_doc = Mock()
        mock_doc.exists = False
        self.mock_firestore.collection.return_value.document.return_value.get.return_value = mock_doc
        
        update_data = UpdatePortfolioRequest(title="New Title")
        
        with pytest.raises(NotFoundError) as exc_info:
            self.manager.update_portfolio("nonexistent_portfolio", update_data)
        
        assert "Portfolio not found" in str(exc_info.value)
    
    def test_update_portfolio_unauthorized(self):
        """Test updating portfolio owned by different user"""
        # Mock Firestore response for portfolio owned by different user
        mock_doc = Mock()
        mock_doc.exists = True
        mock_doc.to_dict.return_value = {
            "creative_id": "different_user_456",  # Different owner
            "title": "Existing Portfolio"
        }
        self.mock_firestore.collection.return_value.document.return_value.get.return_value = mock_doc
        
        update_data = UpdatePortfolioRequest(title="New Title")
        
        with pytest.raises(AuthorizationError) as exc_info:
            self.manager.update_portfolio("portfolio_123", update_data)
        
        assert "Can only update your own portfolios" in str(exc_info.value)
    
    def test_update_portfolio_as_admin(self):
        """Test admin can update any portfolio"""
        # Set up admin auth context
        admin_auth = create_mock_auth_context(
            user_id="admin_123",
            roles=["admin"],
            email_verified=True
        )
        
        admin_manager = PortfolioManager(
            auth_context=admin_auth,
            firestore_client=self.mock_firestore
        )
        
        # Mock Firestore responses
        mock_doc = Mock()
        mock_doc.exists = True
        mock_doc.to_dict.return_value = {
            "creative_id": "different_user_456",
            "title": "Existing Portfolio"
        }
        self.mock_firestore.collection.return_value.document.return_value.get.return_value = mock_doc
        
        update_data = UpdatePortfolioRequest(title="Admin Updated Title")
        
        # Should not raise authorization error for admin
        result = admin_manager.update_portfolio("portfolio_123", update_data)
        
        assert result["title"] == "Admin Updated Title"
        
        # Verify update was called
        self.mock_firestore.collection.return_value.document.return_value.update.assert_called_once()
```

## Migration Guide

### From GACO Framework v1 to v2

#### 1. Update Imports

```python
# Old (v1)
from gaco_framework import GacoContext, gaco_endpoint

# New (v2)
from gaco_framework import (
    GacoContext,
    gaco_endpoint,
    AuthRequirement,
    require_auth,
    check_resource_access,
    ResourceAccess,
    AuthContext
)
```

#### 2. Update Endpoint Declarations

```python
# Old - Manual auth checking
@gaco_endpoint()
def my_endpoint(context: GacoContext, data: dict):
    # Manual auth validation
    if not context.user_id:
        raise AuthenticationError("Authentication required")
    
    if context.user_id != data.get("user_id"):
        raise AuthorizationError("Access denied")

# New - Declarative auth
@gaco_endpoint(
    require_auth=AuthRequirement.VERIFIED_EMAIL,
    required_roles=["user"]
)
@require_custom_auth(lambda ctx: ctx.auth_context.user_id == ctx.request_data.get("user_id"))
def my_endpoint(context: GacoContext, data: dict):
    # Auth already validated
    pass
```

#### 3. Access User Information

```python
# Old
def my_endpoint(context: GacoContext, data: dict):
    user_id = context.user_id  # Direct access

# New
def my_endpoint(context: GacoContext, data: dict):
    user_id = context.auth_context.user_id  # Through auth_context
    email = context.auth_context.email
    is_verified = context.auth_context.email_verified
    
    # Check roles and permissions
    if context.auth_context.has_role("admin"):
        # Admin logic
    
    if context.auth_context.has_permission("write"):
        # Write permission logic
```

#### 4. Update Manager Classes

```python
# Old - No auth context in managers
class MyManager:
    def __init__(self, firestore_client):
        self.firestore = firestore_client
    
    def create_item(self, user_id, data):
        # Manual user_id parameter
        pass

# New - Auth context automatically available
class MyManager(BaseGacoManager):
    def create_item(self, data):
        # Access auth context
        user_id = self.auth_context.user_id
        
        # Automatic authorization checks
        if not self.auth_context.has_permission("create"):
            raise AuthorizationError("No create permission")
```

#### 5. Replace Manual Security Checks

```python
# Old - Manual ownership checking
@gaco_endpoint()
def update_resource(context: GacoContext, data: dict):
    resource_id = data.get("resource_id")
    
    # Manual database check
    firestore_client = context.get_firestore_client()
    resource_doc = firestore_client.collection('resources').document(resource_id).get()
    
    if not resource_doc.exists:
        raise NotFoundError("Resource not found")
    
    resource_data = resource_doc.to_dict()
    if resource_data["owner_id"] != context.user_id:
        raise AuthorizationError("Not resource owner")

# New - Using security rules
@gaco_endpoint(require_auth=AuthRequirement.VERIFIED_EMAIL)
def update_resource(context: GacoContext, data: dict):
    resource_id = data.get("resource_id")
    
    # Automatic security check
    check_resource_access(
        context.auth_context,
        "resource",
        {"resource_id": resource_id},
        ResourceAccess.WRITE
    )
    
    # Proceed with update
```

#### 6. Update Tests

```python
# Old - Manual context creation
def test_my_endpoint():
    context = GacoContext(
        request_data={"test": "data"},
        user_id="test_user",
        # ... other manual setup
    )

# New - Using test utilities
def test_my_endpoint():
    client = GacoTestClient()
    client.set_auth(
        user_id="test_user",
        roles=["user"],
        email_verified=True
    )
    
    response = client.call_endpoint(my_endpoint, {"test": "data"})
    assert response.success
```

### Breaking Changes

1. **Auth Context Structure**: User information is now accessed through `context.auth_context` instead of directly on context
2. **Manager Base Class**: All managers should extend `BaseGacoManager` to receive auth context
3. **Authorization Decorators**: New decorator system replaces manual auth checks
4. **Security Rules**: Resource access should use `check_resource_access` instead of manual checks
5. **Error Types**: More specific error types for different authorization failures

### New Features to Leverage

1. **Role-Based Access Control**: Use `required_roles` parameter in `@gaco_endpoint`
2. **Permission-Based Access**: Use `required_permissions` parameter
3. **Custom Claims**: Access Firebase custom claims through `auth_context.get_claim()`
4. **Resource Security**: Define and use security rules for consistent access control
5. **Enhanced Testing**: Use `GacoTestClient` for easier testing with auth mocking
6. **Middleware System**: Add cross-cutting concerns with reusable middleware

### Configuration Updates

```python
# Old - Basic configuration
from gaco_framework import configure_gaco

configure_gaco(
    project_id="my-project",
    debug=True
)

# New - Enhanced configuration with security
from gaco_framework import configure_gaco

configure_gaco(
    project_id="my-project",
    debug=True,
    auth_required_by_default=True,  # Require auth unless explicitly disabled
    enable_security_rules=True,     # Enable resource-level security
    log_security_violations=True,   # Log authorization failures
    rate_limiting_enabled=True      # Enable built-in rate limiting
)
```

This migration guide should help you upgrade from GACO Framework v1 to v2 while taking advantage of the new security and authorization features.
