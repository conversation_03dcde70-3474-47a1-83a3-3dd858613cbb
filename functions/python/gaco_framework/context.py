"""
Enhanced context management for Gaco Framework with authorization
Backward compatible with v1
"""

from dataclasses import dataclass, field
from typing import Any, Dict, Optional, Type, TypeVar, Union
from firebase_admin import firestore
from firebase_functions.https_fn import CallableRequest
import inspect

from .types import AuthContext
from .config import get_config

T = TypeVar("T")


@dataclass
class GacoContext:
    """Enhanced context for HTTP callable cloud functions with auth support"""

    db: firestore.Client
    auth: Optional[Any]
    auth_context: Optional[AuthContext] = None
    raw_request: Optional[CallableRequest] = None
    user_id: Optional[str] = None
    request_data: Optional[Dict[str, Any]] = None

    def __post_init__(self) -> None:
        """Extract user_id and create auth context"""
        # Create auth context from Firebase auth
        if self.auth:
            self.auth_context = AuthContext.from_firebase_auth(self.auth)
            self.user_id = self.auth_context.user_id
        elif self.auth and hasattr(self.auth, "uid"):
            # Fallback for simple auth object (v1 compatibility)
            self.user_id = self.auth.uid

        # Store request data for authorization checks
        if self.raw_request and hasattr(self.raw_request, "data"):
            self.request_data = self.raw_request.data

    def get_manager(self, manager_class: Type[T]) -> T:
        """
        Factory method for manager classes with dependency injection
        Backward compatible with v1 managers

        Args:
            manager_class: The manager class to instantiate

        Returns:
            Instantiated manager with database client and auth context injected
        """
        # Check if manager accepts auth_context by inspecting __init__ signature
        try:
            sig = inspect.signature(manager_class.__init__)
            params = list(sig.parameters.keys())

            # If manager accepts auth_context parameter, pass it
            if "auth_context" in params:
                return manager_class(db=self.db, auth_context=self.auth_context)
            else:
                # v1 compatibility - only pass db
                return manager_class(db=self.db)
        except Exception:
            # Fallback to v1 behavior
            return manager_class(db=self.db)

    def get_collection(self, collection_name: str) -> firestore.CollectionReference:
        """
        Get a Firestore collection reference

        Args:
            collection_name: Name of the collection

        Returns:
            Firestore collection reference
        """
        config = get_config()
        actual_name = config.collections.get(collection_name, collection_name)
        return self.db.collection(actual_name)

    def is_authenticated(self) -> bool:
        """Check if the request is authenticated"""
        return (
            self.auth_context is not None and self.auth_context.user_id is not None
        ) or self.user_id is not None

    def has_role(self, role: str) -> bool:
        """Check if the authenticated user has a specific role"""
        return self.auth_context and self.auth_context.has_role(role)

    def has_permission(self, permission: str) -> bool:
        """Check if the authenticated user has a specific permission"""
        return self.auth_context and self.auth_context.has_permission(permission)

    def get_claim(self, key: str, default: Any = None) -> Any:
        """Get a custom claim value from the auth context"""
        if self.auth_context:
            return self.auth_context.get_claim(key, default)
        return default


@dataclass
class GacoEventContext:
    """Context for event-driven cloud functions"""

    db: firestore.Client
    event: Any
    event_type: Optional[str] = None

    def get_manager(self, manager_class: Type[T]) -> T:
        """Factory method for manager classes"""
        return manager_class(db=self.db)

    def get_collection(self, collection_name: str) -> firestore.CollectionReference:
        """Get a Firestore collection reference"""
        config = get_config()
        actual_name = config.collections.get(collection_name, collection_name)
        return self.db.collection(actual_name)
