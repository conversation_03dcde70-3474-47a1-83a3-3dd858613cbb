"""
Enhanced endpoint handling for Gaco Framework
"""

import functools
from typing import Any, Callable, Generic, Type, TypeVar

from pydantic import ValidationError as PydanticValidationError
from firebase_functions import logger

from .context import GacoContext
from .exceptions import ValidationError
from .models import BaseGacoModel, GacoResponse

T = TypeVar("T", bound=BaseGacoModel)
R = TypeVar("R")


class GacoEndpoint(Generic[T]):
    """
    Enhanced endpoint wrapper with type-safe request validation.

    This class provides automatic request validation using Pydantic models
    and ensures type safety between the request model and the handler function.

    Example:
        @gaco_endpoint()
        @GacoEndpoint(CreateUserRequest)
        def create_user(context: GacoContext, request: CreateUserRequest) -> GacoResponse:
            user_manager = context.get_manager(UserManager)
            user_id = user_manager.create_user(request, context.user_id)

            return GacoResponse(
                success=True,
                message=f"User created with ID: {user_id}",
                data={"user_id": user_id},
                code=201
            )
    """

    def __init__(self, request_model: Type[T]):
        """
        Initialize the endpoint with a request model.

        Args:
            request_model: Pydantic model class for request validation
        """
        self.request_model = request_model

    def __call__(
        self, func: Callable[[GacoContext, T], R]
    ) -> Callable[[GacoContext, dict], R]:
        """
        Wrap the function with request validation.

        Args:
            func: The handler function to wrap

        Returns:
            Wrapped function with automatic request validation
        """

        @functools.wraps(func)
        def wrapper(context: GacoContext, raw_data: dict) -> R:
            try:
                # Store raw data in context for authorization checks
                context.request_data = raw_data

                # Validate and parse the request data
                validated_request = self.request_model.model_validate(raw_data)

                # Call the original function with validated data
                return func(context, validated_request)

            except PydanticValidationError as e:
                logger.error(f"Request validation failed for {func.__name__}: {e}")
                raise ValidationError(
                    message="Request validation failed",
                    errors=[
                        {
                            "field": err.get("loc", ["unknown"])[-1],
                            "message": err.get("msg", "Validation error"),
                            "type": err.get("type", "validation_error"),
                            "input": err.get("input"),
                        }
                        for err in e.errors()
                    ],
                )

        return wrapper


class GacoEndpointNoValidation:
    """
    Endpoint wrapper without request validation.

    Use this when you want to handle raw dictionary data without
    automatic Pydantic validation.

    Example:
        @gaco_endpoint()
        @GacoEndpointNoValidation()
        def raw_handler(context: GacoContext, data: dict) -> GacoResponse:
            # Handle raw data manually
            return GacoResponse(success=True, message="OK", code=200)
    """

    def __call__(
        self, func: Callable[[GacoContext, dict], R]
    ) -> Callable[[GacoContext, dict], R]:
        """Simply pass through without validation"""

        @functools.wraps(func)
        def wrapper(context: GacoContext, raw_data: dict) -> R:
            # Store raw data in context for authorization checks
            context.request_data = raw_data
            return func(context, raw_data)

        return wrapper


# Convenience function for simpler syntax
def validate_request(request_model: Type[T]) -> GacoEndpoint[T]:
    """
    Convenience function to create a GacoEndpoint.

    Args:
        request_model: Pydantic model for request validation

    Returns:
        GacoEndpoint instance

    Example:
        @gaco_endpoint()
        @validate_request(CreateUserRequest)
        def create_user(context: GacoContext, request: CreateUserRequest):
            # Implementation
            pass
    """
    return GacoEndpoint(request_model)
