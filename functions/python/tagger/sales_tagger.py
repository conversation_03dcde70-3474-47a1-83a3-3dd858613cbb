from typing import Dict, List, Optional, Any
from firebase_admin import firestore
from firebase_functions import logger

from gaco_framework.exceptions import NotFoundError
from gaco_framework.managers import BaseGacoManager
from queries.sales_staging_query_builder import SalesStagingQueryBuilder
from models.sales import Sale, SalesStaging
from constants.collections import (
    sales_staging_collection, 
    producer_collection,
    sales_tagging_requests_collection
)
from models.producer import Producer
from models.requests.tagger_requests import SalesStagingProducerTagRequest


class SalesStagingProducerTagger(BaseGacoManager):
    """
    Sales tagger that applies producer tagging rules to sales staging data based on field content.

    This class processes sales data from the sales-staging collection only and tags them
    with producer IDs when the specified fields contain keywords associated with specific
    producers according to the tag rules. It can also update vendor information based on
    the configuration flags.
    """
    sales_staging_collection = sales_staging_collection
    producer_collection = producer_collection
    sales_tagging_requests_collection = sales_tagging_requests_collection

    def __init__(self, db: firestore.Client, auth_context=None):
        super().__init__(db, auth_context)
        self.sales_staging_query_builder = SalesStagingQueryBuilder(self.db)
        self._get_collection_ref = self._get_collection(self.sales_staging_collection)
        self._get_producer_collection_ref = self._get_collection(self.producer_collection)

    def tag_sales_data(self, tag_request: SalesStagingProducerTagRequest) -> Dict[str, Any]:
        """
        Tag sales staging data based on the provided configuration.

        Args:
            tag_request: Configuration specifying which fields to check and tag rules

        Returns:
            Dictionary containing tagging results and statistics
        """
        logger.info(f"Starting sales staging tagging for store: {tag_request.store_id}")

        # Get sales staging data for the store only
        sales_staging_docs = self._get_sales_staging_data(tag_request.store_id)

        # Process staging sales only
        staging_results = self._process_sales_documents(
            sales_staging_docs, tag_request, self.sales_staging_collection
        )

        # Use store_id as document ID to overwrite
        self.db.collection(self.sales_tagging_requests_collection)\
            .document(tag_request.store_id)\
            .set(tag_request.model_dump())

        results = {
            "success": True,
            "store_id": tag_request.store_id,
            "total_processed": staging_results["processed"],
            "total_tagged": staging_results["tagged"],
            "staging_results": staging_results,
            "tag_rules_applied": len(tag_request.tag_rule),
            "override_vendor": tag_request.override_vendor,
            "update_vendor_if_null": tag_request.update_vendor_if_null
        }

        logger.info(f"Staging tagging completed. Processed: {staging_results['processed']}, Tagged: {staging_results['tagged']}")
        return results

    def _get_sales_staging_data(self, store_id: str) -> List[firestore.DocumentSnapshot]:
        """Get sales staging documents for the store"""
        return list(
            self.db.collection(self.sales_staging_collection)\
                .where(SalesStaging.STORE_ID_FIELD, '==', store_id)\
                .stream()
            )

    def _process_sales_documents(
        self,
        docs: List[firestore.DocumentSnapshot],
        tag_request: SalesStagingProducerTagRequest,
        collection_name: str
    ) -> Dict[str, Any]:
        """
        Process a list of sales documents and apply tagging rules.

        Args:
            docs: List of Firestore document snapshots
            tag_request: Tagging configuration
            collection_name: Name of the collection being processed

        Returns:
            Dictionary with processing results
        """
        processed_count = 0
        tagged_count = 0
        tagged_documents = []

        for doc in docs:
            processed_count += 1

            logger.info(f"Processing document {type(doc)}")
            if not isinstance(doc, firestore.DocumentSnapshot):
                raise ValueError(f"Expected DocumentSnapshot, got {type(doc)}")

            sale_data = doc.to_dict()

            # Check if this sale should be tagged
            matched_producer_id = self._apply_tag_rules(sale_data, tag_request)

            if matched_producer_id:
                # Update the document with the producer tag and vendor if needed
                self._update_sale_with_producer_tag(
                    doc.id, matched_producer_id, collection_name, sale_data, tag_request
                )
                tagged_count += 1
                tagged_documents.append({
                    "document_id": doc.id,
                    "producer_id": matched_producer_id,
                    "matched_fields": self._get_matched_fields(sale_data, tag_request, matched_producer_id)
                })

        return {
            "collection": collection_name,
            "processed": processed_count,
            "tagged": tagged_count,
            "tagged_documents": tagged_documents
        }

    def _apply_tag_rules(
        self,
        sale_data: Dict[str, Any],
        tag_request: SalesStagingProducerTagRequest
    ) -> Optional[str]:
        """
        Apply tag rules to a sale and return the matching producer ID if any.

        Args:
            sale_data: Sale document data
            tag_request: Tagging configuration

        Returns:
            Producer ID if a match is found, None otherwise
        """
        # Check each producer's keywords against the specified fields
        for producer_id, keywords in tag_request.tag_rule.items():
            if self._check_keywords_match(sale_data, tag_request.fields, keywords):
                logger.info(f"Sale matched producer {producer_id} with keywords: {keywords}")
                return producer_id

        return None

    def _check_keywords_match(
        self,
        sale_data: Dict[str, Any],
        fields: List[str],
        keywords: List[str]
    ) -> bool:
        """
        Check if any of the keywords match content in the specified fields.

        Args:
            sale_data: Sale document data
            fields: List of field names to check
            keywords: List of keywords to search for

        Returns:
            True if any keyword matches, False otherwise
        """
        # Get content from specified fields
        field_content = []
        for field in fields:
            if field in sale_data and sale_data[field]:
                field_content.append(str(sale_data[field]).lower())

        # Check if any keyword matches any field content
        combined_content = " ".join(field_content)

        for keyword in keywords:
            if keyword.lower() in combined_content:
                logger.info(f"Keyword '{keyword}' found in fields {fields}")
                return True

        return False

    def _get_matched_fields(
        self,
        sale_data: Dict[str, Any],
        tag_request: SalesStagingProducerTagRequest,
        producer_id: str
    ) -> List[str]:
        """Get the fields that matched for the given producer"""
        matched_fields = []
        keywords = tag_request.tag_rule[producer_id]

        for field in tag_request.fields:
            if field in sale_data and sale_data[field]:
                field_content = str(sale_data[field]).lower()
                for keyword in keywords:
                    if keyword.lower() in field_content:
                        matched_fields.append(field)
                        break

        return matched_fields

    def _update_sale_with_producer_tag(
        self,
        document_id: str,
        producer_id: str,
        collection_name: str,
        sale_data: Dict[str, Any],
        tag_request: SalesStagingProducerTagRequest
    ) -> None:
        """
        Update a sale document with the producer tag and vendor information if configured.

        Args:
            document_id: Document ID to update
            producer_id: Producer ID to tag with
            collection_name: Collection name (should be sales-staging)
            sale_data: Current sale data
            tag_request: Tag request configuration
        """
        producer_doc = self._get_producer_collection_ref.document(producer_id).get()
        if not producer_doc.exists:
            raise NotFoundError(f"Producer document {producer_id} does not exist")

        producer_display_name = Producer.model_validate(
            producer_doc.to_dict()
        ).display_name

        try:
            # Prepare base update data
            update_data = {
                SalesStaging.PRODUCER_ID_FIELD: producer_id,
                SalesStaging.TAGGED_BY_FIELD: "sales_tagger"
            }

            # Handle vendor field updates based on configuration
            current_vendor = sale_data.get(Sale.VENDOR_FIELD)

            if tag_request.override_vendor:
                # Override vendor with producer_id regardless of current value
                update_data[Sale.VENDOR_FIELD] = producer_display_name
                logger.info(f"Overriding vendor with producer_id: {producer_id}")

            elif tag_request.update_vendor_if_null and (current_vendor is None or current_vendor == ""):
                # Only update vendor if it's null/empty
                update_data[Sale.VENDOR_FIELD] = producer_display_name
                logger.info(f"Setting vendor to producer_id {producer_id} (was null/empty)")

            # Update the document
            self.db.collection(collection_name).document(document_id).update(update_data)

            vendor_action = ""
            if Sale.VENDOR_FIELD in update_data:
                vendor_action = f", vendor updated to: {update_data[Sale.VENDOR_FIELD]}"

            logger.info(f"Updated {collection_name} document {document_id} with producer_id: {producer_id}{vendor_action}")

        except Exception as e:
            logger.error(f"Failed to update document {document_id}: {str(e)}")
            raise
