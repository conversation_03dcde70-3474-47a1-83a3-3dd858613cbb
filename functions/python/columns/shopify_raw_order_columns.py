from enum import Enum

class ShopifyOrderAdditionalColumnNames(Enum):
    STORE_ID = 'store_id'

class ShopifyOrderColumnNames(Enum):
    ORDER_ID = 'order_id'
    LINE_ITEM_ID = 'line_item_id'
    UPDATED_AT = 'updated_at'
    PRODUCT_ID = 'product_id'
    TITLE = 'title'
    VARIANT_TITLE = 'variant_title'
    VARIANT_DISPLAY_NAME = 'variant_display_name'
    QUANTITY = 'quantity'
    VENDOR = 'vendor'
    UNIT_PRICE = 'unit_price'
    TOTAL_PRICE = 'total_price'
    SUBTOTAL = 'subtotal'
    CURRENCY = 'currency'
    DISCOUNT = 'discount'

# JSON Keys
class ShopifyOrderJsonKeys(Enum):
    NODE = 'node'
    ID = 'id'
    PRODUCT = 'product'
    VARIANT = 'variant'
    TITLE = 'title'
    VARIANT_TITLE = 'variantTitle'
    DISPLAY_NAME = 'displayName'
    QUANTITY = 'quantity'
    VENDOR = 'vendor'
    ORIGINAL_UNIT_PRICE_SET = 'originalUnitPriceSet'
    ORIGINAL_TOTAL_SET = 'originalTotalSet'
    SHOP_MONEY = 'shopMoney'
    PRESENTMENT_MONEY = 'presentmentMoney'
    AMOUNT = 'amount'
    CURRENCY_CODE = 'currencyCode'
    UPDATED_AT = 'updatedAt'
    CLOSED = 'closed'
    FULLY_PAID = 'fullyPaid'
    CURRENT_SUBTOTAL_PRICE_SET = 'currentSubtotalPriceSet'
    CART_DISCOUNT_AMOUNT_SET = 'cartDiscountAmountSet'
    LINE_ITEMS = 'lineItems'
    EDGES = 'edges'
    ORDERS = 'orders'
