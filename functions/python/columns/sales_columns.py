from enum import Enum


class SalesColumns(Enum):
    SALE_ID = "sale_id"
    TITLE = "title"
    VARIANT_TITLE = "variant_title"
    VARIANT_DISPLAY_NAME = "variant_display_name"
    DOCUMENT_ID = "document_id"
    PRODUCER_ID = "producer_id"
    PRODUCER_DISPLAY_NAME = "producer_display_name"
    COMMISSION = "commission"
    PRODUCER_TAX_A2 = "producer_tax_a2"
    STORE_TAX_A2 = "store_tax_a2"
    DISCOUNT = "discount"
    SUBTOTAL = "subtotal"
    STORE_ID = "store_id"
    UPDATED_AT = "updated_at"


class SalesCommonComputedColumns(Enum):
    PRODUCER_GROSS_PAYOUT = "producer_gross_payout"
    STORE_GROSS_PAYOUT = "store_gross_payout"


class SalesReportColumns(Enum):
    PRODUCER_GROSS_PAYOUT = SalesCommonComputedColumns.PRODUCER_GROSS_PAYOUT.value
    STORE_GROSS_PAYOUT = SalesCommonComputedColumns.STORE_GROSS_PAYOUT.value


class SalesInvoiceColumns(Enum):
    ASSIGNED_VAT_RATE = "assigned_vat_rate"
    VAT_EXCLUDED_SALE = "vat_excluded_sale"
    NET_SALES = "net_sales"
    STORE_NET_PAYOUT = "store_net_payout"
    VAT_ON_SALES_SERVICE = "vat_on_sales_service"
    STORE_TOTAL_GROSS_PAYOUT = "store_total_gross_payout"
    PRODUCER_GROSS_PAYOUT = SalesCommonComputedColumns.PRODUCER_GROSS_PAYOUT.value

