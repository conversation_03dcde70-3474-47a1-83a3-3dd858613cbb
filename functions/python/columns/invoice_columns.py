from enum import Enum

# column names for sales level "data"
class InvoiceSalesLevelColumnNames(Enum):
    TITLE = 'title'
    VARIANT_TITLE = 'variant_title'
    VARIANT_DISPLAY_NAME = 'variant_display_name'
    PRODUCER_ID = 'producer_id'
    COMMISSION = 'commission'
    PRODUCER_TAX_A2 = 'producer_tax_a2'
    STORE_TAX_A2 = 'store_tax_a2'
    DISCOUNT = 'discount'
    SUBTOTAL = 'subtotal'
    STORE_ID = 'store_id'
    ASSIGNED_VAT_RATE = 'assigned_vat_rate'
    VAT_EXCLUDED_SALE = 'vat_excluded_sale'
    NET_SALES = 'net_sales'
    STORE_NET_PAYOUT = 'store_net_payout'
    VAT_ON_SALES_SERVICE = 'vat_on_sales_service'
    STORE_TOTAL_GROSS_PAYOUT = 'store_total_gross_payout'
    PRODUCER_GROSS_PAYOUT = 'producer_gross_payout'

# column names for sales level "report"
class InvoiceSalesLevelReportColumnNames(Enum):
    TITLE = 'title'
    VARIANT_TITLE = 'variant_title'
    VARIANT_DISPLAY_NAME = 'variant_display_name'
    PRODUCER_ID = 'producer_id'
    COMMISSION = 'commission'
    PRODUCER_TAX_A2 = 'producer_tax_a2'
    STORE_TAX_A2 = 'store_tax_a2'
    DISCOUNT = 'discount'
    SUBTOTAL = 'subtotal'
    STORE_ID = 'store_id'
    ASSIGNED_VAT_RATE = 'assigned_vat_rate'
    VAT_EXCLUDED_SALE = 'vat_excluded_sale'
    NET_SALES = 'net_sales'
    STORE_NET_PAYOUT = 'store_net_payout'
    VAT_ON_SALES_SERVICE = 'vat_on_sales_service'
    STORE_TOTAL_GROSS_PAYOUT = 'store_total_gross_payout'
    PRODUCER_GROSS_PAYOUT = 'producer_gross_payout'
    ITEM_COUNT = 'item_count'
