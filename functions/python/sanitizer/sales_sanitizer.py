from firebase_admin import firestore
from typing import Dict, List, Optional, Union
from models.sales import Sale, SalesStaging
from models.requests.sanitization_request import SanitizeSalesStagingWithAgreementRequest
from matchers.factories import MatchingStrategy
from sales_sanitizer_factory import SalesSanitizerFactory
from sales_sanitizer_interface import SalesSanitizerInterface
from gaco_framework.auth import AuthContext

class SalesSanitizer:
    """
    Main entry point for sales sanitization service.
    This class delegates to the implementation provided by the factory.
    """
    
    def __init__(
            self, 
            db: Optional[firestore.Client] = None,
            auth_context: Optional[AuthContext] = None,
            matching_strategy: MatchingStrategy = MatchingStrategy.ALL_STRATEGIES
        ):
        """
        Initialize the SalesSanitizer with database client and dependencies.
        
        Args:
            db: Firestore client instance
            matching_strategy: Strategy for matching applications
        """
        self._sanitizer = SalesSanitizerFactory.create(
            db=db,
            auth_context=auth_context,
            matching_strategy=matching_strategy
        )
    
    def sanitize_sales_from_sale_object(
        self, 
        sale_input: Union[Sale, SalesStaging], 
        active_partnership: Optional[List[Dict]] = None,
        force_sanitize_with_producer_id: Optional[str] = None,
        document_id: Optional[str] = None
    ) -> Dict[str, dict]:
        """
        Sanitize a Sale or SalesStaging object, match it with partnerships, 
        and determine if it belongs in sales-silver or sales-staging.
        
        Args:
            sale_input: Either a Sale or SalesStaging object to sanitize
            active_partnership: List of accepted partnerships to match against
            force_sanitize_with_producer_id: Optional producer ID to force sanitization with
            document_id: Required document ID when passing a Sale object
            
        Returns:
            A dictionary representing the SanitizedSales model
        """
        return self._sanitizer.sanitize_sales_from_sale_object(
            sale_input,
            active_partnership,
            force_sanitize_with_producer_id,
            document_id
        )
    
    def sanitize_sale_with_no_parentId_producer(
        self, 
        sanitization_request: SanitizeSalesStagingWithAgreementRequest,
        user_uuid: str
    ) -> Dict[str, dict]:
        """
        Sanitize a sale object with a temporary artist.
        
        Args:
            sanitization_request: Request containing producer details and sale ID
            user_uuid: UUID of the user performing the action
            
        Returns:
            A dictionary representing the sanitized sale
        """
        return self._sanitizer.sanitize_sale_with_no_parentId_producer(
            sanitization_request,
            user_uuid
        )
    
    def process_sanitized_sale(
        self, 
        sanitized_sale: Dict[str, dict], 
        document_id: str
    ) -> None:
        """
        Process a sanitized sale by moving it to the appropriate collection.
        
        Args:
            sanitized_sale: The sanitized sale object returned from sanitization functions
            document_id: The document ID of the sale
        """
        self._sanitizer.process_sanitized_sale(sanitized_sale, document_id)
    
    def process_sanitized_sale_batch(
        self, 
        sanitized_sales: List[Dict[str, dict]]
    ) -> None:
        """
        Process multiple sanitized sales using batch operations for better performance.
        
        Args:
            sanitized_sales: List of sanitized sale objects
        """
        self._sanitizer.process_sanitized_sale_batch(sanitized_sales)