from typing import Optional, List, Dict, Union
from datetime import datetime
from firebase_admin import firestore
from models.sales import Sale, SalesSilver, SalesStaging
from models.application import Status, Application, ApplicationMatcher
from queries.partners_query_builder import PartnersQueryBuilder
from queries.producers_query_builder_v2 import ProducersQueryBuilderV2
from queries.stores_query_builder_v2 import StoresQueryBuilderV2
from queries.sales_staging_query_builder import SalesStagingQueryBuilder
from matchers.factories import ApplicationMatcherFactory, MatchingStrategy
from features.sale_metadata import SaleMetadataFeatures
from models.sanitized_sales import SanitizedSales, sales_staging_collection, sales_silver_collection
from firebase_functions import logger
from models.requests.sanitization_request import SanitizeSalesStagingWithAgreementRequest, ForceAgreementRequest
from services.producer_manager import ProducerManager
from services.partner_manager import PartnershipManager
from flows.force_active_partnership import ForceActivePartnershipFlow
from services.agreement_manager import AgreementManager
from sanitizer.sales_sanitizer_interface import SalesSanitizerInterface
from models.partner import PartnershipStatus, Partnership
from models.agreement import Agreement, agreement_collection
from gaco_framework.auth import AuthContext
from gaco_framework.exceptions import NotFoundError


class SalesSanitizerImpl:
    """
    Implementation of the SalesSanitizerInterface for sanitizing sales data.
    """
    
    def __init__(
            self, 
            db: Optional[firestore.Client] = None,
            auth_context: Optional[AuthContext] = None,
            matching_strategy: MatchingStrategy = MatchingStrategy.ALL_STRATEGIES
        ):
        """
        Initialize the SalesSanitizer with database client and dependencies.
        
        Args:
            db: Firestore client instance
            matching_strategy: Strategy for matching applications
        """
        self.db = db or firestore.client()
        self.partners_query_builder = PartnersQueryBuilder(db)
        self.producers_query_builder = ProducersQueryBuilderV2(db)
        self.stores_query_builder = StoresQueryBuilderV2(db)
        self.sales_staging_query_builder = SalesStagingQueryBuilder(db)
        self.partnership_matcher = ApplicationMatcherFactory.create(
            matching_strategy
        )
        self.sale_metadata_features = SaleMetadataFeatures(db)
        self.sales_staging_collection = sales_staging_collection
        self.sales_silver_collection = sales_silver_collection
        self.producer_manager = ProducerManager(db, auth_context)
        self.partner_manager = PartnershipManager(db, auth_context)
        self.force_active_partnership_flow = ForceActivePartnershipFlow(db, auth_context)
        self.agreement_manager = AgreementManager(db, auth_context)
        self.agreements_collection = agreement_collection

    def fetch_active_partnerships(self, store_id: str) -> List[Dict]:
        """
        Fetch all accepted partnerships for a store and enrich with producer display names.
        
        Args:
            store_id: ID of the store to fetch partnerships for
            
        Returns:
            List of partnership dictionaries
        """
        # Get accepted partnerships
        query = (self.partners_query_builder
                .for_store_id(store_id)
                .with_status(PartnershipStatus.ACTIVE.value)
                .build())
        
        active_partnerships = []
        for partnership_doc in query.stream():
            partnership_data = partnership_doc.to_dict()
            
            producer_doc = (self.producers_query_builder
                           .for_id(partnership_data[Partnership.PRODUCER_ID_FIELD])
                           .build()
                           .get())
            
            if producer_doc.exists:
                producer_data = producer_doc.to_dict()
                partnership_data[SalesSilver.PRODUCER_DISPLAY_NAME_FIELD] = producer_data.get('displayName')
            
            # keep it as a dict since this is partnership data + display name
            active_partnerships.append(partnership_data)

        return active_partnerships

    def _enrich_sale_with_producer_data(
        self, 
        sale_dict: Dict,
        producer_doc: firestore.DocumentSnapshot, 
        store_doc: firestore.DocumentSnapshot,
        agreement_data: firestore.DocumentSnapshot
    ) -> SalesSilver:
        """
        Enrich sale data with producer information.
        
        Args:
            sale_dict: Sale data dictionary
            producer_doc: Producer document snapshot
            store_doc: Store document snapshot
            partnership_data: Partnership data dictionary
            agreement_data: Agreement data dictionary
        Returns:
            Enriched SalesSilver object
        """
        return self.sale_metadata_features.generate(
            sale_dict,
            producer_id=producer_doc.id,
            store_id=store_doc.id,
            commission=agreement_data.to_dict()[Agreement.COMMISSION_FIELD],
            agreement_id=agreement_data.id
        )
    
    def _prepare_sale_input(
        self, 
        sale_input: Union[Sale, SalesStaging], 
        document_id: Optional[str]
    ) -> tuple[dict, str, str, Union[Sale, SalesStaging]]:
        """Extracts standardized data from the sale input."""
        if isinstance(sale_input, SalesStaging):
            sales_staging_obj = sale_input
            doc_id = sales_staging_obj.document_id
            store_id = sales_staging_obj.store_id
            sale_dict = sales_staging_obj.model_dump()
            input_object_for_matcher = sales_staging_obj
        elif isinstance(sale_input, Sale):
            if document_id is None:
                raise ValueError("document_id is required when passing a Sale object")
            sale_obj = sale_input
            doc_id = document_id
            store_id = sale_obj.store_id
            sale_dict = sale_obj.model_dump()
            sale_dict[SalesStaging.DOCUMENT_ID_FIELD] = doc_id
            input_object_for_matcher = sale_obj
        else:
            raise TypeError("sale_input must be an instance of Sale or SalesStaging")
        
        return sale_dict, doc_id, store_id, input_object_for_matcher

    def _get_store_document(self, store_id: str) -> firestore.DocumentSnapshot:
        """Fetches the store document."""
        return self.stores_query_builder.for_id(store_id).build().get()

    def _get_producer_document(self, producer_id: str) -> firestore.DocumentSnapshot:
        """Fetches the producer document."""
        return self.producers_query_builder.for_id(producer_id).build().get()

    def _get_matched_partnership(
        self, 
        input_object_for_matcher: Union[Sale, SalesStaging], 
        active_partnership: List[Dict], 
        force_sanitize_with_producer_id: Optional[str]
    ) -> Optional[Dict]:
        """Finds the matching partnership, handling forced sanitization."""
        if force_sanitize_with_producer_id:
            matched_apps = [
                app for app in active_partnership 
                if app[Partnership.PRODUCER_ID_FIELD] == force_sanitize_with_producer_id
            ]
            if matched_apps:
                return matched_apps[0]
            else:
                logger.error(f"No partnership found for forced producer ID: {force_sanitize_with_producer_id}")
                return None # Explicitly return None if forced ID not found
        
        # Standard matching
        return self.partnership_matcher.match(
            input_object_for_matcher, 
            active_partnership
        )

    def _create_staging_output(
        self, 
        sale_dict: dict, 
        log_message: Optional[str] = None
    ) -> Dict[str, dict]:
        """Creates the SanitizedSales dictionary for staging."""
        if log_message:
            logger.info(log_message)
        
        try:
            sales_staging_output = SalesStaging(**sale_dict)
            return SanitizedSales(
                collection=self.sales_staging_collection,
                sale=sales_staging_output.model_dump() 
            ).model_dump()
        except Exception as validation_error:
            # Log validation error during staging creation itself
            doc_id = sale_dict.get(SalesStaging.DOCUMENT_ID_FIELD, 'unknown')
            logger.error(f"Validation error creating staging object for doc {doc_id}: {validation_error}")
            # It might be better to raise here or return a specific error structure
            # depending on how upstream callers handle failures.
            # For now, re-raising to indicate a failure in staging creation.
            raise validation_error

    def sanitize_sales_staging_with_agreement(
        self,
        sale_staging: SalesStaging,
        agreement_ref: firestore.DocumentSnapshot
    ) -> Dict[str, dict]:
        """
        Sanitize a sales staging document with a new active agreement.
        """
        agreement_data = Agreement.model_validate(agreement_ref.to_dict())

        sales_silver = self._enrich_sale_with_producer_data(
            sale_dict=sale_staging.model_dump(),
            producer_doc=self._get_producer_document(agreement_data.producer_id),
            store_doc=self._get_store_document(agreement_data.store_id),
            agreement_data=agreement_ref
        )
        
        return sales_silver

    def sanitize_sales_from_sale_object(
        self, 
        sale_input: Union[Sale, SalesStaging], 
        active_partnership: Optional[List[Dict]] = None,
        force_sanitize_with_producer_id: Optional[str] = None,
        document_id: Optional[str] = None,
        agreement_data: Optional[List[firestore.DocumentSnapshot]] = None
    ) -> Dict[str, dict]:
        """
        Sanitize a Sale or SalesStaging object, match it with partnerships, 
        and determine if it belongs in sales-silver or sales-staging.
        Refactored for clarity and maintainability.
        """
        doc_id_for_logging = document_id or getattr(sale_input, 'document_id', 'unknown')

        
        try:
            # 1. Prepare Input
            sale_dict, doc_id, store_id, input_object_for_matcher = self._prepare_sale_input(
                sale_input, document_id)
            doc_id_for_logging = doc_id # Update with actual doc_id once extracted

            # 2. Get Active Partnerships
            if active_partnership is None:
                active_partnership = self.fetch_active_partnerships(store_id)

            # 3. Validate Store
            store_doc = self._get_store_document(store_id)
            if not store_doc.exists:
                return self._create_staging_output(sale_dict, 
                    f"Store {store_id} not found for sale {doc_id}")

            # 4. Check for Partnerships
            if not active_partnership:
                return self._create_staging_output(sale_dict, 
                    f"No active partnerships found for store {store_id} on sale {doc_id}")

            # 5. Match Partnership
            matched_partnership = self._get_matched_partnership(
                input_object_for_matcher, active_partnership, force_sanitize_with_producer_id
            )
            
            if not matched_partnership:
                # Logged inside _get_matched_partnership if forced ID fails
                log_msg = None if force_sanitize_with_producer_id else f"No matching partnership found for sale {doc_id}"
                return self._create_staging_output(sale_dict, log_msg)

            # 6. Validate Producer
            producer_id = matched_partnership[Partnership.PRODUCER_ID_FIELD]
            producer_doc = self._get_producer_document(producer_id)

            logger.info(f"Producer doc: {producer_doc.to_dict()}")

            # 7. Validate Agreement
            if agreement_data is None:
                agreement_data = self.agreement_manager.get_agreement_for_sale_doc(producer_id, store_id, sale_dict)

            # 7.5 Vefiry that there was an agreement found
            if agreement_data is None or len(agreement_data) == 0:
                return self._create_staging_output(sale_dict, 
                    f"No agreement found for sale {doc_id}")
                     

            # 8. Enrich Sale Data for Silver Collection
            try:
                if len(agreement_data) == 1:
                    sales_silver = self._enrich_sale_with_producer_data(
                        sale_dict = sale_dict, 
                        producer_doc = producer_doc, 
                        store_doc = store_doc, 
                        agreement_data = agreement_data[0]
                    )
                
                    # Validate the enriched SalesSilver object before returning
                    validated_sales_silver = SalesSilver\
                        .model_validate(sales_silver.model_dump())

                    # 8. Return Silver Output
                    return SanitizedSales(
                        collection=self.sales_silver_collection, 
                        sale=validated_sales_silver.model_dump() # Use validated data
                    ).model_dump()
                else:
                    sale_dict[SalesStaging.AGREEMENTS_ID_FIELD] = [agreement.id for agreement in agreement_data]
                    return self._create_staging_output(sale_dict, 
                        f"Multiple agreements found for sale {doc_id}, keeping in staging")

            except Exception as e:
                # Handle errors during enrichment or final validation
                logger.error(f"Validation or enrichment error for potential sales-silver {doc_id}: {str(e)}")
                return self._create_staging_output(sale_dict, 
                    f"Validation/enrichment failed for sale {doc_id}, keeping in staging")

        except Exception as e:
            # Catch-all for errors during preparation, fetching, or matching
            logger.error(f"Unhandled error in sanitize_sales_from_sale_object for doc {doc_id_for_logging}: {str(e)}")
            
            # Attempt to return staging output if possible
            if 'sale_dict' in locals():
                return self._create_staging_output(sale_dict, 
                    f"Unhandled error during sanitization for {doc_id_for_logging}, keeping in staging: {str(e)}")
            else:
                # If error occurred before sale_dict could be determined, re-raise
                raise e

    def sanitize_sale_with_no_parentId_producer(
        self, 
        sanitization_request: SanitizeSalesStagingWithAgreementRequest
    ) -> Dict[str, dict]:
        """
        Sanitize a sale object with a temporary artist.
        
        Args:
            sanitization_request: Request containing producer details and sale ID
            user_uuid: UUID of the user performing the action
            
        Returns:
            A dictionary representing the sanitized sale
        """

        force_partnership_request = ForceAgreementRequest(
            store_id=sanitization_request.store_id,
            title=sanitization_request.title,
            effective_date=sanitization_request.effective_date,
            expiration_date=sanitization_request.expiration_date,
            commission=sanitization_request.commission,
            document_url=sanitization_request.document_url,
            display_name=sanitization_request.display_name,
            email=sanitization_request.email,
            tax_a2=sanitization_request.tax_a2
        )

        result = self.force_active_partnership_flow.force_create_active_partnership(
            force_partnership_request        
        )

        logger.info(f"Created producer document with id {result['producer_id']}")

        # Query sales staging documents with sales_id
        sales_staging_doc = self.sales_staging_query_builder\
            .get_document(sanitization_request.sale_id)
        

        if not sales_staging_doc.exists:
            error_msg = f"Sales staging document with id {sanitization_request.sale_id} not found"
            logger.error(error_msg)
            raise NotFoundError(f"CRITICAL ERROR: {error_msg}")
        
        sales_staging_data = SalesStaging.model_validate(sales_staging_doc.to_dict())

        active_partnership = self.fetch_active_partnerships(
            sales_staging_data.store_id
        )

        agreement = self.db.collection(self.agreements_collection).document(result['agreement_id']).get()

        # Force sanitize once
        sanitized_sale = self.sanitize_sales_from_sale_object(
            sale_input=sales_staging_data,
            active_partnership=active_partnership,
            force_sanitize_with_producer_id=result['producer_id'],
            agreement_data=[agreement]
        )
        self.process_sanitized_sale(
            sanitized_sale, 
            sales_staging_data.document_id
        )

        # Query all sales staging documents with store_id and sanitize them
        sales_staging_docs = self.sales_staging_query_builder\
            .for_store_id(sales_staging_data.store_id)\
            .build()\
            .stream()
        
        # For each sales staging document, sanitize the sale and move to sales-silver
        for doc in sales_staging_docs:
            sales_staging_data = SalesStaging.model_validate(doc.to_dict())
            sanitized_sale = self.sanitize_sales_from_sale_object(
                sale_input=sales_staging_data,
                active_partnership=active_partnership,
            )
            self.process_sanitized_sale(
                sanitized_sale, 
                sales_staging_data.document_id
            )
            
        return sanitized_sale

    def process_sanitized_sale(
        self, 
        sanitized_sale: Dict[str, dict], 
        document_id: str
    ) -> None:
        """
        Process a sanitized sale by moving it to the appropriate collection.
        
        Args:
            sanitized_sale: The sanitized sale object returned from sanitization functions
            document_id: The document ID of the sale
        """
        # Set the document in the target collection
        logger.info(f"Setting document in collection: {sanitized_sale['collection']}")
        logger.info(f"Document ID: {document_id}")

        self.db.collection(sanitized_sale['collection'])\
            .document(document_id)\
            .set(sanitized_sale['sale'])
        
        # If we're putting this in sales-silver, delete from staging
        if sanitized_sale['collection'] == self.sales_silver_collection:
            logger.info(f"Deleting from staging: {document_id}")
            self.db.collection(self.sales_staging_collection)\
                .document(document_id)\
                .delete()

    def process_sanitized_sale_batch(
            self, 
            sanitized_sales: List[Dict[str, dict]]
        ) -> None:
        """
        Process multiple sanitized sales using batch operations for better performance.
        
        Args:
            sanitized_sales: List of sanitized sale objects
        """
        # Create a batch
        batch = self.db.batch()
        
        # Track which documents need to be deleted from staging
        staging_docs_to_delete = []
        
        # Add all set operations to the batch
        for sanitized_sale in sanitized_sales:
            # Set reference to target collection document
            target_ref = self.db.collection(sanitized_sale['collection'])\
                .document(sanitized_sale['sale']['documentId'])
            
            # Add document to batch
            batch.set(target_ref, sanitized_sale['sale'])
            
            # If we're putting this in sales-silver, mark for deletion from staging
            if sanitized_sale['collection'] == self.sales_silver_collection:
                staging_docs_to_delete.append(sanitized_sale['sale']['documentId'])
        
        # Commit the batch write
        logger.info(f"Committing batch with {len(sanitized_sales)} documents")
        batch.commit()
        
        # Handle deletions in another batch if needed
        if staging_docs_to_delete:
            delete_batch = self.db.batch()
            for doc_id in staging_docs_to_delete:
                staging_ref = self.db.collection(self.sales_staging_collection).document(doc_id)
                delete_batch.delete(staging_ref)
            
            logger.info(f"Deleting {len(staging_docs_to_delete)} documents from staging")
            delete_batch.commit()
        
    def force_sanitize_sales_staging_with_agreement(
        self,
        sale_staging_id: str,
        agreement_id: str
    ) -> Dict[str, dict]:
        """
        Sanitize a sales staging document with a new active agreement.
        """
        agreement_ref = self.db.collection(self.agreements_collection)\
            .document(agreement_id).get()

        if not agreement_ref.exists:
            raise NotFoundError(f"Agreement {agreement_id} not found")

        sales_staging = self.db.collection(self.sales_staging_collection)\
            .document(sale_staging_id).get()

        if not sales_staging.exists:
            raise NotFoundError(f"Sales staging document {sale_staging_id} not found")

        sales_staging = SalesStaging.model_validate(sales_staging.to_dict())

        sales_silver = self.sanitize_sales_staging_with_agreement(
            sale_staging=sales_staging,
            agreement_ref=agreement_ref
        )

        sanitized_sales = SanitizedSales(
            collection=self.sales_silver_collection,
            sale=sales_silver.model_dump()
        )

        self.process_sanitized_sale(
            sanitized_sales.model_dump(), 
            sale_staging_id
        )

        return sanitized_sales.model_dump()
