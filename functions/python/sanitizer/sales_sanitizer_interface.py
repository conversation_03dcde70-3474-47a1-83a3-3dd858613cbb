from typing import Dict, List, Optional, Protocol, Union
from firebase_admin import firestore
from models.sales import Sale, SalesStaging
from models.requests.sanitization_request import SanitizeSalesStagingWithAgreementRequest
from matchers.factories import MatchingStrategy

class SalesSanitizerInterface(Protocol):
    """
    Interface for the SalesSanitizer service.
    This interface defines the contract for sanitizing sales data.
    """

    def fetch_active_partnerships(
        self, 
        store_id: str
    ) -> List[Dict]:
        """
        Fetch active partnerships for a store.
        """
        ...

    def sanitize_sales_from_sale_object(
        self, 
        sale_input: Union[Sale, SalesStaging], 
        active_partnership: Optional[List[Dict]] = None,
        force_sanitize_with_producer_id: Optional[str] = None,
        document_id: Optional[str] = None,
        agreement_data: Optional[List[firestore.DocumentSnapshot]] = None
    ) -> Dict[str, dict]:
        """
        Sanitize a Sale or SalesStaging object, match it with partnerships, 
        and determine if it belongs in sales-silver or sales-staging.
        
        Args:
            sale_input: Either a Sale or SalesStaging object to sanitize
            active_partnership: List of accepted partnerships to match against
            force_sanitize_with_producer_id: Optional producer ID to force sanitization with
            document_id: Required document ID when passing a Sale object
            
        Returns:
            A dictionary representing the SanitizedSales model
        """
        ...
    
    def sanitize_sale_with_no_parentId_producer(
        self, 
        sanitization_request: SanitizeSalesStagingWithAgreementRequest
    ) -> Dict[str, dict]:
        """
        Sanitize a sale object with a temporary artist.
        
        Args:
            sanitization_request: Request containing producer details and sale ID
            user_uuid: UUID of the user performing the action
            
        Returns:
            A dictionary representing the sanitized sale
        """
        ...
    
    def process_sanitized_sale(
        self, 
        sanitized_sale: Dict[str, dict], 
        document_id: str
    ) -> None:
        """
        Process a sanitized sale by moving it to the appropriate collection.
        
        Args:
            sanitized_sale: The sanitized sale object returned from sanitization functions
            document_id: The document ID of the sale
        """
        ...
    
    def process_sanitized_sale_batch(
        self, 
        sanitized_sales: List[Dict[str, dict]]
    ) -> None:
        """
        Process multiple sanitized sales using batch operations for better performance.
        
        Args:
            sanitized_sales: List of sanitized sale objects
        """
        ...