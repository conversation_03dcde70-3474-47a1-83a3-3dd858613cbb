from firebase_admin import firestore, functions
from firebase_functions import logger
from models.requests.agreements_requests import (
    SanitizeSaleStagingWithExistingAgreementRequest,
    SanitizeSaleWithAgreementTaskRequest,
    SanitizeSaleWithAgreementTask,
)
from constants.collections import sales_sanitization_tasks_collection
from constants.gaco_values import sanitize_sales_staging_with_agreement_task_queue


class SanitizationTaskEnqueuer:
    def __init__(self, db: firestore.Client):
        self.db = db

    def enqueue_sanitization_task(
            self, 
            data: SanitizeSaleStagingWithExistingAgreementRequest
        ) -> dict:

        sanitization_request_id = (
            f"{data.sale_id}_"
            f"{data.agreement_id}"
        )

        sanitization_task_request = SanitizeSaleWithAgreementTaskRequest(
            sanitization_request_id=sanitization_request_id,
            data=data
        )

        self.db.collection(sales_sanitization_tasks_collection)\
            .document(sanitization_request_id)\
            .set(sanitization_task_request.model_dump())

        sanitization_task = SanitizeSaleWithAgreementTask(
            sanitization_request_id=sanitization_request_id
        )

        task_payload = {
            'data': sanitization_task.model_dump()
        }

        logger.info(f"Enqueuing sanitization task: {task_payload}")

        task_queue = functions.task_queue(
            sanitize_sales_staging_with_agreement_task_queue
        )
        task_queue.enqueue(task_payload)

        return task_payload
