from firebase_admin import firestore
from matchers.factories import MatchingStrategy
from sanitizer.sales_sanitizer_impl import SalesSanitizerImpl
from sanitizer.sales_sanitizer_interface import SalesSanitizerInterface
from gaco_framework.auth import AuthContext

class SalesSanitizerFactory:
    """
    Factory class for creating instances of SalesSanitizer.
    This follows the factory pattern to provide the correct implementation.
    """
    
    @staticmethod
    def create(
        db: firestore.Client = None,
        auth_context: AuthContext = None,
        matching_strategy: MatchingStrategy = MatchingStrategy.ALL_STRATEGIES
    ) -> SalesSanitizerInterface:
        """
        Create a new instance of SalesSanitizer.
        
        Args:
            db: Firestore client instance
            matching_strategy: Strategy for matching applications
            
        Returns:
            An implementation of the SalesSanitizerInterface
        """
        return SalesSanitizerImpl(db, auth_context, matching_strategy)