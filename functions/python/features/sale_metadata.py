from typing import Dict
from features.base import FeatureGenerator
from queries.producers_query_builder_v2 import ProducersQueryBuilderV2
from queries.stores_query_builder_v2 import StoresQueryBuilderV2
from models.sales import SalesSilver
from firebase_admin import firestore
from models.producer import Producer
from models.store import StoreV2

class SaleMetadataFeatures(FeatureGenerator[Dict, Dict]):
    """
    Generates and adds metadata features for sales data from related entities 
    like producers and stores.
    """
    
    def __init__( self, db: firestore.Client = None ):
        self.db = db or firestore.client()
        self.producers_query_builder = ProducersQueryBuilderV2(self.db)
        self.stores_query_builder = StoresQueryBuilderV2(self.db)

    def generate(
            self, 
            sale: Dict, 
            producer_id: str, 
            store_id: str, 
            commission: float,
            agreement_id: str
        ) -> SalesSilver:
        """
        Generates additional features for sale data using producer and store metadata.
        """
        producer_doc = self.producers_query_builder.for_id(producer_id).build().get().to_dict()
        producer_doc = Producer.model_validate(producer_doc)

        store_doc = self.stores_query_builder.for_id(store_id).build().get().to_dict()
        store_doc = StoreV2.model_validate(store_doc)

        sale.update({
            SalesSilver.PRODUCER_DISPLAY_NAME_FIELD: producer_doc.display_name,
            SalesSilver.PRODUCER_TAX_A2_FIELD: producer_doc.tax_a2,
            SalesSilver.PRODUCER_ID_FIELD: producer_id,
            SalesSilver.COMMISSION_FIELD: commission,
            SalesSilver.STORE_TAX_A2_FIELD: store_doc.tax_a2,
            SalesSilver.AGREEMENT_ID_FIELD: agreement_id
        })

        return SalesSilver.model_validate(sale)