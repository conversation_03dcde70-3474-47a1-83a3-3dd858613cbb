from abc import ABC, abstractmethod
from typing import Dict, Any, Generic, TypeVar

# Type variable for input data
InputT = TypeVar('InputT')
# Type variable for output data
OutputT = TypeVar('OutputT')

class FeatureGenerator(Generic[InputT, OutputT], ABC):
    """
    Base class for all feature generators.
    Defines the contract that all feature generators must follow.
    """
    
    @abstractmethod
    def generate(self, data: InputT, **kwargs) -> OutputT:
        """
        Generate features from input data.
        
        Args:
            data: The input data to generate features from
            **kwargs: Additional parameters needed for feature generation
            
        Returns:
            Data with generated features
        """
        pass


class BatchFeatureGenerator(Generic[InputT, OutputT], ABC):
    """
    Base class for feature generators that process data in batches.
    """
    
    @abstractmethod
    def generate_batch(self, data_batch: list[InputT], **kwargs) -> list[OutputT]:
        """
        Generate features for a batch of input data.
        
        Args:
            data_batch: List of input data to generate features from
            **kwargs: Additional parameters needed for feature generation
            
        Returns:
            List of data with generated features
        """
        pass


class CompositeFeatureGenerator(FeatureGenerator[InputT, OutputT]):
    """
    Combines multiple feature generators into a single pipeline.
    """
    
    def __init__(self, generators: list[FeatureGenerator]):
        self.generators = generators

    def generate(self, data: InputT, **kwargs) -> OutputT:
        result = data
        for generator in self.generators:
            result = generator.generate(result, **kwargs)
        return result