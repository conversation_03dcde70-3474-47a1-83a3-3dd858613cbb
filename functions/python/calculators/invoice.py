import polars as pl

class InvoiceCalculator:
    """Pure calculation logic that can be tested independently"""
    
    @staticmethod
    def calculate_vat_rates(df: pl.DataFrame, vat_rates_dict: dict) -> pl.DataFrame:
        """Calculate assigned VAT rates based on country matching"""
        return df.with_columns([
            pl.when(pl.col('producer_tax_a2') == pl.col('store_tax_a2'))
            .then(pl.col('store_tax_a2').map_elements(
                lambda x: vat_rates_dict.get(x, {}).get('default', 0)
                ))
            .otherwise(pl.lit(0))
            .alias('assigned_vat_rate')
        ])

    @staticmethod
    def calculate_vat_excluded_sales(df: pl.DataFrame) -> pl.DataFrame:
        """Calculate VAT-excluded sale amounts"""
        return df.with_columns([
            ((pl.col('subtotal') / (1 + pl.col('assigned_vat_rate')/100) - pl.col('subtotal')) * -1)
            .alias('vat_excluded_sale')
        ])

    @staticmethod
    def calculate_net_sales(df: pl.DataFrame) -> pl.DataFrame:
        """Calculate net sales after VAT exclusion"""
        return df.with_columns([
            (pl.col('subtotal') - pl.col('vat_excluded_sale'))
            .alias('net_sales')
        ])

    @staticmethod
    def calculate_commission_payouts(df: pl.DataFrame) -> pl.DataFrame:
        """Calculate commission-related payouts and VAT on service"""
        df = df.with_columns([
            (pl.col('net_sales') * pl.col('commission') / 100)
            .alias('store_net_payout')
        ])
        
        return df.with_columns([
            (pl.col('store_net_payout') * pl.col('assigned_vat_rate') / 100)
            .alias('vat_on_sales_service')
        ])

    @staticmethod
    def calculate_final_payouts(df: pl.DataFrame) -> pl.DataFrame:
        """Calculate final payouts for store and producer"""
        df = df.with_columns([
            (pl.col('store_net_payout') + pl.col('vat_on_sales_service'))
            .alias('store_total_gross_payout')
        ])
        
        return df.with_columns([
            (pl.col('subtotal') - pl.col('store_total_gross_payout'))
            .alias('producer_gross_payout')
        ])


class InvoiceCalculatorPipeline:
    """Pipeline that implements the invoice calculation logic in a step by step manner"""

    @staticmethod
    def compute(sales_df: pl.DataFrame, vat_rates_dict: dict) -> pl.DataFrame:
        df = InvoiceCalculator.calculate_vat_rates(sales_df, vat_rates_dict)
        df = InvoiceCalculator.calculate_vat_excluded_sales(df)
        df = InvoiceCalculator.calculate_net_sales(df)
        df = InvoiceCalculator.calculate_commission_payouts(df)
        df = InvoiceCalculator.calculate_final_payouts(df)
        return df 
