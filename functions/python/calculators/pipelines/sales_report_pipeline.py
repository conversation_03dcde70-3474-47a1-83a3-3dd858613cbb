# calculators/pipelines/sales_report_pipeline.py
from calculators.calculator_protocols import CalculatorPipeline
from calculators.pipelines.pipeline_runner import CalculationPipelineRunner
from calculators.steps.sales_report_steps import (
    CalculateStoreGrossPayoutStep,
    CalculateProducerGrossPayoutStep
)

def create_sales_report_pipeline() -> CalculatorPipeline:
    """Assembles the steps for the Sales Report calculation pipeline."""
    steps = [
        CalculateStoreGrossPayoutStep(),
        CalculateProducerGrossPayoutStep(),
    ]
    return CalculationPipelineRunner(steps)