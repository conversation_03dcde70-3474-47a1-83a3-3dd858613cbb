# calculators/pipelines/invoice_pipeline.py
from firebase_admin import firestore
from calculators.calculator_protocols import CalculatorPipeline
from calculators.pipelines.pipeline_runner import CalculationPipelineRunner
from data_source.auxiliary.vat_rates import VatRatesAuxiliaryDataSource
from calculators.steps.invoice_steps import (
    CalculateVatRatesStep,
    CalculateVatExcludedSalesStep,
    CalculateNetSalesStep,
    CalculateCommissionPayoutsStep,
    CalculateFinalPayoutsStep,
)

# TODO: Update the dependecies to be in proper config, dict()
# {'CalculateVatRatesStep': vat_provider}
def create_invoice_pipeline(vat_provider: VatRatesAuxiliaryDataSource) -> CalculatorPipeline:
    """Assembles the steps for the Invoice calculation pipeline."""
    # Instantiate dependencies needed by steps
    
    steps = [
        CalculateVatRatesStep(vat_provider), # Inject dependency
        CalculateVatExcludedSalesStep(),
        CalculateNetSalesStep(),
        CalculateCommissionPayoutsStep(),
        CalculateFinalPayoutsStep(),
    ]
    return Calcula<PERSON><PERSON><PERSON>eline<PERSON>unner(steps)