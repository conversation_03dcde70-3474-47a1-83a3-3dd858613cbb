# calculators/pipelines/pipeline_runner.py
import polars as pl
from typing import List, Dict, Any
from calculators.calculator_protocols import CalculationStep, CalculatorPipeline # Import protocols

class CalculationPipelineRunner: # Implements CalculatorPipeline
    def __init__(self, steps: List[CalculationStep]):
        self.steps = steps
        self._validate_steps() # Optional: Validate steps on init

    def _validate_steps(self):
        # Optional: Add checks here, e.g., ensure steps implement the protocol correctly
        pass

    def compute(self, df: pl.DataFrame, context: Dict[str, Any] = None) -> pl.DataFrame:
        if df.is_empty():
             print("PipelineRunner received empty DataFrame, skipping computation.")
             return df # Return the empty DF with its existing schema

        current_columns = set(df.columns)
        
        for i, step in enumerate(self.steps):
            step_name = step.__class__.__name__
            print(f"Running step {i+1}: {step_name}")

            # Dependency Check
            missing_cols = [col for col in step.required_columns if col not in current_columns]
            if missing_cols:
                raise ValueError(
                    f"Pipeline Error: Step {step_name} missing required columns: {missing_cols}. Available: {current_columns}"
                )
            
            # Execute Step
            try:
                df = step.execute(df, context)
            except Exception as e:
                 print(f"Error executing step {step_name}: {e}")
                 raise # Re-raise the exception

            # Validate output columns exist (optional but good practice)
            newly_added = []
            for out_col in step.output_columns:
                 if out_col not in df.columns:
                      print(f"Warning: Step {step_name} was expected to produce column '{out_col}' but it is missing.")
                 elif out_col not in current_columns:
                     newly_added.append(out_col)
            
            # Update known columns
            current_columns.update(newly_added) 
            print(f"Step {step_name} finished. Current columns: {current_columns}")
        
        return df