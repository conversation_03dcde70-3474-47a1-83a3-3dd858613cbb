import polars as pl
from typing import List

# Assume you have constants defined elsewhere, e.g., in models or a constants file
# Example:
class ReportColumns:
    SUBTOTAL = 'subtotal'
    COMMISSION = 'commission'
    STORE_GROSS_PAYOUT = 'store_gross_payout'
    PRODUCER_GROSS_PAYOUT = 'producer_gross_payout'


class SalesReportCalculator:
    """
    Pure calculation logic that can be tested independently.
    Includes dependency checks for calculation steps.
    """

    @staticmethod
    def _check_dependencies(df: pl.DataFrame, required_columns: List[str], calculation_name: str):
        """Checks if required columns exist in the DataFrame."""
        missing_cols = [col for col in required_columns if col not in df.columns]
        if missing_cols:
            raise ValueError(
                f"Missing required columns for '{calculation_name}' calculation: {', '.join(missing_cols)}"
            )

    @staticmethod
    def calculate_store_commission_payouts(df: pl.DataFrame) -> pl.DataFrame:
        """Calculates store_gross_payout based on subtotal and commission."""
        required = [ReportColumns.SUBTOTAL, ReportColumns.COMMISSION]
        calculation_name = "Store Commission Payouts"
        
        SalesReportCalculator._check_dependencies(df, required, calculation_name)
        
        return df.with_columns([
            (pl.col(ReportColumns.SUBTOTAL) * pl.col(ReportColumns.COMMISSION) / 100)
            .alias(ReportColumns.STORE_GROSS_PAYOUT)
        ])

    @staticmethod
    def calculate_producer_commission_payouts(df: pl.DataFrame) -> pl.DataFrame:
        """
        Calculates producer_gross_payout based on subtotal and store_gross_payout.
        Note: Original code used 'store_net_payout', assuming 'store_gross_payout' 
              from the previous step is the intended dependency. Adjust if needed.
        """
        # IMPORTANT: Assuming the dependency is the 'store_gross_payout' calculated above.
        # If 'store_net_payout' is truly needed, it implies another calculation step exists.
        required = [ReportColumns.SUBTOTAL, ReportColumns.STORE_GROSS_PAYOUT] 
        calculation_name = "Producer Commission Payouts"
        
        SalesReportCalculator._check_dependencies(df, required, calculation_name)
        
        return df.with_columns([
            # Using STORE_GROSS_PAYOUT based on the assumed dependency
            (pl.col(ReportColumns.SUBTOTAL) - pl.col(ReportColumns.STORE_GROSS_PAYOUT)) 
            .alias(ReportColumns.PRODUCER_GROSS_PAYOUT)
        ])


class SalesReportCalculatorPipeline:
    """Pipeline that implements the sales report calculation logic in a step by step manner"""

    @staticmethod
    def compute(sales_df: pl.DataFrame) -> pl.DataFrame:
        df = SalesReportCalculator.calculate_store_commission_payouts(sales_df)
        df = SalesReportCalculator.calculate_producer_commission_payouts(df)
        return df