# protocols.py (or a suitable shared location)
from typing import Protocol, List, Dict, Any
import polars as pl

class CalculationStep(Protocol):
    """Interface for a single step in a calculation pipeline."""
    
    @property
    def required_columns(self) -> List[str]:
        """Columns required by this step."""
        ...

    @property
    def output_columns(self) -> List[str]:
        """Columns produced by this step."""
        ...
        
    def execute(self, df: pl.DataFrame, context: Dict[str, Any] = None) -> pl.DataFrame:
        """Executes the calculation step."""
        ...

class CalculatorPipeline(Protocol):
    """Interface for running a sequence of calculations."""
    def compute(self, df: pl.DataFrame, context: Dict[str, Any] = None) -> pl.DataFrame:
        ...