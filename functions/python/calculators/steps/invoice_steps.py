# calculators/steps/invoice_steps.py
import polars as pl
from typing import List, Dict, Any
from calculators.calculator_protocols import CalculationStep
from columns.sales_columns import SalesColumns, SalesInvoiceColumns # Use your constants enum/class
from data_source.auxiliary.vat_rates import VatRatesAuxiliaryDataSource # Import the provider
from firebase_functions import logger


class CalculateVatRatesStep: # Implements CalculationStep
    # Inject VatRateProvider
    def __init__(self, vat_provider: VatRatesAuxiliaryDataSource): 
            self.vat_provider = vat_provider
            # Define output column using constant
            self._output_col = SalesInvoiceColumns.ASSIGNED_VAT_RATE.value

    @property
    def required_columns(self) -> List[str]:
        return [SalesColumns.PRODUCER_TAX_A2.value, SalesColumns.STORE_TAX_A2.value]

    @property
    def output_columns(self) -> List[str]:
        return [self._output_col]
        
    def execute(self, df: pl.DataFrame, context: Dict[str, Any] = None) -> pl.DataFrame:

        # Check if producer and store tax country codes are always the same
        mismatched_codes = df.filter(
            pl.col(SalesColumns.STORE_TAX_A2.value) != pl.col(SalesColumns.PRODUCER_TAX_A2.value)
        )

        if not mismatched_codes.is_empty():
            # Log details of the first few mismatches for easier debugging
            sample_mismatches = mismatched_codes.head(5) # Get a sample
            logger.error(
                f"Validation Error: Producer and Store Tax Country Codes (A2) do not match for all rows. Halting calculation. Sample mismatches:\n{sample_mismatches}"
            )
            raise ValueError(
                "Producer and Store Tax Country Codes (A2) must match for all sales rows in this calculation step."
            )

        # Get unique country codes needed from the current batch
        unique_store_tax_a2 = df[SalesColumns.STORE_TAX_A2.value].unique().drop_nulls().to_list()


        # Fetch VAT rates using the injected provider for this batch
        vat_rates_dict = self.vat_provider.get_rates(unique_store_tax_a2)

        # Perform calculation using the fetched rates
        # Ensure return_dtype is specified for map_elements for type stability
        return df.with_columns(
            pl.when(pl.col(SalesColumns.PRODUCER_TAX_A2.value) == pl.col(SalesColumns.STORE_TAX_A2.value))
            .then(
                pl.col(SalesColumns.STORE_TAX_A2.value).map_elements(
                    lambda code: vat_rates_dict.get(code, {}).get('default_rate', 0.0), # Use consistent key like 'default_rate'
                    return_dtype=pl.Float64 
                )
             )
            .otherwise(pl.lit(0.0, dtype=pl.Float64)) # Ensure literal has dtype
            .alias(self._output_col)
        )


class CalculateVatExcludedSalesStep: # Implements CalculationStep
    @property
    def required_columns(self) -> List[str]:
        return [
            SalesColumns.SUBTOTAL.value, 
            SalesInvoiceColumns.ASSIGNED_VAT_RATE.value
        ]

    @property
    def output_columns(self) -> List[str]:
        return [SalesInvoiceColumns.VAT_EXCLUDED_SALE.value] # Use constant

    def execute(self, df: pl.DataFrame, context: Dict[str, Any] = None) -> pl.DataFrame:
        # Calculate the multiplier safely (1 + rate/100)
        # Handle potential division by zero if rate is -100? Unlikely for VAT.
        vat_multiplier = 1 + pl.col(SalesInvoiceColumns.ASSIGNED_VAT_RATE.value) / 100
        
        return df.with_columns([
            # Calculate VAT amount: subtotal - (subtotal / (1 + rate/100))
            # Simplified to: subtotal * (1 - 1 / (1 + rate/100))
             (pl.col(SalesColumns.SUBTOTAL.value) * (1 - (1 / vat_multiplier)) )
             # Ensure result is rounded appropriately if needed (e.g., .round(2))
            .alias(SalesInvoiceColumns.VAT_EXCLUDED_SALE.value) 
        ])


class CalculateNetSalesStep: # Implements CalculationStep
    @property
    def required_columns(self) -> List[str]:
        return [SalesColumns.SUBTOTAL.value, SalesInvoiceColumns.VAT_EXCLUDED_SALE.value]

    @property
    def output_columns(self) -> List[str]:
        return [SalesInvoiceColumns.NET_SALES.value] # Use constant

    def execute(self, df: pl.DataFrame, context: Dict[str, Any] = None) -> pl.DataFrame:
        return df.with_columns([
            (pl.col(SalesColumns.SUBTOTAL.value) - pl.col(SalesInvoiceColumns.VAT_EXCLUDED_SALE.value))
            .alias(SalesInvoiceColumns.NET_SALES.value)
        ])


class CalculateCommissionPayoutsStep: # Implements CalculationStep
    @property
    def required_columns(self) -> List[str]:
        return [
            SalesInvoiceColumns.NET_SALES.value, 
            SalesColumns.COMMISSION.value, 
            SalesInvoiceColumns.ASSIGNED_VAT_RATE.value
        ]

    @property
    def output_columns(self) -> List[str]:
        # Use constants
        return [
            SalesInvoiceColumns.STORE_NET_PAYOUT.value, 
            SalesInvoiceColumns.VAT_ON_SALES_SERVICE.value
        ] 

    def execute(self, df: pl.DataFrame, context: Dict[str, Any] = None) -> pl.DataFrame:
        # Calculate store_net_payout first as it's needed for VAT on service
        df = df.with_columns(
            (pl.col(SalesInvoiceColumns.NET_SALES.value) * pl.col(SalesColumns.COMMISSION.value) / 100)
            .alias(SalesInvoiceColumns.STORE_NET_PAYOUT.value)
        )
        # Now calculate VAT on the service (commission amount)
        return df.with_columns(
            (pl.col(SalesInvoiceColumns.STORE_NET_PAYOUT.value) * pl.col(SalesInvoiceColumns.ASSIGNED_VAT_RATE.value) / 100)
            .alias(SalesInvoiceColumns.VAT_ON_SALES_SERVICE.value)
        )


class CalculateFinalPayoutsStep: # Implements CalculationStep
    @property
    def required_columns(self) -> List[str]:
        # Use constants
        return [
            SalesInvoiceColumns.STORE_NET_PAYOUT.value, 
            SalesInvoiceColumns.VAT_ON_SALES_SERVICE.value,
            SalesColumns.SUBTOTAL.value # Needed for producer payout
        ]

    @property
    def output_columns(self) -> List[str]:
        # Use constants
        return [
            SalesInvoiceColumns.STORE_TOTAL_GROSS_PAYOUT.value, 
            SalesInvoiceColumns.PRODUCER_GROSS_PAYOUT.value
        ] 

    def execute(self, df: pl.DataFrame, context: Dict[str, Any] = None) -> pl.DataFrame:
        # Calculate store total gross payout
        df = df.with_columns(
            (pl.col(SalesInvoiceColumns.STORE_NET_PAYOUT.value) + pl.col(SalesInvoiceColumns.VAT_ON_SALES_SERVICE.value))
            .alias(SalesInvoiceColumns.STORE_TOTAL_GROSS_PAYOUT.value)
        )
        # Calculate producer gross payout (Subtotal - Store Total Gross)
        return df.with_columns(
            (pl.col(SalesColumns.SUBTOTAL.value) - pl.col(SalesInvoiceColumns.STORE_TOTAL_GROSS_PAYOUT.value))
            .alias(SalesInvoiceColumns.PRODUCER_GROSS_PAYOUT.value)
        )