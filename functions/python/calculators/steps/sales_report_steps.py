# calculators/steps/sales_report_steps.py
import polars as pl
from typing import List, Dict, Any
from calculators.calculator_protocols import CalculationStep # Import the protocol
from columns.sales_columns import SalesColumns, SalesCommonComputedColumns # Use your constants enum/class

class CalculateStoreGrossPayoutStep: # Implements CalculationStep
    @property
    def required_columns(self) -> List[str]:
        return [SalesColumns.SUBTOTAL.value, SalesColumns.COMMISSION.value]
        
    @property
    def output_columns(self) -> List[str]:
        # Ensure you have this constant defined in SalesColumns
        return [SalesCommonComputedColumns.STORE_GROSS_PAYOUT.value] 

    def execute(self, df: pl.DataFrame, context: Dict[str, Any] = None) -> pl.DataFrame:
         return df.with_columns([
             (pl.col(SalesColumns.SUBTOTAL.value) * pl.col(SalesColumns.COMMISSION.value) / 100)
             .alias(SalesCommonComputedColumns.STORE_GROSS_PAYOUT.value)
         ])

class CalculateProducerGrossPayoutStep: # Implements CalculationStep
    @property
    def required_columns(self) -> List[str]:
        # Depends on the output of the previous step
        return [SalesColumns.SUBTOTAL.value, SalesCommonComputedColumns.STORE_GROSS_PAYOUT.value] 

    @property
    def output_columns(self) -> List[str]:
        # Ensure you have this constant defined in SalesColumns
        return [SalesCommonComputedColumns.PRODUCER_GROSS_PAYOUT.value]

    def execute(self, df: pl.DataFrame, context: Dict[str, Any] = None) -> pl.DataFrame:
         return df.with_columns([
             (pl.col(SalesColumns.SUBTOTAL.value) - pl.col(SalesCommonComputedColumns.STORE_GROSS_PAYOUT.value)) 
             .alias(SalesCommonComputedColumns.PRODUCER_GROSS_PAYOUT.value)
         ])