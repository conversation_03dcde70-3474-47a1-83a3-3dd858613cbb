from firebase_functions import storage_fn, logger, options
from firebase_admin import storage, firestore
from constants.gaco_path_patterns import shopify_products_pattern
from services.shopify_product_processor import ShopifyProductProcessor
import pathlib
import re


@storage_fn.on_object_finalized(
    region="europe-west3",
    bucket="gast-art-platform-87104.appspot.com",  # Optional: specify bucket name
    timeout_sec=300,
    memory=options.MemoryOption.GB_2
)
def on_storage_shopify_products_file_added(
    event: storage_fn.CloudEvent[storage_fn.StorageObjectData]
    ) -> None:
    """Triggered when a file is added to Cloud Storage"""
    file_data = event.data
    file_path = file_data.name

    logger.info(f"File added to Cloud Storage: {file_path}")
    file_path = pathlib.PurePath(event.data.name)

    if re.match(shopify_products_pattern, str(file_path)):
        logger.info(f"Shopify products file detected: {str(file_path)}")

        bucket = storage.bucket()

        raw_product_blob = bucket.blob(str(file_path))
        raw_product_json = raw_product_blob.download_as_string()
        
        db = firestore.client()
        shopify_product_processor = ShopifyProductProcessor(db)
        shopify_product_processor.process_product_file(
            file_content=raw_product_json,
            file_path=str(file_path)
        )
    else:
        logger.info(f"File does not match Shopify products pattern: {str(file_path)}")
