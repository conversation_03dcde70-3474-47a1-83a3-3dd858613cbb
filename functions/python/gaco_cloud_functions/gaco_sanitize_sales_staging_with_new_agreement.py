from gaco_framework.decorators import gaco_endpoint
from gaco_framework.auth import AuthRequirement
from gaco_framework.context import GacoContext
from gaco_framework.models import GacoResponse
from models.requests.agreements_requests import CreateAgreementRequest
from firebase_functions import options, tasks_fn, logger
from flows.force_active_partnership import ForceActivePartnershipFlow
from models.requests.agreements_requests import (
    SanitizeSalesStagingWithActiveAgreementRequest,
    SanitizeSalesStagingWithExistingAgreementRequest,
    SanitizeSaleStagingWithExistingAgreementRequest,
    SanitizeSaleWithAgreementTaskRequest,
    SanitizeSaleWithAgreementTask,
)
from models.agreement import Role
from gaco_framework.auth import AuthContext
from sanitizer.sales_sanitizer_impl import SalesSanitizerImpl
from firebase_admin import firestore
from constants.collections import sales_sanitization_tasks_collection
from gaco_framework.exceptions import NotFoundError
from sanitizer.sanitization_task_enqueuer import SanitizationTaskEnqueuer


@gaco_endpoint(
    require_auth=AuthRequirement.AUTHENTICATED,
    memory=options.MemoryOption.GB_1,
)
def sanitize_sales_staging_with_new_active_agreement(context: GacoContext, data: dict) -> dict:
    """
    Sanitize sales staging with a new agreement.
    This will not create a new producer.
    """
    auth_context = AuthContext(user_id=context.auth_context.user_id)
    data = SanitizeSalesStagingWithActiveAgreementRequest.model_validate(data)

    new_agreement_request = CreateAgreementRequest(
        store_id=data.store_id,
        producer_id=data.producer_id,
        title=data.title,
        effective_date=data.effective_date,
        expiration_date=data.expiration_date,
        commission=data.commission,
        document_url=data.document_url,
        created_by_role=Role.STORE.value
    )

    flow = ForceActivePartnershipFlow(
        db=context.db,
        auth_context=auth_context
    )
    agreement_id = flow.force_create_active_agreement(new_agreement_request)

    sales_sanitizer = SalesSanitizerImpl(
        db=context.db,
        auth_context=auth_context
    )

    sanitized_sales_staging = sales_sanitizer.force_sanitize_sales_staging_with_agreement(
        sale_staging_id=data.sale_id,
        agreement_id=agreement_id
    )

    return GacoResponse(
        success=True,
        code=200,
        message=f"Sales staging sanitized successfully",
        data=sanitized_sales_staging
    )


@gaco_endpoint(
    require_auth=AuthRequirement.AUTHENTICATED,
    memory=options.MemoryOption.GB_1,
)
def sanitize_sales_staging_with_existing_agreement(context: GacoContext, data: dict) -> dict:
    """
    Sanitize sales staging with an existing agreement.
    """

    sanitization_request = SanitizeSalesStagingWithExistingAgreementRequest.model_validate(data)

    sanitization_task_enqueuer = SanitizationTaskEnqueuer(db=context.db)
    for sale_id in sanitization_request.sale_ids:
        task = SanitizeSaleStagingWithExistingAgreementRequest(
            sale_id=sale_id,
            agreement_id=sanitization_request.agreement_id
        )
        sanitization_task_enqueuer.enqueue_sanitization_task(task)

    return GacoResponse(
        success=True,
        code=200,
        message=f"Sanitization tasks enqueued successfully",
    )


@tasks_fn.on_task_dispatched(
    retry_config=options.RetryConfig(
        min_backoff_seconds=10,
        max_backoff_seconds=60,
        max_attempts=2,
    ),
    rate_limits=options.RateLimits(
        max_concurrent_dispatches=5,
    ),
    region="europe-west3",
    memory=options.MemoryOption.GB_1
)
def sanitizeSalesStagingWithAgreement(request: tasks_fn.CallableRequest) -> dict:
    """
    Sanitize sales staging with a new agreement.
    """
    sanitization_request_id = request.data.get(
        SanitizeSaleWithAgreementTask.SANITIZATION_REQUEST_ID_FIELD
    )

    db = firestore.client()
    sanitization_task_request = db.collection(sales_sanitization_tasks_collection)\
        .document(sanitization_request_id).get()

    if not sanitization_task_request.exists:
        logger.error(f"Sanitization task request not found: {sanitization_request_id}")
        raise NotFoundError(f"Sanitization task request not found: {sanitization_request_id}")
    
    try:
        sanitization_task_request = SanitizeSaleWithAgreementTaskRequest\
            .model_validate(sanitization_task_request.to_dict())

        sales_sanitizer = SalesSanitizerImpl(db=db)

        sanitized_sales_staging = sales_sanitizer.force_sanitize_sales_staging_with_agreement(
            sale_staging_id=sanitization_task_request.data.sale_id,
            agreement_id=sanitization_task_request.data.agreement_id
        )

        logger.info(f"Sanitized sales staging: {sanitized_sales_staging}")

    except Exception as e:
        logger.error(f"Error in sanitizeSalesStagingWithAgreement: {str(e)}")
        raise e
    