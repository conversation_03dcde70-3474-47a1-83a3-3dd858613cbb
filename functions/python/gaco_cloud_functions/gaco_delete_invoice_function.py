# from firebase_functions import https_fn, logger, options
# from firebase_admin import firestore
# from models.response import ResponseData
# from models.requests.invoice_request import DeleteInvoiceRequest
# from services.invoice_manager import InvoiceManager


# @https_fn.on_call(
#     region="europe-west3",
#     timeout_sec=300,
#     memory=options.MemoryOption.GB_1
# )
# def delete_invoice(request: https_fn.CallableRequest) -> dict:
#     """HTTP Cloud Function to delete an invoice and revert all related changes."""

#     if not request.auth:
#         return ResponseData(
#             success=False,
#             message='User must be authenticated',
#             code=401
#         ).model_dump()

#     # check the request data
#     try:
#         invoice_doc_id = request.data.get('invoice_doc_id')
#         delete_invoice_request = DeleteInvoiceRequest(invoice_doc_id=invoice_doc_id)
#     except Exception as e:
#         logger.error(f"Error in delete_invoice function: {e}")
#         return ResponseData(
#             success=False,
#             message=f'Error in delete_invoice function: {str(e)}',
#             code=422
#         ).model_dump()

#     try:
#         # Get invoice document ID from request
#         logger.info(f"Request data: {request.data}")
        
#         invoice_doc_id = delete_invoice_request.invoice_doc_id
        
#         db = firestore.client()
        
#         # Use the InvoiceManager to handle the deletion logic
#         invoice_manager = InvoiceManager(db)
#         success, result_data, error_message = invoice_manager.delete_invoice(invoice_doc_id)
        
#         if not success:
#             return ResponseData(
#                 success=False,
#                 message=error_message,
#                 code=500 if "Error deleting" in error_message else 404
#             ).model_dump()
        
#         return ResponseData(
#             success=True,
#             message='Invoice successfully deleted',
#             data=result_data,
#             code=200
#         ).model_dump()

#     except Exception as e:
#         logger.error(f"Error in delete_invoice function: {e}")
#         return ResponseData(
#             success=False,
#             message=f'Error in delete_invoice function: {str(e)}',
#             code=500
#         ).model_dump() 