from firebase_admin import firestore
from firebase_functions import https_fn, logger, options
from pydantic import BaseModel, Field, ValidationError
from typing import Optional
import requests # For catching requests.exceptions.HTTPError
from models.requests.create_product_in_shopify import CreateShopifyProductRequest

from services.shopify_product_creator import ShopifyProductCreator
from models.response import ResponseData
from models.products import Product
from gaco_secrets.secret_manager import SecretManager
from constants.gaco_values import project_id # For SecretManager initialization


@https_fn.on_call(
  region="europe-west3",
  timeout_sec=120, 
  memory=options.MemoryOption.GB_4)
def create_product_in_shopify(request: https_fn.CallableRequest) -> dict:
    """
    HTTP Cloud Function to create a product in a specified Shopify store.
    Expects CreateShopifyProductRequest model in request.data.
    """
    if not request.auth:
        return ResponseData(
            success=False,
            message='User must be authenticated',
            code=401
        ).model_dump()
    
    try:
        db = firestore.client()
        secret_manager_instance = SecretManager(project_id=project_id)
        shopify_creator = ShopifyProductCreator(db=db, secret_manager=secret_manager_instance)
        
        req_data = CreateShopifyProductRequest.model_validate(request.data)
        
        shopify_product_gid = shopify_creator.add_product_to_shopify(
            store_id=req_data.store_id,
            product_to_add=req_data.product_data,
            api_version=req_data.shopify_api_version,
            default_location_id=req_data.shopify_location_gid
        )
        
        return ResponseData(
            success=True,
            message="Product successfully created in Shopify.",
            data={"shopify_product_gid": shopify_product_gid},
            code=201
        ).model_dump()
        
    except ValidationError as e:
        logger.error(f"Pydantic validation error in create_product_in_shopify: {e.errors()}")
        return ResponseData(success=False, message="Invalid request data.", errors=e.errors(), code=400).model_dump()
    except ValueError as e: 
        logger.error(f"Value error in create_product_in_shopify: {str(e)}") 
        return ResponseData(success=False, message=str(e), code=400).model_dump()
    except requests.exceptions.HTTPError as e: 
        logger.error(f"Shopify API HTTP error in create_product_in_shopify: {e.response.status_code} - {e.response.text if e.response else 'No response text'}")
        # Ensure status_code is an int for ResponseData
        status_code_val = e.response.status_code if e.response and isinstance(e.response.status_code, int) else 500
        error_reason = e.response.reason if e.response and e.response.reason else "Shopify API Error"
        return ResponseData(success=False, message=f"Shopify API request failed: {status_code_val} - {error_reason}", code=status_code_val).model_dump()
    except Exception as e:
      logger.error(f"Error in create_product_in_shopify function: {e}", exc_info=True)
      return ResponseData(
        success=False,
        message=f"An unexpected error occurred: {e}",
        code=500
      ).model_dump()
