from firebase_functions import options
from firebase_admin import firestore

from gaco_framework.auth import AuthRequirement
from gaco_framework.decorators import gaco_endpoint
from gaco_framework.context import GacoContext
from gaco_framework.models import GacoResponse
from models.requests.partnership_requests import DeletePartnershipRequest
from services.partner_manager import PartnershipManager


@gaco_endpoint(
    require_auth=AuthRequirement.AUTHENTICATED,
    memory=options.MemoryOption.MB_512,
)
def delete_partnership(
    context: GacoContext,
    data: dict,
) -> str:
    """Delete a partnership"""
    delete_request = DeletePartnershipRequest.model_validate(data)
    partnership_manager = PartnershipManager(context.db, context.auth_context)
    deleted_id = partnership_manager.delete_partnership_and_agreement(
        delete_request.partnership_id
    )
    return GacoResponse(
        success=True,
        code=200,
        message=f"Partnership deleted successfully",
        data={"partnership_id": deleted_id}
    )
