from gaco_framework import gaco_firestore_trigger, GacoContext
from gaco_framework.trigger_types import DocumentTriggerType
from constants.collections import user_root_account_collection
from services.user_root_account_manager import UserRootAccountManager
from firebase_functions import logger


@gaco_firestore_trigger(
    document_path=f"{user_root_account_collection}/{{user_uuid}}"
)
def on_user_root_account_update(context: GacoContext, event):
    user_root_account_manager = context.get_manager(UserRootAccountManager)

    try:
        user_uuid = event.data.after.id
        user_root_account_manager._update_user_custom_claims(user_uuid)
        logger.info(f"User root account updated for user {user_uuid}")
    except Exception as e:
        logger.error(f"Error updating user root account: {e}")
        raise e
