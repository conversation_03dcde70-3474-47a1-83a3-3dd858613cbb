from firebase_functions import tasks_fn, logger, options
from firebase_admin import firestore
from gaco_secrets.secret_manager import SecretManager
from services.shopify_order_getter import ShopifyOrderGetter
from constants.gaco_values import project_id
from firebase_functions.options import RetryConfig, RateLimits


@tasks_fn.on_task_dispatched(
    retry_config=RetryConfig(
        min_backoff_seconds=60,
        max_backoff_seconds=60,
        max_attempts=2,
    ),
    rate_limits=RateLimits(
        max_concurrent_dispatches=5,
    ),
    region="europe-west3",
    memory=options.MemoryOption.GB_4
)
def continueGettingOrders(req: tasks_fn.CallableRequest) -> None:
    """Background task to continue getting Shopify orders.
    Due to naming limitation for the queue and task function, we use this name.
    """
    order_getter_id = req.data.get('order_getter_id')
    if not order_getter_id:
        logger.error("Missing order_getter_id in task payload")
        return
    
    logger.info(f"Continuing fetch operation {order_getter_id}")
    
    # Initialize services
    db = firestore.client()
    secret_manager = SecretManager(project_id=project_id)
    shopify_getter = ShopifyOrderGetter(db, secret_manager)
    
    # Continue getting
    result = shopify_getter.continue_getting_orders(order_getter_id)
    
    logger.info(f"Background process complete: {result}")
