from services.producer_manager import Producer<PERSON>anager
from firebase_functions import https_fn, logger, options
from models.response import ResponseData
from models.requests.producer_requests import CreateProducerRequest, DeleteProducerRequest
from firebase_admin import firestore

from gaco_framework.auth import AuthRequirement
from gaco_framework.context import GacoContext
from gaco_framework.decorators import gaco_endpoint
from gaco_framework.models import GacoResponse


def _parse_create_producer_request(request: https_fn.CallableRequest) -> CreateProducerRequest:
  return CreateProducerRequest.from_request_data(request.data)


@gaco_endpoint(
    require_auth=AuthRequirement.AUTHENTICATED,
    region="europe-west3",
    timeout_sec=60,
    memory=options.MemoryOption.GB_1,
)
def create_producer(context: GacoContext, data: dict) -> str:
    """
    Creates a new producer.

    Authentication:
        - Authenticated user required.
    """
    producer_request = CreateProducerRequest.model_validate(data)
    producer_manager = ProducerManager(
        db=context.db, auth_context=context.auth_context
    )
    producer_id = producer_manager.create_producer(producer_request)
    return GacoResponse(
        success=True,
        code=200,
        message=f"Producer created successfully with id: {producer_id}"
    )


@gaco_endpoint(
    require_auth=AuthRequirement.AUTHENTICATED,
    region="europe-west3",
    timeout_sec=60,
    memory=options.MemoryOption.GB_1,
)
def delete_producer(context: GacoContext, data: dict) -> None:
    """
    Deletes a producer.

    Authentication:
        - Authenticated user required.
        - User must be the owner of the producer to delete.
    """
    delete_request = DeleteProducerRequest.model_validate(data)
    producer_manager = ProducerManager(
        db=context.db, auth_context=context.auth_context
    )
    producer_id = producer_manager.delete_producer(delete_request)
    return GacoResponse(
        success=True,
        code=200,
        message=f"Producer deleted successfully with id: {producer_id}"
    )
