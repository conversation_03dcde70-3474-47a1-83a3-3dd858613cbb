from firebase_functions import https_fn, logger, options
from firebase_admin import firestore
from models.response import ResponseData
from models.requests.shopify_requests import GetShopifyOrdersRequest
from services.shopify_order_getter import ShopifyOrderGetter
from gaco_secrets.secret_manager import SecretManager
from constants.gaco_values import project_id


@https_fn.on_call(
    region="europe-west3",
    timeout_sec=300,  # Reduced timeout since we're only getting one page
    memory=options.MemoryOption.GB_2
)
def get_shopify_orders(request: https_fn.CallableRequest) -> dict:
    """HTTP Cloud Function to fetch orders from Shopify."""

    logger.info(f"Request data: {request.data}")
    
    if not request.auth:
        return ResponseData(
            success=False,
            message='User must be authenticated',
            code=401
        ).model_dump()
    
    try:
        shopify_request = GetShopifyOrdersRequest.model_validate(request.data)

        logger.info(f"Shopify request: {shopify_request.store_id}")
        
        # Initialize services
        db = firestore.client()
        secret_manager = SecretManager(project_id=project_id)
        shopify_service = ShopifyOrderGetter(db, secret_manager)
        
        # Fetch initial page and start background process
        result = shopify_service.fetch_initial_orders(
            store_id=shopify_request.store_id,
            start_date=shopify_request.start_date,
            end_date=shopify_request.end_date
        )

        logger.info(f"Result: {result}")
        
        return ResponseData(
            success=True,
            message='Started getting orders from Shopify',
            code=200,
            data={
                **result,
                'note': 'Additional pages are being processed in the background' if result['hasMorePages'] else 'All data retrieved'
            }
        ).model_dump()
        
    except ValueError as e:
        msg = f"Validation error in get_shopify_orders: {str(e)}"
        logger.error(msg)
        return ResponseData(
            success=False,
            message=msg,
            code=400
        ).model_dump()
    except Exception as e:
        logger.error(f"Error in get_shopify_orders: {str(e)}")
        return ResponseData(
            success=False,
            message=f"An unexpected error occurred: {e}",
            code=500
        ).model_dump()
