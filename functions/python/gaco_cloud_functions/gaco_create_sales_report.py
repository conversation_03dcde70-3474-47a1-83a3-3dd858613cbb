from gaco_framework import (
    gaco_endpoint,
    GacoEndpoint,
    GacoContext,
    GacoResponse,
    AuthRequirement
)
from models.requests.sales_report_request import CreateSalesReportRequest
from firebase_functions import options
from services.sales_report_manager import SalesReportManager


@gaco_endpoint(
    require_auth=AuthRequirement.AUTHENTICATED,
    region="europe-west3",
    timeout_sec=600,
    memory=options.MemoryOption.GB_4
)
@GacoEndpoint(CreateSalesReportRequest)
def create_sales_reports(context: GacoContext, request: CreateSalesReportRequest) -> GacoResponse:
    """HTTP Cloud Function to create sales reports."""

    sales_report_manager = context.get_manager(SalesReportManager)
    
    sales_reports = sales_report_manager.create_sales_reports(request)
    
    return GacoResponse(
        success=True,
        message=f'Successfully created {len(sales_reports)} sales reports',
        data={
            "sales_reports": sales_reports
          },
        code=200
    )
