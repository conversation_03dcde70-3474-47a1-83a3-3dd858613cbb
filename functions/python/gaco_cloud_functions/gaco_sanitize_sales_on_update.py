from firebase_functions.firestore_fn import (
    on_document_updated, 
    on_document_created,
    Event, 
    Change, 
    DocumentSnapshot
)
from sanitizer.sales_sanitizer_factory import SalesSanitizerFactory
from firebase_admin import firestore
from firebase_functions import logger, options
from models.sales import SalesStaging


@on_document_updated(
    document="sales-staging/{document_id}",
    region="europe-west3",
    timeout_sec=300,
    memory=options.MemoryOption.GB_1
)
def on_sales_staging_updated(event: Event[Change[DocumentSnapshot | None]]):
    staging_sale = event.data.after.to_dict() if event.data.after else None

    logger.info(f"Sales staging updated: {staging_sale}")

    db = firestore.client()
    sales_staging_sale = SalesStaging(**staging_sale)

    sales_sanitizer = SalesSanitizerFactory.create(db)


    active_partnership = sales_sanitizer.fetch_active_partnerships(
        sales_staging_sale.store_id
    )
    sanitized_sale = sales_sanitizer.sanitize_sales_from_sale_object(
        sale_input=sales_staging_sale,
        active_partnership=active_partnership
    )

    logger.info(f"Sales sanitized: {sanitized_sale}")
    
    if event.data:
        sales_sanitizer.process_sanitized_sale(
            sanitized_sale, 
            event.data.after.id
        )


@on_document_created(
    document="sales-staging/{document_id}",
    region="europe-west3",
    timeout_sec=300,
    memory=options.MemoryOption.GB_1
)
def on_sales_staging_created(event: Event[DocumentSnapshot | None]):
    staging_sale = event.data.to_dict() if event.data else None

    logger.info(f"Sales staging updated: {staging_sale}")

    db = firestore.client()
    sales_staging_sale = SalesStaging(**staging_sale)

    sales_sanitizer = SalesSanitizerFactory.create(db)
    active_partnership = sales_sanitizer.fetch_active_partnerships(
        sales_staging_sale.store_id
    )
    sanitized_sale = sales_sanitizer.sanitize_sales_from_sale_object(
        sale_input=sales_staging_sale,
        active_partnership=active_partnership
    )

    logger.info(f"Sales sanitized: {sanitized_sale}")
    
    if event.data:
        sales_sanitizer.process_sanitized_sale(
            sanitized_sale, 
            event.data.id
       )
 