from firebase_functions import https_fn, logger, options
from firebase_admin import firestore
from gaco_secrets.secret_manager import SecretManager
from services.secret_service import SecretService
from request_verifier.firebase_auth import verify_firebase_auth
from models.response import Response, ResponseData
from constants.gaco_values import project_id

@https_fn.on_request(
    timeout_sec=300,
    region="europe-west3",
    cors=options.CorsOptions(
        cors_origins=[r"*"],
        cors_methods=["options", "delete", "post"],
    )
)
def delete_api_key(req: https_fn.Request) -> https_fn.Response:
    """HTTP Cloud Function to delete API keys securely."""

    is_authenticated, error_message = verify_firebase_auth(req)
    if not is_authenticated:
        return Response(
            code=401,
            data=ResponseData(
                success=False,
                message=error_message,
                code=401
            )
        ).model_dump()

    try:
        # Get store_id from query parameters
        request_data = req.get_json()
        request_data = request_data.get('data')
        store_id = request_data.get('store_id')
        
        if not store_id:
            return Response(
                code=400,
                data=ResponseData(
                    success=False,
                    message='Missing required parameter: store_id',
                    code=400
                )
            ).model_dump()

        db = firestore.client()
        secret_manager = SecretManager(project_id=project_id)
        secret_service = SecretService(db, secret_manager)

        secret_data = db.collection('customer_api_keys').document(store_id).get()
        secret_data = secret_data.to_dict()
        secret_name = secret_data.get('secret_name')

        # Delete the secret
        secret_service.delete_secret(store_id, secret_name)

        return Response(
            code=200,
            data=ResponseData(
                success=True,
                message=f'Secret deleted for store: {store_id}',
                code=200
            )
        ).model_dump()

    except Exception as e:
        logger.error(f"Error deleting API key: {str(e)}")
        return Response(
            code=500,
            data=ResponseData(
                success=False,
                message='Internal server error with error: ' + str(e),
                code=500
            )
        ).model_dump()