from services.email_manager import EmailManager
from models.requests.email_requests import (
   SendEmailRequest, 
   SendEmailTaskRequest, 
   email_requests_collection, 
   SendEmailTask,
   SendSalesReportEmailRequest,
   SendInviteEmailRequest
)
from firebase_functions import https_fn, options, tasks_fn, logger
from models.response import ResponseData
from firebase_admin import firestore, functions
from datetime import datetime, timezone
from constants.gaco_values import email_task_queue


# @https_fn.on_call(
#     region="europe-west3",
#     timeout_sec=60,
#     memory=options.MemoryOption.GB_1
# )
# def enqueue_send_email(request: https_fn.CallableRequest) -> ResponseData:

#     if not request.auth:
#         return ResponseData(
#             success=False,
#             message='User must be authenticated',
#             code=401
#         ).model_dump()

#     # TODO: use token to validate if user can send email
#     token = request.auth.token

#     try:
#       db = firestore.client()

#       email_request = SendEmailRequest.model_validate(request.data)
#       email_manager = EmailManager(db)
#       task_payload = email_manager.enqueue_send_email(email_request)
#       logger.info(f"Email sent task payload: {task_payload}")

#       return ResponseData(
#         success=True,
#         message='Email queued successfully',
#         code=200,
#         data=task_payload
#       ).model_dump()

#     except Exception as e:
#         return ResponseData(
#             success=False,
#             message=f'Error in trigger_send_email function: {str(e)}',
#             code=500
#         ).model_dump()


@https_fn.on_call(
    region="europe-west3",
    timeout_sec=60,
    memory=options.MemoryOption.GB_1
)
def enqueue_send_sales_report_email(request: https_fn.CallableRequest) -> ResponseData:

    if not request.auth:
        return ResponseData(
            success=False,
            message='User must be authenticated',
            code=401
        ).model_dump()

    try:
        db = firestore.client()

        send_sales_report_email_request = SendSalesReportEmailRequest\
          .model_validate(request.data)

        email_manager = EmailManager(db)
        task_payload = email_manager.enqueue_send_sales_report_email(
            send_sales_report_email_request
        )
        logger.info(f"Email sent task payload: {task_payload}")

        return ResponseData(
            success=True,
            message='Email queued successfully',
            code=200,
            data=task_payload
        ).model_dump()
    except ValueError as e:
        return ResponseData(
            success=False,
            message=f'Error in enqueue_send_sales_report_email function: {str(e)}',
            code=400
        ).model_dump()
    except Exception as e:
        return ResponseData(
            success=False,
            message=f'Error in enqueue_send_sales_report_email function: {str(e)}',
            code=500
        ).model_dump()
    

@https_fn.on_call(
    region="europe-west3",
    timeout_sec=60,
    memory=options.MemoryOption.GB_1
)
def enqueue_send_invite_email(request: https_fn.CallableRequest) -> ResponseData:

    if not request.auth:
        return ResponseData(
            success=False,
            message='User must be authenticated',
            code=401
        ).model_dump()

    try:
        db = firestore.client()
        send_invite_email_request = SendInviteEmailRequest.model_validate(request.data)
        email_manager = EmailManager(db)
        task_payload = email_manager.enqueue_send_invite_email(send_invite_email_request)
        logger.info(f"Email sent task payload: {task_payload}")

        return ResponseData(
            success=True,
            message='Email queued successfully',
            code=200,
            data=task_payload
        ).model_dump()

    except ValueError as e:
        return ResponseData(
            success=False,
            message=f'Error in enqueue_send_invite_email function: {str(e)}',
            code=400
        ).model_dump()
    except Exception as e:
        return ResponseData(
            success=False,
            message=f'Error in enqueue_send_invite_email function: {str(e)}',
            code=500
        ).model_dump()



@tasks_fn.on_task_dispatched(
    retry_config=options.RetryConfig(
        min_backoff_seconds=10,
        max_backoff_seconds=60,
        max_attempts=2,
    ),
    rate_limits=options.RateLimits(
        max_concurrent_dispatches=5,
    ),
    region="europe-west3",
    memory=options.MemoryOption.GB_1
)
def sendEmail(request: tasks_fn.CallableRequest) -> None:
    email_request_id = request.data.get(
        SendEmailTask.EMAIL_REQUEST_ID_FIELD
    )

    logger.info(f"request data: {request.data}")

    db = firestore.client()
    email_task_request = db.collection(email_requests_collection)\
      .document(email_request_id).get()

    if not email_task_request.exists:
        logger.error(f"Email request not found: {email_request_id}")
        return
    
    try:
      logger.info(f"Email task request: {email_request_id}")

      email_task_request = SendEmailTaskRequest.model_validate(
          email_task_request.to_dict()
      )

      email_manager = EmailManager(db)
      task_id = email_manager.trigger_send_email(email_task_request)

      logger.info(f"Email sent task id: {task_id}")
    except Exception as e:
      logger.error(f"Error in sendEmail function: {str(e)}")
      raise e
    
    
