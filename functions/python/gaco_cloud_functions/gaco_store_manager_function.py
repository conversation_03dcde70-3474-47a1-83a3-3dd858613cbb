from gaco_framework import (
    gaco_endpoint,
    GacoEndpoint,
    GacoContext,
    GacoResponse,
    AuthRequirement
)
from services.store_manager import StoreManager
from services.shopify_order_getter_handler import ShopifyOrderGetter<PERSON>andler
from firebase_functions import https_fn, logger, options
from models.requests.store_requests import CreateStoreRequest, DeleteStoreRequest
from models.requests.shopify_requests import GetShopifyOrdersWithDynamicQueryRequest


@gaco_endpoint(
  require_auth=AuthRequirement.AUTHENTICATED,
  timeout_sec=60,
  memory=options.MemoryOption.GB_1
)
@GacoEndpoint(CreateStoreRequest)
def create_store(context: GacoContext, request: CreateStoreRequest) -> GacoResponse:
    """HTTP Cloud Function to create a store."""
    store_manager = context.get_manager(StoreManager)
    store_id = store_manager.create_store(request)

    # After creating the store, immediately fetch its initial orders from Shopify
    try:
        order_handler = context.get_manager(ShopifyOrderGetterHandler)
        order_handler.fetch_initial_orders(
            request=GetShopifyOrdersWithDynamicQueryRequest(store_id=store_id)
        )
    except Exception as e:
        # If fetching orders fails, we don't fail the whole operation,
        # but we log a critical error. The store is already created.
        logger.error(
            f"Store '{store_id}' was created, but failed to fetch initial Shopify orders. "
            f"Manual intervention may be required. Error: {e}"
        )
        # We can still return success but with a warning message.
        return GacoResponse(
            success=False,
            message=(
                f"Store created successfully with id: {store_id}. "
                "However, there was an issue fetching initial orders from Shopify. Please check the logs."
            ),
            data={"store_id": store_id},
            code=400
        )

    return GacoResponse(
        success=True,
        message=f"Store created successfully with id: {store_id}",
        data={"store_id": store_id},
        code=201
    )


@gaco_endpoint(
  require_auth=AuthRequirement.AUTHENTICATED,
  timeout_sec=300,
  memory=options.MemoryOption.GB_4
)
@GacoEndpoint(DeleteStoreRequest)
def delete_store(context: GacoContext, request: DeleteStoreRequest) -> GacoResponse:
    """HTTP Cloud Function to delete a store."""
    store_manager = context.get_manager(StoreManager)
    deleted_data = store_manager.delete_store(request)
    return GacoResponse(
        success=True,
        message='Store deleted successfully',
        data=deleted_data,
        code=200
    )
