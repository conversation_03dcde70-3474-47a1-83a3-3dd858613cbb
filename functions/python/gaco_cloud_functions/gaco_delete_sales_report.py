from gaco_framework import (
    gaco_endpoint,
    GacoEndpoint,
    GacoContext,
    GacoResponse,
    AuthRequirement
)
from models.requests.sales_report_request import DeleteSalesReportRequest
from firebase_functions import options
from services.sales_report_manager import SalesReportManager


@gaco_endpoint(
    require_auth=AuthRequirement.AUTHENTICATED,
    region="europe-west3",
    timeout_sec=600,
    memory=options.MemoryOption.GB_4
)
@GacoEndpoint(DeleteSalesReportRequest)
def delete_sales_report(context: GacoContext, request: DeleteSalesReportRequest) -> GacoResponse:
    """HTTP Cloud Function to delete sales reports."""

    sales_report_manager = context.get_manager(SalesReportManager)
    
    success, result_data, message = sales_report_manager\
        .delete_sales_report(request.sales_report_id)
    
    return GacoResponse(
        success=success,
        message=message,
        data={"result_data": result_data},
        code=200
    )
