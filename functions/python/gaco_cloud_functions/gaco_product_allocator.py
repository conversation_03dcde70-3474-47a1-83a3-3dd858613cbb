from firebase_admin import firestore
from firebase_functions import https_fn, options, logger
from pydantic import ValidationError

from gaco_framework import (
    gaco_endpoint,
    GacoEndpoint,
    GacoContext,
    GacoResponse,
    AuthRequirement,
)
from services.product_allocator import ProductAllocationManager
from services.product_manager import ProductManager # ProductAllocator needs ProductManager
from models.allocations import ProductAllocation, AllocationStatus # ProductAllocation is the request for create
from models.requests.allocation_requests import (
    GetAllocationRequest,
    UpdateAllocationStatusRequest,
    ListAllocationsByProducerRequest,
    ListAllocationsByStoreRequest
)
from models.response import ResponseData


@gaco_endpoint(
    require_auth=AuthRequirement.AUTHENTICATED,
)
@GacoEndpoint(ProductAllocation)
def create_allocation(context: GacoContext, request: ProductAllocation) -> GacoResponse:
    """
    Creates a new product allocation.
    Expects ProductAllocation model data in request.data.
    Auth: requires verified email.
    """
    manager = context.get_manager(ProductAllocationManager)
    created_allocation = manager.create_allocation(request)
    return GacoResponse(
        success=True,
        message="Allocation created successfully.",
        data=created_allocation.model_dump(),
        code=201
    )

@gaco_endpoint(
    require_auth=AuthRequirement.AUTHENTICATED,
)
@GacoEndpoint(UpdateAllocationStatusRequest)
def update_allocation_status(context: GacoContext, request: UpdateAllocationStatusRequest) -> GacoResponse:
    """
    Updates the status of a product allocation.
    Expects UpdateAllocationStatusRequest model data in request.data.
    Auth: requires verified email.
    """
    manager = context.get_manager(ProductAllocationManager)
    
    updated_allocation = manager.update_allocation_status(
        allocation_id=request.allocation_id,
        new_status=request.new_status,
        details=request.details,
        updated_by_user_id=context.auth_context.user_id
    )
            
    return GacoResponse(
        success=True, 
        message="Allocation status updated.", 
        data=updated_allocation.model_dump(), 
        code=200
    )
