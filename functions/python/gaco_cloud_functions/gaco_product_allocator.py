from firebase_admin import firestore
from firebase_functions import https_fn, options, logger
from pydantic import ValidationError

from gaco_framework import (
    gaco_endpoint,
    GacoEndpoint,
    GacoContext,
    GacoResponse,
    AuthRequirement,
)
from services.product_allocator import ProductAllocationManager
from services.product_manager import ProductManager # ProductAllocator needs ProductManager
from models.allocations import ProductAllocation, AllocationStatus # ProductAllocation is the request for create
from models.requests.allocation_requests import (
    GetAllocationRequest,
    UpdateAllocationStatusRequest,
    ListAllocationsByProducerRequest,
    ListAllocationsByStoreRequest
)
from models.response import ResponseData


@gaco_endpoint(
    require_auth=AuthRequirement.AUTHENTICATED,
)
@GacoEndpoint(ProductAllocation)
def create_allocation(context: GacoContext, request: ProductAllocation) -> GacoResponse:
    """
    Creates a new product allocation.
    Expects ProductAllocation model data in request.data.
    Auth: requires verified email.
    """
    manager = context.get_manager(ProductAllocationManager)
    created_allocation = manager.create_allocation(request)
    return GacoResponse(
        success=True,
        message="Allocation created successfully.",
        data=created_allocation.model_dump(),
        code=201
    )

# @https_fn.on_call(
#   region="europe-west3",
#   timeout_sec=60,
#   memory=options.MemoryOption.GB_1
# )
# def get_allocation(request: https_fn.CallableRequest) -> dict:
#     """
#     Retrieves a specific product allocation.
#     Expects GetAllocationRequest model data in request.data.
#     """
#     if not request.auth:
#         return ResponseData(success=False, message='User must be authenticated', code=401).model_dump()

#     try:
#         db = firestore.client()
#         product_manager = ProductManager(db=db)
#         product_allocator = ProductAllocator(db=db, product_manager=product_manager)

#         req_data = GetAllocationRequest.model_validate(request.data)
#         allocation = product_allocator.get_allocation(req_data.allocation_id)

#         if not allocation:
#             return ResponseData(success=False, message="Allocation not found.", code=404).model_dump()
        
#         return ResponseData(success=True, data=allocation.model_dump(), code=200).model_dump()
#     except ValidationError as e:
#         logger.error(f"Pydantic validation error in get_allocation: {e.errors()}")
#         return ResponseData(success=False, message="Invalid request data.", errors=e.errors(), code=400).model_dump()
#     except Exception as e:
#         logger.error(f"Unexpected error in get_allocation: {str(e)}")
#         return ResponseData(success=False, message="An unexpected error occurred.", code=500).model_dump()

@gaco_endpoint(
    require_auth=AuthRequirement.AUTHENTICATED,
)
@GacoEndpoint(UpdateAllocationStatusRequest)
def update_allocation_status(context: GacoContext, request: UpdateAllocationStatusRequest) -> GacoResponse:
    """
    Updates the status of a product allocation.
    Expects UpdateAllocationStatusRequest model data in request.data.
    Auth: requires verified email.
    """
    manager = context.get_manager(ProductAllocationManager)
    
    updated_allocation = manager.update_allocation_status(
        allocation_id=request.allocation_id,
        new_status=request.new_status,
        details=request.details,
        updated_by_user_id=context.auth_context.user_id
    )
            
    return GacoResponse(
        success=True, 
        message="Allocation status updated.", 
        data=updated_allocation.model_dump(), 
        code=200
    )

# @https_fn.on_call(
#   region="europe-west3",
#   timeout_sec=60,
#   memory=options.MemoryOption.GB_1
# )
# def list_allocations_by_producer(request: https_fn.CallableRequest) -> dict:
#     """
#     Lists allocations for a given producer, optionally filtered by status.
#     Expects ListAllocationsByProducerRequest model data in request.data.
#     """
#     if not request.auth:
#         return ResponseData(success=False, message='User must be authenticated', code=401).model_dump()

#     try:
#         db = firestore.client()
#         product_manager = ProductManager(db=db)
#         product_allocator = ProductAllocator(db=db, product_manager=product_manager)

#         req_data = ListAllocationsByProducerRequest.model_validate(request.data)
#         allocations = product_allocator.list_allocations_by_producer(
#             producer_id=req_data.producer_id,
#             status=req_data.status
#         )
        
#         return ResponseData(success=True, data=[alloc.model_dump() for alloc in allocations], code=200).model_dump()
#     except ValidationError as e:
#         logger.error(f"Pydantic validation error in list_allocations_by_producer: {e.errors()}")
#         return ResponseData(success=False, message="Invalid request data.", errors=e.errors(), code=400).model_dump()
#     except Exception as e:
#         logger.error(f"Unexpected error in list_allocations_by_producer: {str(e)}")
#         return ResponseData(success=False, message="An unexpected error occurred.", code=500).model_dump()

# @https_fn.on_call(
#   region="europe-west3",
#   timeout_sec=60,
#   memory=options.MemoryOption.GB_1
# )
# def list_allocations_by_store(request: https_fn.CallableRequest) -> dict:
#     """
#     Lists allocations for a given store, optionally filtered by status.
#     Expects ListAllocationsByStoreRequest model data in request.data.
#     (Assumes ProductAllocator service will have a 'list_allocations_by_store' method)
#     """
#     if not request.auth:
#         return ResponseData(success=False, message='User must be authenticated', code=401).model_dump()

#     try:
#         db = firestore.client()
#         product_manager = ProductManager(db=db)
#         product_allocator = ProductAllocator(db=db, product_manager=product_manager)

#         req_data = ListAllocationsByStoreRequest.model_validate(request.data)
        
#         # NOTE: This assumes you will add/have a 'list_allocations_by_store' method
#         # in your ProductAllocator service. If not, this function needs to be adapted
#         # or the service method created.
#         if not hasattr(product_allocator, 'list_allocations_by_store'):
#             logger.error("Service method 'list_allocations_by_store' not implemented in ProductAllocator.")
#             return ResponseData(success=False, message="Service functionality not available.", code=501).model_dump()

#         allocations = product_allocator.list_allocations_by_store(
#             store_id=req_data.store_id,
#             status=req_data.status
#         )
        
#         return ResponseData(success=True, data=[alloc.model_dump() for alloc in allocations], code=200).model_dump()
#     except ValidationError as e:
#         logger.error(f"Pydantic validation error in list_allocations_by_store: {e.errors()}")
#         return ResponseData(success=False, message="Invalid request data.", errors=e.errors(), code=400).model_dump()
#     except Exception as e:
#         logger.error(f"Unexpected error in list_allocations_by_store: {str(e)}")
#         return ResponseData(success=False, message="An unexpected error occurred.", code=500).model_dump()
