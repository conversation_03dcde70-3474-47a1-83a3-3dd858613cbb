from gaco_secrets.secret_manager import Secret<PERSON>anager
from services.secret_service import SecretService
from firebase_functions import https_fn, logger, options
from firebase_admin import firestore
from constants.gaco_values import project_id
from models.response import ResponseData
from constants.gaco_secret_constant import SecretVariants
from models.requests.secret_requests import StoreSecretRequest


@https_fn.on_call(
    region="europe-west3",
    timeout_sec=300,
    memory=options.MemoryOption.GB_1
)
def store_api_key(request: https_fn.CallableRequest) -> dict:
    """HTTP Cloud Function to store API keys securely."""

    if not request.auth:
        return ResponseData(
                success=False,
                message='User must be authenticated',
                code=401
            ).model_dump()

    try:
        # Get data from callable request
        request_data = StoreSecretRequest.model_validate(request.data)

        if request_data.store_id is None or request_data.api_key is None:
            return ResponseData(
                    success=False,
                    message='Missing required fields: store_id and api_key',
                    code=400
                ).model_dump()

        db = firestore.client()
        secret_manager = SecretManager(
            project_id=project_id
        )
        secret_service = SecretService(db, secret_manager)

        try:
            secret_service.create_secret(
                store_id=request_data.store_id,
                secret_name=SecretVariants.SHOPIFY_API_KEY.value,
                secret_value=request_data.api_key,
                shop_name=request_data.shop_name,
                description="Shopify API Key for authentication"
            )
        except Exception as e:
            logger.error(f"Error creating secret: {str(e)}")

            return ResponseData(
                success=False,
                message='Secret service failure with error: ' + str(e),
                code=500
            ).model_dump()

        return ResponseData(
                success=True,
                message='API key stored successfully',
                code=200,
                data=request_data.model_dump()
            ).model_dump()

    except Exception as e:
        logger.error(f"Error storing API key: {str(e)}")
        return ResponseData(
                success=False,
                message='Internal server error with error: ' + str(e),
                code=500
            ).model_dump()
