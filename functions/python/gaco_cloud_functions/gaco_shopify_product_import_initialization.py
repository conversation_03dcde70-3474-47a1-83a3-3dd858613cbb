from firebase_admin import firestore
from models.shopify_product_get_operation import ShopifyProductGetOperation
from firebase_functions import logger, https_fn, options, tasks_fn
from services.shopify_product_getter import ShopifyProductGetter
from models.response import ResponseData
from models.requests.shopify_product_import import InitializeShopifyProductImportRequest
from gaco_secrets.secret_manager import SecretManager
from constants.gaco_values import project_id


@tasks_fn.on_task_dispatched(
    region="europe-west3",
    memory=options.MemoryOption.GB_4,
    retry_config=options.RetryConfig(
        min_backoff_seconds=60,
        max_backoff_seconds=60,
        max_attempts=2,
    ),
    rate_limits=options.RateLimits(
        max_concurrent_dispatches=5,
    )
)
def continueGettingProducts(req: tasks_fn.CallableRequest) -> dict:
    """
    Initialize the Shopify product import process.
    """
    product_getter_id = req.data.get(ShopifyProductGetOperation.PRODUCT_GETTER_ID_FIELD)
    if not product_getter_id:
        logger.error("Missing product_getter_id in task payload")
        return

    logger.info(f"Continuing fetch operation {product_getter_id}")

    db = firestore.client()
    secret_manager = SecretManager(project_id=project_id)
    product_getter = ShopifyProductGetter(db, secret_manager)
    fetch_result = product_getter.continue_getting_products(product_getter_id)

    logger.info(f"Fetch result: {fetch_result}")


@https_fn.on_call(
    region="europe-west3",
    memory=options.MemoryOption.GB_4,
    timeout_sec=240
)
def initialize_shopify_product_import(req: https_fn.CallableRequest) -> dict:
    """
    Initialize the Shopify product import process.
    """
    if not req.auth:
        return ResponseData(
            success=False,
            message="Unauthorized",
            code=401
        ).model_dump()

    initialize_shopify_product_import_request = InitializeShopifyProductImportRequest\
        .model_validate(req.data)
    
    store_id = initialize_shopify_product_import_request.store_producer_id
    if not store_id:
        return ResponseData(
            success=False,
            message="store_id is required",
            code=400
        ).model_dump()

    
    try:
        db = firestore.client()
        secret_manager = SecretManager(project_id=project_id)
        product_getter = ShopifyProductGetter(db, secret_manager)
        fetch_result = product_getter.fetch_initial_products(store_id)
        return ResponseData(
            code=200,
            success=True,
            message="Shopify product import initialized",
            data={'data': fetch_result}
        ).model_dump()
    except Exception as e:
        msg = f"Error initializing Shopify product import: {e}"
        logger.error(msg)
        return ResponseData(
            success=False,
            message=msg,
            code=500
        ).model_dump()
