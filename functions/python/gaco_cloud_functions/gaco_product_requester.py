from gaco_framework import (
    gaco_endpoint,
    GacoEndpoint,
    GacoContext,
    GacoResponse,
    AuthRequirement,
)
from firebase_admin import firestore

from services.product_requester import ProductRequester
from services.product_manager import ProductManager
from services.producer_manager import ProducerManager
from services.store_manager import StoreManager
from services.product_allocator import ProductAllocationManager

from models.allocations import StoreProductRequest # This is the primary model for creation
from models.requests.product_request_requests import (
    ApproveStoreProductRequest,
    RejectStoreProductRequest,
    CancelStoreProductRequest
)

# Helper to initialize all services
def _initialize_services(db_client: firestore.Client):
    product_manager = ProductManager(db=db_client)
    # Assuming ProducerManager and StoreManager have simple constructors like ProductManager
    # Adjust if they require more complex setup (e.g., UserRootAccountManager)
    producer_manager = ProducerManager(db=db_client)
    store_manager = StoreManager(db=db_client)
    product_allocator = ProductAllocationManager(db=db_client)
    product_requester = ProductRequester(
        db=db_client,
        product_manager=product_manager,
        producer_manager=producer_manager,
        store_manager=store_manager,
        product_allocator=product_allocator
    )
    return product_requester

@gaco_endpoint(
    require_auth=AuthRequirement.AUTHENTICATED,
    timeout_sec=60,
    memory=1024
)
@GacoEndpoint(StoreProductRequest)
def create_store_product_request(context: GacoContext, request: StoreProductRequest) -> GacoResponse:
    """
    A store user creates a new product request for a producer.
    The user must have the 'store' role and their 'store_id' claim must match the request.
    """
    manager = context.get_manager(ProductRequester)
    created_request = manager.create_request(request_data=request)
    return GacoResponse(
        success=True,
        message="Product request created successfully.",
        data=created_request.model_dump(),
        code=201
    )

@gaco_endpoint(
    require_auth=AuthRequirement.AUTHENTICATED,
    timeout_sec=60,
    memory=1024
)
@GacoEndpoint(ApproveStoreProductRequest)
def approve_store_product_request(context: GacoContext, request: ApproveStoreProductRequest) -> GacoResponse:
    """
    A producer user approves a store's product request.
    The user must have the 'producer' role and their 'producer_id' claim must match the request's producer.
    """
    manager = context.get_manager(ProductRequester)
    approved_request = manager.approve_request(
        request_id=request.request_id,
        producer_response_notes=request.producer_response_notes,
        delivery_method_for_allocation=request.delivery_method_for_allocation,
        allocation_producer_notes=request.allocation_producer_notes
    )
    return GacoResponse(
        success=True,
        message="Request approved and allocation created.",
        data=approved_request.model_dump(),
        code=200
    )

@gaco_endpoint(
    require_auth=AuthRequirement.AUTHENTICATED,
    timeout_sec=60,
    memory=1024
)
@GacoEndpoint(RejectStoreProductRequest)
def reject_store_product_request(context: GacoContext, request: RejectStoreProductRequest) -> GacoResponse:
    """
    A producer user rejects a store's product request.
    The user must have the 'producer' role and their 'producer_id' claim must match the request's producer.
    """
    manager = context.get_manager(ProductRequester)
    rejected_request = manager.reject_request(
        request_id=request.request_id,
        producer_response_notes=request.producer_response_notes
    )
    return GacoResponse(
        success=True,
        message="Request rejected.",
        data=rejected_request.model_dump(),
        code=200
    )

@gaco_endpoint(
    require_auth=AuthRequirement.AUTHENTICATED,
    timeout_sec=60,
    memory=1024
)
@GacoEndpoint(CancelStoreProductRequest)
def cancel_store_product_request(context: GacoContext, request: CancelStoreProductRequest) -> GacoResponse:
    """
    A store user cancels their own product request.
    The user must have the 'store' role and their 'store_id' claim must match the request's store.
    """
    manager = context.get_manager(ProductRequester)
    cancelled_request = manager.cancel_request(request_id=request.request_id)
    return GacoResponse(
        success=True,
        message="Request cancelled.",
        data=cancelled_request.model_dump(),
        code=200
    )
