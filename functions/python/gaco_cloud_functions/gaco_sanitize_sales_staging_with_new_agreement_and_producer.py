from firebase_functions import https_fn, logger, options
from firebase_admin import firestore
from sanitizer.sales_sanitizer_factory import SalesSanitizerFactory
from models.response import Response, ResponseData
from models.requests.sanitization_request import SanitizeSalesStagingWithAgreementRequest
from gaco_framework.auth import AuthContext


@https_fn.on_call(
    region="europe-west3",
    timeout_sec=540,
    memory=options.MemoryOption.GB_4
)
def sanitize_sales_staging_with_new_agreement_and_producer(
    request: https_fn.CallableRequest
) -> dict:
    """
    HTTP Cloud Function to sanitize sales staging with no parentId producer.
    This will create a new agreement and a new producer if does not exist.
    """

    if not request.auth:
        return ResponseData(
            success=False,
            message='User must be authenticated',
            code=401
        ).model_dump()
    
    try:
        # Get data from callable request
        sanitization_request = SanitizeSalesStagingWithAgreementRequest\
            .model_validate(request.data)

        logger.info(f"Sanitization request: {sanitization_request}")

    except Exception as e:
        logger.error(f"Error getting data from callable request: {e}")
        return ResponseData(
            success=False,
            message=f'Error getting data from callable request: {str(e)}',
            code=422
        ).model_dump()

    try:
        # Get data from callable request
        db = firestore.client()
        auth_context = AuthContext(user_id=request.auth.uid)
        sales_sanitizer = SalesSanitizerFactory.create(
            db=db, 
            auth_context=auth_context
        )
        sales_sanitizer.sanitize_sale_with_no_parentId_producer(
            sanitization_request
        )
        
        # Use the helper method to handle document operations
        return ResponseData(
            success=True,
            message='Sales staging with no parentId producer sanitized successfully',
            code=200
        ).model_dump()

    except ValueError as e:
        logger.error(f"Error getting data from callable request: {e}")
        return ResponseData(
            success=False,
            message=f'Client side error: {str(e)}',
            code=400
        ).model_dump()

    except Exception as e:
        logger.error(f"Error getting data from callable request: {e}")
        return ResponseData(
            success=False,
            message='Error getting data from callable request',
            code=500
        ).model_dump()

