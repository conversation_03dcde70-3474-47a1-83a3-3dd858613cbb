from services.product_manager import ProductManager
from gaco_framework import (
    gaco_endpoint,
    GacoEndpoint,
    GacoContext,
    GacoResponse,
    AuthRequirement
)
from gaco_framework.exceptions import ConflictError, NotFoundError, ValidationError as GacoValidationError
from models.products import Product, ProductVariant
from models.requests.product_requests import (
    GetProductRequest,
    UpdateProductRequest,
    DeleteProductRequest,
    GetVariantRequest,
    UpdateVariantRequest,
    DeleteVariantRequest,
    GetVariantsForProductRequest
)
from firebase_functions import logger, options
from typing import Dict, Any


@gaco_endpoint(
  require_auth=AuthRequirement.AUTHENTICATED,
  timeout_sec=60,
  memory=options.MemoryOption.GB_1)
@GacoEndpoint(Product)
def create_product(context: GacoContext, request: Product) -> GacoResponse:
    """
    HTTP Cloud Function to create a product.
    Expects product data (models.products.Product) in request.data.
    """
    manager = context.get_manager(ProductManager)
    created_product = manager.create_product(request)
    return GacoResponse(
        success=True,
        message="Product created successfully.",
        data=created_product.model_dump(),
        code=201
    )


@gaco_endpoint(
  require_auth=AuthRequirement.AUTHENTICATED,
  timeout_sec=60,
  memory=options.MemoryOption.GB_1)
@GacoEndpoint(UpdateProductRequest)
def update_product(context: GacoContext, request: UpdateProductRequest) -> GacoResponse:
    """
    HTTP Cloud Function to update a product.
    Expects UpdateProductRequest model in request.data.
    """
    manager = context.get_manager(ProductManager)
    updated_product = manager.update_product(
        request.logical_product_id, 
        request.update_data
    )
    if not updated_product:
        raise NotFoundError(f"Product with logical ID '{request.logical_product_id}' not found.")
    
    return GacoResponse(
        code=200,
        success=True, 
        message="Product updated successfully.", 
        data=updated_product.model_dump()
    )


@gaco_endpoint(
  require_auth=AuthRequirement.AUTHENTICATED,
  timeout_sec=60,
  memory=options.MemoryOption.GB_1)
@GacoEndpoint(DeleteProductRequest)
def delete_product(context: GacoContext, request: DeleteProductRequest) -> GacoResponse:
    """
    HTTP Cloud Function to delete a product.
    Expects DeleteProductRequest model in request.data.
    """
    manager = context.get_manager(ProductManager)
    doc_id = manager.delete_product(request.logical_product_id, request.delete_variants)
    return GacoResponse(
        code=200,
        success=True, 
        message="Product (and variants, if requested) deleted successfully.",
        data={'product_id': doc_id}
    )

# --- Product Variant Cloud Functions ---

@gaco_endpoint(
  require_auth=AuthRequirement.AUTHENTICATED,
  timeout_sec=60,
  memory=options.MemoryOption.GB_1)
@GacoEndpoint(ProductVariant)
def create_variant(context: GacoContext, request: ProductVariant) -> GacoResponse:
    """
    HTTP Cloud Function to create a product variant.
    Expects variant data (models.products.ProductVariant) in request.data.
    """
    manager = context.get_manager(ProductManager)
    created_variant = manager.create_variant(request)
    return GacoResponse(
        success=True,
        message="Variant created successfully.",
        data=created_variant.model_dump(),
        code=201
    )


@gaco_endpoint(
  require_auth=AuthRequirement.AUTHENTICATED,
  timeout_sec=60,
  memory=options.MemoryOption.GB_1)
@GacoEndpoint(UpdateVariantRequest)
def update_variant(context: GacoContext, request: UpdateVariantRequest) -> GacoResponse:
    """
    HTTP Cloud Function to update a product variant.
    Expects UpdateVariantRequest model in request.data.
    """
    manager = context.get_manager(ProductManager)
    updated_variant = manager.update_variant(request.logical_variant_id, request.update_data)
    if not updated_variant:
        raise NotFoundError(f"Variant with logical ID '{request.logical_variant_id}' not found.")

    return GacoResponse(
        code=200,
        success=True, 
        message="Variant updated successfully.", 
        data=updated_variant.model_dump()
    )


@gaco_endpoint(
  require_auth=AuthRequirement.AUTHENTICATED,
  timeout_sec=60,
  memory=options.MemoryOption.GB_1)
@GacoEndpoint(DeleteVariantRequest)
def delete_variant(context: GacoContext, request: DeleteVariantRequest) -> GacoResponse:
    """
    HTTP Cloud Function to delete a product variant.
    Expects DeleteVariantRequest model in request.data.
    """
    manager = context.get_manager(ProductManager)
    doc_id = manager.delete_variant(request.logical_variant_id)
    return GacoResponse(
        code=200,
        success=True, 
        message="Variant deleted successfully.",
        data={'variant_id': doc_id}
    )

# @https_fn.on_call(
#   region="europe-west3",
#   timeout_sec=60,
#   memory=options.MemoryOption.GB_1)
# def get_product_with_logical_id(request: https_fn.CallableRequest) -> dict:
#     """
#     HTTP Cloud Function to get a product.
#     Expects 'logical_product_id' (GetProductRequest) in request.data.
#     """
#     if not request.auth:
#         return ResponseData(success=False, message='User must be authenticated', code=401).model_dump()
    
#     try:
#         db = firestore.client()
#         product_manager = ProductManager(db=db)
#         req_data = GetProductRequest.model_validate(request.data)
        
#         product = product_manager.get_product(req_data.logical_product_id)
#         if product:
#             return ResponseData(success=True, data=product.model_dump(), code=200).model_dump()
#         else:
#             return ResponseData(success=False, message="Product not found.", code=404).model_dump()
#     except ValidationError as e:
#         logger.error(f"Pydantic validation error in get_product_with_logical_id: {e.errors()}")
#         return ResponseData(success=False, message="Invalid request data.", errors=e.errors(), code=400).model_dump()
#     except ValueError as e: # Should be less common now with Pydantic, but for manager-raised errors
#         logger.error(f"Value error in get_product_with_logical_id: {str(e)}")
#         return ResponseData(success=False, message=str(e), code=400).model_dump()
#     except Exception as e:
#         logger.error(f"Error in get_product_with_logical_id function: {e}")
#         return ResponseData(success=False, message=f"An unexpected error occurred: {e}", code=500).model_dump()


# @https_fn.on_call(
#   region="europe-west3",
#   timeout_sec=60,
#   memory=options.MemoryOption.GB_1)
# def get_variant(request: https_fn.CallableRequest) -> dict:
#     """
#     HTTP Cloud Function to get a product variant.
#     Expects GetVariantRequest model in request.data.
#     """
#     if not request.auth:
#         return ResponseData(success=False, message='User must be authenticated', code=401).model_dump()
        
#     try:
#         db = firestore.client()
#         product_manager = ProductManager(db=db)
#         req_data = GetVariantRequest.model_validate(request.data)
            
#         variant = product_manager.get_variant(req_data.logical_variant_id)
#         if variant:
#             return ResponseData(success=True, data=variant.model_dump(), code=200).model_dump()
#         else:
#             return ResponseData(success=False, message="Variant not found.", code=404).model_dump()
#     except ValidationError as e:
#         logger.error(f"Pydantic validation error in get_variant: {e.errors()}")
#         return ResponseData(success=False, message="Invalid request data.", errors=e.errors(), code=400).model_dump()
#     except ValueError as e:
#         logger.error(f"Value error in get_variant: {str(e)}")
#         return ResponseData(success=False, message=str(e), code=400).model_dump()
#     except Exception as e:
#         logger.error(f"Error in get_variant function: {e}")
#         return ResponseData(success=False, message=f"An unexpected error occurred: {e}", code=500).model_dump()


# @https_fn.on_call(
#   region="europe-west3",
#   timeout_sec=60,
#   memory=options.MemoryOption.GB_1)
# def get_variants_for_product(request: https_fn.CallableRequest) -> dict:
#     """
#     HTTP Cloud Function to get all variants for a product.
#     Expects GetVariantsForProductRequest model in request.data.
#     """
#     if not request.auth:
#         return ResponseData(success=False, message='User must be authenticated', code=401).model_dump()
    
#     try:
#         db = firestore.client()
#         product_manager = ProductManager(db=db)
#         req_data = GetVariantsForProductRequest.model_validate(request.data)
        
#         # Check if parent product exists - this is good practice
#         parent_product = product_manager.get_product(req_data.logical_product_id)
#         if not parent_product:
#              return ResponseData(success=False, message=f"Parent product with ID '{req_data.logical_product_id}' not found.", code=404).model_dump()

#         variants = product_manager.get_variants_for_product_by_logical_id(req_data.logical_product_id)
#         return ResponseData(success=True, data=[v.model_dump() for v in variants], code=200).model_dump()
#     except ValidationError as e:
#         logger.error(f"Pydantic validation error in get_variants_for_product: {e.errors()}")
#         return ResponseData(success=False, message="Invalid request data.", errors=e.errors(), code=400).model_dump()
#     except ValueError as e: # Manager might raise this (though less likely for this specific function)
#         logger.error(f"Value error in get_variants_for_product: {str(e)}")
#         return ResponseData(success=False, message=str(e), code=400).model_dump()
#     except Exception as e:
#         logger.error(f"Error in get_variants_for_product function: {e}")
#         return ResponseData(success=False, message=f"An unexpected error occurred: {e}", code=500).model_dump()
        
        




