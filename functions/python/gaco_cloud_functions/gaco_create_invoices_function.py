# from models.requests.invoice_request import CreateInvoiceRequest
# from firebase_functions import https_fn, logger, options
# from firebase_admin import firestore
# from models.response import ResponseData
# from services.invoice_manager import InvoiceManager


# @https_fn.on_call(
#     region="europe-west3",
#     timeout_sec=600,
#     memory=options.MemoryOption.GB_4
# )
# def create_invoices(request: https_fn.CallableRequest) -> dict:
#     """HTTP Cloud Function to create invoices."""

#     if not request.auth:
#         return ResponseData(
#             success=False,
#             message='User must be authenticated',
#             code=401
#         ).model_dump()

#     try:
#         invoice_request = CreateInvoiceRequest.model_validate(request.data)
#     except Exception as e:
#         logger.error(f"Error in create_invoices function: {e}")
#         return ResponseData(
#             success=False,
#             message=f'Error in create_invoices function: {str(e)}',
#             code=422
#         ).model_dump()

#     try:
#         db = firestore.client()
        
#         # Use the InvoiceManager to handle the creation logic
#         invoice_manager = InvoiceManager(db)
#         success, invoices, error_message = invoice_manager.create_invoices(invoice_request)
        
#         if not success:
#             return ResponseData(
#                 success=False,
#                 message=error_message,
#                 code=500
#             ).model_dump()
        
#         return ResponseData(
#             success=True,
#             message=f'Successfully created {len(invoices)} invoices',
#             code=200
#         ).model_dump()

#     except Exception as e:
#         logger.error(f"Error in create_invoices function: {e}")
#         return ResponseData(
#             success=False,
#             message=f'Error in create_invoices function: {str(e)}',
#             code=500
#         ).model_dump()
