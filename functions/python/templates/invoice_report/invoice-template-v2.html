<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>{{ title }}</title>
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
  <style>
    @page {
      size: A4;
      margin: 0;
    }

    body {
      margin: 0;
      padding: 0;
    }

    #content-to-print {
      width: 210mm;
      height: 297mm;
      padding: 10mm;
      box-sizing: border-box;
    }

    @media print {

      html,
      body {
        width: 210mm;
        height: 297mm;
      }

      .no-print {
        display: none !important;
      }
    }

    table {
      font-size: 9px;
    }

    th,
    td {
      padding: 2px 4px;
    }
  </style>
</head>

<body class="bg-gray-100 p-10">
  <button onclick="generatePDF()"
    class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded m-4 no-print">Export to PDF</button>
  <div id="content-to-print" class="max-w-screen-lg mx-auto bg-white p-8 shadow-lg ">
    <header class="flex justify-between items-center border-b-4 border-black pb-8 mb-8">
      <div>
        <img src="../ga!-monogram.png" alt="gastartcoop" class="w-48">
        <h2 class="pt-6 text-xl">FACTUUR : {{ invoice_id }}</h2>
      </div>
      <div class="text-right">
        <p>{{ producer_level.producer_display_name }}</p>
        <!-- Remove or update other recipient details as needed -->
      </div>
    </header>
    <table class="w-full mb-8">
      <thead>
        <tr class="bg-black">
          <th class="border px-4 py-2 text-white">Item title
          </th>
          <th class="border px-4 py-2 text-white">variant</th>
          <th class="border px-4 py-2 text-white">Item Count</th>
          <th class="border px-4 py-2 text-white">Subtotal</th>
          <th class="border px-4 py-2 text-white">VAT Rate</th>
          <th class="border px-4 py-2 text-white">Net Sales</th>
          <th class="border px-4 py-2 text-white">Commission</th>
          <th class="border px-4 py-2 text-white">Producer Payout</th>
        </tr>
      </thead>
      <tbody>
        {% for sale in sales_level_data %}
        <tr>
          <td class="border px-4 py-2">{{ sale.title }}</td>
          <td class="border px-4 py-2">{{ sale.variant_title }}</td>
          <td class="border px-4 py-2">{{ sale.item_count }}</td>
          <td class="border px-4 py-2 text-center">€{{ sale.subtotal }}</td>
          <td class="border px-4 py-2 text-center">{{ sale.assigned_vat_rate }}%</td>
          <td class="border px-4 py-2 text-center">€{{ sale.net_sales }}</td>
          <td class="border px-4 py-2 text-center">{{ sale.commission }}%</td>
          <td class="border px-4 py-2 text-center">€{{ sale.producer_gross_payout }}</td>
        </tr>
        {% endfor %}
      </tbody>
    </table>
    <div class="text-right mb-8">
      <p>Subtotal: <span class="font-bold">€{{ producer_level.subtotal }}</span></p>
      <p>Net Sales: <span class="font-bold">€{{ producer_level.net_sales }}</span></p>
      <p>VAT on Sale: <span class="font-bold">€{{ producer_level.vat_excluded_sale }}</span></p>
      <p>VAT on Sales Service: <span class="font-bold">€{{ producer_level.vat_on_sales_service }}</span></p>
      <p>Store Total Gross Payout: <span class="font-bold">€{{ producer_level.store_total_gross_payout }}</span></p>
      <p class="text-xl font-bold">Total Producer Payout: €{{ producer_level.producer_gross_payout }}</p>
    </div>
    <div class="mb-8">
      <p>Factuur Date: {{ factuur_date }}</p>
      <p>Payout due by: {{ payout_due_date }}</p>
    </div>
    <footer class="border-t border-black pt-8 text-sm">
      <div class="flex justify-between">
        <div>
          <p class="font-bold">Payout details:</p>
          <p>Payment Reference: {{ invoice_number }}</p>
          <p>Address: Gasthuismolensteeg 3, 1016AM, Amsterdam, Netherlands</p>
          <p>E-mail: <EMAIL></p>
          <p>IBAN: ******************, BIC: BUNQNL2AXXX</p>
          <p>KVK No.: 83765395</p>
          <p>VAT No.: NL862892467B01</p>
        </div>
        <div class="mt-6">
          <img src="../g-a-c-o-p.jpeg" alt="gastartcoop" class="w-48 h-24">
        </div>
      </div>
    </footer>
  </div>

  <script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js"></script>
  <script>
    function generatePDF() {
      const element = document.getElementById('content-to-print');
      const options = {
        filename: 'invoice_{{ recipient_name | replace(" ", "_") }}_{{ invoice_number }}_{{ factuur_date }}.pdf',
        image: { type: 'jpeg', quality: 0.98 },
        html2canvas: { scale: 2, logging: true, dpi: 192, letterRendering: true },
        jsPDF: { unit: 'mm', format: 'a4', orientation: 'portrait' }
      };
      html2pdf().set(options).from(element).save();
    }
  </script>
</body>

</html>