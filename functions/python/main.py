__all__ = [
  'store_api_key', 
  'get_api_key', 
  'on_storage_shopify_orders_file_added', 
  'on_sales_staging_updated',
  'on_sales_staging_created',
  'sanitize_sales_staging_with_new_agreement',
  'on_sales_silver_created',
  'on_sales_silver_updated',
  'delete_invoice',
  'create_store',
  'delete_store',
  'create_producer',
  'delete_producer',
  'get_shopify_orders',
  'continueGettingOrders', # due to naming limitation for the queue and task function
  'continueGettingProducts', # due to naming limitation for the queue and task function
  'get_shopify_orders_with_dynamic_query',
  'create_agreement',
  'update_draft_agreement',
  'submit_agreement_for_approval',
  'approve_agreement',
  'terminate_agreement',
  'delete_agreement',
  'delete_partnership',
  'create_sales_reports',
  'delete_sales_report',
  'enqueue_send_email',
  'sendEmail',
  'get_product_with_logical_id',
  'create_product',
  'update_product',
  'delete_product',
  'get_product',
  'get_variants_for_product',
  'create_variant',
  'get_variant',
  'update_variant',
  'delete_variant',
  'get_variant_for_product',
  'create_allocation',
  'update_allocation_status',
  'create_store_product_request',
  'approve_store_product_request',
  'reject_store_product_request',
  'cancel_store_product_request',
  'create_product_in_shopify',
  'initialize_shopify_product_import',
  'on_storage_shopify_products_file_added'
]

from firebase_admin import initialize_app

from gaco_framework.config import configure_gaco
from gaco_cloud_functions.gaco_shopify_orders_parser import * 
from gaco_cloud_functions.gaco_store_secret_function import *
from gaco_cloud_functions.gaco_delete_secret_function import *
# from gaco_cloud_functions.gaco_create_invoices_function import *
# from gaco_cloud_functions.gaco_delete_invoice_function import *
from gaco_cloud_functions.gaco_sanitize_sales_on_update import *
from gaco_cloud_functions.gaco_sanitize_sales_staging_with_new_agreement_and_producer import *
from gaco_cloud_functions.gaco_sanitize_sales_staging_with_new_agreement import *
from gaco_cloud_functions.gaco_update_sales_silver_to_gold import *
from gaco_cloud_functions.gaco_store_manager_function import *
from gaco_cloud_functions.gaco_producer_manager_function import *
from gaco_cloud_functions.gaco_get_shopify_orders import *
from gaco_cloud_functions.gaco_shopify_orders_background import *
from gaco_cloud_functions.gaco_get_shopify_orders_with_dynamic_query import *
from gaco_cloud_functions.gaco_agreements import *
from gaco_cloud_functions.gaco_delete_partnership import *
from gaco_cloud_functions.gaco_create_sales_report import *
from gaco_cloud_functions.gaco_delete_sales_report import *
from gaco_cloud_functions.gaco_email_manager import *
from gaco_cloud_functions.gaco_product_manager import *
from gaco_cloud_functions.gaco_product_allocator import *
from gaco_cloud_functions.gaco_product_requester import *
from gaco_cloud_functions.gaco_create_product_in_shopify import *
from gaco_cloud_functions.gaco_shopify_product_import_initialization import *
from gaco_cloud_functions.gaco_shopify_products_parser import *
from gaco_cloud_functions.gaco_create_product_in_shopify import *
from gaco_cloud_functions.gaco_on_user_root_account_update import *

from constants.gaco_values import project_id


# Configure Gaco Framework
# TODO: Replace 'your-gcp-project-id' with your actual GCP project ID
configure_gaco(
    project_id=project_id,
    default_region="europe-west3",
    enable_security_rules=True,
    log_security_violations=True,
)

initialize_app()
