from hash_forge import <PERSON><PERSON><PERSON><PERSON><PERSON>
from hash_forge.hashers import <PERSON><PERSON><PERSON><PERSON><PERSON>

def shopify_gid_to_hash(gid: str, salt: str = 'shopify_gid') -> str:
    # Define base62 characters (0-9, a-z, A-Z)
    hash_length = 30
    BASE62 = (
        "0123456789"
        "abcdefghijklmnopqrstuvwxyz"
        "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
    )
    
    hash_manager = HashManager(Blake2Hasher(salt))
    hashed_gid = hash_manager.hash(gid)
    
    # Convert the hash to a number
    hash_value = hashed_gid.split('$')[-1]
    n = int(hash_value, 16)
    result = ""
    
    # Take first 30 characters only
    while n > 0 and len(result) < hash_length:
        n, remainder = divmod(n, 62)
        result = BASE62[remainder] + result
    
    # Pad with leading characters if necessary
    while len(result) < hash_length:
        result = BASE62[0] + result
        
    return result
